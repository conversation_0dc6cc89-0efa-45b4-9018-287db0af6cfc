// types/pdfjs-dist-legacy.d.ts
declare module "pdfjs-dist/legacy/build/pdf" {
  // Re-export types from the pdfjs-dist types
  export * from "pdfjs-dist/types/src/display/api";
  export * from "pdfjs-dist/types/src/pdf";

  // If needed, declare GlobalWorkerOptions explicitly:
  export const GlobalWorkerOptions: {
    workerSrc: string;
  };

  // If needed, declare getDocument function explicitly:
  export function getDocument(
    src: string | Uint8Array | { data: Uint8Array }
  ): any;
}
