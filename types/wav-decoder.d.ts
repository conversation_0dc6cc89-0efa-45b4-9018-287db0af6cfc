// File: types/wav-decoder.d.ts

declare module "wav-decoder" {
  interface DecodedWav {
    sampleRate: number;
    channelData: Float32Array[]; // Based on your usage: channelData[0] is Float32Array
    // You might need to add other properties if the library returns more
    // and you intend to use them. For now, this covers your current code.
  }

  // Your import is: import WavDecoder from "wav-decoder";
  // And usage: const decoded = WavDecoder.decode.sync(wavBuffer);
  // This implies WavDecoder is an object with a 'decode' property,
  // which itself is an object with a 'sync' method.

  const WavDecoder: {
    decode: {
      sync: (buffer: Buffer, options?: { lazy: boolean }) => DecodedWav; // Added optional options based on common wav-decoder usage
      // If there's an async version:
      // (buffer: Buffer, options?: { lazy: boolean }): Promise<DecodedWav>;
    };
  };

  export default WavDecoder;
}
