{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "typeRoots": ["./types", "./node_modules/@types"],
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true, // [ADDED] Helps with default imports
    "forceConsistentCasingInFileNames": true, // [ADDED] Avoid case-related import issues
    "skipDefaultLibCheck": true, // [ADDED] Skips checking default lib .d.ts
    "module": "commonjs",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "types": ["node", "jest"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ],
    "target": "es2015"
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "jest.config.js",
    "app/api/annotateDocx",
    "server.ts"
  ],
  "exclude": ["node_modules"]
}
