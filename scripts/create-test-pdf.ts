// scripts/create-test-pdf.ts
import { PDFDocument, StandardFonts, rgb } from "pdf-lib";
import fs from "fs";
import path from "path";

async function createTestPDF() {
  // Create a new PDFDocument
  const pdfDoc = await PDFDocument.create();

  // Add a blank page
  const page = pdfDoc.addPage();

  // Get the times-roman font
  const font = await pdfDoc.embedFont(StandardFonts.TimesRoman);

  // Add some text to the page
  page.setFont(font);
  page.setFontSize(24);
  page.drawText("Test PDF Document", {
    x: 50,
    y: page.getHeight() - 50,
    color: rgb(0, 0, 0),
  });

  page.setFontSize(12);
  page.drawText('This is a sample PDF with the word "fatha" in it.', {
    x: 50,
    y: page.getHeight() - 100,
    color: rgb(0, 0, 0),
  });

  page.drawText("Here is another fatha for testing purposes.", {
    x: 50,
    y: page.getHeight() - 150,
    color: rgb(0, 0, 0),
  });

  // Save the PDF
  const pdfBytes = await pdfDoc.save();

  // Write to the public directory
  const publicDir = path.join(process.cwd(), "public");
  fs.writeFileSync(path.join(publicDir, "test.pdf"), pdfBytes);
}

createTestPDF().catch(console.error);
