[{"id": 11, "challenge_id": 1, "text": "ب", "correct": false, "audio_src": "/ba.wav"}, {"id": 12, "challenge_id": 1, "text": "ج", "correct": false, "audio_src": "/jim.wav"}, {"id": 13, "challenge_id": 1, "text": "ا", "correct": true, "audio_src": "/alif.wav"}, {"id": 21, "challenge_id": 2, "text": "tha", "correct": true, "audio_src": ""}, {"id": 22, "challenge_id": 2, "text": "ta", "correct": false, "audio_src": ""}, {"id": 23, "challenge_id": 2, "text": "ba", "correct": false, "audio_src": ""}, {"id": 35, "challenge_id": 3, "text": "ج", "correct": false, "image_src": "", "audio_src": "", "match_pair_id": 5, "side": "left"}, {"id": 33, "challenge_id": 3, "text": "ت", "correct": false, "image_src": "", "audio_src": "", "match_pair_id": 3, "side": "left"}, {"id": 36, "challenge_id": 3, "text": "<PERSON>if", "correct": false, "image_src": "/audiowave.svg", "audio_src": "/alif.wav", "match_pair_id": 1, "side": "right"}, {"id": 32, "challenge_id": 3, "text": "ب", "correct": false, "image_src": "", "audio_src": "", "match_pair_id": 2, "side": "left"}, {"id": 38, "challenge_id": 3, "text": "ta", "correct": false, "image_src": "/audiowave.svg", "audio_src": "/ta.wav", "match_pair_id": 3, "side": "right"}, {"id": 34, "challenge_id": 3, "text": "ث", "correct": false, "image_src": "", "audio_src": "", "match_pair_id": 4, "side": "left"}, {"id": 40, "challenge_id": 3, "text": "jim", "correct": false, "image_src": "/audiowave.svg", "audio_src": "/jim.wav", "match_pair_id": 5, "side": "right"}, {"id": 37, "challenge_id": 3, "text": "ba", "correct": false, "image_src": "/audiowave.svg", "audio_src": "/ba.wav", "match_pair_id": 2, "side": "right"}, {"id": 31, "challenge_id": 3, "text": "ا", "correct": false, "image_src": "", "audio_src": "", "match_pair_id": 1, "side": "left"}, {"id": 39, "challenge_id": 3, "text": "tha", "correct": false, "image_src": "/audiowave.svg", "audio_src": "/tha.wav", "match_pair_id": 4, "side": "right"}, {"id": 41, "challenge_id": 4, "text": "ت", "correct": true, "audio_src": "/ta.wav"}, {"id": 42, "challenge_id": 4, "text": "ث", "correct": false, "audio_src": "/tha.wav"}, {"id": 43, "challenge_id": 4, "text": "ا", "correct": false, "audio_src": "/alif.wav"}, {"id": 51, "challenge_id": 5, "text": "ت", "correct": false, "audio_src": "/ta.wav"}, {"id": 52, "challenge_id": 5, "text": "ج", "correct": true, "audio_src": "/jim.wav"}, {"id": 53, "challenge_id": 5, "text": "ث", "correct": false, "audio_src": "/tha.wav"}, {"id": 61, "challenge_id": 6, "text": "<PERSON>if", "correct": false, "audio_src": "/alif.wav"}, {"id": 62, "challenge_id": 6, "text": "jim", "correct": true, "audio_src": "/jim.wav"}, {"id": 63, "challenge_id": 6, "text": "ta", "correct": false, "audio_src": "/ta.wav"}, {"id": 64, "challenge_id": 6, "text": "ba", "correct": false, "audio_src": "/ba.wav"}, {"id": 71, "challenge_id": 7, "text": "<PERSON>if", "correct": false}, {"id": 72, "challenge_id": 7, "text": "jim", "correct": false}, {"id": 73, "challenge_id": 7, "text": "ta", "correct": true}, {"id": 74, "challenge_id": 7, "text": "tha", "correct": false}, {"id": 81, "challenge_id": 8, "text": "ب", "correct": true, "audio_src": ""}, {"id": 82, "challenge_id": 8, "text": "ا", "correct": false, "audio_src": ""}, {"id": 83, "challenge_id": 8, "text": "ت", "correct": false, "audio_src": ""}, {"id": 84, "challenge_id": 8, "text": "ج", "correct": false, "audio_src": ""}, {"id": 95, "challenge_id": 9, "text": "ج", "correct": false, "audio_src": "/jim.wav", "match_pair_id": 5, "side": "left"}, {"id": 98, "challenge_id": 9, "text": "ta", "correct": false, "audio_src": "", "match_pair_id": 3, "side": "right"}, {"id": 97, "challenge_id": 9, "text": "ba", "correct": false, "audio_src": "", "match_pair_id": 2, "side": "right"}, {"id": 91, "challenge_id": 9, "text": "ا", "correct": false, "audio_src": "/alif.wav", "match_pair_id": 1, "side": "left"}, {"id": 94, "challenge_id": 9, "text": "ث", "correct": false, "audio_src": "/tha.wav", "match_pair_id": 4, "side": "left"}, {"id": 92, "challenge_id": 9, "text": "ب", "correct": false, "audio_src": "/ba.wav", "match_pair_id": 2, "side": "left"}, {"id": 93, "challenge_id": 9, "text": "ت", "correct": false, "audio_src": "/ta.wav", "match_pair_id": 3, "side": "left"}, {"id": 96, "challenge_id": 9, "text": "<PERSON>if", "correct": false, "audio_src": "", "match_pair_id": 1, "side": "right"}, {"id": 99, "challenge_id": 9, "text": "tha", "correct": false, "audio_src": "", "match_pair_id": 4, "side": "right"}, {"id": 100, "challenge_id": 9, "text": "jim", "correct": false, "audio_src": "", "match_pair_id": 5, "side": "right"}, {"id": 101, "challenge_id": 10, "text": "<PERSON>if", "correct": true, "audio_src": "/alif.wav", "image_src": "/audiowave.svg"}, {"id": 102, "challenge_id": 10, "text": "ba", "correct": false, "audio_src": "/ba.wav", "image_src": "/audiowave.svg"}, {"id": 103, "challenge_id": 10, "text": "jim", "correct": false, "audio_src": "/jim.wav", "image_src": "/audiowave.svg"}]