// convertExcelToJson.js
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

// Read the Excel file
const workbook = xlsx.readFile(path.join(__dirname, "quizStructure.xlsx"));

// Ensure the 'data' directory exists
const dataDir = path.join(__dirname, "data");
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir);
}

// Convert each sheet to JSON and write to respective JSON files
const sheetNames = workbook.SheetNames;
sheetNames.forEach((sheetName) => {
  const sheetData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);
  fs.writeFileSync(
    path.join(dataDir, `${sheetName}.json`),
    JSON.stringify(sheetData, null, 2)
  );
});

console.log("Conversion from Excel to JSON completed successfully.");
