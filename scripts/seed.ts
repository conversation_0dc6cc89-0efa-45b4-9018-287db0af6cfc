import "dotenv/config";
import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import fs from "fs";
import path from "path";
import * as schema from "../db/schema";

// Set up the database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema });

// Utility function to read JSON files
const readJsonFile = (fileName: string) => {
  const filePath = path.join(__dirname, "data", fileName);
  const fileContent = fs.readFileSync(filePath, "utf8");
  console.log(`Reading file: ${fileName}`);
  try {
    return JSON.parse(fileContent);
  } catch (error) {
    console.error(`Error parsing JSON file: ${fileName}`, error);
    throw error;
  }
};

const main = async () => {
  try {
    console.log("Seeding database");

    // Delete existing data
    await db.delete(schema.challengeOptions);
    await db.delete(schema.challenges);
    await db.delete(schema.lessons);
    await db.delete(schema.units);
    await db.delete(schema.courses);
    await db.delete(schema.userProgress);
    await db.delete(schema.challengeProgress);
    await db.delete(schema.userSubscription);

    // Insert courses
    let coursesData = readJsonFile("Courses.json");
    // Ensure keys align with your schema: image_src
    coursesData = coursesData.map((course: any) => ({
      id: course.id,
      title: course.title,
      imageSrc: course.image_src, // Mapping image_src to imageSrc
    }));
    console.log("Courses data:", coursesData);
    if (coursesData.length > 0) {
      await db.insert(schema.courses).values(coursesData);
    }

    // Insert units
    let unitsData = readJsonFile("Units.json");
    // Ensure keys align with your schema: course_id
    unitsData = unitsData.map((unit: any) => ({
      id: unit.id,
      title: unit.title,
      description: unit.description,
      courseId: unit.course_id, // Mapping course_id to courseId
      order: unit.order,
    }));
    if (unitsData.length > 0) {
      await db.insert(schema.units).values(unitsData);
    }

    // Insert lessons
    let lessonsData = readJsonFile("Lessons.json");
    // Ensure keys align with your schema: unit_id
    lessonsData = lessonsData.map((lesson: any) => ({
      id: lesson.id,
      title: lesson.title,
      unitId: lesson.unit_id, // Mapping unit_id to unitId
      order: lesson.order,
    }));
    if (lessonsData.length > 0) {
      await db.insert(schema.lessons).values(lessonsData);
    }

    // Insert challenges
    let challengesData = readJsonFile("Challenges.json");
    // Ensure keys align with your schema: lesson_id, audio_src, media_type, etc.
    challengesData = challengesData.map((challenge: any) => ({
      id: challenge.id,
      lessonId: challenge.lesson_id, // Mapping lesson_id to lessonId
      type: challenge.type,
      question: challenge.question,
      order: challenge.order,
      audioSrc: challenge.audio_src, // Mapping audio_src to audioSrc
      mediaType: challenge.media_type, // Mapping media_type to mediaType
      topCardText: challenge.top_card_text,
      topCardAudio: challenge.top_card_audio,
      sentence: challenge.sentence,
    }));
    if (challengesData.length > 0) {
      await db.insert(schema.challenges).values(challengesData);
    }

    // Insert challenge options
    let challengeOptionsData = readJsonFile("Challenge Options.json");
    // Ensure keys align with your schema: challenge_id, image_src, audio_src, etc.
    challengeOptionsData = challengeOptionsData.map((option: any) => ({
      id: option.id,
      challengeId: option.challenge_id, // Mapping challenge_id to challengeId
      text: option.text,
      correct: option.correct,
      imageSrc: option.image_src, // Mapping image_src to imageSrc
      audioSrc: option.audio_src, // Mapping audio_src to audioSrc
      matchPairId: option.match_pair_id, // Mapping match_pair_id to matchPairId
      side: option.side,
      sequence: option.sequence,
    }));
    if (challengeOptionsData.length > 0) {
      await db.insert(schema.challengeOptions).values(challengeOptionsData);
    }

    console.log(
      "Seeding finished with courses, units, lessons, challenges, and challenge options data"
    );
  } catch (error) {
    console.error("Failed to seed the database:", error);
    throw new Error("Database seeding failed");
  }
};

main();
