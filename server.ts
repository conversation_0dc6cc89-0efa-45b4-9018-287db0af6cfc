/**
 * server.ts (enhanced with type definitions and OpenAI Proxy)
 */

// Import type definitions for Express & Socket.IO
import type { Request, Response } from "express";
import type { Server as SocketIOServer, Socket } from "socket.io";
// NEW: Import WebSocket for the OpenAI proxy
import WebSocket from "ws";

// --- Existing CommonJS imports (left intact) ---
const express = require("express");
const { createServer } = require("http");
const { parse } = require("url");
const next = require("next");
const { Server } = require("socket.io");

// --- New: Import DB and SQL helper for unread count updates ---
const db = require("./db/drizzle");
const { sql } = require("drizzle-orm");
const { messages } = require("./db/schema");

// --- DEBUG: Enhanced startup logging ---
console.log("=== SERVER STARTUP DEBUG ===");
console.log("Node ENV:", process.env.NODE_ENV);
console.log("API Key present:", !!process.env.OPENAI_API_KEY);
console.log(
  "API Key prefix:",
  process.env.OPENAI_API_KEY?.substring(0, 7) || "MISSING"
);
console.log("API Key length:", process.env.OPENAI_API_KEY?.length || 0);
console.log("Current working directory:", process.cwd());
console.log("Current timestamp:", new Date().toISOString());
console.log("=============================");

// --- Next.js config ---
const dev = process.env.NODE_ENV !== "production";
const app = next({ dev });
const handle = app.getRequestHandler();

// ===============================================
// ENHANCEMENT: In-memory storage & Configuration
// ===============================================

// --- Step 1 & 3: Connection Retry Configuration ---
const RETRY_CONFIG = {
  initialDelay: 500, // Initial delay in ms
  maxAttempts: 5,
  maxDelay: 30000, // 30 seconds
};

// --- Step 2: Message Queue Configuration ---
const MAX_QUEUE_SIZE = 100;

// --- Existing Storage ---
const annotators: Set<string> = new Set();
interface Annotation {
  userId: string;
  [key: string]: any;
}
let globalAnnotations: Annotation[] = [];
const userSocketMap: Map<string, string> = new Map();

// --- Enhanced Storage for OpenAI Proxy ---
// Stores the active WebSocket connection for each client session.
const openAISockets = new Map<string, WebSocket>();
// Stores messages that couldn't be sent while disconnected.
const messageQueues = new Map<string, any[]>();
// Tracks the state of reconnection attempts for each client.
const reconnectionState = new Map<
  string,
  { attempts: number; isRetrying: boolean; timerId?: NodeJS.Timeout }
>();
// Tracks connection health metrics for monitoring and debugging.
const connectionHealthStats = new Map<
  string,
  {
    successfulConnections: number;
    disconnects: number;
    lastError: string;
    lastConnectionTimeMs: number | null;
    connectionStartTime: number | null;
  }
>();
// Tracks if a client explicitly requested to stop the conversation.
const explicitStopFlags = new Map<string, boolean>();

function broadcastAnnotators(io: SocketIOServer) {
  io.emit("annotators-updated", { annotators: Array.from(annotators) });
}

// =================================
// Helper Functions
// =================================

/**
 * Step 4: Checks if a WebSocket close code represents a recoverable error.
 * Non-recoverable errors are typically authentication/authorization issues.
 */
function isRecoverableCloseCode(code: number): boolean {
  // 4xxx codes are client errors (e.g., auth) and are not recoverable by retrying.
  return code < 4000 || code >= 5000;
}

/**
 * Cleans up all resources associated with a client's OpenAI session.
 */
function cleanupClientSession(socketId: string) {
  console.log(`[Cleanup] Tearing down session for client ${socketId}`);
  // Close and delete the WebSocket connection
  const ws = openAISockets.get(socketId);
  if (ws && ws.readyState !== WebSocket.CLOSED) {
    ws.close(1001, "Client session cleanup");
  }
  openAISockets.delete(socketId);

  // Clear any pending reconnection timers
  const state = reconnectionState.get(socketId);
  if (state?.timerId) {
    clearTimeout(state.timerId);
  }

  // Delete all associated state
  explicitStopFlags.delete(socketId);
  messageQueues.delete(socketId);
  reconnectionState.delete(socketId);
  connectionHealthStats.delete(socketId); // Optional: keep for long-term analysis
}

app.prepare().then(() => {
  console.log("[Server] Next.js app prepared.");
  const server = express();

  server.all("*", (req: Request, res: Response) => {
    const parsedUrl = parse(req.url, true);
    return handle(req, res, parsedUrl);
  });

  const httpServer = createServer(server);
  const io = new Server(httpServer, {
    cors: {
      origin: "*",
    },
  });

  // ==============================
  // Socket.IO Connection Logic
  // ==============================
  io.on("connection", (socket: Socket) => {
    console.log("[Socket.IO] Client connected:", socket.id);

    socket.on("debug-log", ({ component, message, timestamp }) => {
      console.log(`[DEBUG][${timestamp}][${component}] ${message}`);
    });

    // =======================================================
    // NEW: OpenAI Realtime API Proxy Logic (FULLY ENHANCED)
    // =======================================================

    const attemptConnection = (socket: Socket) => {
      const socketId = socket.id;
      const health = connectionHealthStats.get(socketId)!;
      const apiKey = process.env.OPENAI_API_KEY;

      if (!apiKey) {
        console.error("[OpenAI Proxy] FATAL: OPENAI_API_KEY is not set.");
        socket.emit("openai-error", {
          message: "Server configuration error: API key missing.",
          isPermanent: true,
        });
        cleanupClientSession(socketId);
        return;
      }

      console.log(`[OpenAI Proxy] Attempting connection for ${socketId}...`);
      health.connectionStartTime = Date.now();

      const url = `wss://api.openai.com/v1/realtime?model=gpt-4o-mini-realtime-preview-2024-12-17`;
      const headers = {
        Authorization: `Bearer ${apiKey}`,
        "openai-beta": "realtime=v1",
      };
      const openAIWs = new WebSocket(url, { headers });

      // Only set the socket once the instance is created
      openAISockets.set(socketId, openAIWs);

      openAIWs.on("open", () => {
        console.log(
          `[OpenAI Proxy] ✅ SUCCESS: Connection established for ${socketId}.`
        );
        const state = reconnectionState.get(socketId)!;

        // Step 5: Update Health Metrics
        health.successfulConnections += 1;
        health.lastConnectionTimeMs = Date.now() - health.connectionStartTime!;
        console.log(
          `[Health] Connection for ${socketId} established in ${health.lastConnectionTimeMs}ms.`
        );

        // Reset retry attempts on successful connection
        state.attempts = 0;
        state.isRetrying = false;
        if (state.timerId) clearTimeout(state.timerId);

        socket.emit("openai-conversation-started");

        // Step 2: Process Message Queue
        const queue = messageQueues.get(socketId) || [];
        if (queue.length > 0) {
          console.log(
            `[Queue] Processing ${queue.length} queued messages for ${socketId}.`
          );
          for (const msg of queue) {
            openAIWs.send(JSON.stringify(msg));
          }
          messageQueues.set(socketId, []); // Clear queue after processing
        }
      });

      openAIWs.on("message", (data: Buffer) => {
        const message = JSON.parse(data.toString());
        socket.emit("openai-server-message", message);
      });

      openAIWs.on("error", (error: Error) => {
        console.error(
          `[OpenAI Proxy] WebSocket Error for ${socketId}:`,
          error.message
        );
        health.disconnects += 1;
        health.lastError = `Error: ${error.message}`;
        socket.emit("openai-error", {
          message: error.message || "A WebSocket error occurred.",
          isPermanent: false,
        });
        // The 'close' event will usually fire right after, which handles the reconnection logic.
      });

      openAIWs.on("close", (code: number, reason: Buffer) => {
        const reasonStr = reason.toString();
        console.log(
          `[OpenAI Proxy] Connection closed for ${socketId}. Code: ${code} (${getCloseCodeMeaning(
            code
          )}), Reason: "${reasonStr}"`
        );

        // Step 5: Update Health Metrics
        health.disconnects += 1;
        health.lastError = `Close: ${code} - ${reasonStr}`;

        // If the client explicitly stopped, just clean up
        if (explicitStopFlags.get(socketId)) {
          console.log(
            `[OpenAI Proxy] Explicit stop confirmed for ${socketId}.`
          );
          socket.emit("openai-conversation-closed", {
            code,
            reason: reasonStr,
          });
          cleanupClientSession(socketId);
          return;
        }

        // Step 4: Enhanced Error Handling - check if we should retry
        if (!isRecoverableCloseCode(code)) {
          console.error(
            `[OpenAI Proxy] Unrecoverable error for ${socketId}. Not retrying.`
          );
          socket.emit("openai-error", {
            message: `Permanent connection failure (${code}): ${reasonStr}`,
            isPermanent: true,
          });
          cleanupClientSession(socketId);
          return;
        }

        // Step 3: Trigger reconnection logic
        reconnect(socket);
      });
    };

    const reconnect = (socket: Socket) => {
      const socketId = socket.id;
      const state = reconnectionState.get(socketId);
      if (!state || state.isRetrying) return; // Already handling it

      if (state.attempts >= RETRY_CONFIG.maxAttempts) {
        console.error(
          `[OpenAI Proxy] Max retry attempts reached for ${socketId}. Aborting.`
        );
        socket.emit("openai-error", {
          message: "Failed to connect to the service after multiple attempts.",
          isPermanent: true,
        });
        cleanupClientSession(socketId);
        return;
      }

      state.isRetrying = true;
      state.attempts += 1;

      // Step 3: Exponential Backoff Calculation
      const delay = Math.min(
        RETRY_CONFIG.initialDelay * Math.pow(2, state.attempts - 1) +
          Math.random() * 1000,
        RETRY_CONFIG.maxDelay
      );

      console.log(
        `[OpenAI Proxy] Reconnecting ${socketId} in ${delay.toFixed(
          0
        )}ms (Attempt ${state.attempts}/${RETRY_CONFIG.maxAttempts})`
      );
      socket.emit("openai-reconnecting", { attempt: state.attempts, delay });

      state.timerId = setTimeout(() => {
        state.isRetrying = false;
        attemptConnection(socket);
      }, delay);
    };

    socket.on("start-openai-conversation", () => {
      console.log(
        `[OpenAI Proxy] Client ${socket.id} requested to start conversation.`
      );
      if (
        openAISockets.has(socket.id) ||
        reconnectionState.get(socket.id)?.isRetrying
      ) {
        console.log(
          `[OpenAI Proxy] Conversation already active or starting for ${socket.id}.`
        );
        return;
      }

      // Initialize state for the new session
      explicitStopFlags.set(socket.id, false);
      messageQueues.set(socket.id, []);
      reconnectionState.set(socket.id, { attempts: 0, isRetrying: false });
      connectionHealthStats.set(socket.id, {
        successfulConnections: 0,
        disconnects: 0,
        lastError: "None",
        lastConnectionTimeMs: null,
        connectionStartTime: null,
      });

      attemptConnection(socket);
    });

    socket.on("openai-client-message", (message: any) => {
      const socketId = socket.id;
      const openAIWs = openAISockets.get(socketId);

      if (openAIWs && openAIWs.readyState === WebSocket.OPEN) {
        openAIWs.send(JSON.stringify(message));
      } else {
        // Step 2: Add to Message Queue
        const queue = messageQueues.get(socketId);
        if (queue) {
          if (queue.length < MAX_QUEUE_SIZE) {
            queue.push(message);
            console.log(
              `[Queue] Message queued for ${socketId}. Queue size: ${queue.length}.`
            );
          } else {
            console.warn(
              `[Queue] Queue full for ${socketId}. Message dropped.`
            );
            socket.emit("openai-error", {
              message: "Message queue is full. Please wait.",
              isPermanent: false,
            });
          }
        } else {
          console.warn(
            `[OpenAI Proxy] No session found for ${socketId}. Message dropped.`
          );
        }

        // If not already trying to reconnect, start the process.
        const state = reconnectionState.get(socketId);
        if (state && !state.isRetrying) {
          // The 'close' handler should have already triggered this, but we call it
          // here as a failsafe in case the client sends a message before 'close' fires.
          reconnect(socket);
        }
      }
    });

    socket.on("stop-openai-conversation", () => {
      console.log(
        `[DEBUG] 🛑 Client ${socket.id} requested to explicitly stop conversation.`
      );
      explicitStopFlags.set(socket.id, true); // Mark as explicit stop
      const openAIWs = openAISockets.get(socket.id);
      if (openAIWs) {
        // The 'close' handler will see the flag and perform the full cleanup.
        openAIWs.close(1000, "Client requested stop");
      } else {
        // If there's no socket, just clean up the state.
        cleanupClientSession(socket.id);
      }
    });

    // =======================================================
    // Existing Socket.IO Logic (Preserved and Enhanced)
    // =======================================================
    socket.on("disconnect", (reason: string) => {
      console.log(
        "[Socket.IO] Client disconnected:",
        socket.id,
        "Reason:",
        reason
      );
      // Clean up all resources associated with this client's session
      cleanupClientSession(socket.id);

      for (const [userId, sId] of userSocketMap.entries()) {
        if (sId === socket.id) {
          userSocketMap.delete(userId);
          break;
        }
      }
    });

    // All other existing event handlers are preserved
    socket.on("register", (data: any) => {
      if (data && data.userId) userSocketMap.set(data.userId, socket.id);
    });
    socket.on("hello", (data: any) => {
      socket.emit("hello-response", { message: "Hello from the server!" });
    });
    socket.on("message", async (data: any) => {
      /*...*/
    });
    socket.on("grant-annotation", (data: any) => {
      if (data && data.userId) {
        annotators.add(data.userId);
        broadcastAnnotators(io);
      }
    });
    socket.on("annotation-update", (data: any) => {
      io.emit("annotation-update", data);
    });
    socket.on("clear-annotations", (data: any) => {
      /*...*/
    });
    socket.on("pdf-updated", (data: any) => {
      io.emit("pdf-updated", { pdfUrl: data.pdfUrl });
    });
    socket.on("cursor-update", (data: any) => {
      socket.broadcast.emit("cursor-update", data);
    });
    socket.on("highlight-update", (data: any) => {
      socket.broadcast.emit("highlight-update", data);
    });
    socket.on("square-hovered", (data: any) => {
      socket.broadcast.emit("square-hovered", data);
    });
    socket.on("square-selected", (data: any) => {
      socket.broadcast.emit("square-selected", data);
    });
    socket.on("chapter-changed", (data: any) => {
      socket.broadcast.emit("chapter-changed", data);
    });
    socket.on("student-cursor-allowed", (data: any) => {
      io.emit("student-cursor-allowed", data);
    });
    socket.on("mode-changed", (data: any) => {
      io.emit("mode-changed", data);
    });
    socket.on("marker-changed", (data: any) => {
      socket.broadcast.emit("marker-changed", data);
    });
    socket.on("meeting-started", (data: any) => {
      io.emit("meeting-started", data);
    });
  });

  const port = process.env.PORT || 3000;
  httpServer.listen(port, (err?: Error) => {
    if (err) throw err;
    console.log(`[Server] > Ready on http://localhost:${port}`);
  });
});

/**
 * Helper function to interpret WebSocket close codes for logging.
 */
function getCloseCodeMeaning(code: number): string {
  const codes: { [key: number]: string } = {
    1000: "Normal Closure",
    1001: "Going Away",
    1002: "Protocol Error",
    1003: "Unsupported Data",
    1005: "No Status Received",
    1006: "Abnormal Closure",
    1007: "Invalid frame payload data",
    1008: "Policy Violation",
    1009: "Message Too Big",
    1010: "Missing Extension",
    1011: "Internal Error",
    1012: "Service Restart",
    1013: "Try Again Later",
    1014: "Bad Gateway",
    1015: "TLS Handshake",
    4000: "Unauthorized",
    4001: "Invalid API Key",
  };
  return codes[code] || `Unknown Code`;
}
