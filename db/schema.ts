import { relations } from "drizzle-orm";
import {
  boolean,
  foreignKey,
  integer,
  pgEnum,
  pgTable,
  primaryKey,
  serial,
  text,
  timestamp,
} from "drizzle-orm/pg-core";

// --- New Curriculums Table ---
export const curriculums = pgTable("curriculums", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(), // Added unique constraint for ON CONFLICT usage
  description: text("description"),
});

// --- Tables Definitions ---

// Courses table (updated to include curriculum_id)
// Note: The curriculum_id column is added without calling .nullable(), making it nullable by default.
export const courses = pgTable("courses", {
  id: serial("id").primaryKey(),
  curriculumId: integer("curriculum_id").references(() => curriculums.id, {
    onDelete: "cascade",
  }),
  englishTitle: text("english_title").notNull(),
  description: text("description").notNull(),
  arabicTitle: text("arabic_title").notNull(),
});

// Add this to your schema file
export const users = pgTable("users", {
  userId: text("user_id").primaryKey(),
  email: text("email").notNull(),
  role: text("role").notNull().default("student"), // "student" or "teacher"
  displayName: text("display_name").default(""),
  avatarSrc: text("avatar_src").default(""), // <-- ADDED: avatar source column
  createdAt: timestamp("created_at").defaultNow(),

  // ========== ADDED: class credentials for teacher only ==========
  schoolUsername: text("school_username"), // e.g. "MATH101"
  schoolPassword: text("school_password"), // store hashed or plain if you prefer
});

// Surahs table (kept for compatibility, but no longer required for memorization)
export const surahs = pgTable("surahs", {
  id: serial("id").primaryKey(),
  number: integer("number").notNull(),
  name: text("name").notNull(),
  englishName: text("english_name").notNull(),
  revelationPlace: text("revelation_place").notNull(),
  numberOfAyahs: integer("number_of_ayahs").notNull(),
});

// Verses table with composite primary key (kept for compatibility, but no longer required for memorization)
export const verses = pgTable(
  "verses",
  {
    surahId: integer("surah_id")
      .references(() => surahs.id, { onDelete: "cascade" })
      .notNull(),
    verseNumber: integer("verse_number").notNull(),
    text: text("text").notNull(),
  },
  (table) => ({
    pk: primaryKey(table.surahId, table.verseNumber),
  })
);

// Units table
export const units = pgTable("units", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  courseId: integer("course_id")
    .references(() => courses.id, { onDelete: "cascade" })
    .notNull(),
  order: integer("order").notNull(),
});

// Lessons table
export const lessons = pgTable("lessons", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  unitId: integer("unit_id")
    .references(() => units.id, { onDelete: "cascade" })
    .notNull(),
  order: integer("order").notNull(),
});

// Challenges enumeration
export const challengesEnum = pgEnum("type", [
  "SELECT",
  "ASSIST",
  "MATCHING",
  "TAP_WHAT_YOU_HEAR",
  "IMAGE_AUDIO_SELECT",
  "DRAG_AND_DROP",
  "FILL_IN_THE_BLANK",
  "SPEAK_THIS",
  // NEWLY ADDED:
  "SPEAK_THIS_ADVANCED",
]);

// Challenges table
export const challenges = pgTable("challenges", {
  id: serial("id").primaryKey(),
  lessonId: integer("lesson_id")
    .references(() => lessons.id, { onDelete: "cascade" })
    .notNull(),
  type: challengesEnum("type").notNull(),
  question: text("question").notNull(),
  order: integer("order").notNull(),
  audioSrc: text("audio_src"),
  mediaType: text("media_type"),
  mediaUrl: text("media_url"),
  topCardText: text("top_card_text"),
  topCardAudio: text("top_card_audio"),
  sentence: text("sentence"),

  // === NEW COLUMN to enable stacked layout ===
  optionsStacked: boolean("options_stacked").default(false),
});

// Challenge options table
export const challengeOptions = pgTable("challenge_options", {
  id: serial("id").primaryKey(),
  challengeId: integer("challenge_id")
    .references(() => challenges.id, { onDelete: "cascade" })
    .notNull(),
  text: text("text").notNull(),
  correct: boolean("correct").notNull(),
  imageSrc: text("imagesrc"),
  audioSrc: text("audiosrc"),
  matchPairId: integer("match_pair_id"),
  side: text("side").default(""),
  sequence: integer("sequence"),
});

// Challenge progress table
export const challengeProgress = pgTable("challenge_progress", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  challengeId: integer("challenge_id")
    .references(() => challenges.id, { onDelete: "cascade" })
    .notNull(),
  completed: boolean("completed").notNull().default(false),
});

// User progress table
export const userProgress = pgTable("user_progress", {
  userId: text("user_id").primaryKey(),
  userName: text("user_name").notNull().default("User"),
  userImageSrc: text("user_image_src").notNull().default("/mascot.svg"),
  activeCourseId: integer("active_course_id").references(() => courses.id, {
    onDelete: "cascade",
  }),
  hearts: integer("hearts").notNull().default(5),
  points: integer("points").notNull().default(0),
});

// User subscription table
export const userSubscription = pgTable("user_subscription", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull().unique(),
  stripeCustomerId: text("stripe_customer_id").notNull().unique(),
  stripeSubscriptionId: text("stripe_subscription_id").notNull().unique(),
  stripePriceId: text("stripe_price_id").notNull(),
  stripeCurrentPeriodEnd: timestamp("stripe_current_period_end").notNull(),
});

// Events table
export const events = pgTable("events", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  googleEventId: text("google_event_id").notNull(),
  calendarLink: text("calendar_link"),
  recurrenceRule: text("recurrence_rule"),
  // ADDED: We'll store a Jitsi room name or link and a flag if it's a meeting
  jitsiRoomName: text("jitsi_room_name"),
  isMeeting: boolean("is_meeting").default(false),
});

/*
  ================================
  OLD memorization tables referencing surahs/verses directly
  ================================
  We'll refactor them to use surahNumber & verseNumber
*/

// Memorization Progress Table (refactored to surahNumber, no foreign key to surahs)
export const memorizationProgress = pgTable("memorization_progress", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  completedVerses: integer("completed_verses").notNull().default(0),
  recitationAttempts: integer("recitation_attempts").notNull().default(0),
  score: integer("score").notNull().default(0),
  difficulty: text("difficulty").notNull(),
});

// memorizedVerses table (refactored to surahNumber, no foreign key to verses)
export const memorizedVerses = pgTable(
  "memorized_verses",
  {
    userId: text("user_id").notNull(),
    surahNumber: integer("surah_number").notNull(), // replaced surahId
    verseNumber: integer("verse_number").notNull(),
    dateMemorized: timestamp("date_memorized").notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey(table.userId, table.surahNumber, table.verseNumber),
  })
);

// Memorization Sessions Table (refactored to surahNumber)
export const memorizationSessions = pgTable("memorization_sessions", {
  sessionId: serial("session_id").primaryKey(),
  userId: text("user_id").notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  versesRange: text("verses_range").notNull(),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  result: text("result").notNull(),
  difficulty: text("difficulty").notNull(),
});

// Memorization Feedback Table (refactored to surahNumber, verseNumber)
export const memorizationFeedback = pgTable("memorization_feedback", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  sessionId: integer("session_id")
    .references(() => memorizationSessions.sessionId, {
      onDelete: "cascade",
    })
    .notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  verseNumber: integer("verse_number").notNull(),
  feedbackType: text("feedback_type").notNull(),
  feedbackDetails: text("feedback_details").notNull(),
});

// Memorized Surahs Table (refactored to surahNumber)
export const memorizedSurahs = pgTable("memorized_surahs", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  surahNumber: integer("surah_number").notNull(), // replaced surahId
  dateMemorized: timestamp("date_memorized").notNull().defaultNow(),
});

// Chapters table
export const chapters = pgTable("chapters", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  order: integer("order").notNull().default(1),
});

// Squares table
export const squares = pgTable("squares", {
  id: serial("id").primaryKey(),
  chapterId: integer("chapter_id")
    .references(() => chapters.id, { onDelete: "cascade" })
    .notNull(),
  squareNumber: integer("square_number").notNull(),
  content: text("content").notNull(),
  transliteration: text("transliteration").default(""), // New Column
  // ...other columns like audioUrl or group if needed...
});

// ========== ADDED: Teacher-Student Pivot Table ==========
export const teacherStudents = pgTable(
  "teacher_students",
  {
    teacherId: text("teacher_id")
      .references(() => users.userId, { onDelete: "cascade" })
      .notNull(),
    studentId: text("student_id")
      .references(() => users.userId, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => ({
    pk: primaryKey(table.teacherId, table.studentId),
  })
);

/* 
  ===========================================================
  ADDED: messages - store chat messages for 30 days
  You can run a scheduled job to delete messages older than 30 days
  ===========================================================
*/
export const messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  senderId: text("sender_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  recipientId: text("recipient_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  content: text("content").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  // You might add a 'read' column, attachments, etc.

  // NEWLY ADDED: Track when the message was read (null = unread).
  readAt: timestamp("read_at"),
});

/* 
  ===========================================================
  ADDED: eventParticipants - link multiple participants to an event
  Teacher can invite multiple students
  ===========================================================
*/
export const eventParticipants = pgTable(
  "event_participants",
  {
    eventId: integer("event_id")
      .references(() => events.id, { onDelete: "cascade" })
      .notNull(),
    userId: text("user_id")
      .references(() => users.userId, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => ({
    pk: primaryKey(table.eventId, table.userId),
  })
);

/* 
  ===========================================================
  ADDED: homeworks - track assigned tasks from teacher to student
  We'll keep it simple for now
  ===========================================================
*/
export const homeworks = pgTable("homeworks", {
  id: serial("id").primaryKey(),
  teacherId: text("teacher_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  studentId: text("student_id")
    .references(() => users.userId, { onDelete: "cascade" })
    .notNull(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  assignedAt: timestamp("assigned_at").defaultNow().notNull(),
  dueDate: timestamp("due_date"),
  completed: boolean("completed").default(false),
});

// --- Relations Definitions ---

// Keep old surahsRelations, versesRelations, etc. for reference. They are no longer used in memorization logic but do not break the schema if you wish to keep them.
export const surahsRelations = relations(surahs, ({ many }) => ({
  verses: many(verses),
}));

export const versesRelations = relations(verses, ({ one }) => ({
  surah: one(surahs, {
    fields: [verses.surahId],
    references: [surahs.id],
  }),
}));

// ENHANCED: Updated courses relations to include curriculum
export const coursesRelations = relations(courses, ({ one, many }) => ({
  // NEW: Add curriculum relation
  curriculum: one(curriculums, {
    fields: [courses.curriculumId],
    references: [curriculums.id],
  }),
  // Existing relations
  userProgress: many(userProgress),
  units: many(units),
}));

// NEW: Add curriculums relations
export const curriculumsRelations = relations(curriculums, ({ many }) => ({
  courses: many(courses),
}));

export const unitsRelations = relations(units, ({ many, one }) => ({
  course: one(courses, {
    fields: [units.courseId],
    references: [courses.id],
  }),
  lessons: many(lessons),
}));

export const lessonsRelations = relations(lessons, ({ one, many }) => ({
  unit: one(units, {
    fields: [lessons.unitId],
    references: [units.id],
  }),
  challenges: many(challenges),
}));

export const challengesRelations = relations(challenges, ({ one, many }) => ({
  lesson: one(lessons, {
    fields: [challenges.lessonId],
    references: [lessons.id],
  }),
  challengeOptions: many(challengeOptions),
  challengeProgress: many(challengeProgress),
}));

export const challengeOptionsRelations = relations(
  challengeOptions,
  ({ one }) => ({
    challenge: one(challenges, {
      fields: [challengeOptions.challengeId],
      references: [challenges.id],
    }),
  })
);

export const challengeProgressRelations = relations(
  challengeProgress,
  ({ one }) => ({
    challenge: one(challenges, {
      fields: [challengeProgress.challengeId],
      references: [challenges.id],
    }),
  })
);

export const userProgressRelations = relations(userProgress, ({ one }) => ({
  activeCourse: one(courses, {
    fields: [userProgress.activeCourseId],
    references: [courses.id],
  }),
}));

// ENHANCED: Chapter relations
export const chaptersRelations = relations(chapters, ({ many }) => ({
  squares: many(squares),
}));

export const squaresRelations = relations(squares, ({ one }) => ({
  chapter: one(chapters, {
    fields: [squares.chapterId],
    references: [chapters.id],
  }),
}));

// FIXED: Teacher-Student pivot relations with proper relationName matching
export const teacherStudentsRelations = relations(
  teacherStudents,
  ({ one }) => ({
    teacher: one(users, {
      fields: [teacherStudents.teacherId],
      references: [users.userId],
      relationName: "teacherStudentTeacher",
    }),
    student: one(users, {
      fields: [teacherStudents.studentId],
      references: [users.userId],
      relationName: "teacherStudentStudent",
    }),
  })
);

// FIXED: Users relations with proper relationName matching
export const usersRelations = relations(users, ({ many }) => ({
  // Teacher side relations
  studentsAsTeacher: many(teacherStudents, {
    relationName: "teacherStudentTeacher",
  }),
  // Student side relations
  teachersAsStudent: many(teacherStudents, {
    relationName: "teacherStudentStudent",
  }),
  // Message relations
  sentMessages: many(messages, {
    relationName: "messageSender",
  }),
  receivedMessages: many(messages, {
    relationName: "messageRecipient",
  }),
  // Event participation
  eventParticipations: many(eventParticipants),
  // Homework relations
  assignedHomeworks: many(homeworks, {
    relationName: "homeworkTeacher",
  }),
  receivedHomeworks: many(homeworks, {
    relationName: "homeworkStudent",
  }),
}));

// FIXED: Messages relations with proper relationName matching
export const messagesRelations = relations(messages, ({ one }) => ({
  sender: one(users, {
    fields: [messages.senderId],
    references: [users.userId],
    relationName: "messageSender",
  }),
  recipient: one(users, {
    fields: [messages.recipientId],
    references: [users.userId],
    relationName: "messageRecipient",
  }),
}));

// ENHANCED: Event and event participants relations
export const eventsRelations = relations(events, ({ many }) => ({
  participants: many(eventParticipants),
}));

export const eventParticipantsRelations = relations(
  eventParticipants,
  ({ one }) => ({
    event: one(events, {
      fields: [eventParticipants.eventId],
      references: [events.id],
    }),
    user: one(users, {
      fields: [eventParticipants.userId],
      references: [users.userId],
    }),
  })
);

// FIXED: Homework relations with proper relationName matching
export const homeworksRelations = relations(homeworks, ({ one }) => ({
  teacher: one(users, {
    fields: [homeworks.teacherId],
    references: [users.userId],
    relationName: "homeworkTeacher",
  }),
  student: one(users, {
    fields: [homeworks.studentId],
    references: [users.userId],
    relationName: "homeworkStudent",
  }),
}));

// ENHANCED: Memorization-related relations (though they now use surahNumber instead of foreign keys)
export const memorizationProgressRelations = relations(
  memorizationProgress,
  ({ one }) => ({
    // Note: No direct relation to surahs table since we use surahNumber
    // But we can still relate to user if needed
  })
);

export const memorizedVersesRelations = relations(
  memorizedVerses,
  ({ one }) => ({
    // Note: No direct relations since we use surahNumber and verseNumber
    // But could add user relation if needed
  })
);

export const memorizationSessionsRelations = relations(
  memorizationSessions,
  ({ one, many }) => ({
    // Note: No direct relation to surahs since we use surahNumber
    feedbacks: many(memorizationFeedback),
  })
);

export const memorizationFeedbackRelations = relations(
  memorizationFeedback,
  ({ one }) => ({
    session: one(memorizationSessions, {
      fields: [memorizationFeedback.sessionId],
      references: [memorizationSessions.sessionId],
    }),
    // Note: No direct relation to verses since we use surahNumber and verseNumber
  })
);

export const memorizedSurahsRelations = relations(
  memorizedSurahs,
  ({ one }) => ({
    // Note: No direct relation to surahs since we use surahNumber
  })
);
