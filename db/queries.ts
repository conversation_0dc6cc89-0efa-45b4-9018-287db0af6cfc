/* eslint-disable */

import { cache } from "react";
import { eq, and, gte, lte, desc, inArray } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import {
  challengeProgress,
  courses, // Keep this
  lessons,
  units, // Keep this (from units table)
  userProgress,
  userSubscription,
  challengeOptions,
  challenges,
  events,
  surahs,
  verses,
  memorizationProgress,
  memorizationSessions,
  memorizationFeedback,
  chapters,
  squares,
  memorizedVerses,
  teacherStudents,
  eventParticipants,
  curriculums,
} from "@/db/schema";

// Event functions
export const createEvent = async (eventData: any) => {
  console.log("[queries.ts] createEvent called with:", eventData);
  const { invitedStudentIds, ...otherFields } = eventData;
  console.log(
    "[queries.ts] About to insert event with isMeeting:",
    otherFields.isMeeting,
    " and jitsiRoomName:",
    otherFields.jitsiRoomName
  );
  const newEvent = await db
    .insert(events)
    .values({ ...otherFields })
    .returning();
  const createdEvent = newEvent[0];
  console.log("[queries.ts] Inserted new event ID:", createdEvent.id);
  if (Array.isArray(invitedStudentIds) && invitedStudentIds.length > 0) {
    console.log(
      "[queries.ts] Will insert eventParticipants for these students:",
      invitedStudentIds
    );
    const participantsData = invitedStudentIds.map((studentId: string) => ({
      eventId: createdEvent.id,
      userId: studentId,
    }));
    const insertedParticipants = await db
      .insert(eventParticipants)
      .values(participantsData)
      .returning();
    console.log(
      "[queries.ts] Inserted eventParticipants rows count:",
      insertedParticipants.length
    );
  }
  return createdEvent;
};

export const getEvents = async () => {
  return await db.query.events.findMany({
    orderBy: (events, { asc }) => [asc(events.startTime)],
  });
};

export const getEventById = async (eventId: number) => {
  return await db.query.events.findFirst({
    where: eq(events.id, eventId),
  });
};

export const getEventsByDateRange = async (
  startDate: string,
  endDate: string
) => {
  return await db.query.events.findMany({
    where: (events) =>
      gte(events.startTime, new Date(startDate)) &&
      lte(events.endTime, new Date(endDate)),
  });
};

export const updateEventById = async (eventId: number, eventData: any) => {
  console.log("[queries.ts] updateEventById called with:", eventId, eventData);
  const updatedEvent = await db
    .update(events)
    .set({ ...eventData })
    .where(eq(events.id, eventId))
    .returning();
  return updatedEvent[0];
};

export const deleteEventById = async (eventId: number) => {
  const deletedEvent = await db
    .delete(events)
    .where(eq(events.id, eventId))
    .returning();
  return deletedEvent[0];
};

export const getUserProgress = cache(async () => {
  const { userId } = await auth();
  if (!userId) {
    console.log(
      "[queries.ts -> getUserProgress] No userId found, returning null."
    );
    return null;
  }
  let data = await db.query.userProgress.findFirst({
    where: eq(userProgress.userId, userId),
    with: { activeCourse: true },
  });
  if (!data) {
    console.log(
      "[queries.ts -> getUserProgress] No user_progress row found for userId:",
      userId
    );
    console.log(
      "[queries.ts -> getUserProgress] Auto-creating a new user_progress row..."
    );
    try {
      await db.insert(userProgress).values({
        userId,
        userName: "NewUser",
        userImageSrc: "/mascot.svg",
        hearts: 5,
        points: 0,
      });
      data = await db.query.userProgress.findFirst({
        where: eq(userProgress.userId, userId),
        with: { activeCourse: true },
      });
      console.log(
        "[queries.ts -> getUserProgress] Created user_progress row:",
        data
      );
    } catch (error) {
      console.error(
        "[queries.ts -> getUserProgress] Error creating user_progress row:",
        error
      );
      return null; // Return null if creation fails
    }
  }
  return data;
});

export const getSurahs = async () => {
  return await db.query.surahs.findMany({
    orderBy: (surahs, { asc }) => [asc(surahs.number)],
    columns: {
      id: true,
      number: true,
      name: true,
      englishName: true,
      revelationPlace: true,
      numberOfAyahs: true,
    },
  });
};

export const getSurahById = async (surahId: number) => {
  const surah = await db.query.surahs.findFirst({
    where: eq(surahs.id, surahId),
    with: {
      verses: { orderBy: (verses, { asc }) => [asc(verses.verseNumber)] },
    },
  });
  return surah;
};

export const getVerse = async (surahId: number, verseNumber: number) => {
  return await db.query.verses.findFirst({
    where: and(
      eq(verses.surahId, surahId),
      eq(verses.verseNumber, verseNumber)
    ),
  });
};

export const checkMemorizedVerse = async (
  userId: string,
  surahNumber: number,
  verseNumber: number
) => {
  return await db.query.memorizedVerses.findFirst({
    where: and(
      eq(memorizedVerses.userId, userId),
      eq(memorizedVerses.surahNumber, surahNumber),
      eq(memorizedVerses.verseNumber, verseNumber)
    ),
  });
};

export const getMemorizedVersesBySurah = async (
  userId: string,
  surahNumber: number
) => {
  return await db.query.memorizedVerses.findMany({
    where: and(
      eq(memorizedVerses.userId, userId),
      eq(memorizedVerses.surahNumber, surahNumber)
    ),
    orderBy: (mv, { asc }) => [asc(mv.verseNumber)],
  });
};

export const upsertMemorizationProgress = async (progressData: {
  userId: string;
  surahNumber: number;
  completedVerses?: number;
  recitationAttempts?: number;
  score?: number;
  difficulty?: string;
}) => {
  const existingProgress = await db.query.memorizationProgress.findFirst({
    where: and(
      eq(memorizationProgress.userId, progressData.userId),
      eq(memorizationProgress.surahNumber, progressData.surahNumber)
    ),
  });
  if (existingProgress) {
    const updatedProgress = await db
      .update(memorizationProgress)
      .set({
        completedVerses:
          progressData.completedVerses ?? existingProgress.completedVerses,
        recitationAttempts:
          progressData.recitationAttempts ??
          existingProgress.recitationAttempts,
        score: progressData.score ?? existingProgress.score,
        difficulty: progressData.difficulty ?? existingProgress.difficulty,
      })
      .where(
        and(
          eq(memorizationProgress.userId, progressData.userId),
          eq(memorizationProgress.surahNumber, progressData.surahNumber)
        )
      )
      .returning();
    return updatedProgress[0];
  } else {
    const newProgress = await db
      .insert(memorizationProgress)
      .values({
        userId: progressData.userId,
        surahNumber: progressData.surahNumber,
        completedVerses: progressData.completedVerses ?? 0,
        recitationAttempts: progressData.recitationAttempts ?? 0,
        score: progressData.score ?? 0,
        difficulty: progressData.difficulty ?? "easy",
      })
      .returning();
    return newProgress[0];
  }
};

export const getMemorizationProgress = cache(
  async (userId: string, surahNumber: number) => {
    const progress = await db.query.memorizationProgress.findFirst({
      where: and(
        eq(memorizationProgress.userId, userId),
        eq(memorizationProgress.surahNumber, surahNumber)
      ),
    });
    return progress;
  }
);

export const createMemorizationSession = async (sessionData: {
  userId: string;
  surahNumber: number;
  versesRange: string;
  startTime: Date;
  endTime: Date;
  result: string;
  difficulty?: string;
}) => {
  const newSession = await db
    .insert(memorizationSessions)
    .values({ ...sessionData, difficulty: sessionData.difficulty ?? "easy" })
    .returning();
  return newSession[0];
};

export const getMemorizationSessionsBySurah = async (
  userId: string,
  surahNumber: number
) => {
  return await db.query.memorizationSessions.findMany({
    where: and(
      eq(memorizationSessions.userId, userId),
      eq(memorizationSessions.surahNumber, surahNumber)
    ),
    orderBy: (ms, { desc }) => [desc(ms.startTime)],
  });
};

export const recordMemorizationFeedback = async (feedbackData: {
  sessionId: number;
  surahNumber: number;
  verseNumber: number;
  feedbackType: string;
  feedbackDetails: string;
}) => {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");
  const newFeedback = await db
    .insert(memorizationFeedback)
    .values({ ...feedbackData, userId })
    .returning();
  return newFeedback[0];
};

export const getMemorizationFeedback = cache(async (sessionId: number) => {
  return await db.query.memorizationFeedback.findMany({
    where: eq(memorizationFeedback.sessionId, sessionId),
    with: { session: true },
  });
});

export const getCourses = cache(async () => {
  console.log("[getCourses] Fetching all courses from 'courses' table."); // Added log
  const data = await db.query.courses.findMany({
    columns: {
      id: true,
      englishTitle: true,
      arabicTitle: true,
      description: true,
      curriculumId: true,
    },
  });
  console.log(`[getCourses] Found ${data.length} courses.`); // Added log
  return data;
});

// ENHANCED getCourseById with logging
export const getCourseById = cache(async (courseId: number) => {
  console.log(
    `[getCourseById] Attempting to fetch course with ID: ${courseId} from 'courses' table.`
  );
  const data = await db.query.courses.findFirst({
    where: eq(courses.id, courseId),
    columns: {
      id: true,
      englishTitle: true,
      arabicTitle: true,
      description: true,
      curriculumId: true,
    },
    // The 'units' relation here fetches from the 'units' table where units.courseId = courses.id
    // This is important if this function is used to get a "course" that then *contains* units.
    // For the learn page's "single unit view", this might fetch more than needed if courseId IS the unit itself.
    // However, for getting the title (singleUnitTitle), this is fine.
    with: {
      units: {
        // This will fetch related units if the schema is set up for courses -> units relation
        orderBy: (units, { asc }) => [asc(units.order)],
        with: {
          lessons: {
            orderBy: (lessons, { asc }) => [asc(lessons.order)],
          },
        },
      },
    },
  });
  if (data) {
    console.log(
      `[getCourseById] Found course for ID ${courseId}: ${data.englishTitle}`
    );
  } else {
    console.warn(`[getCourseById] Course NOT FOUND for ID ${courseId}.`);
  }
  return data;
});

export const getCoursesByCurriculum = cache(async (curriculumId: number) => {
  console.log(
    "[getCoursesByCurriculum] Looking for curriculumId:",
    curriculumId
  );
  const coursesData = await db.query.courses.findMany({
    where: eq(courses.curriculumId, curriculumId),
    columns: {
      id: true,
      englishTitle: true,
      arabicTitle: true,
      description: true,
      curriculumId: true,
    },
  });
  console.log("[getCoursesByCurriculum] Found courses:", coursesData.length);
  return coursesData;
});

export const getCurriculumById = cache(async (curriculumId: number) => {
  console.log("[getCurriculumById] Looking for curriculumId:", curriculumId);
  try {
    const curriculum = await db.query.curriculums.findFirst({
      where: eq(curriculums.id, curriculumId),
      columns: { id: true, name: true, description: true },
    });
    console.log("[getCurriculumById] Found curriculum:", curriculum);
    if (!curriculum) {
      console.log(
        "[getCurriculumById] No curriculum found with id:",
        curriculumId
      );
      return null;
    }
    const result = {
      id: curriculum.id,
      englishTitle: curriculum.name,
      arabicTitle: curriculum.name,
      description: curriculum.description || "Interactive learning content",
      curriculumId: curriculum.id,
      curriculum: {
        id: curriculum.id,
        name: curriculum.name,
        description: curriculum.description,
      },
    };
    console.log("[getCurriculumById] Returning result:", {
      id: result.id,
      title: result.englishTitle,
      curriculumName: result.curriculum.name,
    });
    return result;
  } catch (error) {
    console.error("[getCurriculumById] Error:", error);
    return null;
  }
});

export const getCourseByCurriculum = cache(async (curriculumId: number) => {
  console.log(
    "[getCourseByCurriculum] DEPRECATED - redirecting to getCurriculumById"
  );
  return await getCurriculumById(curriculumId);
});

export const getCurriculums = cache(async () => {
  console.log("[getCurriculums] Fetching all curriculums...");
  try {
    const curriculumsData = await db.query.curriculums.findMany({
      columns: { id: true, name: true, description: true },
      orderBy: (curriculums, { asc }) => [asc(curriculums.id)],
    });
    console.log("[getCurriculums] Found curriculums:", {
      count: curriculumsData.length,
      ids: curriculumsData.map((c) => c.id),
      names: curriculumsData.map((c) => c.name),
    });
    return curriculumsData;
  } catch (error) {
    console.error("[getCurriculums] Error fetching curriculums:", error);
    return [];
  }
});

const DAY_IN_MS = 86_400_000;

export const getCourseProgress = cache(async () => {
  const { userId } = await auth();
  const userProgressData = await getUserProgress();
  if (!userId || !userProgressData?.activeCourseId) {
    console.log(
      "[getCourseProgress] No userId or no activeCourseId in userProgress, returning null."
    );
    return null;
  }
  console.log(
    `[getCourseProgress] Fetching units for activeCourseId: ${userProgressData.activeCourseId}`
  );
  const unitsInActiveCourse = await db.query.units.findMany({
    orderBy: (units, { asc }) => [asc(units.order)],
    where: eq(units.courseId, userProgressData.activeCourseId),
    with: {
      lessons: {
        orderBy: (lessons, { asc }) => [asc(lessons.order)],
        with: {
          unit: true,
          challenges: {
            with: {
              challengeProgress: {
                where: eq(challengeProgress.userId, userId),
              },
              challengeOptions: true,
            },
          },
        },
      },
    },
  });
  console.log(
    `[getCourseProgress] Found ${unitsInActiveCourse.length} units in active course.`
  );
  const firstUncompletedLesson = unitsInActiveCourse
    .flatMap((unit) => unit.lessons)
    .find((lesson) => {
      return lesson.challenges.some((challenge) => {
        return (
          !challenge.challengeProgress ||
          challenge.challengeProgress.length === 0 ||
          challenge.challengeProgress.some(
            (progress) => progress.completed === false
          )
        );
      });
    });
  console.log(
    `[getCourseProgress] First uncompleted lesson ID: ${firstUncompletedLesson?.id}`
  );
  return {
    activeLesson: firstUncompletedLesson,
    activeLessonId: firstUncompletedLesson?.id,
  };
});

export const getUserSubscription = cache(async () => {
  const { userId } = await auth();
  if (!userId) return null;
  const data = await db.query.userSubscription.findFirst({
    where: eq(userSubscription.userId, userId),
  });
  if (!data) return null;
  const isActive =
    data.stripePriceId &&
    data.stripeCurrentPeriodEnd?.getTime()! + DAY_IN_MS > Date.now();
  return { ...data, isActive: !!isActive };
});

export const getTopTenUsers = cache(async () => {
  const { userId } = await auth();
  if (!userId) return [];
  const data = await db.query.userProgress.findMany({
    orderBy: (userProgress, { desc }) => [desc(userProgress.points)],
    limit: 10,
    columns: { userId: true, userName: true, userImageSrc: true, points: true },
  });
  return data;
});

export const getMatchingPairs = cache(async (challengeId: number) => {
  const pairs = await db.query.challengeOptions.findMany({
    where: eq(challengeOptions.challengeId, challengeId),
    orderBy: (challengeOptions, { asc }) => [asc(challengeOptions.matchPairId)],
  });
  return pairs;
});

export const getDragAndDropOptions = cache(async (challengeId: number) => {
  const options = await db.query.challengeOptions.findMany({
    where: eq(challengeOptions.challengeId, challengeId),
    orderBy: (challengeOptions, { asc }) => [asc(challengeOptions.sequence)],
  });
  return options;
});

// Fetch units - Enhanced with more logging
export const getUnits = cache(async (filterByCourseId?: number) => {
  const { userId } = await auth();
  const userProgressData = await getUserProgress();

  if (!userId) {
    console.log("[getUnits] No userId, returning empty array.");
    return [];
  }

  const courseIdToFetch =
    filterByCourseId !== undefined && filterByCourseId !== null
      ? filterByCourseId
      : userProgressData?.activeCourseId;

  if (!courseIdToFetch) {
    console.log(
      `[getUnits] No courseId available to fetch units for. filterByCourseId: ${filterByCourseId}, userProgressData?.activeCourseId: ${userProgressData?.activeCourseId}. Returning empty array.`
    );
    return [];
  }

  console.log(
    `[getUnits] Attempting to fetch units for effective courseId: ${courseIdToFetch}`
  );

  const data = await db.query.units.findMany({
    orderBy: (units, { asc }) => [asc(units.order)],
    where: eq(units.courseId, courseIdToFetch),
    with: {
      lessons: {
        orderBy: (lessons, { asc }) => [asc(lessons.order)],
        with: {
          challenges: {
            orderBy: (challenges, { asc }) => [asc(challenges.order)],
            with: {
              challengeProgress: {
                where: eq(challengeProgress.userId, userId),
              },
              challengeOptions: true,
            },
            columns: {
              id: true,
              lessonId: true,
              type: true,
              question: true,
              order: true,
              audioSrc: true,
              mediaType: true,
              mediaUrl: true,
              topCardText: true,
              topCardAudio: true,
              sentence: true,
              optionsStacked: true,
            },
          },
        },
      },
    },
  });

  console.log(
    `[getUnits] Raw data fetched from DB for courseId ${courseIdToFetch}. Units found: ${data.length}`
  );
  if (data.length > 0) {
    data.forEach((unit) => {
      console.log(
        `[getUnits] Raw Unit ID: ${unit.id}, Title: ${unit.title}, Lessons count: ${unit.lessons.length}`
      );
      if (unit.lessons.length > 0) {
        unit.lessons.forEach((lesson) => {
          console.log(
            `  [getUnits]   Raw Lesson ID: ${lesson.id}, Title: ${lesson.title}, Challenges count: ${lesson.challenges.length}`
          );
        });
      } else {
        console.log(
          `  [getUnits]   Raw Unit ID: ${unit.id} has no lessons in raw data.`
        );
      }
    });
  }

  const normalizedData = data.map((unit) => {
    const lessonsWithCompletedStatus = unit.lessons.map((lesson) => {
      if (!Array.isArray(lesson.challenges) || lesson.challenges.length === 0) {
        return { ...lesson, completed: false };
      }
      const allCompletedChallenges = lesson.challenges.every((challenge) => {
        return (
          Array.isArray(challenge.challengeProgress) &&
          challenge.challengeProgress.length > 0 &&
          challenge.challengeProgress.every((progress) => progress.completed)
        );
      });
      return { ...lesson, completed: allCompletedChallenges };
    });
    return { ...unit, lessons: lessonsWithCompletedStatus };
  });

  console.log(
    `[getUnits] Returning ${normalizedData.length} normalized units for courseId: ${courseIdToFetch}.`
  );
  if (normalizedData.length > 0) {
    normalizedData.forEach((unit) => {
      console.log(
        `[getUnits] Normalized Unit ID: ${unit.id}, Title: ${unit.title}, Normalized Lessons count: ${unit.lessons.length}`
      );
      if (unit.lessons.length > 0) {
        unit.lessons.forEach((lesson) => {
          console.log(
            `  [getUnits]   Normalized Lesson ID: ${lesson.id}, Title: ${lesson.title}, Completed: ${lesson.completed}`
          );
        });
      } else {
        console.log(
          `  [getUnits]   Normalized Unit ID: ${unit.id} has no lessons after normalization (or originally).`
        );
      }
    });
  }
  return normalizedData;
});

export const getLesson = cache(async (id?: number) => {
  const { userId } = await auth();
  if (!userId) {
    console.log("[getLesson] No userId, returning null.");
    return null;
  }
  const courseProgress = await getCourseProgress();
  const lessonId = id || courseProgress?.activeLessonId;
  if (!lessonId) {
    console.log(
      `[getLesson] No lessonId to fetch (id param: ${id}, activeLessonId: ${courseProgress?.activeLessonId}). Returning null.`
    );
    return null;
  }
  console.log(`[getLesson] Attempting to fetch lesson with ID: ${lessonId}`);
  const data = await db.query.lessons.findFirst({
    where: eq(lessons.id, lessonId),
    with: {
      challenges: {
        orderBy: (challenges, { asc }) => [asc(challenges.order)],
        with: {
          challengeOptions: true,
          challengeProgress: { where: eq(challengeProgress.userId, userId) },
        },
        columns: {
          id: true,
          lessonId: true,
          type: true,
          question: true,
          order: true,
          audioSrc: true,
          mediaType: true,
          mediaUrl: true,
          topCardText: true,
          topCardAudio: true,
          sentence: true,
          optionsStacked: true,
        },
      },
    },
  });
  if (!data) {
    console.warn(`[getLesson] Lesson NOT FOUND for ID: ${lessonId}.`);
    return null;
  }
  if (!data.challenges) {
    console.warn(
      `[getLesson] Lesson ID ${lessonId} found, but it has no challenges array.`
    );
    return { ...data, challenges: [] }; // Return lesson with empty challenges array
  }
  console.log(
    `[getLesson] Lesson ID ${lessonId} found with ${data.challenges.length} challenges.`
  );
  // console.log("[queries.ts -> getLesson] Raw challenge data:", data.challenges); // Keep if needed for deep dive
  const normalizedChallenges = data.challenges.map((challenge) => {
    const completed =
      challenge.challengeProgress &&
      challenge.challengeProgress.length > 0 &&
      challenge.challengeProgress.every((progress) => progress.completed);
    // console.log("[queries.ts -> getLesson] Challenge row with optionsStacked:", challenge.id, " -> ", challenge.optionsStacked);
    return { ...challenge, completed };
  });
  return { ...data, challenges: normalizedChallenges };
});

export const getLessonPercentage = cache(async () => {
  const courseProgress = await getCourseProgress();
  if (!courseProgress?.activeLessonId) {
    console.log("[getLessonPercentage] No active lesson ID, returning 0%.");
    return 0;
  }
  const lesson = await getLesson(courseProgress.activeLessonId);
  if (!lesson || !lesson.challenges || lesson.challenges.length === 0) {
    console.log(
      `[getLessonPercentage] No lesson data or no challenges for activeLessonId ${courseProgress.activeLessonId}, returning 0%.`
    );
    return 0;
  }
  const completedChallenges = lesson.challenges.filter(
    (challenge) => challenge.completed
  );
  const percentage = Math.round(
    (completedChallenges.length / lesson.challenges.length) * 100
  );
  console.log(
    `[getLessonPercentage] Calculated for lesson ${lesson.id}: ${completedChallenges.length}/${lesson.challenges.length} challenges completed = ${percentage}%.`
  );
  return percentage;
});

export const getChapterWithSquares = async (chapterId: number) => {
  const chapter = await db.query.chapters.findFirst({
    where: eq(chapters.id, chapterId),
  });
  if (!chapter) return null;
  const squaresData = await db.query.squares.findMany({
    where: eq(squares.chapterId, chapterId),
    orderBy: (squares, { asc }) => [asc(squares.squareNumber)],
    columns: {
      id: true,
      squareNumber: true,
      content: true,
      transliteration: true,
    },
  });
  return { ...chapter, squares: squaresData };
};

export const getChapterWithSquaresByOrder = async (chapterOrder: number) => {
  const chapter = await db.query.chapters.findFirst({
    where: eq(chapters.order, chapterOrder),
  });
  if (!chapter) return null;
  const squaresData = await db.query.squares.findMany({
    where: eq(squares.chapterId, chapter.id),
    orderBy: (squares, { asc }) => [asc(squares.squareNumber)],
    columns: {
      id: true,
      squareNumber: true,
      content: true,
      transliteration: true,
    },
  });
  return { ...chapter, squares: squaresData };
};

export const addStudentToTeacher = async (
  teacherId: string,
  studentId: string
) => {
  console.log("[queries.ts] addStudentToTeacher called with:", {
    teacherId,
    studentId,
  });
  try {
    const inserted = await db
      .insert(teacherStudents)
      .values({ teacherId, studentId })
      .returning();
    console.log("[queries.ts] addStudentToTeacher insertion result:", inserted);
    return inserted[0];
  } catch (error) {
    console.error("[queries.ts] addStudentToTeacher error:", error);
    throw error;
  }
};

export const getStudentsForTeacher = async (teacherId: string) => {
  console.log("[queries.ts] getStudentsForTeacher called with:", teacherId);
  try {
    const list = await db.query.teacherStudents.findMany({
      where: eq(teacherStudents.teacherId, teacherId),
    });
    console.log(
      "[queries.ts] getStudentsForTeacher result length:",
      list.length
    );
    return list;
  } catch (error) {
    console.error("[queries.ts] getStudentsForTeacher error:", error);
    return [];
  }
};
