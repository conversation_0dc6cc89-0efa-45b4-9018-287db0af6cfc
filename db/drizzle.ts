import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";

// 1. Import all your schema definitions
//    Make sure this path is correct relative to your db/drizzle.ts file
import * as schema from "./schema";

// Create a Postgres-JS client using your DATABASE_URL
const sql = postgres(process.env.DATABASE_URL!, {
  ssl: "require",
  max: 5, // Adjust pool size as needed
  idle_timeout: 30, // seconds
});

// Initialize Drizzle with the Postgres-JS client AND your schema
const db = drizzle(sql, {
  schema, // <--- THIS IS THE MISSING PIECE!
  // Optionally add logger here, e.g., logger: true
});

export default db;
