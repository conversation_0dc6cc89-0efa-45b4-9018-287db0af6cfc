import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";

// 1. Import all your schema definitions
//    Make sure this path is correct relative to your db/drizzle.ts file
import * as schema from "./schema";

// Create a Postgres-JS client using your DATABASE_URL with optimized connection pooling
const sql = postgres(process.env.DATABASE_URL!, {
  ssl: "require",
  max: 3, // Reduced pool size for better performance
  idle_timeout: 20, // Reduced idle timeout (seconds)
  max_lifetime: 60 * 30, // 30 minutes max connection lifetime
  connect_timeout: 10, // 10 seconds connection timeout
});

// Initialize Drizzle with the Postgres-JS client AND your schema
const db = drizzle(sql, {
  schema, // <--- THIS IS THE MISSING PIECE!
  // Optionally add logger here, e.g., logger: true
});

export default db;
