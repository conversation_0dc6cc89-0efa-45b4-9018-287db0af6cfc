"use server";

import { auth } from "@clerk/nextjs/server";
// Import necessary functions and tables from Drizzle
import { and, eq, inArray, desc, countDistinct, gt } from "drizzle-orm"; // Ensure all needed functions are imported
import db from "@/db/drizzle";
import {
  memorizationProgress,
  memorizationSessions,
  memorizationFeedback,
  memorizedVerses,
  memorizedSurahs, // <-- Ensure this is imported if used in addMemorizedVerse
  // surahs table schema might be needed if you revert API call later
} from "@/db/schema";

/*
  ======================================================================
  Server Actions for Memorization Progress Tracking.
  Relies on surahNumber for identifying Surahs.
  ======================================================================
*/

// Utility function to get current UTC date
const getCurrentUTCDate = () => {
  return new Date(Date.now());
};

// Valid result values for sessions
const VALID_RESULTS = ["Completed", "Failed", "In Progress", "Cancelled"];
// Valid feedback types (optional, can be used for validation)
// const VALID_FEEDBACK_TYPES = ["Pronunciation", "Memorization", "Understanding"];

// --- Helper Function to Fetch Surah Info from API (Copied from previous correct version) ---
async function getNumberOfAyahsFromAPI(
  surahNumber: number
): Promise<number | null> {
  const apiUrl = `https://api.alquran.cloud/v1/surah/${surahNumber}`;
  console.log(
    `Server Action: Fetching numberOfAyahs from AlQuranCloud: ${apiUrl}`
  );
  try {
    const response = await fetch(apiUrl, { cache: "force-cache" }); // Or adjust cache strategy

    if (!response.ok) {
      console.error(
        `Server Action: AlQuranCloud API error fetching surah info! Status: ${response.status}, URL: ${apiUrl}`
      );
      return null;
    }
    const data = await response.json();
    const numberOfAyahs = data?.data?.numberOfAyahs;

    if (typeof numberOfAyahs !== "number" || isNaN(numberOfAyahs)) {
      console.error(
        `Server Action: Invalid numberOfAyahs received from API for Surah ${surahNumber}. Data:`,
        data?.data
      );
      return null;
    }
    console.log(
      `Server Action: Fetched numberOfAyahs=${numberOfAyahs} for Surah ${surahNumber}`
    );
    return numberOfAyahs;
  } catch (error) {
    console.error(
      `Server Action: Network or fetch error getting numberOfAyahs for Surah ${surahNumber}:`,
      error
    );
    return null;
  }
}

// Function to upsert memorization progress
export const upsertMemorizationProgress = async (progressData: {
  surahId?: number; // Kept for potential backward compatibility in input, but not used
  surahNumber?: number; // Used as the primary identifier
  completedVerses?: number;
  recitationAttempts?: number;
  score?: number;
  difficulty?: string;
}) => {
  const { userId } = await auth();
  console.log("Upsert: Retrieved userId from auth:", userId);
  if (!userId) throw new Error("Unauthorized");

  const actualSurahNumber = progressData.surahNumber;
  if (!actualSurahNumber)
    throw new Error("Missing surahNumber in upsertMemorizationProgress");

  // Prepare data with defaults for insertion if needed
  const dataForDb = {
    userId, // Required
    surahNumber: actualSurahNumber, // Required
    completedVerses: progressData.completedVerses ?? 0,
    recitationAttempts: progressData.recitationAttempts ?? 0,
    score: progressData.score ?? 0,
    difficulty: progressData.difficulty ?? "easy", // Required, provide default
  };

  console.log("Upsert: Preparing data:", dataForDb);

  try {
    // Check if progress already exists for this user and surahNumber
    const existingProgress = await db.query.memorizationProgress.findFirst({
      where: and(
        eq(memorizationProgress.userId, userId),
        eq(memorizationProgress.surahNumber, actualSurahNumber)
      ),
    });

    if (existingProgress) {
      console.log(
        `Upsert: Found existing progress for Surah ${actualSurahNumber}. Updating.`
      );
      // Prepare update data - only set fields that were provided in input
      const fieldsToUpdate: Partial<typeof dataForDb> = {};
      if (progressData.completedVerses !== undefined)
        fieldsToUpdate.completedVerses = progressData.completedVerses;
      if (progressData.recitationAttempts !== undefined)
        fieldsToUpdate.recitationAttempts = progressData.recitationAttempts;
      if (progressData.score !== undefined)
        fieldsToUpdate.score = progressData.score;
      if (progressData.difficulty !== undefined)
        fieldsToUpdate.difficulty = progressData.difficulty;

      // Only update if there are fields to update
      if (Object.keys(fieldsToUpdate).length > 0) {
        const updatedProgress = await db
          .update(memorizationProgress)
          .set(fieldsToUpdate) // Pass only the fields to be updated
          .where(
            // Ensure .where() is correctly chained after .set()
            and(
              eq(memorizationProgress.userId, userId),
              eq(memorizationProgress.surahNumber, actualSurahNumber)
            )
          )
          .returning(); // Get the updated record back

        console.log("Upsert: Update successful.");
        return updatedProgress[0];
      } else {
        console.log(
          "Upsert: No fields provided for update, returning existing."
        );
        return existingProgress; // Return existing if nothing to update
      }
    } else {
      console.log(
        `Upsert: No existing progress for Surah ${actualSurahNumber}. Inserting.`
      );
      // Insert new progress record using the fully prepared dataForDb object
      const newProgress = await db
        .insert(memorizationProgress)
        .values(dataForDb) // Pass the full object with defaults
        .returning(); // Get the newly inserted record

      console.log("Upsert: Insert successful.");
      return newProgress[0];
    }
  } catch (error: any) {
    console.error("Error upserting memorization progress:", error);
    throw new Error(`Failed to upsert memorization progress: ${error.message}`);
  }
};

// Function to start a memorization session (No changes needed from previous version)
export const startMemorizationSession = async (sessionData: {
  surahId?: number;
  surahNumber?: number;
  versesRange: string;
  mode: "startFresh" | "continue";
  difficulty?: string;
}) => {
  const { userId } = await auth();
  console.log("Start Session: Retrieved userId from auth:", userId);
  if (!userId) throw new Error("Unauthorized");

  const actualSurahNumber = sessionData.surahNumber;
  if (!actualSurahNumber)
    throw new Error("Missing surahNumber in startMemorizationSession");

  if (sessionData.mode === "startFresh") {
    console.log(
      `Start Session: Mode is startFresh. Cancelling active sessions for Surah ${actualSurahNumber}.`
    );
    await db
      .update(memorizationSessions)
      .set({ result: "Cancelled" })
      .where(
        and(
          eq(memorizationSessions.userId, userId),
          eq(memorizationSessions.surahNumber, actualSurahNumber),
          eq(memorizationSessions.result, "In Progress")
        )
      );
  }

  if (sessionData.mode === "continue") {
    console.log(
      `Start Session: Mode is continue. Checking for active session for Surah ${actualSurahNumber}.`
    );
    const activeSession = await db.query.memorizationSessions.findFirst({
      where: and(
        eq(memorizationSessions.userId, userId),
        eq(memorizationSessions.surahNumber, actualSurahNumber),
        eq(memorizationSessions.result, "In Progress")
      ),
    });

    if (activeSession) {
      console.log(
        `Start Session: Found active session ${activeSession.sessionId}. Returning it.`
      );
      return activeSession;
    }
    console.log(`Start Session: No active session found for continue mode.`);
  }

  console.log("Start Session: Creating new session.");
  const newSession = await db
    .insert(memorizationSessions)
    .values({
      userId,
      surahNumber: actualSurahNumber,
      versesRange: sessionData.versesRange,
      startTime: getCurrentUTCDate(),
      endTime: getCurrentUTCDate(), // Initialize endTime
      result: "In Progress",
      difficulty: sessionData.difficulty ?? "easy",
    })
    .returning();

  console.log("Start Session: New session started:", newSession[0]);
  return newSession[0];
};

// Function to end a memorization session
export const endMemorizationSession = async (
  sessionId: number,
  result: string
) => {
  const { userId } = await auth();
  console.log("End Session: Retrieved userId from auth:", userId);
  if (!userId) throw new Error("Unauthorized");

  // Validate the result input first
  if (!VALID_RESULTS.includes(result)) {
    console.error(`End Session: Invalid result value provided: ${result}`);
    throw new Error("Invalid result value");
  }

  // Find the session to ensure it exists and belongs to the user and is in progress
  const session = await db.query.memorizationSessions.findFirst({
    where: and(
      eq(memorizationSessions.sessionId, sessionId),
      eq(memorizationSessions.userId, userId)
      // eq(memorizationSessions.result, "In Progress") // Check state before update is safer
    ),
  });

  if (!session) {
    console.error(
      `End Session: Session ${sessionId} not found for user ${userId}.`
    );
    throw new Error("Session not found or access denied");
  }

  if (session.result !== "In Progress") {
    console.warn(
      `End Session: Session ${sessionId} is already finalized with result: ${session.result}. No update performed.`
    );
    // Decide whether to return the existing session or throw an error
    return session; // Returning existing session might be more graceful
    // throw new Error(`Session ${sessionId} already completed`);
  }

  console.log(
    `End Session: Updating session ${sessionId} with result: ${result}`
  );

  const sessionUpdateData = {
    endTime: getCurrentUTCDate(),
    result, // Already validated
  };

  try {
    // Perform the update
    const updatedSession = await db
      .update(memorizationSessions)
      .set(sessionUpdateData) // Pass the update data object
      .where(
        // Ensure .where() is correctly chained
        and(
          eq(memorizationSessions.sessionId, sessionId),
          eq(memorizationSessions.userId, userId)
          // Re-checking result === "In Progress" in where might be redundant but safer
          // eq(memorizationSessions.result, "In Progress")
        )
      )
      .returning(); // Get the updated session back

    // Check if the update actually happened (returning array should have one element)
    if (updatedSession.length === 0) {
      console.error(
        `End Session: Failed to update session ${sessionId}. It might have been modified concurrently.`
      );
      // This case is less likely if the checks above passed, but good to consider.
      throw new Error("Failed to update session state.");
    }

    console.log("End Session: Memorization session ended successfully.");
    return updatedSession[0];
  } catch (error: any) {
    console.error("Error ending memorization session:", error);
    throw new Error(`Failed to end memorization session: ${error.message}`);
  }
};

// Function to store memorization feedback (No significant changes needed from previous version)
export const storeMemorizationFeedback = async (
  feedbackData: {
    sessionId: number;
    surahId?: number; // Kept for input compatibility
    surahNumber: number; // Used
    verseNumber: number;
    feedbackType: string;
    feedbackDetails: string;
  }[]
) => {
  const { userId } = await auth();
  console.log("Store Feedback: Retrieved userId from auth:", userId);
  if (!userId) throw new Error("Unauthorized");

  if (!Array.isArray(feedbackData) || feedbackData.length === 0) {
    console.log("Store Feedback: No feedback data provided.");
    return { success: true, message: "No feedback data to store." };
  }

  const sessionIds = Array.from(new Set(feedbackData.map((f) => f.sessionId)));

  // Validate that all session IDs belong to the user
  const userSessionsCount = await db
    .select({ count: countDistinct(memorizationSessions.sessionId) })
    .from(memorizationSessions)
    .where(
      and(
        eq(memorizationSessions.userId, userId),
        inArray(memorizationSessions.sessionId, sessionIds)
      )
    );

  if ((userSessionsCount[0]?.count ?? 0) !== sessionIds.length) {
    console.error(
      "Store Feedback: Invalid or unauthorized session ID(s) provided."
    );
    throw new Error("Invalid session ID(s) for feedback");
  }

  // Prepare data for insertion, ensuring surahNumber exists
  const feedbackToInsert = feedbackData
    .filter((feedback) => {
      if (!feedback.surahNumber) {
        console.warn(
          "Store Feedback: Skipping feedback item due to missing surahNumber:",
          feedback
        );
        return false;
      }
      // Optional: Add validation for feedbackType here if needed
      // if (!VALID_FEEDBACK_TYPES.includes(feedback.feedbackType)) return false;
      return true;
    })
    .map((feedback) => ({
      userId,
      sessionId: feedback.sessionId,
      surahNumber: feedback.surahNumber, // Already checked existence
      verseNumber: feedback.verseNumber,
      feedbackType: feedback.feedbackType,
      feedbackDetails: feedback.feedbackDetails,
    }));

  if (feedbackToInsert.length === 0) {
    console.log(
      "Store Feedback: No valid feedback items to insert after filtering."
    );
    return { success: true, message: "No valid feedback data to store." };
  }

  console.log(
    `Store Feedback: Preparing to insert ${feedbackToInsert.length} feedback items.`
  );

  // Batch insert for efficiency
  const batchSize = 100;
  try {
    for (let i = 0; i < feedbackToInsert.length; i += batchSize) {
      const batch = feedbackToInsert.slice(i, i + batchSize);
      await db.insert(memorizationFeedback).values(batch); // Use .values() for multi-row insert
      console.log(`Store Feedback: Inserted batch ${i / batchSize + 1}`);
    }
    console.log("Store Feedback: Memorization feedback stored successfully.");
    return { success: true };
  } catch (error: any) {
    console.error("Error storing memorization feedback:", error.message);
    throw new Error(`Failed to store memorization feedback: ${error.message}`);
  }
};

// --- Enhanced addMemorizedVerse Function (Includes Completion Check using API) ---
export const addMemorizedVerse = async (verseData: {
  surahNumber?: number;
  verseNumber: number;
}) => {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized - No userId found");

  const actualSurahNumber = verseData.surahNumber;
  if (!actualSurahNumber)
    throw new Error("Missing surahNumber in addMemorizedVerse");

  let newlyInsertedRecord: typeof memorizedVerses.$inferSelect | undefined =
    undefined;
  let verseWasAlreadyMemorized = false;

  try {
    // --- Start of operations (NO transaction block) ---
    console.log(
      `Checking verse: User=${userId}, Surah=${actualSurahNumber}, Verse=${verseData.verseNumber}`
    );
    const existing = await db.query.memorizedVerses.findFirst({
      // Use 'db' directly
      where: and(
        eq(memorizedVerses.userId, userId),
        eq(memorizedVerses.surahNumber, actualSurahNumber),
        eq(memorizedVerses.verseNumber, verseData.verseNumber)
      ),
    });

    if (existing) {
      console.log(
        `Verse ${actualSurahNumber}:${verseData.verseNumber} already exists. Skipping.`
      );
      newlyInsertedRecord = existing;
      verseWasAlreadyMemorized = true;
      // Still return the existing record
      return newlyInsertedRecord; // Return directly, no transaction to exit
    }

    console.log(
      `Inserting verse: User=${userId}, Surah=${actualSurahNumber}, Verse=${verseData.verseNumber}`
    );
    const result = await db // Use 'db' directly
      .insert(memorizedVerses)
      .values({
        userId,
        surahNumber: actualSurahNumber,
        verseNumber: verseData.verseNumber,
        dateMemorized: getCurrentUTCDate(),
      })
      .returning();
    newlyInsertedRecord = result[0];
    console.log(`Inserted verse record:`, newlyInsertedRecord);

    // --- Check Surah Completion (Proceed even without transaction) ---
    // This check now runs *after* the insert is committed.
    console.log(
      `Checking completion for Surah ${actualSurahNumber}, User ${userId}`
    );
    const totalAyahs = await getNumberOfAyahsFromAPI(actualSurahNumber);

    if (totalAyahs === null) {
      console.error(
        `Failed to get numberOfAyahs from API for Surah ${actualSurahNumber}. Cannot check completion.`
      );
      // The verse IS inserted, but we can't check completion now.
      // Maybe log this for later manual check?
      return newlyInsertedRecord; // Return the inserted record
    }

    const requiredCount = Math.max(0, totalAyahs - 2);
    if (requiredCount <= 0) {
      console.log(
        `Surah ${actualSurahNumber} requires ${requiredCount} verses (>${2}). No standard check needed.`
      );
      // Handle potential automatic completion for short surahs if needed
      return newlyInsertedRecord;
    }

    const memorizedCountResult = await db // Use 'db' directly
      .select({ count: countDistinct(memorizedVerses.verseNumber) })
      .from(memorizedVerses)
      .where(
        and(
          eq(memorizedVerses.userId, userId),
          eq(memorizedVerses.surahNumber, actualSurahNumber),
          gt(memorizedVerses.verseNumber, 2)
        )
      );
    const currentMemorizedCount = memorizedCountResult[0]?.count ?? 0;

    console.log(
      `Surah ${actualSurahNumber}: API Ayahs=${totalAyahs}, Required=${requiredCount}, User Memorized (DB >2)=${currentMemorizedCount}`
    );

    if (currentMemorizedCount >= requiredCount) {
      console.log(
        `Completion criteria met for Surah ${actualSurahNumber}. Checking memorizedSurahs.`
      );
      const existingMemorizedSurah = await db.query.memorizedSurahs.findFirst({
        // Use 'db' directly
        where: and(
          eq(memorizedSurahs.userId, userId),
          eq(memorizedSurahs.surahNumber, actualSurahNumber)
        ),
      });

      if (!existingMemorizedSurah) {
        console.log(
          `Inserting into memorizedSurahs: User=${userId}, Surah=${actualSurahNumber}`
        );
        try {
          await db // Use 'db' directly
            .insert(memorizedSurahs)
            .values({
              userId,
              surahNumber: actualSurahNumber,
              dateMemorized: getCurrentUTCDate(),
            });
          console.log(`Marked Surah ${actualSurahNumber} as memorized.`);
        } catch (insertError) {
          // Handle potential error during this second insert (e.g., constraint violation)
          console.error(
            `Error inserting into memorizedSurahs for Surah ${actualSurahNumber}:`,
            insertError
          );
          // Decide how critical this is. Maybe just log?
        }
      } else {
        console.log(`Surah ${actualSurahNumber} already marked as memorized.`);
      }
    } else {
      console.log(
        `Surah ${actualSurahNumber} not yet complete (${currentMemorizedCount}/${requiredCount}).`
      );
    }
    // --- End of operations ---

    return newlyInsertedRecord; // Return the found or inserted verse record
  } catch (error) {
    // Catch errors from any of the DB operations or API call
    console.error("Error in addMemorizedVerse function:", error);
    throw new Error(
      `Failed to process memorized verse: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
};

// Function to get a memorization session by its ID (No changes needed)
export const getMemorizationSessionById = async (sessionId: number) => {
  const { userId } = await auth();
  console.log("Get Session: Retrieved userId from auth:", userId);
  if (!userId) throw new Error("Unauthorized");

  const session = await db.query.memorizationSessions.findFirst({
    where: and(
      eq(memorizationSessions.sessionId, sessionId),
      eq(memorizationSessions.userId, userId)
    ),
  });
  console.log(`Get Session: Found session for ID ${sessionId}:`, !!session);
  return session;
};

// Function to get user memorization progress (No changes needed)
export const getUserMemorizationProgress = async (params: {
  surahId?: number; // Kept for input compatibility
  surahNumber?: number; // Used
}) => {
  const { userId } = await auth();
  console.log("Get Progress: Retrieved userId from auth:", userId);
  if (!userId) throw new Error("Unauthorized");

  const actualSurahNumber = params.surahNumber;
  if (!actualSurahNumber) {
    console.log("Get Progress: No surahNumber provided.");
    return null; // Return null or empty state as appropriate
  }

  console.log(`Get Progress: Fetching progress for Surah ${actualSurahNumber}`);
  const progress = await db.query.memorizationProgress.findFirst({
    where: and(
      eq(memorizationProgress.userId, userId),
      eq(memorizationProgress.surahNumber, actualSurahNumber)
    ),
  });

  if (!progress) {
    console.log(
      `Get Progress: No progress record found for Surah ${actualSurahNumber}.`
    );
    return null; // Indicate no progress found
  }

  console.log(
    `Get Progress: Fetching recent sessions for Surah ${actualSurahNumber}`
  );
  const sessions = await db.query.memorizationSessions.findMany({
    where: and(
      eq(memorizationSessions.userId, userId),
      eq(memorizationSessions.surahNumber, actualSurahNumber)
    ),
    orderBy: [desc(memorizationSessions.endTime)], // Order by most recent first
    limit: 3, // Limit the number of sessions returned
  });

  console.log(`Get Progress: Found ${sessions.length} recent sessions.`);
  return { ...progress, sessions };
};
