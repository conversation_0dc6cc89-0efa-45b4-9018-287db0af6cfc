"use server";

import { auth } from "@clerk/nextjs/server";
import { and, eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

import db from "@/db/drizzle";
import { getUserProgress, getUserSubscription } from "@/db/queries";
import {
  challengeProgress,
  challenges,
  userProgress,
  challengeOptions as challengeOptionsSchema,
} from "@/db/schema";
import { validateMatch } from "./validate-match"; // Import the new function

export const upsertChallengeProgress = async (
  challengeId: number,
  option1Id?: number | string,
  option2Id?: number
) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const currentUserProgress = await getUserProgress();
  const userSubscription = await getUserSubscription();

  if (!currentUserProgress) {
    throw new Error("User progress not found");
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  const lessonId = challenge.lessonId;

  const existingChallengeProgress = await db.query.challengeProgress.findFirst({
    where: and(
      eq(challengeProgress.userId, userId),
      eq(challengeProgress.challengeId, challengeId)
    ),
  });

  const isPractice = !!existingChallengeProgress;

  if (
    currentUserProgress.hearts === 0 &&
    !isPractice &&
    !userSubscription?.isActive
  ) {
    console.log(
      "upsertChallengeProgress: No hearts left and not practice mode."
    );
    return { error: "hearts" };
  }

  // Logic for MATCHING challenge type
  if (challenge.type === "MATCHING") {
    console.log("Matching challenge detected");
    console.log("option1Id:", option1Id, "option2Id:", option2Id);

    if (typeof option1Id !== "number" || typeof option2Id !== "number") {
      throw new Error(
        `Matching options are required and must be numbers. Received option1Id: ${option1Id}, option2Id: ${option2Id}`
      );
    }

    const result = await validateMatch(option1Id, option2Id);
    console.log("validateMatch result:", result);

    if (result.error) {
      return result;
    }

    // Update challenge progress after successful validation
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "TAP_WHAT_YOU_HEAR") {
    console.log("Tap What You Hear challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "number") {
      throw new Error(
        `An option is required for Tap What You Hear and must be a number. Received option1Id: ${option1Id}`
      );
    }

    const correctOption = await db.query.challengeOptions.findFirst({
      where: and(
        eq(challengeOptionsSchema.challengeId, challengeId),
        eq(challengeOptionsSchema.correct, true)
      ),
    });

    if (!correctOption || correctOption.id !== option1Id) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "IMAGE_AUDIO_SELECT") {
    console.log("Image and Audio Select challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "number") {
      throw new Error(
        `An option is required for Image and Audio Select and must be a number. Received option1Id: ${option1Id}`
      );
    }

    const correctOption = await db.query.challengeOptions.findFirst({
      where: and(
        eq(challengeOptionsSchema.challengeId, challengeId),
        eq(challengeOptionsSchema.correct, true)
      ),
    });

    if (!correctOption || correctOption.id !== option1Id) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "DRAG_AND_DROP") {
    console.log("Drag and Drop challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "string") {
      throw new Error(
        `An option is required for Drag and Drop and must be a string. Received option1Id: ${option1Id}`
      );
    }

    const selectedOptions: number[] = (option1Id as string)
      .split(",")
      .map(Number);

    console.log("Selected options from frontend:", selectedOptions);

    const challengeOptions = await db.query.challengeOptions.findMany({
      where: eq(challengeOptionsSchema.challengeId, challengeId),
      orderBy: (challengeOptionsSchema, { asc }) => [
        asc(challengeOptionsSchema.sequence),
      ],
    });

    const correctOrder = challengeOptions.map((option) => option.id);
    const challengeZoneSequences = challengeOptions
      .filter((option) => !selectedOptions.includes(option.id))
      .map((option) => option.sequence);

    console.log("Correct order from database:", correctOrder);
    console.log("Challenge zone sequences:", challengeZoneSequences);

    const isCorrectOrder = selectedOptions.every(
      (id, index) => id === correctOrder[index]
    );

    const hasIncorrectOption = challengeZoneSequences.some((seq) => seq !== 99);

    if (!isCorrectOrder || hasIncorrectOption) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  } else if (challenge.type === "FILL_IN_THE_BLANK") {
    console.log("Fill in the Blank challenge detected");
    console.log("option1Id:", option1Id);

    if (typeof option1Id !== "number") {
      throw new Error(
        `An option is required for Fill in the Blank and must be a number. Received option1Id: ${option1Id}`
      );
    }

    const correctOption = await db.query.challengeOptions.findFirst({
      where: and(
        eq(challengeOptionsSchema.challengeId, challengeId),
        eq(challengeOptionsSchema.correct, true)
      ),
    });

    if (!correctOption || correctOption.id !== option1Id) {
      return { error: "incorrect" };
    }

    // Update challenge progress
    if (existingChallengeProgress) {
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points and hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  }
  // ADDED: "SPEAK_THIS_ADVANCED" logic
  else if (challenge.type === "SPEAK_THIS_ADVANCED") {
    console.log("Speak This Advanced challenge detected");

    // If there's no "option" needed, we simply mark it as completed.
    // If you had to check correctness, you'd do that here.

    if (existingChallengeProgress) {
      // Already attempted => practice or repeated run
      await db
        .update(challengeProgress)
        .set({ completed: true })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));
    } else {
      // First time completing this challenge
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });
    }

    // Update user progress with new points, maybe hearts
    await db
      .update(userProgress)
      .set({
        hearts: Math.min(currentUserProgress.hearts + 1, 5),
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath(`/lesson/${lessonId}`);
    revalidatePath("/learn");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
  }
  // End of special challenge types
  else {
    if (isPractice) {
      console.log("upsertChallengeProgress: Practice mode detected.");
      await db
        .update(challengeProgress)
        .set({
          completed: true,
        })
        .where(eq(challengeProgress.id, existingChallengeProgress.id));

      await db
        .update(userProgress)
        .set({
          hearts: Math.min(currentUserProgress.hearts + 1, 5),
          points: currentUserProgress.points + 10,
        })
        .where(eq(userProgress.userId, userId));

      revalidatePath("/learn");
      revalidatePath("/lesson");
      revalidatePath("/quests");
      revalidatePath("/leaderboard");
      revalidatePath(`/lesson/${lessonId}`);
      return;
    }

    console.log("upsertChallengeProgress: Inserting new challenge progress.");
    await db.insert(challengeProgress).values({
      challengeId,
      userId,
      completed: true,
    });

    await db
      .update(userProgress)
      .set({
        points: currentUserProgress.points + 10,
      })
      .where(eq(userProgress.userId, userId));

    revalidatePath("/learn");
    revalidatePath("/lesson");
    revalidatePath("/quests");
    revalidatePath("/leaderboard");
    revalidatePath(`/lesson/${lessonId}`);
  }

  console.log("Challenge progress upserted successfully");

  revalidatePath("/learn");
  revalidatePath("/lesson");
  revalidatePath("/quests");
  revalidatePath("/leaderboard");
  revalidatePath(`/lesson/${lessonId}`);
};
