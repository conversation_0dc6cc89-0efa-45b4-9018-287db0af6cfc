"use server";

import { auth } from "@clerk/nextjs/server";
import { and, eq } from "drizzle-orm";
import db from "@/db/drizzle";
import {
  challengeOptions,
  challengeProgress,
  userProgress,
  challenges,
} from "@/db/schema";
import { getUserProgress, getUserSubscription } from "@/db/queries";

export const validateMatch = async (option1Id: number, option2Id: number) => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  // Retrieve the options
  const option1 = await db.query.challengeOptions.findFirst({
    where: eq(challengeOptions.id, option1Id),
  });

  const option2 = await db.query.challengeOptions.findFirst({
    where: eq(challengeOptions.id, option2Id),
  });

  if (!option1 || !option2) {
    throw new Error("Options not found");
  }

  // Retrieve the challenge
  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, option1.challengeId),
  });

  if (!challenge) {
    throw new Error("Challenge not found");
  }

  // Validate that the options are part of a matching challenge
  if (challenge.type !== "MATCHING") {
    throw new Error("Options do not belong to a matching challenge");
  }

  // Validate the 'side' attribute is set correctly
  if (option1.side === option2.side) {
    throw new Error("Both options cannot be on the same side");
  }

  // Check if both options belong to the same challenge and if they match
  if (
    option1.challengeId !== option2.challengeId ||
    option1.matchPairId !== option2.matchPairId
  ) {
    // If the match is incorrect, reduce hearts
    const currentUserProgress = await getUserProgress();
    const userSubscription = await getUserSubscription();

    if (!currentUserProgress) {
      throw new Error("User progress not found");
    }

    if (currentUserProgress.hearts === 0 && !userSubscription?.isActive) {
      return { error: "hearts" };
    }

    await db
      .update(userProgress)
      .set({
        hearts: Math.max(currentUserProgress.hearts - 1, 0),
      })
      .where(eq(userProgress.userId, userId));

    return { error: "incorrect" };
  }

  // If the match is correct, update challenge progress
  await db.insert(challengeProgress).values({
    challengeId: option1.challengeId,
    userId,
    completed: true,
  });

  return { success: true };
};
