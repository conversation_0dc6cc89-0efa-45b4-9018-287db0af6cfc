"use client";

import { useUser } from "@clerk/nextjs";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

type UserRole = "teacher" | "student";

interface UserRecord {
  userId: string;
  email: string;
  role: UserRole;
  displayName?: string;
  createdAt?: Date;
  schoolUsername?: string | null;
  schoolPassword?: string | null;
}

interface TeacherStudentRelation {
  studentId: string;
  teacherId: string;
  joinedAt: Date;
}

// Client-side user data fetching
async function fetchUserData(): Promise<UserRecord | null> {
  try {
    const response = await fetch("/api/users/me", {
      headers: { "Cache-Control": "no-cache" },
    });
    
    if (!response.ok) {
      if (response.status === 404) {
        return null; // User doesn't exist yet
      }
      throw new Error(`Failed to fetch user data: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("[useAuth] Error fetching user data:", error);
    throw error;
  }
}

// Client-side user creation
async function createUser(): Promise<UserRecord> {
  try {
    const response = await fetch("/api/users/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create user: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("[useAuth] Error creating user:", error);
    throw error;
  }
}

// Check teacher-student relationship
async function fetchTeacherStudentRelation(): Promise<TeacherStudentRelation | null> {
  try {
    const response = await fetch("/api/users/teacher-student-relation", {
      headers: { "Cache-Control": "no-cache" },
    });
    
    if (!response.ok) {
      if (response.status === 404) {
        return null; // No relation exists
      }
      throw new Error(`Failed to fetch teacher-student relation: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("[useAuth] Error fetching teacher-student relation:", error);
    throw error;
  }
}

export function useAuth() {
  const { user: clerkUser, isLoaded: clerkLoaded } = useUser();
  const router = useRouter();
  const queryClient = useQueryClient();

  // Query for user data
  const {
    data: userData,
    isLoading: userDataLoading,
    error: userDataError,
  } = useQuery({
    queryKey: ["userData", clerkUser?.id],
    queryFn: fetchUserData,
    enabled: clerkLoaded && !!clerkUser?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });

  // Query for teacher-student relationship (only for students)
  const {
    data: teacherStudentRelation,
    isLoading: relationLoading,
  } = useQuery({
    queryKey: ["teacherStudentRelation", clerkUser?.id],
    queryFn: fetchTeacherStudentRelation,
    enabled: clerkLoaded && !!clerkUser?.id && userData?.role === "student",
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });

  // Mutation for creating user
  const createUserMutation = useMutation({
    mutationFn: createUser,
    onSuccess: (newUser) => {
      queryClient.setQueryData(["userData", clerkUser?.id], newUser);
    },
  });

  // Auto-create user if they don't exist
  useEffect(() => {
    if (clerkLoaded && clerkUser && userData === null && !userDataLoading && !createUserMutation.isPending) {
      console.log("[useAuth] User doesn't exist, creating...");
      createUserMutation.mutate();
    }
  }, [clerkLoaded, clerkUser, userData, userDataLoading, createUserMutation]);

  // Redirect logic for incomplete onboarding
  useEffect(() => {
    if (!clerkLoaded || !userData || userDataLoading || relationLoading) {
      return; // Still loading
    }

    // Check if teacher has credentials
    if (userData.role === "teacher") {
      const hasTeacherCreds = userData.schoolUsername && userData.schoolPassword;
      if (!hasTeacherCreds) {
        console.log("[useAuth] Teacher missing credentials, redirecting to /joinSchool");
        router.push("/joinSchool");
        return;
      }
    }

    // Check if student has joined a teacher
    if (userData.role === "student" && !teacherStudentRelation) {
      console.log("[useAuth] Student hasn't joined a teacher, redirecting to /joinSchool");
      router.push("/joinSchool");
      return;
    }

    console.log("[useAuth] User fully authenticated and onboarded");
  }, [clerkLoaded, userData, teacherStudentRelation, userDataLoading, relationLoading, router]);

  const isLoading = !clerkLoaded || userDataLoading || createUserMutation.isPending || 
    (userData?.role === "student" && relationLoading);

  const isAuthenticated = clerkLoaded && !!clerkUser && !!userData;

  const isFullyOnboarded = isAuthenticated && 
    (userData.role === "teacher" ? 
      !!(userData.schoolUsername && userData.schoolPassword) : 
      !!teacherStudentRelation);

  return {
    user: userData,
    clerkUser,
    isLoading,
    isAuthenticated,
    isFullyOnboarded,
    teacherStudentRelation,
    error: userDataError,
    refetch: () => {
      queryClient.invalidateQueries({ queryKey: ["userData", clerkUser?.id] });
      queryClient.invalidateQueries({ queryKey: ["teacherStudentRelation", clerkUser?.id] });
    },
  };
}
