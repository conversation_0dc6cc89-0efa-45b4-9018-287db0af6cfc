import { create } from "zustand";

export interface Annotation {
  userId: string;
  [key: string]: any;
}

interface AnnotationStoreState {
  annotations: Annotation[];
  add: (annotation: Annotation) => void;
  clear: () => void;
  set: (annotations: Annotation[]) => void;
}

export const useAnnotationsStore = create<AnnotationStoreState>((set) => ({
  annotations: [],
  add: (annotation) =>
    set((state) => ({ annotations: [...state.annotations, annotation] })),
  clear: () => set({ annotations: [] }),
  set: (annotations) => set({ annotations }),
}));
