// store/useVoiceStore.ts

import { create } from "zustand";

// Types
export type ConversationStatus =
  | "inactive"
  | "connecting"
  | "ready"
  | "listening";
export type ConnectionStatus = "disconnected" | "connecting" | "connected";

interface VoiceState {
  // Core state
  conversationActive: boolean;
  connectionStatus: ConnectionStatus;
  isRecording: boolean;
  transcript: string;
  aiResponse: string;
  error: string | null;
  userAmplitude: number;
  isAssistantSpeaking: boolean;

  // Derived state (now stored as regular state for better reactivity)
  isListening: boolean;
  isSpeaking: boolean;
  conversationStatus: ConversationStatus;

  // Internal helper to update derived state
  _updateDerivedState: () => void;

  // Actions
  setConversationActive: (active: boolean) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  setIsRecording: (recording: boolean) => void;
  setTranscript: (transcript: string) => void;
  setAiResponse: (response: string) => void;
  setError: (error: string | null) => void;
  setUserAmplitude: (amplitude: number) => void;
  setIsAssistantSpeaking: (speaking: boolean) => void;

  // Composite actions
  startConversation: () => void;
  stopConversation: () => void;
  resetConversationData: () => void;

  // Event handlers (for socket events)
  handleSpeechStarted: () => void;
  handleSpeechStopped: () => void;
  handleTranscriptionCompleted: (transcript: string) => void;
  handleResponseTextDelta: (delta: string) => void;
  handleResponseAudioDelta: () => void;
  handleResponseAudioDone: () => void;
  handleError: (errorMessage: string) => void;
}

export const useVoiceStore = create<VoiceState>((set, get) => ({
  // Initial state
  conversationActive: false,
  connectionStatus: "disconnected",
  isRecording: false,
  transcript: "",
  aiResponse: "",
  error: null,
  userAmplitude: 0,
  isAssistantSpeaking: false,

  // Initial derived state
  isListening: false,
  isSpeaking: false,
  conversationStatus: "inactive",

  // Internal helper to update derived state
  _updateDerivedState: () => {
    const state = get();

    // Calculate derived values
    const newIsListening = state.conversationActive && state.isRecording;
    const newIsSpeaking = state.isAssistantSpeaking;

    let newConversationStatus: ConversationStatus = "inactive";
    if (!state.conversationActive) {
      newConversationStatus = "inactive";
    } else if (state.connectionStatus === "connecting") {
      newConversationStatus = "connecting";
    } else if (state.isRecording) {
      newConversationStatus = "listening";
    } else if (state.connectionStatus === "connected" && !state.isRecording) {
      newConversationStatus = "ready";
    } else {
      newConversationStatus = "ready";
    }

    // Only update if values actually changed
    if (
      state.isListening !== newIsListening ||
      state.isSpeaking !== newIsSpeaking ||
      state.conversationStatus !== newConversationStatus
    ) {
      set({
        isListening: newIsListening,
        isSpeaking: newIsSpeaking,
        conversationStatus: newConversationStatus,
      });
    }
  },

  // Enhanced setters that update derived state
  setConversationActive: (active) => {
    set({ conversationActive: active });
    get()._updateDerivedState();
  },

  setConnectionStatus: (status) => {
    set({ connectionStatus: status });
    get()._updateDerivedState();
  },

  setIsRecording: (recording) => {
    set({ isRecording: recording });
    get()._updateDerivedState();
  },

  setTranscript: (transcript) => {
    set({ transcript });
    // Transcript changes don't affect derived state
  },

  setAiResponse: (response) => {
    set({ aiResponse: response });
    // AI response changes don't affect derived state
  },

  setError: (error) => {
    set({ error });
    // Error changes don't affect derived state
  },

  setUserAmplitude: (amplitude) => {
    set({ userAmplitude: amplitude });
    // Amplitude changes don't affect derived state
  },

  setIsAssistantSpeaking: (speaking) => {
    set({ isAssistantSpeaking: speaking });
    get()._updateDerivedState();
  },

  // Enhanced composite actions
  startConversation: () => {
    set({
      conversationActive: true,
      transcript: "",
      aiResponse: "",
      error: null,
    });
    get()._updateDerivedState();
  },

  stopConversation: () => {
    set({
      conversationActive: false,
      connectionStatus: "disconnected",
      isRecording: false,
      userAmplitude: 0,
      isAssistantSpeaking: false,
    });
    get()._updateDerivedState();
  },

  resetConversationData: () => {
    set({
      transcript: "",
      aiResponse: "",
      error: null,
    });
    // No derived state changes
  },

  // Enhanced socket event handlers
  handleSpeechStarted: () => {
    set({
      isRecording: true,
      transcript: "",
      aiResponse: "",
    });
    get()._updateDerivedState();
  },

  handleSpeechStopped: () => {
    set({
      isRecording: false,
      userAmplitude: 0,
    });
    get()._updateDerivedState();
  },

  handleTranscriptionCompleted: (transcript) => {
    set({ transcript });
    // No derived state changes
  },

  handleResponseTextDelta: (delta) => {
    set((state) => ({
      aiResponse: state.aiResponse + delta,
    }));
    // No derived state changes
  },

  handleResponseAudioDelta: () => {
    // Ensure we set speaking state immediately when audio starts
    set({ isAssistantSpeaking: true });
    get()._updateDerivedState();
  },

  handleResponseAudioDone: () => {
    // Ensure we clear speaking state when audio ends
    set({ isAssistantSpeaking: false });
    get()._updateDerivedState();
  },

  handleError: (errorMessage) => {
    set({
      error: `API Error: ${errorMessage}`,
      connectionStatus: "disconnected",
      isAssistantSpeaking: false,
    });
    get()._updateDerivedState();
  },
}));
