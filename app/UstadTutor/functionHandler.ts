// functionHandler.ts

import { Socket } from "socket.io-client";

// ================================================================== //
// TYPE DEFINITIONS AND INTERFACES
// ================================================================== //

export interface FunctionCallItem {
  name: string;
  arguments: string; // JSON-stringified arguments
  call_id: string;
}

export type Executor = (args: any) => Promise<any> | any;

interface LetterMetadata {
  complexity: "basic" | "intermediate" | "advanced";
  complexityScore: number;
  position: {
    index: number;
    total: number;
    isFirst: boolean;
    isLast: boolean;
    relativePosition: "beginning" | "middle" | "end";
  };
  phonetic: { family: string; group: string; similarLetters: string[] };
  search: {
    keywords: string[];
    variations: string[];
    commonMisspellings: string[];
    descriptions: string[];
  };
  visual: {
    characterCount: number;
    hasSpecialMarks: boolean;
    visualComplexity: number;
    shapeFamily: string;
  };
}
interface EnhancedSquare {
  id: number;
  squareNumber: number;
  content: string;
  chapterId: number;
  metadata: LetterMetadata;
}
interface SearchResult {
  square: EnhancedSquare;
  confidence: number;
  matchType:
    | "exact_id"
    | "exact_content"
    | "fuzzy_content"
    | "keyword"
    | "description"
    | "position"
    | "arabic_variant";
  matchedTerm: string;
  arabicContext?: {
    normalizedForm: string;
    variants: string[];
    withoutDiacritics: string;
    shapeFamily: string;
  };
}
interface SmartSearchArgs {
  letterId?: number;
  content?: string;
  description?: string;
  position?: string | number;
  complexity?: "basic" | "intermediate" | "advanced";
}
interface CardSelectionArgs {
  cardId: number;
  content: string;
  complexity?: "basic" | "intermediate" | "advanced";
  selectionMethod: "click" | "touch" | "voice_navigation";
  metadata?: LetterMetadata;
  timestamp: number;
}
interface GoToChapterArgs {
  chapter: number;
}
// --- (NEW) Step 1: Types for analyze_drawing ---
interface StrokeObject {
  userId: string;
  tool: "pen" | "eraser";
  color: string;
  lineWidth: number;
  opacity: number;
  points: { x: number; y: number }[];
}

interface AnalyzeDrawingArgs {
  strokes: StrokeObject[];
  target: string;
}
// --- Step 13: Type definition for function timing metrics ---
interface FunctionMetric {
  name: string;
  callId: string;
  executionTimeMs: number;
  status: "success" | "failure";
  timestamp: number;
}

// ================================================================== //
// DYNAMIC RESPONSE TEMPLATES
// ================================================================== //

const CONFIDENCE_QUESTION_TEMPLATES = [
  "On a scale of 1 to 5, how confident are you with Lesson {chapter}?",
  "Rate your understanding of Lesson {chapter} from 1 to 5.",
  "How well did you grasp Lesson {chapter}? Scale of 1-5.",
  "From 1 to 5, how confident do you feel about Lesson {chapter}?",
  "Please rate your confidence with Lesson {chapter}, 1 to 5.",
  "How confident are you with Lesson {chapter}? Rate 1-5.",
  "On a 1-5 scale, how did Lesson {chapter} go for you?",
  "Rate your Lesson {chapter} understanding, 1 to 5.",
];
const LETTER_SUCCESS_MESSAGES = [
  "Found {letter}!",
  "Got it - {letter}!",
  "There's {letter}!",
  "{letter} selected!",
  "Perfect - {letter}!",
  "Nice! {letter}!",
  "{letter} found!",
  "Awesome - {letter}!",
];
const CHAPTER_SUCCESS_MESSAGES = [
  "Opening chapter {chapter}!",
  "Chapter {chapter} ready!",
  "Let's explore chapter {chapter}!",
  "Chapter {chapter} loaded!",
  "Here's chapter {chapter}!",
  "Welcome to chapter {chapter}!",
  "Chapter {chapter} unlocked!",
  "Diving into chapter {chapter}!",
];
const ERROR_MESSAGE_TEMPLATES = [
  "Hmm, can't locate that one.",
  "Not finding it.",
  "Can't spot that letter.",
  "Oops, not seeing it.",
  "Having trouble finding that.",
  "Can't track that down.",
  "Not locating it right now.",
  "Struggling to find that one.",
];
const SUGGESTION_PREFIXES = [
  "Maybe try:",
  "How about:",
  "Try these:",
  "Consider:",
  "What about:",
  "Perhaps:",
  "You could try:",
  "How about these:",
];
const CONFIDENCE_SUCCESS_MESSAGES = [
  "Got it! Let's check your confidence.",
  "Perfect! Time for a quick confidence check.",
  "Great! How did that lesson feel?",
  "Awesome! Let's see how you're doing.",
  "Nice work! Quick confidence question.",
  "Excellent! Rate your experience.",
  "Fantastic! How confident are you feeling?",
  "Well done! Let's gauge your confidence.",
];
const CARD_SELECTION_ACKNOWLEDGMENTS = [
  "Nice choice - {letter}!",
  "Great! You picked {letter}.",
  "Good selection - {letter}!",
  "Perfect! That's {letter}.",
  "Excellent - {letter}!",
  "Well done! {letter} it is.",
  "Smart pick - {letter}!",
  "Awesome! You chose {letter}.",
  "Good eye - {letter}!",
  "Right on! {letter}.",
];
const CARD_SELECTION_EDUCATIONAL = [
  "{letter} makes a '{sound}' sound.",
  "{letter} - pronounced '{sound}'.",
  "That's {letter}, sounds like '{sound}'.",
  "{letter} - the '{sound}' sound.",
  "Good! {letter} goes '{sound}'.",
  "{letter} - say '{sound}'.",
  "Nice! {letter} sounds '{sound}'.",
  "{letter} - makes '{sound}'.",
];
const CARD_SELECTION_COMPLEXITY_RESPONSES = {
  basic: [
    "Great start with {letter}!",
    "Perfect choice - {letter}!",
    "Nice! {letter} is fundamental.",
    "Good! {letter} is essential.",
    "Excellent! {letter} is key.",
  ],
  intermediate: [
    "Nice work! {letter} is trickier.",
    "Good! {letter} needs practice.",
    "Well done! {letter} is challenging.",
    "Great! {letter} is important.",
    "Perfect! {letter} is advanced.",
  ],
  advanced: [
    "Impressive! {letter} is complex.",
    "Excellent! {letter} is difficult.",
    "Wow! {letter} is challenging.",
    "Great work! {letter} is tough.",
    "Amazing! {letter} is advanced.",
  ],
};
const CARD_SELECTION_ENCOURAGEMENT = [
  "Keep exploring!",
  "Try another one!",
  "What's next?",
  "Pick another!",
  "Keep going!",
  "Next letter?",
  "Choose more!",
  "Continue learning!",
];

// ================================================================== //
// DYNAMIC RESPONSE UTILITIES
// ================================================================== //

function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}
function generateConfidenceQuestion(chapter: number): string {
  const template = getRandomItem(CONFIDENCE_QUESTION_TEMPLATES);
  return template.replace("{chapter}", chapter.toString());
}
function generateLetterSuccessMessage(letterName: string): string {
  const template = getRandomItem(LETTER_SUCCESS_MESSAGES);
  return template.replace("{letter}", letterName);
}
function generateChapterSuccessMessage(chapterNumber: number): string {
  const template = getRandomItem(CHAPTER_SUCCESS_MESSAGES);
  return template.replace("{chapter}", chapterNumber.toString());
}
function generateErrorMessage(): string {
  return getRandomItem(ERROR_MESSAGE_TEMPLATES);
}
function generateSuggestionPrefix(): string {
  return getRandomItem(SUGGESTION_PREFIXES);
}
function generateConfidenceSuccessMessage(): string {
  return getRandomItem(CONFIDENCE_SUCCESS_MESSAGES);
}
function generateCardSelectionResponse(
  letter: string,
  complexity?: "basic" | "intermediate" | "advanced",
  responseType:
    | "acknowledgment"
    | "educational"
    | "encouragement" = "acknowledgment"
): string {
  switch (responseType) {
    case "acknowledgment":
      const template = getRandomItem(CARD_SELECTION_ACKNOWLEDGMENTS);
      return template.replace("{letter}", letter);
    case "educational":
      const eduTemplate = getRandomItem(CARD_SELECTION_EDUCATIONAL);
      return eduTemplate.replace("{letter}", letter).replace("{sound}", letter);
    case "encouragement":
      return getRandomItem(CARD_SELECTION_ENCOURAGEMENT);
    default:
      return generateCardSelectionResponse(
        letter,
        complexity,
        "acknowledgment"
      );
  }
}
function generateComplexityAwareResponse(
  letter: string,
  complexity: "basic" | "intermediate" | "advanced"
): string {
  const templates = CARD_SELECTION_COMPLEXITY_RESPONSES[complexity];
  const template = getRandomItem(templates);
  return template.replace("{letter}", letter);
}

// ================================================================== //
// REGISTRY, STORAGE, AND ENHANCED CONFIGURATION
// ================================================================== //

const functionRegistry: Record<string, Executor> = {};
// Step 1 & 3: Add navigation context to the global context storage
let currentChapterContext: {
  squares: EnhancedSquare[];
  chapterId: number;
  chapterOrder: number;
  restrictNavigation: boolean;
} | null = null;

// --- Step 13 & 14: Configuration and Storage for Metrics and Retries ---
const MAX_METRICS = 50; // Store the last 50 function call metrics
const functionMetrics: FunctionMetric[] = [];
const RETRY_CONFIG = {
  maxAttempts: 2, // Initial call + 1 retry
  delayMs: 500,
  // List of functions that are safe to retry (idempotent or read-only operations)
  safeToRetry: ["find_and_select_letter", "go_to_chapter"],
};

// ================================================================== //
// ENHANCED ARABIC PROCESSING UTILITIES
// ================================================================== //

function normalizeArabicText(text: string): string {
  return text
    .replace(/\s+/g, "")
    .replace(/[\u064B-\u065F\u0670\u0640]/g, "")
    .replace(/[\u0671]/g, "\u0627")
    .replace(/[\u0622\u0623\u0625]/g, "\u0627")
    .replace(/[\u0624]/g, "\u0648")
    .replace(/[\u0626]/g, "\u064A")
    .replace(/[\u0629]/g, "\u0647")
    .replace(/[\u064A\u0649]/g, "\u064A")
    .replace(/[\u0643]/g, "\u0643")
    .trim()
    .toLowerCase();
}
function containsArabicText(text: string): boolean {
  return /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(
    text
  );
}
function getArabicCharacterVariants(char: string): string[] {
  const variants: { [key: string]: string[] } = {
    "\u0627": ["\u0627", "\u0622", "\u0623", "\u0625", "\u0671"],
    "\u0648": ["\u0648", "\u0624"],
    "\u064A": ["\u064A", "\u0649", "\u0626"],
    "\u0647": ["\u0647", "\u0629"],
    "\u062A": ["\u062A", "\u0629"],
  };
  return variants[char] || [char];
}
function calculateArabicSimilarity(str1: string, str2: string): number {
  const normalized1 = normalizeArabicText(str1);
  const normalized2 = normalizeArabicText(str2);
  if (normalized1 === normalized2) {
    return 1.0;
  }
  const baseSimilarity = calculateSimilarity(normalized1, normalized2);
  if (containsArabicText(str1) || containsArabicText(str2)) {
    let variantMatches = 0;
    let totalChars = Math.max(normalized1.length, normalized2.length);
    for (let i = 0; i < Math.min(normalized1.length, normalized2.length); i++) {
      const char1 = normalized1[i];
      const char2 = normalized2[i];
      const variants1 = getArabicCharacterVariants(char1);
      const variants2 = getArabicCharacterVariants(char2);
      if (variants1.some((v) => variants2.includes(v))) {
        variantMatches++;
      }
    }
    const variantBonus =
      totalChars > 0 ? (variantMatches / totalChars) * 0.2 : 0;
    return Math.min(1.0, baseSimilarity + variantBonus);
  }
  return baseSimilarity;
}
function getArabicLetterContext(square: EnhancedSquare): {
  normalizedForm: string;
  variants: string[];
  withoutDiacritics: string;
  shapeFamily: string;
} {
  const normalizedForm = normalizeArabicText(square.content);
  const variants = square.content
    .split("")
    .flatMap((char) => getArabicCharacterVariants(char));
  const withoutDiacritics = square.content.replace(
    /[\u064B-\u065F\u0670]/g,
    ""
  );
  const shapeFamily = square.metadata?.visual?.shapeFamily || "unknown";
  return {
    normalizedForm,
    variants: [...new Set(variants)],
    withoutDiacritics,
    shapeFamily,
  };
}

// ================================================================== //
// SEARCH UTILITIES AND ALGORITHMS
// ================================================================== //

function calculateSimilarity(str1: string, str2: string): number {
  const len1 = str1.length;
  const len2 = str2.length;
  if (len1 === 0) return len2 === 0 ? 1 : 0;
  if (len2 === 0) return 0;
  const matrix: number[][] = [];
  for (let i = 0; i <= len1; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  const maxLen = Math.max(len1, len2);
  return (maxLen - matrix[len1][len2]) / maxLen;
}
function parsePositionalDescriptor(
  position: string | number,
  totalSquares: number
): {
  targetIndex?: number;
  relativePosition?: "beginning" | "middle" | "end";
  isFirst?: boolean;
  isLast?: boolean;
} {
  if (typeof position === "number") {
    return { targetIndex: position };
  }
  const lowerPos = position.toLowerCase().trim();
  if (lowerPos.includes("first") || lowerPos === "1st") {
    return { targetIndex: 0, isFirst: true };
  }
  if (lowerPos.includes("last") || lowerPos.includes("final")) {
    return { targetIndex: totalSquares - 1, isLast: true };
  }
  if (lowerPos.includes("beginning") || lowerPos.includes("start")) {
    return { relativePosition: "beginning" };
  }
  if (lowerPos.includes("middle") || lowerPos.includes("center")) {
    return { relativePosition: "middle" };
  }
  if (lowerPos.includes("end") || lowerPos.includes("finish")) {
    return { relativePosition: "end" };
  }
  const numericMatch = lowerPos.match(/(\d+)/);
  if (numericMatch) {
    const num = parseInt(numericMatch[1], 10);
    if (num > 0) {
      return { targetIndex: num };
    }
  }
  return {};
}
function searchLetters(
  args: SmartSearchArgs,
  squares: EnhancedSquare[]
): SearchResult[] {
  const results: SearchResult[] = [];
  const isArabicSearch = args.content
    ? containsArabicText(args.content)
    : false;
  console.log(
    "[SMART_SEARCH] Starting Arabic-enhanced search with args:",
    args
  );
  console.log("[SMART_SEARCH] Arabic content detected:", isArabicSearch);
  console.log("[SMART_SEARCH] Searching through", squares.length, "squares");
  if (args.letterId !== undefined) {
    const exactIdMatch = squares.find((sq) => sq.id === args.letterId);
    if (exactIdMatch) {
      const arabicContext = getArabicLetterContext(exactIdMatch);
      results.push({
        square: exactIdMatch,
        confidence: 1.0,
        matchType: "exact_id",
        matchedTerm: `ID:${args.letterId}`,
        arabicContext,
      });
      console.log("[SMART_SEARCH] Found exact ID match:", exactIdMatch.id);
    }
  }
  if (args.content) {
    const normalizedContent = normalizeArabicText(args.content);
    squares.forEach((square) => {
      const normalizedSquareContent = normalizeArabicText(square.content);
      const arabicContext = getArabicLetterContext(square);
      if (normalizedSquareContent === normalizedContent) {
        results.push({
          square,
          confidence: 1.0,
          matchType: "exact_content",
          matchedTerm: args.content || "content match",
          arabicContext,
        });
        console.log(
          "[SMART_SEARCH] Found exact Arabic content match:",
          square.content
        );
      }
    });
    if (isArabicSearch && results.length === 0) {
      squares.forEach((square) => {
        const arabicSimilarity = calculateArabicSimilarity(
          args.content || "",
          square.content
        );
        if (arabicSimilarity >= 0.85) {
          const arabicContext = getArabicLetterContext(square);
          results.push({
            square,
            confidence: arabicSimilarity * 0.95,
            matchType: "arabic_variant",
            matchedTerm: `${args.content} → ${square.content}`,
            arabicContext,
          });
          console.log("[SMART_SEARCH] Found Arabic variant match:", {
            searched: args.content,
            found: square.content,
            similarity: arabicSimilarity,
          });
        }
      });
    }
    if (args.content && isArabicSearch && results.length === 0) {
      squares.forEach((square) => {
        const arabicSimilarity = calculateArabicSimilarity(
          args.content || "",
          square.content
        );
        if (arabicSimilarity >= 0.6) {
          const arabicContext = getArabicLetterContext(square);
          results.push({
            square,
            confidence: arabicSimilarity * 0.9,
            matchType: "fuzzy_content",
            matchedTerm: `${args.content} ≈ ${square.content}`,
            arabicContext,
          });
          console.log("[SMART_SEARCH] Found fuzzy Arabic content match:", {
            searched: args.content,
            found: square.content,
            similarity: arabicSimilarity,
          });
        }
      });
    }
  }
  if (args.position !== undefined) {
    if (typeof args.position === "number") {
      const targetSquare = squares.find(
        (sq) => sq.squareNumber === args.position
      );
      if (targetSquare) {
        const arabicContext = getArabicLetterContext(targetSquare);
        results.push({
          square: targetSquare,
          confidence: 1.0,
          matchType: "position",
          matchedTerm: `visual card number ${args.position}`,
          arabicContext,
        });
      }
    } else {
      const positionCriteria = parsePositionalDescriptor(
        args.position,
        squares.length
      );
      squares.forEach((square, index) => {
        const metadata = square.metadata;
        let matches = false;
        let matchDescription = "";
        if (
          positionCriteria.targetIndex !== undefined &&
          index === positionCriteria.targetIndex
        ) {
          matches = true;
          matchDescription = `position by index ${index + 1}`;
        } else if (
          metadata &&
          positionCriteria.relativePosition &&
          metadata.position.relativePosition ===
            positionCriteria.relativePosition
        ) {
          matches = true;
          matchDescription = `${positionCriteria.relativePosition} section`;
        } else if (
          metadata &&
          positionCriteria.isFirst &&
          metadata.position.isFirst
        ) {
          matches = true;
          matchDescription = "first letter";
        } else if (
          metadata &&
          positionCriteria.isLast &&
          metadata.position.isLast
        ) {
          matches = true;
          matchDescription = "last letter";
        }
        if (matches) {
          const arabicContext = getArabicLetterContext(square);
          results.push({
            square,
            confidence: 0.8,
            matchType: "position",
            matchedTerm: `${args.position} → ${matchDescription}`,
            arabicContext,
          });
        }
      });
    }
  }
  if (args.complexity) {
    const complexityMatches = squares.filter(
      (square) =>
        square.metadata && square.metadata.complexity === args.complexity
    );
    if (complexityMatches.length > 0 && results.length === 0) {
      complexityMatches.forEach((square) => {
        const arabicContext = getArabicLetterContext(square);
        results.push({
          square,
          confidence: 0.5,
          matchType: "description",
          matchedTerm: `${args.complexity} complexity`,
          arabicContext,
        });
      });
    }
  }
  if (args.description) {
    const descLower = args.description.toLowerCase();
    squares.forEach((square) => {
      const metadata = square.metadata;
      if (!metadata) return;
      if (
        (descLower.includes("simple") ||
          descLower.includes("basic") ||
          descLower.includes("easy")) &&
        metadata.complexity === "basic"
      ) {
        const arabicContext = getArabicLetterContext(square);
        results.push({
          square,
          confidence: 0.6,
          matchType: "description",
          matchedTerm: `${args.description} → basic complexity`,
          arabicContext,
        });
      }
      if (
        (descLower.includes("complex") ||
          descLower.includes("advanced") ||
          descLower.includes("difficult")) &&
        metadata.complexity === "advanced"
      ) {
        const arabicContext = getArabicLetterContext(square);
        results.push({
          square,
          confidence: 0.6,
          matchType: "description",
          matchedTerm: `${args.description} → advanced complexity`,
          arabicContext,
        });
      }
      if (
        (descLower.includes("medium") ||
          descLower.includes("intermediate") ||
          descLower.includes("moderate")) &&
        metadata.complexity === "intermediate"
      ) {
        const arabicContext = getArabicLetterContext(square);
        results.push({
          square,
          confidence: 0.6,
          matchType: "description",
          matchedTerm: `${args.description} → intermediate complexity`,
          arabicContext,
        });
      }
      if (metadata.search && metadata.search.descriptions) {
        const descriptionMatch = metadata.search.descriptions.some(
          (desc) =>
            desc.toLowerCase().includes(descLower) ||
            descLower.includes(desc.toLowerCase())
        );
        if (descriptionMatch) {
          const arabicContext = getArabicLetterContext(square);
          results.push({
            square,
            confidence: 0.65,
            matchType: "description",
            matchedTerm: args.description || "description match",
            arabicContext,
          });
        }
      }
    });
  }
  const uniqueResults = results.filter(
    (result, index, self) =>
      index === self.findIndex((r) => r.square.id === result.square.id)
  );
  uniqueResults.sort((a, b) => {
    const aBoost =
      a.matchType === "exact_content" || a.matchType === "arabic_variant"
        ? 0.1
        : 0;
    const bBoost =
      b.matchType === "exact_content" || b.matchType === "arabic_variant"
        ? 0.1
        : 0;
    return b.confidence + bBoost - (a.confidence + aBoost);
  });
  console.log(
    "[SMART_SEARCH] Search completed. Found",
    uniqueResults.length,
    "unique results"
  );
  return uniqueResults;
}
function generateSearchSuggestions(
  args: SmartSearchArgs,
  squares: EnhancedSquare[]
): string[] {
  const suggestions: string[] = [];
  const availableArabicLetters = squares
    .map((sq) => sq.content)
    .filter(Boolean)
    .slice(0, 3);
  if (availableArabicLetters.length > 0) {
    const prefix = generateSuggestionPrefix();
    suggestions.push(`${prefix} ${availableArabicLetters.join(", ")}`);
  }
  const fallbackPrefix = generateSuggestionPrefix();
  suggestions.push(`${fallbackPrefix} "first", "last", "simple".`);
  return suggestions;
}

// ================================================================== //
// ENHANCED FUNCTION EXECUTORS
// ================================================================== //

export async function findAndSelectLetter(args: SmartSearchArgs): Promise<any> {
  console.log(
    "[FIND_AND_SELECT] Starting Arabic-enhanced letter search with args:",
    args
  );
  if (!currentChapterContext) {
    return {
      success: false,
      error: `${generateErrorMessage()} No chapter available.`,
      suggestions: ["Try navigating to a chapter first."],
    };
  }
  const { squares } = currentChapterContext;
  if (!squares || squares.length === 0) {
    return {
      success: false,
      error: `${generateErrorMessage()} No letters in current chapter.`,
      suggestions: ["Try a different chapter."],
    };
  }
  const hasArabicInput = args.content
    ? containsArabicText(args.content)
    : false;
  console.log("[FIND_AND_SELECT] Arabic input detected:", hasArabicInput);
  const searchResults = searchLetters(args, squares);
  if (searchResults.length === 0) {
    const suggestions = generateSearchSuggestions(args, squares);
    return {
      success: false,
      error: `${generateErrorMessage()}`,
      searchCriteria: args,
      suggestions,
      arabicSearchEnabled: hasArabicInput,
    };
  }
  if (searchResults.length > 1) {
    const bestMatch = searchResults[0];
    const alternatives = searchResults.slice(1, 4);
    console.log(
      "[FIND_AND_SELECT] Multiple matches found, selecting best match:",
      bestMatch.square.content
    );
    const successMessage = generateLetterSuccessMessage(
      bestMatch.square.content
    );
    return {
      success: true,
      selectedLetter: {
        id: bestMatch.square.id,
        content: bestMatch.square.content,
        complexity: bestMatch.square.metadata?.complexity || "unknown",
        position: bestMatch.square.metadata?.position || {
          index: 0,
          total: 0,
          isFirst: false,
          isLast: false,
          relativePosition: "middle",
        },
        phonetic: bestMatch.square.metadata?.phonetic,
        arabicContext: bestMatch.arabicContext,
      },
      matchDetails: {
        confidence: bestMatch.confidence,
        matchType: bestMatch.matchType,
        matchedTerm: bestMatch.matchedTerm,
        arabicMatch:
          bestMatch.matchType === "arabic_variant" ||
          bestMatch.matchType === "fuzzy_content" ||
          bestMatch.matchType === "exact_content",
      },
      alternatives: alternatives.map((alt) => ({
        id: alt.square.id,
        content: alt.square.content,
        confidence: alt.confidence,
        matchType: alt.matchType,
        arabicContext: alt.arabicContext,
      })),
      message: successMessage,
    };
  }
  const result = searchResults[0];
  console.log("[FIND_AND_SELECT] Single match found:", result.square.content);
  const successMessage = generateLetterSuccessMessage(result.square.content);
  return {
    success: true,
    selectedLetter: {
      id: result.square.id,
      content: result.square.content,
      complexity: result.square.metadata?.complexity || "unknown",
      position: result.square.metadata?.position || {
        index: 0,
        total: 0,
        isFirst: false,
        isLast: false,
        relativePosition: "middle",
      },
      phonetic: result.square.metadata?.phonetic,
      visual: result.square.metadata?.visual,
      arabicContext: result.arabicContext,
    },
    matchDetails: {
      confidence: result.confidence,
      matchType: result.matchType,
      matchedTerm: result.matchedTerm,
      arabicMatch:
        result.matchType === "arabic_variant" ||
        result.matchType === "fuzzy_content" ||
        result.matchType === "exact_content",
    },
    message: successMessage,
  };
}

export async function executeGoToChapter(args: GoToChapterArgs): Promise<any> {
  console.log(
    "[GO_TO_CHAPTER] Attempting to navigate to chapter:",
    args.chapter
  );

  if (currentChapterContext?.restrictNavigation) {
    console.warn("[GO_TO_CHAPTER] Navigation blocked due to restriction.");
    return {
      success: false,
      message:
        "I'm sorry, navigation is restricted right now. Please complete the current activity first.",
      reason: "RESTRICTED_NAVIGATION",
    };
  }

  const successMessage = generateChapterSuccessMessage(args.chapter);
  console.log("[GO_TO_CHAPTER] Navigation allowed. Result:", successMessage);

  return {
    success: true,
    chapter: args.chapter,
    message: successMessage,
  };
}

export async function executeStudentSelectedCard(
  args: CardSelectionArgs
): Promise<any> {
  console.log("[STUDENT_CARD_SELECTION] Processing card selection:", args);
  if (!args.cardId || !args.content) {
    return {
      success: false,
      error: `${generateErrorMessage()} Missing card information.`,
      timestamp: Date.now(),
    };
  }
  let enhancedCardData = null;
  if (currentChapterContext) {
    enhancedCardData = currentChapterContext.squares.find(
      (sq) => sq.id === args.cardId
    );
  }
  const complexity =
    args.complexity || enhancedCardData?.metadata?.complexity || "basic";
  const displayLetter = args.content;
  let primaryResponse = "";
  let secondaryResponse = "";
  if (Math.random() > 0.3) {
    primaryResponse = generateCardSelectionResponse(
      displayLetter,
      complexity,
      "acknowledgment"
    );
  } else {
    primaryResponse = generateComplexityAwareResponse(
      displayLetter,
      complexity
    );
  }
  if (Math.random() > 0.7) {
    secondaryResponse = generateCardSelectionResponse(
      displayLetter,
      complexity,
      "educational"
    );
  }
  let finalMessage = primaryResponse;
  if (
    secondaryResponse &&
    finalMessage.length + secondaryResponse.length < 50
  ) {
    finalMessage = `${primaryResponse} ${secondaryResponse}`;
  }
  if (Math.random() > 0.7) {
    const encouragement = generateCardSelectionResponse(
      displayLetter,
      complexity,
      "encouragement"
    );
    if (finalMessage.length + encouragement.length < 60) {
      finalMessage = `${finalMessage} ${encouragement}`;
    }
  }
  console.log("[STUDENT_CARD_SELECTION] Generated response:", finalMessage);
  return {
    success: true,
    acknowledged: true,
    card: {
      id: args.cardId,
      content: args.content,
      complexity: complexity,
      metadata: enhancedCardData?.metadata,
    },
    selection: {
      method: args.selectionMethod,
      timestamp: args.timestamp,
      responseGenerated: true,
    },
    analytics: {
      hasEnhancedMetadata: !!enhancedCardData,
      responseType: secondaryResponse ? "combined" : "acknowledgment",
      responseLength: finalMessage.length,
    },
    message: finalMessage,
    timestamp: Date.now(),
  };
}

// MODIFIED: This function is now more robust, handles edge cases, and returns a structured object for the LLM.
export async function executeAnalyzeDrawing(
  args: AnalyzeDrawingArgs
): Promise<any> {
  console.log("[ANALYZE_DRAWING] Starting analysis with args:", {
    target: args.target,
    strokeCount: args.strokes ? args.strokes.length : 0,
  });

  try {
    // Step 1: Handle the edge case of no drawing.
    if (!args.strokes || args.strokes.length === 0) {
      console.warn("[ANALYZE_DRAWING] No strokes provided for analysis.");
      // Return a structured, predictable error that the LLM can interpret.
      return {
        success: false,
        error: "NO_STROKES_PROVIDED",
        message:
          "The user clicked 'Done' but did not provide any drawing strokes. Please ask them to try drawing the letter.",
      };
    }

    // --- MOCK RECOGNITION LOGIC ---
    // This section simulates a real drawing analysis model.
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network/compute delay

    const strokeCount = args.strokes.length;
    const pointCount = args.strokes.reduce(
      (sum, s) => sum + s.points.length,
      0
    );

    // Mock confidence score calculation
    let confidence = 0;
    if (pointCount > 5) {
      confidence = Math.min(0.4 + strokeCount * 0.1 + pointCount * 0.005, 0.95);
    }

    // Simple mock recognition logic
    let recognizedCharacter: string | null = null;
    if (confidence > 0.75) {
      recognizedCharacter = args.target;
    } else if (confidence > 0.4) {
      recognizedCharacter = args.target === "ب" ? "ت" : "ج";
    }

    // --- END MOCK LOGIC ---

    // Step 2: Return a structured, consistent JSON object for the LLM.
    // The LLM will use this data to generate a natural language response.
    const result = {
      success: true,
      analysis: {
        isCorrect: recognizedCharacter === args.target,
        recognizedLetter: recognizedCharacter,
        targetLetter: args.target,
        confidence: parseFloat(confidence.toFixed(2)),
        strokeCount: strokeCount,
        pointCount: pointCount,
      },
      message: "Analysis complete.", // A message for debugging/logging, not for the user.
    };

    console.log("[ANALYZE_DRAWING] Analysis complete:", result);
    return result;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.error(
      "[ANALYZE_DRAWING] Critical error during analysis:",
      errorMessage
    );

    // Return a structured error for unexpected failures.
    return {
      success: false,
      error: "ANALYSIS_FAILED",
      message: `An internal error occurred: ${errorMessage}. Please ask the user to try again.`,
    };
  }
}

// ENHANCEMENT: New function executor for returning to the lesson view.
export async function executeCloseCanvasAndReturnToLesson(): Promise<any> {
  console.log("[UI_ACTION] Executing close_canvas_and_return_to_lesson.");
  // This function doesn't need to DO anything here. Its purpose is to be
  // a signal that the UstadTutor component will listen for and act upon.
  // We return a simple success object.
  return {
    success: true,
    message: "Okay, returning to the lesson cards.",
  };
}

export function updateChapterContext(
  squares: EnhancedSquare[],
  chapterId: number,
  chapterOrder: number,
  restrictNavigation: boolean
): void {
  currentChapterContext = {
    squares,
    chapterId,
    chapterOrder,
    restrictNavigation,
  };
  const arabicLetterCount = squares.filter((sq) =>
    containsArabicText(sq.content)
  ).length;
  console.log("[CONTEXT_UPDATE] Updated chapter context:", {
    chapterId,
    chapterOrder,
    squareCount: squares.length,
    arabicLetterCount,
    restrictNavigation,
    metadataCount: squares.filter((sq) => sq.metadata).length,
    firstFewSquares: squares.slice(0, 3).map((sq) => ({
      id: sq.id,
      content: sq.content,
      hasMetadata: !!sq.metadata,
      isArabic: containsArabicText(sq.content),
    })),
  });
}
export function getChapterAnalytics(): any {
  if (!currentChapterContext) return null;
  const { squares } = currentChapterContext;
  const squaresWithMetadata = squares.filter((sq) => sq.metadata);
  const arabicLetters = squares.filter((sq) => containsArabicText(sq.content));
  return {
    totalLetters: squares.length,
    arabicLetters: arabicLetters.length,
    arabicPercentage: Math.round((arabicLetters.length / squares.length) * 100),
    complexityDistribution: {
      basic: squaresWithMetadata.filter(
        (sq) => sq.metadata.complexity === "basic"
      ).length,
      intermediate: squaresWithMetadata.filter(
        (sq) => sq.metadata.complexity === "intermediate"
      ).length,
      advanced: squaresWithMetadata.filter(
        (sq) => sq.metadata.complexity === "advanced"
      ).length,
    },
    availableLetters: squares.map((sq) => ({
      id: sq.id,
      content: sq.content,
      complexity: sq.metadata?.complexity || "unknown",
      position: sq.metadata?.position?.index
        ? sq.metadata.position.index + 1
        : 0,
      isArabic: containsArabicText(sq.content),
    })),
    phoneticGroups: [
      ...new Set(
        squaresWithMetadata
          .map((sq) => sq.metadata.phonetic?.group)
          .filter(Boolean)
      ),
    ],
    shapeFamily: [
      ...new Set(
        squaresWithMetadata
          .map((sq) => sq.metadata.visual?.shapeFamily)
          .filter(Boolean)
      ),
    ],
    searchableTerms: squaresWithMetadata.reduce(
      (total, sq) => total + (sq.metadata.search?.keywords?.length || 0),
      0
    ),
    metadataAvailable: squaresWithMetadata.length,
    metadataPercentage: Math.round(
      (squaresWithMetadata.length / squares.length) * 100
    ),
    arabicSearchEnabled: arabicLetters.length > 0,
  };
}
export function askConfidenceExecutor(args: {
  chapter: number;
  subsection: string;
  lessonType: string;
}): any {
  const { chapter, subsection, lessonType } = args;
  console.log(
    `[ASK_CONFIDENCE_EXECUTOR] Processing confidence request for ${lessonType} ${chapter}-${subsection}`
  );
  const successMessage = generateConfidenceSuccessMessage();
  return {
    success: true,
    asked: true,
    chapter,
    subsection,
    lessonType,
    message: successMessage,
  };
}

// ================================================================== //
// ENHANCED REGISTRY, METRICS, AND ERROR HANDLING
// ================================================================== //

export function registerFunctions(funcs: Record<string, Executor>) {
  // Step 6: Register the analyze_drawing executor.
  // This ensures the function is available to be called by the LLM.
  // It's registered first so a caller could theoretically override it.
  functionRegistry["analyze_drawing"] = executeAnalyzeDrawing;

  // Register any other functions provided by the caller.
  Object.assign(functionRegistry, funcs);

  console.log(
    "[REGISTRY] Successfully registered functions:",
    Object.keys(functionRegistry) // Log all functions now in the registry
  );
}

function recordMetric(
  name: string,
  callId: string,
  executionTimeMs: number,
  status: "success" | "failure"
) {
  const metric: FunctionMetric = {
    name,
    callId,
    executionTimeMs,
    status,
    timestamp: Date.now(),
  };
  functionMetrics.push(metric);
  if (functionMetrics.length > MAX_METRICS) {
    functionMetrics.shift();
  }
}

export function getFunctionMetrics() {
  return functionMetrics;
}

function handleFunctionError(
  item: FunctionCallItem,
  error: string,
  suggestions: string[] = []
): any {
  const contextInfo = currentChapterContext
    ? {
        chapterId: currentChapterContext.chapterId,
        chapterOrder: currentChapterContext.chapterOrder,
        availableLetters: currentChapterContext.squares.length,
        arabicLetters: currentChapterContext.squares.filter((sq) =>
          containsArabicText(sq.content)
        ).length,
        navigationRestricted: currentChapterContext.restrictNavigation,
      }
    : null;
  const dynamicError = `${generateErrorMessage()} ${error}`;
  return {
    success: false,
    error: dynamicError,
    function: item.name,
    callId: item.call_id,
    suggestions,
    timestamp: new Date().toISOString(),
    context: contextInfo,
    arabicSearchAvailable: contextInfo ? contextInfo.arabicLetters > 0 : false,
  };
}

// Step 8: Ensure Correct Response Wiring
// This function handles the invocation of all registered executors, including analyze_drawing.
// Its generic design ensures that the `call_id` is preserved and the output is correctly
// formatted and sent back to the LLM, preventing conversation flow breakage.
export async function handleFunctionCall(
  item: FunctionCallItem,
  socket: Socket,
  send: (type: string, payload?: any) => void,
  retryAttempt: number = 0
) {
  if (retryAttempt === 0) {
    console.log("[FUNCTION_HANDLER] Processing function call:", {
      name: item.name,
      callId: item.call_id,
    });
    socket.emit("debug-log", {
      component: "FunctionHandler",
      timestamp: new Date().toISOString(),
      message: `Processing function call: ${item.name} (${item.call_id})`,
    });
  } else {
    console.log(
      `[FUNCTION_HANDLER] Retrying function call (Attempt ${retryAttempt}):`,
      { name: item.name, callId: item.call_id }
    );
  }

  const executor = functionRegistry[item.name];
  if (!executor) {
    console.error(
      `[FUNCTION_HANDLER] No executor registered for function: ${item.name}`
    );
    const errorResult = handleFunctionError(
      item,
      `Unknown function: ${item.name}`,
      ["Available functions: " + Object.keys(functionRegistry).join(", ")]
    );
    recordMetric(item.name, item.call_id, 0, "failure");
    send("conversation.item.create", {
      item: {
        type: "function_call_output",
        call_id: item.call_id,
        output: JSON.stringify(errorResult),
      },
    });
    send("response.create");
    return;
  }

  let args: any;
  try {
    // MODIFICATION: Check for empty arguments string, which is valid for functions with no params.
    if (!item.arguments || item.arguments.trim() === "{}") {
      args = {};
    } else {
      args = JSON.parse(item.arguments);
    }

    if (retryAttempt === 0)
      console.log("[FUNCTION_HANDLER] Parsed arguments:", args);
    if (args.content && containsArabicText(args.content)) {
      console.log("[FUNCTION_HANDLER] Arabic content detected in arguments");
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : "Malformed JSON";
    console.error(
      `[FUNCTION_HANDLER] Failed to parse arguments for call ${item.call_id}:`,
      errorMessage,
      "Received:",
      item.arguments
    );
    const errorResult = handleFunctionError(
      item,
      `Invalid function arguments - ${errorMessage}`,
      ["Ensure arguments are a valid JSON string."]
    );
    recordMetric(item.name, item.call_id, 0, "failure");
    send("conversation.item.create", {
      item: {
        type: "function_call_output",
        call_id: item.call_id,
        output: JSON.stringify(errorResult),
      },
    });
    send("response.create");
    return;
  }

  const startTime = Date.now();
  try {
    console.log("[FUNCTION_HANDLER] Executing function:", item.name);
    const result = await executor(args);
    const executionTime = Date.now() - startTime;
    recordMetric(item.name, item.call_id, executionTime, "success");

    console.log("[FUNCTION_HANDLER] Function executed successfully:", {
      name: item.name,
      executionTime: `${executionTime}ms`,
      success: result.success,
    });

    if (item.name === "student_selected_card" && result.success) {
      console.log("[FUNCTION_HANDLER] Handling student card selection result.");
      const enhancedResult = {
        ...result,
        execution: {
          /* metadata */
        },
      };
      send("conversation.item.create", {
        item: {
          type: "function_call_output",
          call_id: item.call_id,
          output: JSON.stringify(enhancedResult),
        },
      });
      send("response.create");
      return;
    }

    const enhancedResult = {
      ...result,
      execution: {
        functionName: item.name,
        callId: item.call_id,
        executionTime,
        timestamp: new Date().toISOString(),
      },
    };
    send("conversation.item.create", {
      item: {
        type: "function_call_output",
        call_id: item.call_id,
        output: JSON.stringify(enhancedResult),
      },
    });
    send("response.create");
  } catch (execErr: unknown) {
    const executionTime = Date.now() - startTime;
    recordMetric(item.name, item.call_id, executionTime, "failure");
    const errorMessage =
      execErr instanceof Error ? execErr.message : String(execErr);
    console.error(
      `[FUNCTION_HANDLER] Error executing function ${item.name}:`,
      errorMessage
    );

    if (
      RETRY_CONFIG.safeToRetry.includes(item.name) &&
      retryAttempt < RETRY_CONFIG.maxAttempts - 1
    ) {
      console.log(
        `[FUNCTION_HANDLER] Function '${item.name}' failed. Retrying in ${RETRY_CONFIG.delayMs}ms...`
      );
      setTimeout(() => {
        handleFunctionCall(item, socket, send, retryAttempt + 1);
      }, RETRY_CONFIG.delayMs);
      return;
    }

    console.error(
      `[FUNCTION_HANDLER] Function '${item.name}' failed permanently after ${
        retryAttempt + 1
      } attempts.`
    );
    const errorResult = handleFunctionError(item, errorMessage);
    send("conversation.item.create", {
      item: {
        type: "function_call_output",
        call_id: item.call_id,
        output: JSON.stringify(errorResult),
      },
    });
    send("response.create");
  }
}
