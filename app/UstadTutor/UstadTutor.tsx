"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { getSocket } from "@/lib/socketClient";
import type { Socket } from "socket.io-client";
import ChapterPage from "../virtualClassroom/components/ChapterPage";
import { AlertCircle } from "lucide-react";

import {
  layoutContainer,
  mainContentWrapper,
  panelClass,
} from "@/lib/responsiveUtilities";

import Draw, { DrawRefHandles } from "../virtualClassroom/components/draw";
import StartConversation from "./startConversation";
import AssistantSection from "../memorization/AssistantSection";
import { useVoiceStore } from "@/store/useVoiceStore";

// NEW: Import enhanced function handler utilities
import {
  registerFunctions,
  handleFunctionCall,
  findAndSelectLetter,
  updateChapterContext,
  getChapterAnalytics,
  executeStudentSelectedCard,
  executeGoToChapter as executeGoToChapterHandler,
} from "./functionHandler";

// ================================================================== //
// TYPE DEFINITIONS & ENHANCED CONFIGURATION
// ================================================================== //

// --- Step 9: Configuration for Visual Feedback Timing ---
const AI_SELECTION_HIGHLIGHT_DURATION_MS = 5000; // Visual feedback stays for 5 seconds

// --- Step 7: Configuration for Retry Logic ---
const CARD_SELECTION_MAX_RETRIES = 2; // Will try the initial call + 2 retries

// NEW: Stroke object interface for drawing analysis
interface StrokeObject {
  userId: string;
  tool: "pen" | "eraser";
  color: string;
  lineWidth: number;
  opacity: number;
  points: { x: number; y: number }[];
}

interface Square {
  id: number;
  squareNumber: number;
  content: string;
  audioUrl?: string;
  group: "basic" | "intermediate" | "advanced";
  overlayContent?: string;
}

interface ChapterData {
  id: number;
  title: string;
  squares: Square[];
  order?: number;
  // NEW: Enhanced fields from API
  analytics?: {
    totalLetters: number;
    complexityDistribution: {
      basic: number;
      intermediate: number;
      advanced: number;
    };
    phoneticGroups: string[];
    averageComplexityScore: number;
    searchableTerms: number;
  };
  metadata?: {
    enhancedAt: string;
    version: string;
    features: string[];
  };
}

// NEW: Lesson context interface for confidence rating
interface LessonContext {
  chapter: number;
  subsection: string;
  lessonType: string;
  // NEW: Added optional 'expected' property for drawing analysis target
  expected?: string;
}

// NEW: Selection source tracking interface
interface SelectionSourceContext {
  source:
    | "user_click"
    | "ai_selection"
    | "voice_command"
    | "navigation"
    | "system";
  timestamp: number;
  cardId?: number;
  method?: "click" | "touch" | "voice_navigation";
}

interface UstadTutorProps {
  chapterData: ChapterData | null;
  userId?: string;
  // MODIFIED: Retained optional prop to control header visibility
  showHeader?: boolean;
  // NEW: Prop to restrict navigation when embedded in CoursesPlayer
  restrictNavigation?: boolean;
  // NEW: Lesson context for confidence rating
  lessonContext?: LessonContext;
}

// ================================================================== //
// DYNAMIC RESPONSE UTILITIES
// ================================================================== //

const ENCOURAGING_ERROR_MESSAGES = [
  "Oops! Let's try that again.",
  "No worries, let's give it another shot.",
  "That's okay! Want to try once more?",
  "All good! Let's keep going.",
  "Don't worry about it! Try again.",
  "That happens! Let's continue.",
  "No problem! Ready to try again?",
  "It's fine! Let's move forward.",
];

const CONNECTION_ERROR_MESSAGES = [
  "Oops, need mic access to chat!",
  "Let's get your microphone working!",
  "Microphone permission needed to continue!",
  "Need to connect your mic for voice chat!",
  "Let's enable your microphone together!",
  "Mic access required for our conversation!",
  "Please allow microphone to start chatting!",
  "We need mic permission to talk!",
];

const GENERAL_ERROR_MESSAGES = [
  "Something went wrong, but we'll figure it out!",
  "Oops! Let's try that again.",
  "Don't worry, these things happen!",
  "No problem! Let's keep learning.",
  "That's okay! Ready to continue?",
  "All good! Let's move forward.",
  "It happens! Want to try again?",
  "No worries! Let's keep going.",
];

/**
 * Get a random encouraging message from an array
 */
function getRandomEncouragingMessage(messages: string[]): string {
  return messages[Math.floor(Math.random() * messages.length)];
}

// ================================================================== //
// USTAD TUTOR COMPONENT
// ================================================================== //

export default function UstadTutor({
  chapterData,
  userId = "tutor_user",
  showHeader = true,
  restrictNavigation = false, // Default to unrestricted navigation
  lessonContext, // NEW: Lesson context prop
}: UstadTutorProps) {
  /*
    LOGGING CONTROL: To enable verbose debug logs for this component,
    open the browser console and type:
    window.DEBUG_USTAD_TUTOR = true
  */
  const DEBUG_MODE =
    process.env.NODE_ENV === "development" &&
    typeof window !== "undefined" &&
    (window as any).DEBUG_USTAD_TUTOR === true;

  const router = useRouter();

  // Voice store actions and state
  const {
    conversationActive,
    connectionStatus,
    error,
    userAmplitude,
    startConversation,
    stopConversation,
    setConnectionStatus,
    setUserAmplitude,
    handleSpeechStarted,
    handleSpeechStopped,
    handleTranscriptionCompleted,
    handleResponseTextDelta,
    handleResponseAudioDelta,
    handleResponseAudioDone,
    handleError,
  } = useVoiceStore();

  // --- Component State (Original - Keep lesson/drawing state) ---
  const [currentChapterData, setCurrentChapterData] = useState(chapterData);
  const [isLoadingChapter, setIsLoadingChapter] = useState(false);
  const [mode, setMode] = useState<"lesson" | "draw">("lesson");
  const [highlightedSquareId, setHighlightedSquareId] = useState<number | null>(
    null
  );
  const [hoveredSquareId, setHoveredSquareId] = useState<number | null>(null);
  const [selectedSquareId, setSelectedSquareId] = useState<number | null>(null);

  // *** NEW: State to manage the selected tab in the right-hand panel ***
  const [selectedOption, setSelectedOption] = useState("Assistant");

  // NEW: Selection source tracking and timing state
  const [selectionSourceContext, setSelectionSourceContext] =
    useState<SelectionSourceContext | null>(null);
  const [lastUserSelectionTime, setLastUserSelectionTime] = useState<number>(0);
  const [lastAISelectionTime, setLastAISelectionTime] = useState<number>(0);

  // --- Step 6 & 7: State for Adaptive Timeout and Retry Logic ---
  const [pendingCardSelectionCall, setPendingCardSelectionCall] =
    useState<boolean>(false);
  const responseTimesRef = useRef<number[]>([]);
  const pendingCallStartTimeRef = useRef<number>(0);
  const cardSelectionRetryRef = useRef<{
    attempts: number;
    timerId: NodeJS.Timeout | null;
  }>({ attempts: 0, timerId: null });

  // --- UI/Drawing State (Original - Keep drawing tools) ---
  const containerRef = useRef<HTMLDivElement>(null);
  const drawRef = useRef<DrawRefHandles>(null); // MODIFIED: Use specific ref type
  const [tool, setTool] = useState<"pen" | "eraser">("pen");
  const [color, setColor] = useState("#000000");
  const [lineWidth, setLineWidth] = useState(3);
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  // --- NEW: State for Drawing Analysis ---
  const allStrokesRef = useRef<StrokeObject[]>([]);
  const [isAnalyzingDrawing, setIsAnalyzingDrawing] = useState(false);
  const [analysisFeedback, setAnalysisFeedback] = useState<string | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  // ENHANCEMENT: Create a ref to mirror the isAnalyzingDrawing state to stabilize useEffect dependencies.
  const isAnalyzingDrawingRef = useRef(isAnalyzingDrawing);

  // --- Refs for Socket.IO, audio, and media resources (Keep for audio management) ---
  const socketRef = useRef<Socket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const nextStartTimeRef = useRef<number>(0);

  // --- Amplitude throttling for performance ---
  const lastAmplitudeUpdateRef = useRef<number>(0);
  const amplitudeThrottleMs = 50; // Update amplitude max every 50ms

  // ENHANCEMENT: This effect keeps the ref synchronized with the state.
  useEffect(() => {
    isAnalyzingDrawingRef.current = isAnalyzingDrawing;
  }, [isAnalyzingDrawing]);

  // NEW: Helper function to reset all drawing-related state
  const clearDrawingState = useCallback(() => {
    allStrokesRef.current = [];
    setIsAnalyzingDrawing(false);
    setAnalysisFeedback(null);
    setAnalysisError(null);
    if (DEBUG_MODE)
      console.log("[DRAWING] All drawing analysis state cleared.");
  }, [DEBUG_MODE]);

  const clearCanvas = useCallback(() => {
    drawRef.current?.clearCanvas(); // Visually clears the child canvas
    clearDrawingState(); // Clears this component's logical state
  }, [clearDrawingState]);

  // NEW: Enhanced mode switcher to reset drawing state when leaving draw mode
  const handleModeChange = useCallback(
    (newMode: "lesson" | "draw") => {
      if (mode === "draw" && newMode === "lesson") {
        clearDrawingState();
        setSelectedSquareId(null);
      }
      setMode(newMode);
    },
    [mode, clearDrawingState]
  );

  // --- Chapter Data Fetching (Enhanced with Arabic letter context) ---
  useEffect(() => {
    setCurrentChapterData(chapterData);
    // NEW: Update function handler context when chapter data changes
    if (chapterData?.squares) {
      // Step 4: Pass restrictNavigation prop to function handlers via context
      updateChapterContext(
        chapterData.squares as any,
        chapterData.id,
        chapterData.order || 1,
        restrictNavigation
      );
      if (DEBUG_MODE) {
        console.log(
          "[USTAD_TUTOR] Updated function handler context for chapter:",
          chapterData.order,
          "| Restriction:",
          restrictNavigation
        );
        console.log(
          "[USTAD_TUTOR] Available Arabic letters in this chapter:",
          chapterData.squares
            .map((sq: any) => sq.content)
            .slice(0, 10)
            .join(", ") + "..."
        );
      }
    }
  }, [chapterData, DEBUG_MODE, restrictNavigation]); // Step 5: Update on prop change

  const fetchChapter = useCallback(async (order: number) => {
    setIsLoadingChapter(true);
    try {
      const res = await fetch(`/api/chapters/${order}?t=${Date.now()}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
      if (!res.ok) throw new Error(`Error fetching chapter ${order}`);
      const data = await res.json();
      return data;
    } catch (err) {
      console.error("[USTAD-FETCH] fetchChapter error:", err);
      return null;
    } finally {
      setIsLoadingChapter(false);
    }
  }, []);

  // --- Voice Agent Logic (Enhanced with Store) ---

  // --- Step 10: Enhanced sendToServer function for queue awareness ---
  const sendToServer = useCallback(
    (type: string, payload?: object) => {
      if (socketRef.current?.connected) {
        socketRef.current.emit("openai-client-message", { type, ...payload });
      } else {
        // The server-side queuing logic is automatic when the connection to OpenAI is down.
        // We warn the user here because the client is aware of the disconnect.
        console.warn(
          `[Socket] Attempted to send message of type '${type}' while disconnected. Server will queue if possible.`
        );
        handleError("Connection is unstable. Trying to send message...");
      }
    },
    [handleError]
  );

  // (MODIFIED) Enhanced dynamic instructions with drawing feedback logic
  const generateDynamicInstructions = useCallback(() => {
    const analytics = getChapterAnalytics();

    let baseInstructions = `You are Ustad, an expert AI tutor for learning the Quranic alphabet.

CORE INSTRUCTIONS:
1. RESPONSE STYLE: Be extremely concise. Your replies must be 30 words or less. Do not use filler phrases. Be direct and to the point.
2. PERSONALITY: Be helpful, encouraging, and patient, but always brief. VARY your responses - never use the same confirmation twice in a row.
3. TOOL CONFIRMATION: When you use a tool, confirm your action briefly with VARIETY. Examples: "Opening chapter 2!" or "Found ب!" or "Got it - ت!" or "Perfect - خ!" Mix up your confirmations.
4. DYNAMIC RESPONSES: Always vary your language. Instead of repeating "Good job" use: "Awesome!", "Great work!", "Perfect!", "Nice!", "Excellent!", "Well done!", "Fantastic!", "Amazing!", etc.
5. ERROR HANDLING: When something goes wrong, be encouraging: "No worries, let's try again!", "That's okay!", "All good!", etc.
6. CONFIDENCE RATING: When asked about confidence, acknowledge the rating and provide appropriate encouragement or support based on their level.
7. CARD SELECTION RESPONSES: When a student selects a card, you'll be notified via a system message. Respond naturally and briefly to acknowledge their selection.

DRAWING INTERACTIONS:
- To ask a student to draw, you MUST use the 'open_canvas' tool.
- When you receive a system message that says "The user has completed their drawing of the letter 'X'.", that message will also contain a JSON array of stroke data. You MUST immediately call the 'analyze_drawing' tool.
  - For the 'target' parameter, use the letter mentioned in the system message (e.g., 'X').
  - For the 'strokes' parameter, use the entire JSON stroke data array provided in the system message.
  - Do not respond with any text until after the tool has been called and has returned its result.
- After 'analyze_drawing' successfully runs, you MUST give the student natural language feedback based on the analysis. For example: "Great job, that's a perfect 'ب'!" or "Almost! Try making the dot a little lower."

ARABIC ALPHABET KNOWLEDGE - You have complete knowledge of the Arabic alphabet and can map English pronunciations to Arabic letters:

COMPLETE ARABIC LETTER MAPPINGS:
- "alif" or "alef" → ا (first letter)
- "ba" or "baa" → ب 
- "ta" or "taa" → ت
- "tha" or "thaa" → ث
- "jim" or "jeem" → ج
- "haaletter" (the throat letter, heavy ha) → ح
- "kha" or "khaa" or "cha" → خ
- "dal" or "daal" → د
- "dhal" or "dhaal" or "zal" → ذ
- "ra" or "raa" → ر
- "za" or "zaa" or "zay" → ز
- "sin" or "seen" → س
- "shin" or "sheen" → ش
- "sad" or "saad" → ص
- "dad" or "daad" → ض
- "taheavy" or "taw" → ط
- "zaheavy" or "zaw" → ظ
- "ain" or "ayn" → ع
- "ghain" or "ghayn" or "ghee" → غ
- "fa" or "faa" → ف
- "qaf" or "qaaf" or "qee" → ق
- "kaf" or "kaaf" or "kee" → ك
- "lam" or "laam" or "lee" → ل
- "mim" or "meem" or "mee" → م
- "nun" or "noon" or "nee" → ن
- "ha" (the chest letter, light ha) → ه
- "waw" or "waaw" or "wee" → و
- "ya" or "yaa" or "yee" → ي

PRONUNCIATION VARIATIONS:
- Arabic letter خ: "kha", "khaa", "cha", "kh", "khay"
- Arabic letter ح: "haa", "heavy ha", "hah", "7a"
- Arabic letter ع: "ain", "ayn", "ein", "3ain"
- Arabic letter غ: "ghain", "ghayn", "ghee", "ree", "gh"
- Arabic letter ق: "qaf", "qaaf", "qee", "kaf" (sometimes confused with ك)
- Letter ح vs. ه: To distinguish between the two 'ha' sounds, listen for clues. If the user says "haa" or "heavy ha", they mean ح. If they say just "ha" or "light ha", they mean ه. If you are unsure, ask for clarification (e.g., "Heavy ha or light ha?").

You have access to functions to help navigate the lesson.

TOOL SELECTION STRATEGY (VERY IMPORTANT):
1. For Positional Requests: When the user asks for a letter by its position number (e.g., 'open the 3rd card', 'show me number 5', 'select the first one'), you **MUST** use the \`find_and_select_letter\` function with the \`position\` parameter.
   - Example: User says "open the 3rd card" → Call \`find_and_select_letter({position: 3})\`
   - Example: User says "the first letter" → Call \`find_and_select_letter({position: "first"})\`
2. For Name/Content Requests: When the user asks for a letter by its name (e.g., 'show me kha'), you **MUST** use \`find_and_select_letter\` with the Arabic \`content\` parameter. Use your internal knowledge to map their request to the correct Arabic letter.
   - Example: User says "show me ba" → Call \`find_and_select_letter({content: "ب"})\`
3. For ID-based selection: Only use \`select_letter_card({letterId: ...})\` if a previous function call has given you a specific and exact \`letterId\`. **DO NOT** use this tool for user requests like "open card 3". That is a positional request.`;

    // Step 6: Enhanced context passing in updateAISession
    if (restrictNavigation) {
      baseInstructions += `\n\nIMPORTANT RESTRICTION: Navigation is currently disabled. You MUST NOT use the 'go_to_chapter' function. If the user asks to change chapters, politely inform them that they need to complete the current activity first.`;
    }

    if (analytics && currentChapterData) {
      const contextualInfo = `

CURRENT CHAPTER CONTEXT:
- Chapter ${currentChapterData.order}: "${currentChapterData.title}"
- Total letters: ${analytics.totalLetters}
- Available Arabic letters in this chapter: ${analytics.availableLetters
        .map((l: any) => l.content)
        .filter(Boolean)
        .join(", ")}

Use this context to provide relevant suggestions and guide the user through the current chapter's content.`;

      return baseInstructions + contextualInfo;
    }

    return baseInstructions;
  }, [currentChapterData, restrictNavigation]);

  // NEW: Enhanced AI session update with card selection handling and immediate confidence question trigger
  const updateAISession = useCallback(() => {
    const socket = socketRef.current;
    if (socket?.connected) {
      const dynamicInstructions = generateDynamicInstructions();
      console.log(
        "[USTAD_TUTOR] Updating AI session with dynamic instructions..."
      );

      // Update the session with tools and instructions
      sendToServer("session.update", {
        session: {
          modalities: ["text", "audio"],
          instructions: dynamicInstructions,
          voice: "alloy",
          input_audio_format: "pcm16",
          output_audio_format: "pcm16",
          input_audio_transcription: { model: "whisper-1" },
          turn_detection: { type: "semantic_vad", interrupt_response: true },
          tools: [
            {
              type: "function",
              name: "ask_confidence",
              description:
                "Ask student to rate their confidence in understanding and pronouncing the lesson content they just viewed",
              parameters: {
                type: "object",
                properties: {
                  chapter: {
                    type: "number",
                    description: "The chapter number of the lesson",
                  },
                  subsection: {
                    type: "string",
                    description: "The subsection ID of the lesson",
                  },
                  lessonType: {
                    type: "string",
                    description: "The type of lesson (video, lesson, quiz)",
                  },
                },
                required: ["chapter", "subsection", "lessonType"],
              },
            },
            {
              type: "function",
              name: "go_to_chapter",
              description:
                "Navigate to a specific chapter in the Quranic alphabet lesson",
              parameters: {
                type: "object",
                properties: {
                  chapterNumber: {
                    type: "number",
                    description: "The chapter number to navigate to (1-21)",
                    minimum: 1,
                    maximum: 21,
                  },
                },
                required: ["chapterNumber"],
              },
            },
            {
              type: "function",
              name: "select_letter_card",
              description:
                "Open and display a specific letter card in the UI by its ID. ONLY use this if you know the exact 'letterId' from a previous function call.",
              parameters: {
                type: "object",
                properties: {
                  letterId: {
                    type: "number",
                    description:
                      "The unique ID of the letter card to open and display",
                  },
                },
                required: ["letterId"],
              },
            },
            {
              type: "function",
              name: "find_and_select_letter",
              description:
                "Intelligently find and select Arabic letters. Use this for ALL user-facing search requests, including by name, description, or position.",
              parameters: {
                type: "object",
                properties: {
                  content: {
                    type: "string",
                    description:
                      "The actual Arabic letter character (e.g., 'ب', 'ت', 'خ').",
                  },
                  letterId: {
                    type: "number",
                    description: "Direct letter ID if known",
                  },
                  description: {
                    type: "string",
                    description:
                      "Descriptive search terms (e.g., 'simple letter', 'first letter')",
                  },
                  position: {
                    type: ["string", "number"],
                    description:
                      "Positional request (e.g., number for '3rd card' or string 'first')",
                  },
                  complexity: {
                    type: "string",
                    enum: ["basic", "intermediate", "advanced"],
                    description: "Filter by letter complexity level",
                  },
                },
                additionalProperties: false,
              },
            },
            {
              type: "function",
              name: "open_canvas",
              description:
                "Switches the user interface to the drawing canvas, allowing the student to start drawing an Arabic letter. Call this when you want to prompt the student to draw.",
              parameters: {
                type: "object",
                properties: {},
              },
            },
            // NEW: Add the close_canvas tool definition
            {
              type: "function",
              name: "close_canvas_and_return_to_lesson",
              description:
                "Closes the drawing canvas and returns the user to the main lesson view with the letter cards. Use this when the user wants to stop drawing or go back to the lesson.",
              parameters: {
                type: "object",
                properties: {},
              },
            },
            // NEW: Add analyze_drawing tool definition
            {
              type: "function",
              name: "analyze_drawing",
              description:
                "Analyzes a student's drawing of an Arabic letter and provides feedback.",
              parameters: {
                type: "object",
                properties: {
                  strokes: {
                    type: "array",
                    description:
                      "An array of stroke objects representing the drawing.",
                    items: {
                      type: "object",
                      properties: {
                        userId: { type: "string" },
                        tool: { type: "string" },
                        color: { type: "string" },
                        lineWidth: { type: "number" },
                        opacity: { type: "number" },
                        points: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              x: { type: "number" },
                              y: { type: "number" },
                            },
                            required: ["x", "y"],
                          },
                        },
                      },
                      required: [
                        "userId",
                        "tool",
                        "color",
                        "lineWidth",
                        "opacity",
                        "points",
                      ],
                    },
                  },
                  target: {
                    type: "string",
                    description:
                      "The target Arabic letter the student was supposed to draw (e.g., 'ب').",
                  },
                },
                required: ["strokes", "target"],
              },
            },
          ],
        },
      });

      // Trigger confidence question if lessonContext exists
      if (lessonContext) {
        console.log("[USTAD_TUTOR] Triggering immediate confidence question");

        const confidenceQuestion = `Hey, how's it going? On a scale of 1 to 5, how confident are you with Lesson ${lessonContext.chapter}?`;

        setTimeout(() => {
          sendToServer("response.create", {
            response: {
              instructions: `Greet the student warmly and ask: "${confidenceQuestion}" Wait for their response and acknowledge it encouragingly.`,
              modalities: ["text", "audio"],
              max_output_tokens: 200,
            },
          });

          if (DEBUG_MODE) {
            console.log(
              "[USTAD_TUTOR] Sent immediate response.create for confidence question"
            );
          }
        }, 500); // Small delay to ensure session.update is processed first
      }
    }
  }, [sendToServer, generateDynamicInstructions, lessonContext, DEBUG_MODE]);

  // --- Step 6: Helper to calculate adaptive timeout ---
  const getAdaptiveTimeout = useCallback(() => {
    // Wait for at least 3 samples to get a baseline
    if (responseTimesRef.current.length < 3) {
      return 3500; // Default timeout if no history or not enough data
    }
    const avgResponseTime =
      responseTimesRef.current.reduce((a, b) => a + b, 0) /
      responseTimesRef.current.length;
    // Timeout is 1.5x the average + 500ms buffer, clamped between 2.5s and 8s
    const adaptiveTimeout = Math.max(
      2500,
      Math.min(avgResponseTime * 1.5 + 500, 8000)
    );
    if (DEBUG_MODE)
      console.log(
        `[ADAPTIVE_TIMEOUT] Calculated: ${adaptiveTimeout.toFixed(
          0
        )}ms based on average of ${avgResponseTime.toFixed(0)}ms`
      );
    return adaptiveTimeout;
  }, [DEBUG_MODE]);

  // NEW: Handler for the "Done" button to trigger analysis by prompting the AI
  const handleDoneDrawing = useCallback(
    (strokes: StrokeObject[]) => {
      if (DEBUG_MODE)
        console.log(
          "[DRAWING_DONE] 'Done' clicked. Preparing to send to AI for analysis."
        );

      if (isAnalyzingDrawing || strokes.length === 0) {
        if (strokes.length === 0) {
          setAnalysisError("Please draw something first before clicking Done.");
        }
        return;
      }

      setIsAnalyzingDrawing(true);
      setAnalysisFeedback(null);
      setAnalysisError(null);
      allStrokesRef.current = strokes; // Ensure the ref is up to date

      const targetLetter =
        lessonContext?.expected ||
        currentChapterData?.squares.find((s) => s.id === selectedSquareId)
          ?.content ||
        "unknown";

      if (targetLetter === "unknown") {
        setIsAnalyzingDrawing(false);
        setAnalysisError(
          "Could not determine which letter to analyze. Please select a letter first."
        );
        return;
      }

      const systemMessage = `The user has completed their drawing of the letter '${targetLetter}'. Please analyze the following stroke data by calling the 'analyze_drawing' tool with the provided strokes and target letter. Strokes: ${JSON.stringify(
        strokes
      )}`;

      if (DEBUG_MODE) {
        console.log(
          `[DRAWING_DONE] Sending system message to AI. Target: ${targetLetter}, Strokes: ${strokes.length}`
        );
      }

      // Step 1: Send the system message with drawing data
      sendToServer("conversation.item.create", {
        item: {
          type: "message",
          role: "system",
          content: [{ type: "input_text", text: systemMessage }],
        },
      });

      // Step 2: Immediately prompt the AI to process and respond
      sendToServer("response.create");
    },
    [
      isAnalyzingDrawing,
      lessonContext,
      currentChapterData,
      selectedSquareId,
      sendToServer,
      DEBUG_MODE,
    ]
  );

  // MODIFIED: Update stroke ref without triggering analysis
  const handleDrawingComplete = useCallback(
    (fullStrokeHistory: StrokeObject[]) => {
      allStrokesRef.current = fullStrokeHistory;
    },
    []
  );

  // --- Enhanced notifyAIOfCardSelection to use system messages ---
  const notifyAIOfCardSelection = useCallback(
    (
      cardId: number,
      selectedSquare: Square,
      selectionMethod: "click" | "touch" | "voice_navigation" = "click"
    ) => {
      if (!socketRef.current?.connected || !selectedSquare) {
        console.warn(
          "[CARD_SELECTION] Cannot notify AI - socket not connected or no square data"
        );
        return;
      }

      console.log(`[CARD_SELECTION] Notifying AI: ${selectedSquare.content}`);
      console.log(
        "[DEBUG] Using updated notifyAIOfCardSelection with array content"
      );

      const payload = {
        item: {
          type: "message",
          role: "system",
          content: [
            {
              type: "input_text",
              text: `Student selected card: ${selectedSquare.content} (ID: ${cardId})`,
            },
          ],
        },
      };
      console.log("[DEBUG] Payload sent to server:", JSON.stringify(payload));
      sendToServer("conversation.item.create", payload);

      sendToServer("response.create");

      socketRef.current.emit("debug-log", {
        component: "UstadTutor",
        timestamp: new Date().toISOString(),
        message: `student_selected_card(${selectedSquare.content})`,
      });
    },
    [sendToServer]
  );

  // ENHANCED: Function execution handlers with dynamic success messages
  const executeGoToChapter = useCallback(
    async (args: { chapterNumber: number }) => {
      const { chapterNumber } = args;
      if (DEBUG_MODE) {
        console.log(
          `[FUNCTION] Executing go_to_chapter with chapter: ${chapterNumber}`
        );
      }

      if (restrictNavigation) {
        console.warn(
          `[FUNCTION] Blocked go_to_chapter(${chapterNumber}) due to UI-level restriction.`
        );
        return {
          success: false,
          message:
            "I'm sorry, I can't change chapters right now. Please complete the current activity first.",
          reason: "RESTRICTED_NAVIGATION",
        };
      }

      const result = await executeGoToChapterHandler({
        chapter: chapterNumber,
      });
      if (!result.success) {
        return result;
      }

      if (chapterNumber < 1 || chapterNumber > 21) {
        return {
          success: false,
          error: `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} Invalid chapter number: ${chapterNumber}. Must be between 1 and 21.`,
        };
      }

      try {
        const fetchedChapter = await fetchChapter(chapterNumber);
        if (fetchedChapter) {
          setCurrentChapterData(fetchedChapter);

          if (fetchedChapter.squares) {
            updateChapterContext(
              fetchedChapter.squares,
              fetchedChapter.id,
              fetchedChapter.order || chapterNumber,
              restrictNavigation
            );
            console.log(
              "[FUNCTION] Updated context for new chapter:",
              chapterNumber
            );
          }

          setSelectedSquareId(null);
          setHighlightedSquareId(null);
          setSelectionSourceContext(null);

          setTimeout(() => {
            updateAISession();
            console.log(
              "[FUNCTION] Updated AI session with new chapter context"
            );
          }, 500);

          return {
            success: true,
            message: `Opening chapter ${chapterNumber}!`,
            chapterInfo: {
              id: fetchedChapter.id,
              title: fetchedChapter.title,
              order: fetchedChapter.order,
              totalLetters: fetchedChapter.squares?.length || 0,
              analytics: fetchedChapter.analytics,
            },
          };
        } else {
          return {
            success: false,
            error: `${getRandomEncouragingMessage(
              GENERAL_ERROR_MESSAGES
            )} Failed to load chapter ${chapterNumber}`,
          };
        }
      } catch (error) {
        console.error("[FUNCTION] Error in go_to_chapter:", error);
        return {
          success: false,
          error: `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} Error loading chapter ${chapterNumber}: ${error}`,
        };
      }
    },
    [fetchChapter, updateAISession, DEBUG_MODE, restrictNavigation]
  );

  const executeSelectLetterCard = useCallback(
    (args: { letterId: number }) => {
      const { letterId } = args;
      if (DEBUG_MODE) {
        console.log(
          `[FUNCTION] Executing select_letter_card with letterId: ${letterId}`
        );
      }

      if (!currentChapterData?.squares) {
        return {
          success: false,
          error: `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} No chapter data available to select from`,
        };
      }

      const targetSquare = currentChapterData.squares.find(
        (sq) => sq.id === letterId
      );
      if (!targetSquare) {
        return {
          success: false,
          error: `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} Letter card with ID ${letterId} not found in current chapter`,
        };
      }

      setSelectionSourceContext({
        source: "ai_selection",
        timestamp: Date.now(),
        cardId: letterId,
        method: "voice_navigation",
      });
      setLastAISelectionTime(Date.now());
      setSelectedSquareId(letterId);
      setHighlightedSquareId(letterId);

      return {
        success: true,
        message: `Found ${targetSquare.content}!`,
        letter: {
          id: targetSquare.id,
          content: targetSquare.content,
        },
      };
    },
    [currentChapterData, DEBUG_MODE]
  );

  const executeFindAndSelectLetter = useCallback(
    async (args: any) => {
      if (DEBUG_MODE) {
        console.log(
          "[FUNCTION] Executing find_and_select_letter with args:",
          args
        );
      }
      if (args.content) {
        console.log(
          `[FUNCTION] AI is using Arabic alphabet knowledge for: "${args.content}"`
        );
      }

      try {
        const searchResult = await findAndSelectLetter(args);

        if (searchResult.success && searchResult.selectedLetter) {
          const letterId = searchResult.selectedLetter.id;
          setSelectionSourceContext({
            source: "ai_selection",
            timestamp: Date.now(),
            cardId: letterId,
            method: "voice_navigation",
          });
          setLastAISelectionTime(Date.now());
          setSelectedSquareId(letterId);
          setHighlightedSquareId(letterId);

          console.log(
            "[FUNCTION] Smart letter selection successful:",
            searchResult.selectedLetter.content
          );

          return {
            ...searchResult,
            uiUpdate: {
              selectedSquareId: letterId,
              highlightedSquareId: letterId,
            },
          };
        }

        return searchResult;
      } catch (error) {
        console.error("[FUNCTION] Error in find_and_select_letter:", error);
        return {
          success: false,
          error: `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} Error during smart letter search: ${error}`,
        };
      }
    },
    [DEBUG_MODE]
  );

  const executeAskConfidence = useCallback(
    (args: { chapter: number; subsection: string; lessonType: string }) => {
      if (DEBUG_MODE) {
        console.log(
          `[FUNCTION] Executing ask_confidence with chapter: ${args.chapter}`
        );
      }

      return {
        success: true,
        asked: true,
        ...args,
        message: "Perfect! Let's check your confidence.",
      };
    },
    [DEBUG_MODE]
  );

  const executeOpenCanvas = useCallback(() => {
    if (DEBUG_MODE) {
      console.log(`[FUNCTION] Executing open_canvas`);
    }
    handleModeChange("draw");
    return {
      success: true,
      message: "Okay, opening the drawing canvas.",
    };
  }, [handleModeChange, DEBUG_MODE]);

  // NEW: Executor function for closing the canvas
  const executeCloseCanvas = useCallback(() => {
    if (DEBUG_MODE) {
      console.log(`[FUNCTION] Executing close_canvas_and_return_to_lesson`);
    }
    handleModeChange("lesson");
    return {
      success: true,
      message: "Returning to the lesson.",
    };
  }, [handleModeChange, DEBUG_MODE]);

  // NEW: Analyze drawing function executor
  const executeAnalyzeDrawing = useCallback(
    (args: { strokes: StrokeObject[]; target: string }) => {
      // This is a placeholder since the actual analysis happens server-side
      // The server will send back an "analysis_result" event
      if (DEBUG_MODE) {
        console.log(
          `[FUNCTION] Executing analyze_drawing with target: ${args.target}, strokes: ${args.strokes.length}`
        );
      }
      return {
        success: true,
        message: "Analyzing your drawing...",
      };
    },
    [DEBUG_MODE]
  );

  useEffect(() => {
    registerFunctions({
      go_to_chapter: executeGoToChapter,
      select_letter_card: executeSelectLetterCard,
      find_and_select_letter: executeFindAndSelectLetter,
      ask_confidence: executeAskConfidence,
      student_selected_card: executeStudentSelectedCard,
      open_canvas: executeOpenCanvas,
      close_canvas_and_return_to_lesson: executeCloseCanvas,
      analyze_drawing: executeAnalyzeDrawing,
    });
    console.log("[USTAD_TUTOR] Registered enhanced function set.");
  }, [
    executeGoToChapter,
    executeSelectLetterCard,
    executeFindAndSelectLetter,
    executeAskConfidence,
    executeOpenCanvas,
    executeCloseCanvas,
    executeAnalyzeDrawing,
  ]);

  const playAudioChunk = useCallback(async (base64Audio: string) => {
    try {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext ||
          (window as any).webkitAudioContext)();
        nextStartTimeRef.current = audioContextRef.current.currentTime;
      }
      const audioContext = audioContextRef.current;
      const binaryString = atob(base64Audio);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const pcm16Data = new Int16Array(bytes.buffer);
      const audioBuffer = audioContext.createBuffer(1, pcm16Data.length, 24000);
      const channelData = audioBuffer.getChannelData(0);
      for (let i = 0; i < pcm16Data.length; i++) {
        channelData[i] = pcm16Data[i] / 32768.0;
      }
      const source = audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContext.destination);
      const currentTime = audioContext.currentTime;
      if (nextStartTimeRef.current < currentTime) {
        nextStartTimeRef.current = currentTime;
      }
      source.start(nextStartTimeRef.current);
      nextStartTimeRef.current += audioBuffer.duration;
    } catch (error) {
      console.error("Error playing audio chunk:", error);
    }
  }, []);

  // --- Enhanced Realtime Event Handler with Analysis Results ---
  const handleRealtimeEvent = useCallback(
    async (event: any) => {
      if (DEBUG_MODE) {
        console.log("Received event from server:", event.type, event);
      }
      try {
        switch (event.type) {
          case "session.created":
          case "session.updated":
            break;
          case "input_audio_buffer.speech_started":
            handleSpeechStarted();
            break;
          case "input_audio_buffer.speech_stopped":
            handleSpeechStopped();
            break;
          case "conversation.item.input_audio_transcription.completed":
            if (event.transcript) {
              handleTranscriptionCompleted(event.transcript);
            }
            break;
          case "response.text.delta":
            if (event.delta) {
              // ENHANCEMENT: Use ref to check analysis state
              if (isAnalyzingDrawingRef.current) {
                setAnalysisFeedback((prev) => (prev || "") + event.delta);
              } else {
                handleResponseTextDelta(event.delta);
              }
            }
            break;
          case "response.audio.delta":
            handleResponseAudioDelta();
            if (event.delta) {
              playAudioChunk(event.delta);
            }
            break;
          case "response.audio.done":
            // ENHANCEMENT: Use ref to check analysis state
            if (isAnalyzingDrawingRef.current) {
              setIsAnalyzingDrawing(false);
            }
            handleResponseAudioDone();
            break;
          case "response.output_item.added":
            if (event.item?.type === "function_call") {
              console.log(`[FUNCTION] AI call started: ${event.item.name}`);
            }
            break;
          case "response.function_call_arguments.delta":
            break;
          case "response.function_call_arguments.done":
            break;
          case "response.output_item.done":
            const { item } = event;
            if (item?.type === "function_call") {
              if (DEBUG_MODE) {
                console.log(
                  `[FUNCTION] Delegating to centralized handler: ${item.name}`
                );
              }
              await handleFunctionCall(item, socketRef.current!, sendToServer);
            }
            break;
          // REMOVED: 'analysis_result' case is no longer needed
          case "error":
            console.error("Realtime API Error:", event.error);
            const errorMessage =
              event.error?.message ||
              "An unknown error occurred inside the API.";
            const isAuthError =
              errorMessage.toLowerCase().includes("authentication") ||
              errorMessage.toLowerCase().includes("permission");
            // ENHANCEMENT: Use ref to check analysis state
            if (isAnalyzingDrawingRef.current) {
              setIsAnalyzingDrawing(false);
              setAnalysisError("Analysis interrupted due to an error.");
            }
            if (isAuthError) {
              handleError(
                `A critical API error occurred: ${errorMessage}. Please restart the session.`
              );
              stopConversation();
            } else {
              handleError(
                `${getRandomEncouragingMessage(
                  GENERAL_ERROR_MESSAGES
                )} (Details: ${errorMessage})`
              );
            }
            break;
          default:
            console.warn(`[USTAD_TUTOR] Unhandled event type: ${event.type}`);
        }
      } catch (error) {
        console.error("Error handling realtime event:", error);
        handleError(
          `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} Error processing server response`
        );
        // ENHANCEMENT: Use ref to check analysis state
        if (isAnalyzingDrawingRef.current) {
          setIsAnalyzingDrawing(false);
          setAnalysisError("Error during analysis.");
        }
      }
    },
    [
      // ENHANCEMENT: isAnalyzingDrawing has been removed from this dependency array
      // This makes the handleRealtimeEvent function "stable" and prevents re-runs of the main useEffect.
      DEBUG_MODE,
      handleSpeechStarted,
      handleSpeechStopped,
      handleTranscriptionCompleted,
      handleResponseTextDelta,
      handleResponseAudioDelta,
      handleResponseAudioDone,
      handleError,
      sendToServer,
      stopConversation,
      playAudioChunk,
    ]
  );

  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      if (
        !audioContextRef.current ||
        audioContextRef.current.state === "closed"
      ) {
        audioContextRef.current = new (window.AudioContext ||
          (window as any).webkitAudioContext)({ sampleRate: 16000 });
      }
      const audioContext = audioContextRef.current;
      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      processorRef.current = processor;

      processor.onaudioprocess = (event) => {
        if (socketRef.current?.connected) {
          const inputBuffer = event.inputBuffer;
          const inputData = inputBuffer.getChannelData(0);
          let max = 0;
          for (let i = 0; i < inputData.length; i++) {
            max = Math.max(max, Math.abs(inputData[i]));
          }
          const now = Date.now();
          if (now - lastAmplitudeUpdateRef.current >= amplitudeThrottleMs) {
            setUserAmplitude(max);
            lastAmplitudeUpdateRef.current = now;
          }
          const pcm16Buffer = new Int16Array(inputData.length);
          for (let i = 0; i < inputData.length; i++) {
            const sample = Math.max(-1, Math.min(1, inputData[i]));
            pcm16Buffer[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
          }
          const base64Audio = btoa(
            String.fromCharCode.apply(
              null,
              Array.from(new Uint8Array(pcm16Buffer.buffer))
            )
          );
          sendToServer("input_audio_buffer.append", { audio: base64Audio });
        }
      };
      source.connect(processor);
      processor.connect(audioContext.destination);
    } catch (error) {
      console.error("Error starting recording:", error);
      handleError(getRandomEncouragingMessage(CONNECTION_ERROR_MESSAGES));
    }
  }, [sendToServer, setUserAmplitude, handleError]);

  const stopRecording = useCallback(() => {
    setUserAmplitude(0);
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }
    if (socketRef.current?.connected) {
      sendToServer("input_audio_buffer.commit");
    }
  }, [sendToServer, setUserAmplitude]);

  // --- Lifecycle Effect ---
  useEffect(() => {
    if (!conversationActive) {
      setConnectionStatus("disconnected");
      return;
    }

    const socket = getSocket();
    socketRef.current = socket;
    setConnectionStatus("connecting");

    const handleOpenAIStarted = () => {
      console.log("OpenAI conversation started via shared socket");
      setConnectionStatus("connected");
      // Only clear error if it was a connection-related error
      if (error && error.includes("unstable")) handleError("");
      updateAISession();
    };

    const handleOpenAIError = (data: {
      message: string;
      isPermanent?: boolean;
    }) => {
      console.error("OpenAI Socket Error:", data);
      if (data.isPermanent) {
        handleError(
          `A permanent error occurred: ${data.message}. Please restart the conversation.`
        );
        stopConversation();
      } else {
        handleError(
          `${getRandomEncouragingMessage(
            GENERAL_ERROR_MESSAGES
          )} Server Error: ${data.message || "Unknown error"}`
        );
      }
    };

    const handleReconnecting = ({ attempt }: { attempt: number }) => {
      console.log(
        `[Socket] Server is reconnecting to OpenAI... Attempt ${attempt}.`
      );
      setConnectionStatus("connecting");
      handleError(
        `Connection unstable. Messages are being queued. Retrying... (Attempt ${attempt})`
      );
    };

    const handleOpenAIClosed = ({ reason }: { reason: string }) => {
      console.log("OpenAI conversation closed by server:", reason);
      handleError(
        `${getRandomEncouragingMessage(
          GENERAL_ERROR_MESSAGES
        )} Connection closed: ${reason}`
      );
      setConnectionStatus("disconnected");
    };

    const handleSocketDisconnect = (reason: string) => {
      console.log("Shared socket disconnected:", reason);
      setConnectionStatus("disconnected");
    };

    const handleSocketConnectError = (err: Error) => {
      console.error("Shared socket connection error:", err);
      handleError(
        `${getRandomEncouragingMessage(
          CONNECTION_ERROR_MESSAGES
        )} Failed to connect to server`
      );
      setConnectionStatus("disconnected");
    };

    socket.on("openai-conversation-started", handleOpenAIStarted);
    socket.on("openai-server-message", handleRealtimeEvent);
    socket.on("openai-error", handleOpenAIError);
    socket.on("openai-reconnecting", handleReconnecting);
    socket.on("openai-conversation-closed", handleOpenAIClosed);
    socket.on("disconnect", handleSocketDisconnect);
    socket.on("connect_error", handleSocketConnectError);

    console.log("Emitting 'start-openai-conversation' on shared socket.");
    socket.emit("start-openai-conversation");

    return () => {
      socket.off("openai-conversation-started", handleOpenAIStarted);
      socket.off("openai-server-message", handleRealtimeEvent);
      socket.off("openai-error", handleOpenAIError);
      socket.off("openai-reconnecting", handleReconnecting);
      socket.off("openai-conversation-closed", handleOpenAIClosed);
      socket.off("disconnect", handleSocketDisconnect);
      socket.off("connect_error", handleSocketConnectError);

      if (!conversationActive) {
        console.log("Stopping OpenAI conversation due to user action.");
        socket.emit("stop-openai-conversation");
        setConnectionStatus("disconnected");
      }
    };
  }, [
    conversationActive,
    handleRealtimeEvent,
    setConnectionStatus,
    handleError,
    stopConversation,
    updateAISession,
    error,
  ]);

  const toggleConversation = () => {
    if (conversationActive) {
      stopConversation();
      stopRecording();
    } else {
      startConversation();
    }
  };

  useEffect(() => {
    if (connectionStatus === "connected" && conversationActive) {
      startRecording();
    }
  }, [connectionStatus, conversationActive, startRecording]);

  useEffect(() => {
    return () => {
      stopRecording();
      if (
        audioContextRef.current &&
        audioContextRef.current.state !== "closed"
      ) {
        audioContextRef.current.close();
      }
    };
  }, [stopRecording]);

  const onChapterChanged = useCallback(
    async (newChapter: number) => {
      setIsLoadingChapter(true);
      const fetchedChapter = await fetchChapter(newChapter);
      if (fetchedChapter) {
        setCurrentChapterData(fetchedChapter);
        if (fetchedChapter.squares) {
          updateChapterContext(
            fetchedChapter.squares,
            fetchedChapter.id,
            fetchedChapter.order || newChapter,
            restrictNavigation
          );
          console.log(
            `[CHAPTER_CHANGE] Updated context for chapter: ${newChapter}`
          );
          setTimeout(() => {
            updateAISession();
          }, 500);
        }
      }
      setIsLoadingChapter(false);
    },
    [fetchChapter, updateAISession, restrictNavigation]
  );

  const handleTooltipEnter = useCallback(
    (text: string) => setShowTooltip(text),
    []
  );
  const handleTooltipLeave = useCallback(() => setShowTooltip(null), []);
  const handleSquareHighlight = useCallback(
    (id: number) => setHighlightedSquareId(id),
    []
  );
  const onSquareHover = useCallback(
    (id: number | null) => setHoveredSquareId(id),
    []
  );

  const onSquareSelected = useCallback(
    (id: number | null) => {
      setSelectedSquareId(id);
      if (id !== null && currentChapterData?.squares) {
        const selectedSquare = currentChapterData.squares.find(
          (sq) => sq.id === id
        );
        if (selectedSquare) {
          const now = Date.now();
          const timeSinceLastAISelection = now - lastAISelectionTime;
          const isUserSelection = timeSinceLastAISelection > 1000;

          if (DEBUG_MODE) {
            console.log("[SQUARE_SELECTION] Selection analysis:", {
              isUserSelection,
              timeSinceLastAI: timeSinceLastAISelection,
            });
          }

          if (isUserSelection) {
            console.log(
              "[SQUARE_SELECTION] User-initiated selection detected - notifying AI immediately."
            );
            setSelectionSourceContext({
              source: "user_click",
              timestamp: now,
              cardId: id,
              method: "click",
            });
            setLastUserSelectionTime(now);
            notifyAIOfCardSelection(id, selectedSquare, "click");
          } else {
            if (DEBUG_MODE) {
              console.log(
                "[SQUARE_SELECTION] AI-initiated selection detected - not re-notifying AI."
              );
            }
          }
        } else {
          console.warn(
            "[SQUARE_SELECTION] Selected square not found in chapter data"
          );
        }
      }
    },
    [
      currentChapterData,
      lastAISelectionTime,
      notifyAIOfCardSelection,
      DEBUG_MODE,
    ]
  );

  // --- Step 9: Effect to clear AI-driven highlights after a configured duration ---
  useEffect(() => {
    if (
      selectionSourceContext?.source === "ai_selection" &&
      highlightedSquareId !== null
    ) {
      if (DEBUG_MODE)
        console.log(
          `[HIGHLIGHT] AI selection detected. Setting timeout to clear highlight in ${AI_SELECTION_HIGHLIGHT_DURATION_MS}ms.`
        );
      const timer = setTimeout(() => {
        if (DEBUG_MODE)
          console.log(
            `[HIGHLIGHT] Timeout fired. Clearing AI highlight for card ${highlightedSquareId}.`
          );
        setHighlightedSquareId(null);
      }, AI_SELECTION_HIGHLIGHT_DURATION_MS);
      return () => clearTimeout(timer);
    }
  }, [selectionSourceContext, highlightedSquareId, DEBUG_MODE]);

  // ================================================================== //
  // RENDER METHOD
  // ================================================================== //
  return (
    <div
      className={`${layoutContainer} h-full w-full flex flex-col text-sm`}
      style={{
        position: "relative",
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
      ref={containerRef}
    >
      {showHeader && (
        <header className="bg-white border-b border-gray-200 h-16 flex-shrink-0">
          <div className="h-full px-6 flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push("/dashboard")}
                className="flex items-center justify-center w-9 h-9 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300"
                title="Return to Dashboard"
              >
                <span className="text-gray-600 text-2xl font-medium">←</span>
              </button>
              <div className="h-8 w-px bg-gray-200"></div>
              <div className="flex items-center">
                <h1 className="text-lg font-semibold text-neutral-800 tracking-wide">
                  Ustad AI Tutor
                </h1>
              </div>
            </div>

            {mode === "draw" && (
              <div className="flex items-center space-x-6">
                {/* Drawing tools UI fully preserved */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setTool("pen")}
                    className={`p-2 rounded-md ${
                      tool === "pen"
                        ? "bg-neutral-900 text-white"
                        : "bg-neutral-100 text-neutral-600 hover:bg-neutral-200"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() => setTool("eraser")}
                    className={`p-2 rounded-md ${
                      tool === "eraser"
                        ? "bg-neutral-900 text-white"
                        : "bg-neutral-100 text-neutral-600 hover:bg-neutral-200"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 7h10m0 0l-3.5 3.5m3.5-3.5L3.5 20.5M7 7L3.5 10.5m14-3L14 11"
                      />
                    </svg>
                  </button>
                </div>
                <input
                  type="color"
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  className="w-10 h-10 rounded-md border-none cursor-pointer"
                />
                <input
                  type="range"
                  min="1"
                  max="20"
                  value={lineWidth}
                  onChange={(e) => setLineWidth(parseInt(e.target.value))}
                  className="w-24"
                />
              </div>
            )}

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3 p-2 bg-white rounded-xl shadow-sm border border-neutral-200">
                <div className="flex gap-1 bg-neutral-100 rounded-lg">
                  <button
                    onClick={() => handleModeChange("lesson")}
                    className={`px-3 py-1.5 rounded-md font-medium text-sm transition-all duration-200
                    ${
                      mode === "lesson"
                        ? "bg-neutral-900 text-white shadow-sm"
                        : "bg-transparent text-neutral-600 hover:bg-neutral-200"
                    }`}
                  >
                    Lesson
                  </button>
                  <button
                    onClick={() => handleModeChange("draw")}
                    className={`px-3 py-1.5 rounded-md font-medium text-sm transition-all duration-200
                    ${
                      mode === "draw"
                        ? "bg-neutral-900 text-white shadow-sm"
                        : "bg-transparent text-neutral-600 hover:bg-neutral-200"
                    }`}
                  >
                    Draw
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>
      )}

      <div
        className={`${mainContentWrapper} flex-1 p-4 gap-4 flex flex-col md:flex-row overflow-hidden`}
      >
        <div className="relative w-full md:w-2/3 bg-white rounded-2xl shadow-sm border p-4 flex flex-col min-h-0">
          {mode === "lesson" ? (
            isLoadingChapter ? (
              <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                <p className="text-sm text-center">Loading chapter...</p>
              </div>
            ) : currentChapterData ? (
              <ChapterPage
                key={`chapter-${currentChapterData.id}`}
                {...currentChapterData}
                onSquareHighlighted={handleSquareHighlight}
                highlightedSquareId={highlightedSquareId}
                isTeacher={true}
                hoveredSquareId={hoveredSquareId}
                selectedSquareId={selectedSquareId}
                onSquareHover={onSquareHover}
                onSquareSelected={onSquareSelected}
                onChapterChange={onChapterChanged}
                showNavigation={true}
                allowDrawing={true}
                restrictNavigation={restrictNavigation}
                requireIconClick={true} // 2.1: Forward the new flag into ChapterPage
              />
            ) : (
              <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
                <p className="text-sm text-center">No chapter data available</p>
              </div>
            )
          ) : (
            <Draw
              ref={drawRef}
              canDraw={!isAnalyzingDrawing}
              userId={userId}
              externalColor={color}
              externalLineWidth={lineWidth}
              externalTool={tool}
              onDrawingComplete={handleDrawingComplete}
              onDone={handleDoneDrawing}
              onClearCanvas={clearCanvas}
            />
          )}
          {/* NEW: Display analysis feedback and errors */}
          {isAnalyzingDrawing && (
            <div className="mt-2 p-2 bg-blue-50 text-blue-700 rounded">
              Analyzing your drawing...
            </div>
          )}
          {analysisFeedback && (
            <div className="mt-2 p-2 bg-green-50 text-green-700 rounded">
              {analysisFeedback}
            </div>
          )}
          {analysisError && (
            <div className="mt-2 p-2 bg-red-50 text-red-700 rounded">
              {analysisError}
            </div>
          )}
        </div>

        {/* *** ENHANCED RIGHT PANEL WITH TABBED NAVIGATION *** */}
        <div
          className={`${panelClass} w-full md:w-1/3 bg-white rounded-2xl shadow-sm border flex flex-col`}
        >
          {/* Top Nav for sections */}
          <nav className="p-5 border-b border-gray-100 bg-white rounded-t-2xl flex-shrink-0">
            <div className="flex gap-2 h-full">
              {["Assistant", "Video"].map((option) => (
                <button
                  key={option}
                  onClick={() => setSelectedOption(option)}
                  className={`
          relative flex-1 rounded-2xl font-medium tracking-tight transition-all duration-200 px-5 py-3
          ${
            selectedOption === option
              ? "bg-black text-white"
              : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-black"
          }
          text-xl
        `}
                >
                  {option}
                </button>
              ))}
            </div>
          </nav>

          {/* Conditional Content Area */}
          <div className="flex-1 min-h-0 flex flex-col px-4 pt-4 pb-0">
            {selectedOption === "Assistant" && (
              <>
                {error && (
                  <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2 text-red-700">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-xs">{error}</span>
                  </div>
                )}

                {/* center AssistantSection */}
                <div className="flex-1 flex items-center justify-center">
                  <AssistantSection
                    status={connectionStatus}
                    inputAmplitude={userAmplitude}
                  />
                </div>

                {/* Bottom-locked container for the conversation button */}
                <div className="mt-auto flex-shrink-0 border-t py-4 flex items-center justify-center">
                  <StartConversation
                    status={connectionStatus}
                    onStart={toggleConversation}
                  />
                </div>
              </>
            )}
            {selectedOption === "Video" && (
              <div className="flex-1 flex items-center justify-center text-gray-500 text-lg">
                <p>Video content will be displayed here.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {showTooltip && (
        <div
          className="absolute bg-gray-800 text-white text-base rounded py-1 px-2 shadow-lg"
          style={{
            top: "20px",
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 1000,
          }}
        >
          {showTooltip}
        </div>
      )}
    </div>
  );
}
