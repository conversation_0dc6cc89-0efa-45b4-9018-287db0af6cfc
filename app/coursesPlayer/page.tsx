// app/coursesPlayer/page.tsx

import React from "react";
import { Metadata } from "next";
import { getCurriculumById, getCurriculums } from "@/db/queries";
import CoursePlayer from "./coursesPlayer";
import { notFound, redirect } from "next/navigation";

// Force dynamic rendering to prevent caching issues
export const dynamic = "force-dynamic";
export const revalidate = 0;

// Updated TypeScript interfaces to reflect curriculum focus
interface CoursePlayerPageProps {
  searchParams: {
    curriculumId?: string;
    lessonId?: string;
    debug?: string;
  };
}

interface CurriculumData {
  id: number;
  englishTitle: string; // curriculum name
  arabicTitle: string; // curriculum name
  description: string;
  curriculumId: number;
  curriculum: {
    id: number;
    name: string;
    description: string | null;
  };
}

// Enhanced metadata generation
export async function generateMetadata({
  searchParams,
}: CoursePlayerPageProps): Promise<Metadata> {
  const { curriculumId } = searchParams;

  console.log("[Metadata] Generating metadata for curriculumId:", curriculumId);

  if (!curriculumId) {
    return {
      title: "Learning Platform - Select a Curriculum",
      description: "Choose a curriculum to start learning",
    };
  }

  try {
    const curriculumData = await getCurriculumById(Number(curriculumId));
    const curriculumName =
      curriculumData?.curriculum?.name || "Learning Content";

    console.log("[Metadata] Generated for curriculum:", curriculumName);

    return {
      title: `${curriculumName} - Interactive Learning`,
      description:
        curriculumData?.description ||
        `Learn ${curriculumName} with interactive lessons and exercises`,
      openGraph: {
        title: `${curriculumName} - Interactive Learning`,
        description:
          curriculumData?.description ||
          `Master ${curriculumName} with our comprehensive curriculum`,
        type: "website",
      },
    };
  } catch (error) {
    console.error("[Metadata] Error generating metadata:", error);
    return {
      title: "Learning Platform",
      description: "Interactive learning platform",
    };
  }
}

// Helper function to validate curriculumId
async function validateCurriculumId(curriculumId: string): Promise<boolean> {
  console.log("[Validation] Validating curriculumId:", curriculumId);

  // Check if it's a valid number
  const numericId = Number(curriculumId);
  if (isNaN(numericId) || numericId <= 0) {
    console.log("[Validation] Invalid numeric curriculumId:", curriculumId);
    return false;
  }

  // Check if curriculum exists in database
  try {
    const allCurriculums = await getCurriculums();
    const exists = allCurriculums.some(
      (curriculum) => curriculum.id === numericId
    );
    console.log("[Validation] Curriculum exists:", exists);
    return exists;
  } catch (error) {
    console.error("[Validation] Error checking curriculum existence:", error);
    return false;
  }
}

// Main Page Component
export default async function CoursesPlayerPage({
  searchParams,
}: CoursePlayerPageProps) {
  const { curriculumId, lessonId, debug } = searchParams;

  console.log(
    "[CoursesPlayerPage] Server component called with searchParams:",
    {
      curriculumId,
      lessonId,
      debug,
      timestamp: new Date().toISOString(),
    }
  );

  // Enhanced validation and error handling
  if (!curriculumId) {
    console.log(
      "[CoursesPlayerPage] No curriculumId provided, redirecting to curriculum selection"
    );
    redirect("/curriculum");
  }

  // Validate curriculum ID format and existence
  const isValidCurriculum = await validateCurriculumId(curriculumId);
  if (!isValidCurriculum) {
    console.log("[CoursesPlayerPage] Invalid curriculumId, redirecting to 404");
    notFound();
  }

  // Enhanced curriculum fetching
  let fetchedCurriculum: CurriculumData | null = null;
  let errorState: string | null = null;

  try {
    const curriculumIdNum = Number(curriculumId);
    console.log(
      "[CoursesPlayerPage] Fetching curriculum for curriculumId:",
      curriculumIdNum
    );

    fetchedCurriculum = await getCurriculumById(curriculumIdNum);

    console.log("[CoursesPlayerPage] Raw fetchedCurriculum result:", {
      found: !!fetchedCurriculum,
      curriculumId: fetchedCurriculum?.id,
      curriculumName: fetchedCurriculum?.curriculum?.name,
      title: fetchedCurriculum?.englishTitle,
    });

    if (!fetchedCurriculum) {
      console.log("[CoursesPlayerPage] No curriculum found");
      errorState = "curriculum_not_found";
    }
  } catch (error) {
    console.error("[CoursesPlayerPage] Error fetching curriculum:", error);
    errorState = "fetch_error";
  }

  // Pass curriculum data to client (renamed for clarity but keeping same interface)
  const serverCourse = fetchedCurriculum;

  // Enhanced logging for debugging
  console.log(
    "[CoursesPlayerPage] Final serverCourse being passed to client:",
    {
      hasServerCourse: !!serverCourse,
      curriculumData: serverCourse
        ? {
            id: serverCourse.id,
            englishTitle: serverCourse.englishTitle, // This is the curriculum name
            curriculumId: serverCourse.curriculumId,
            curriculumName: serverCourse.curriculum.name,
          }
        : null,
      errorState,
    }
  );

  // Debug mode - show raw data if debug=true
  if (debug === "true") {
    return (
      <div className="min-h-screen bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">
            Debug Information - Curriculum Focus
          </h1>
          <div className="bg-white rounded-lg p-6 shadow-md">
            <h2 className="text-lg font-semibold mb-4">Search Parameters</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(searchParams, null, 2)}
            </pre>

            <h2 className="text-lg font-semibold mb-4 mt-6">
              Fetched Curriculum Data
            </h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(fetchedCurriculum, null, 2)}
            </pre>

            <h2 className="text-lg font-semibold mb-4 mt-6">
              Curriculum Name for Header
            </h2>
            <div className="bg-green-100 p-4 rounded text-sm">
              <strong>Header will show:</strong> &quot;
              {fetchedCurriculum?.curriculum?.name || "Not found"}&quot;
            </div>

            {errorState && (
              <>
                <h2 className="text-lg font-semibold mb-4 mt-6 text-red-600">
                  Error State
                </h2>
                <div className="bg-red-100 p-4 rounded text-sm">
                  {errorState}
                </div>
              </>
            )}
          </div>
          <div className="mt-6 text-center">
            <a
              href={`/coursesPlayer?curriculumId=${curriculumId}`}
              className="inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800"
            >
              Exit Debug Mode
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Enhanced error handling
  if (errorState === "curriculum_not_found") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <div className="mb-6">
            <svg
              className="mx-auto h-16 w-16 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Curriculum Not Found
          </h2>
          <p className="text-gray-600 mb-6">
            The requested curriculum could not be found. Please try another
            curriculum.
          </p>
          <a
            href="/curriculum"
            className="inline-flex items-center px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
          >
            ← Back to Curriculums
          </a>
        </div>
      </div>
    );
  }

  if (errorState === "fetch_error") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <div className="mb-6">
            <svg
              className="mx-auto h-16 w-16 text-red-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Oops! Something went wrong
          </h2>
          <p className="text-gray-600 mb-6">
            We encountered an error while loading the curriculum. Please try
            again.
          </p>
          <div className="space-y-3">
            <a
              href={`/coursesPlayer?curriculumId=${curriculumId}`}
              className="w-full inline-flex items-center justify-center px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
            >
              Try Again
            </a>
            <a
              href="/curriculum"
              className="w-full inline-flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              ← Back to Curriculums
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Success case - render the course player with curriculum data
  return (
    <CoursePlayer
      searchParams={searchParams}
      serverCourse={serverCourse} // This now contains curriculum data
    />
  );
}
