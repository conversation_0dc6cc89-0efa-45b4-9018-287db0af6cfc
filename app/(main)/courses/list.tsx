"use client";

import { toast } from "sonner";
import { useTransition } from "react";
import { useRouter } from "next/navigation";
import { courses, userProgress } from "@/db/schema";
import { upsertUserProgress } from "@/actions/user-progress";
import { Card } from "./card";

type Props = {
  courses: (typeof courses.$inferSelect)[];
  activeCourseId?: typeof userProgress.$inferSelect.activeCourseId;
};

export const List = ({ courses, activeCourseId }: Props) => {
  const router = useRouter();
  const [pending, startTransition] = useTransition();

  const gridClasses = "grid grid-cols-3 gap-4";

  const onClick = (id: number) => {
    if (pending) return;

    if (id === activeCourseId) {
      return router.push("/learn");
    }

    startTransition(() => {
      upsertUserProgress(id).catch(() => toast.error("Something went wrong."));
    });
  };

  return (
    <div className="w-full px-4 md:px-6">
      <div className="relative">
        {/* Loading overlay */}
        {pending && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50 backdrop-blur-sm">
            <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-gray-900"></div>
            <span className="ml-2 text-gray-700">Loading...</span>
          </div>
        )}

        {/* Fixed 3x3 grid layout */}
        <div className={gridClasses}>
          {courses.map((course) => (
            <Card
              key={course.id}
              id={course.id}
              index={courses.indexOf(course) + 1}
              englishTitle={course.englishTitle}
              arabicTitle={course.arabicTitle}
              onClick={onClick}
              disabled={pending}
              active={course.id === activeCourseId}
            />
          ))}
        </div>

        {/* Empty state */}
        {courses.length === 0 && (
          <div className="flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-6 py-12 text-center shadow-sm">
            <p className="mb-4 text-lg text-gray-600">No courses available</p>
            <p className="text-sm text-gray-500">
              Check back later for new content
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
