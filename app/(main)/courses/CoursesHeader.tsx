"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { ChevronLeft } from "lucide-react";

const CoursesHeader: React.FC = () => {
  const router = useRouter();

  const handleBackClick = () => {
    console.log("Back button clicked. Navigating to /learning-path");
    router.push("/challenges");
  };

  return (
    <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        {/* Back Button */}
        <button
          onClick={handleBackClick}
          className="p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
        >
          <ChevronLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
        </button>

        {/* Title - Centered */}
        <h1 className="text-lg font-bold text-neutral-700 whitespace-nowrap">
          <PERSON>orani <PERSON> Exercises
        </h1>

        {/* Placeholder div to balance the layout */}
        <div className="w-6 h-6"></div>
      </div>
    </div>
  );
};

export default CoursesHeader;
