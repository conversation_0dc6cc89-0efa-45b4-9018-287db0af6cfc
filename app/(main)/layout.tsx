import { ReactNode } from "react";
import { headers } from "next/headers";
import { Sidebar } from "@/components/sidebar";
import { MobileHeader } from "@/components/mobile-header";
import { AuthGuard } from "@/components/auth-guard";

type Props = {
  children: ReactNode;
  params: {}; // Layouts receive a params object
};

export default function MainLayout({ children }: Props) {
  // --- TWEAKED EMBEDDED DETECTION ---
  // Reliably checks for an iframe context using the standard `sec-fetch-dest` header.
  const headersList = headers();
  const isEmbedded = headersList.get("sec-fetch-dest") === "iframe";

  // If the page is embedded, return only the child component.
  // This bypasses the main layout, header, sidebar, and auth checks, giving
  // the embedded content the full viewport.
  if (isEmbedded) {
    return <div className="h-full w-full">{children}</div>;
  }

  // --- PERFORMANCE OPTIMIZATION: Removed blocking server-side operations ---
  // All user authentication and validation is now handled client-side
  // This eliminates 30-45 seconds of SSR blocking time

  console.log("[MainLayout] Layout initialization started (client-side auth).");

  // This is the default return for the full application experience.
  // Authentication and user validation now happens client-side in components
  return (
    <AuthGuard>
      <MobileHeader />
      <div className="flex h-screen">
        <Sidebar />
        <main className="flex-1 h-full pt-[50px] lg:pt-0 overflow-auto">
          <div className="w-full h-full">{children}</div>
        </main>
      </div>
    </AuthGuard>
  );
}
