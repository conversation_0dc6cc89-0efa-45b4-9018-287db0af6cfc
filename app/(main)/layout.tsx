import { ReactNode } from "react";
import { redirect } from "next/navigation";
import { headers } from "next/headers";
import { getOrCreateUser } from "@/lib/users";
import db from "@/db/drizzle";
import { teacherStudents } from "@/db/schema";
import { eq } from "drizzle-orm";
import { Sidebar } from "@/components/sidebar";
import { MobileHeader } from "@/components/mobile-header";

type Props = {
  children: ReactNode;
  params: {}; // Layouts receive a params object
};

export default async function MainLayout({ children }: Props) {
  // --- TWEAKED EMBEDDED DETECTION ---
  // Reliably checks for an iframe context using the standard `sec-fetch-dest` header.
  const headersList = headers();
  const isEmbedded = headersList.get("sec-fetch-dest") === "iframe";

  // If the page is embedded, return only the child component.
  // This bypasses the main layout, header, sidebar, and auth checks, giving
  // the embedded content the full viewport.
  if (isEmbedded) {
    return <div className="h-full w-full">{children}</div>;
  }

  // --- The rest of this code only runs for regular, non-embedded pages ---

  console.log("[MainLayout] Layout initialization started.");

  // 1) Attempt to fetch (or create) the user in your DB.
  const userRecord = await getOrCreateUser();
  console.log("[MainLayout] Fetched userRecord:", userRecord);
  if (userRecord) {
    console.log(
      "[MainLayout] Available fields in userRecord:",
      Object.keys(userRecord)
    );
  }

  // 2) If for some reason userRecord is null (e.g., not logged in), redirect to joinSchool.
  if (!userRecord) {
    console.error(
      "[MainLayout] No userRecord found. Redirecting to /joinSchool."
    );
    redirect("/joinSchool");
  }

  // 3) If the user is a TEACHER, ensure they have credentials set.
  if (userRecord.role === "teacher") {
    console.log(
      "[MainLayout] User is a teacher. Checking teacher credentials..."
    );
    const hasTeacherCreds =
      userRecord.schoolUsername && userRecord.schoolPassword;
    console.log(
      "[MainLayout] Teacher credentials:",
      "schoolUsername:",
      userRecord.schoolUsername,
      "schoolPassword:",
      userRecord.schoolPassword
    );
    if (!hasTeacherCreds) {
      console.error(
        "[MainLayout] Teacher credentials missing. Redirecting to /joinSchool."
      );
      redirect("/joinSchool");
    }
  } else {
    // 4) If the user is a STUDENT, ensure they have joined a teacher.
    console.log(
      "[MainLayout] User is a student. Checking teacher_students pivot table..."
    );
    const pivotRow = await db.query.teacherStudents.findFirst({
      where: eq(teacherStudents.studentId, userRecord.userId),
    });
    console.log("[MainLayout] Pivot row result:", pivotRow);

    if (!pivotRow) {
      console.error(
        "[MainLayout] Student has not joined a teacher. Redirecting to /joinSchool."
      );
      redirect("/joinSchool");
    }
  }

  console.log("[MainLayout] User fully onboarded. Rendering layout.");
  // This is the default return for the full application experience.
  return (
    <>
      <MobileHeader />
      <div className="flex h-screen">
        <Sidebar />
        <main className="flex-1 h-full pt-[50px] lg:pt-0 overflow-auto">
          <div className="w-full h-full">{children}</div>
        </main>
      </div>
    </>
  );
}
