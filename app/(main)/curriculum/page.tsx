// app/curriculum/page.tsx
import React, { Suspense } from "react";
import Link from "next/link";
import Loading from "./loading";
import { getCurriculums } from "@/db/queries";
import { ArrowLeft } from "lucide-react";

// Force dynamic rendering to prevent caching issues
export const dynamic = "force-dynamic";
export const revalidate = 0;

interface Curriculum {
  id: number;
  name: string;
  description?: string | null;
}

const CurriculumContent = async () => {
  console.log(
    "[CurriculumContent] Fetching curriculums at:",
    new Date().toISOString()
  );

  const curriculums: Curriculum[] = await getCurriculums();

  console.log("[CurriculumContent] Received curriculums:", {
    count: curriculums.length,
    ids: curriculums.map((c) => c.id),
    names: curriculums.map((c) => c.name),
  });

  if (!curriculums || curriculums.length === 0) {
    return (
      <div className="w-full px-6">
        <div className="flex flex-col items-center justify-center py-12">
          <p className="text-gray-500 text-lg">No curriculums found</p>
          <p className="text-gray-400 text-sm mt-2">Please check back later</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-6">
      {/* 
        Stacking the cards vertically:
        - flex-col for vertical stacking
        - items-start to left-align them
        - gap-6 for space between
      */}
      <div className="flex flex-col gap-6 items-start">
        {curriculums.map((curriculum) => (
          <Link
            key={curriculum.id}
            href={`/coursesPlayer?curriculumId=${curriculum.id}`}
            className="group"
          >
            {/*
              Making the card a vertical rectangle:
              - On small screens (mobile), it will be w-full
              - On sm screens and above, it will be w-72
              - aspect-[12/16] to ensure a vertical rectangle shape
            */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 w-full sm:w-72 aspect-[12/16] flex flex-col justify-center transition-all duration-300 hover:shadow-lg group-hover:border-gray-300 transform hover:scale-105">
              <h2 className="text-xl font-semibold mb-2 text-black group-hover:text-gray-800">
                {curriculum.name}
              </h2>
              {curriculum.description && (
                <p className="text-gray-500 text-sm">
                  {curriculum.description}
                </p>
              )}
              <div className="mt-4 flex items-center text-gray-400 group-hover:text-black">
                <span className="text-sm">Explore Exercises</span>
                <svg
                  className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

const CurriculumPage = async () => {
  return (
    <main
      // Removed noorehuda.className; relying on global.css for "Hafs" font
      className="min-h-screen bg-gray-50"
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Sticky Header with back arrow and centered title */}
      <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
        <div className="relative max-w-7xl mx-auto">
          <Link
            href="/learning-path"
            className="absolute left-0 top-1/2 -translate-y-1/2 p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
          </Link>
          <h1 className="text-lg font-bold text-gray-900 text-center">
            My courses
          </h1>
        </div>
      </div>

      {/* Body Content */}
      <div className="py-16 px-6">
        <Suspense fallback={<Loading />}>
          <CurriculumContent />
        </Suspense>
      </div>
    </main>
  );
};

export default CurriculumPage;
