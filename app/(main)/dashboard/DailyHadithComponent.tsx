// DailyHadithComponent.tsx
"use client";

import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { localHadiths } from "./Hadiths";
import { Card, CardContent } from "@/components/ui/card";

interface HadithData {
  arabic: string;
  english: string;
}

const LS_HADITH_POOL_KEY = "hadith_pool";
const LS_CURRENT_DAY_HADITH_INDEX_KEY = "current_day_hadith_index";
const LS_LAST_SELECTED_DATE_KEY = "last_selected_date";

const getTodayDateString = () => new Date().toDateString();

const selectDailyHadith = (): HadithData => {
  const todayStr = getTodayDateString();
  let currentDayHadithOriginalIndex = parseInt(
    localStorage.getItem(LS_CURRENT_DAY_HADITH_INDEX_KEY) || "-1"
  );
  let lastStoredDate = localStorage.getItem(LS_LAST_SELECTED_DATE_KEY);
  let hadithPool: number[] = JSON.parse(
    localStorage.getItem(LS_HADITH_POOL_KEY) || "[]"
  );

  if (
    todayStr !== lastStoredDate ||
    currentDayHadithOriginalIndex === -1 ||
    hadithPool.length === 0
  ) {
    if (hadithPool.length === 0) {
      hadithPool = Array.from({ length: localHadiths.length }, (_, i) => i);
    }
    const today = new Date();
    const dayOfYear = Math.floor(
      (today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    const seed = today.getFullYear() * 1000 + dayOfYear;
    const seededRandom = (s: number) => {
      const x = Math.sin(s) * 10000;
      return x - Math.floor(x);
    };
    const randomFromPoolIndex = Math.floor(
      seededRandom(seed) * hadithPool.length
    );
    currentDayHadithOriginalIndex = hadithPool[randomFromPoolIndex];
    hadithPool.splice(randomFromPoolIndex, 1);
    localStorage.setItem(LS_HADITH_POOL_KEY, JSON.stringify(hadithPool));
    localStorage.setItem(
      LS_CURRENT_DAY_HADITH_INDEX_KEY,
      currentDayHadithOriginalIndex.toString()
    );
    localStorage.setItem(LS_LAST_SELECTED_DATE_KEY, todayStr);
  }

  if (
    currentDayHadithOriginalIndex >= 0 &&
    currentDayHadithOriginalIndex < localHadiths.length
  ) {
    return localHadiths[currentDayHadithOriginalIndex];
  } else {
    console.warn(
      "Invalid Hadith index from localStorage. Falling back to default."
    );
    return {
      arabic: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
      english: "All praise is due to Allah, Lord of the worlds.",
    };
  }
};

const DailyHadithComponent: React.FC = () => {
  const [hadith, setHadith] = useState<HadithData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchDate, setLastFetchDate] = useState<string>("");
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageOpacity, setImageOpacity] = useState(1);

  const images = [
    { src: "/kaba.svg", alt: "Kaba" },
    { src: "/Kitab.svg", alt: "Kitab" },
    { src: "/Lamp.svg", alt: "Lamp" },
    { src: "/Tasbih.svg", alt: "Tasbih" },
  ];

  const loadHadith = useCallback(async () => {
    setLoading(true);
    setError(null);
    if (localHadiths.length === 0) {
      setError("No Hadiths available in the local repository.");
      setLoading(false);
      return;
    }
    try {
      const selectedHadithData = selectDailyHadith();
      setHadith(selectedHadithData);
      setLastFetchDate(getTodayDateString());
    } catch (err) {
      console.error("Failed to load hadith from local repository:", err);
      setError("Failed to load hadith. Please try again.");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!hadith || getTodayDateString() !== lastFetchDate) {
      loadHadith();
    }
    const interval = setInterval(() => {
      if (getTodayDateString() !== lastFetchDate) {
        loadHadith();
      }
    }, 60000);
    return () => clearInterval(interval);
  }, [lastFetchDate, hadith, loadHadith]);

  useEffect(() => {
    const imageInterval = setInterval(() => {
      setImageOpacity(0);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
        setImageOpacity(1);
      }, 300);
    }, 30000);
    return () => clearInterval(imageInterval);
  }, [images.length]);

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center gap-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-black" />
          <span className="text-gray-600 text-sm">Loading hadith…</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center">
        <div>
          <div className="text-gray-400 mb-2 text-lg">⚠️</div>
          <p className="text-gray-600 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    // fills the parent Card, hides overflow, two‐panel layout
    <div className="h-full w-full flex flex-col lg:flex-row overflow-hidden">
      {/* Left Side: Image */}
      <div className="w-full lg:w-3/5 h-1/2 lg:h-full flex items-center justify-center p-4">
        <Image
          src={images[currentImageIndex].src}
          alt={images[currentImageIndex].alt}
          width={300}
          height={300}
          className="object-contain max-w-full max-h-full transition-opacity duration-300 ease-in-out"
          style={{ opacity: imageOpacity }}
          priority
        />
      </div>

      {/* Right Side: Text */}
      <div className="w-full lg:w-2/5 h-1/2 lg:h-full flex flex-col justify-center text-center lg:text-right p-4 min-h-0">
        <div className="flex-1 overflow-auto space-y-3">
          {hadith?.arabic && (
            <p
              dir="rtl"
              className="text-xl md:text-2xl font-arabic"
              style={{
                fontFamily: 'Amiri, "Times New Roman", serif',
                lineHeight: 2.0,
              }}
            >
              {hadith.arabic}
            </p>
          )}
          <div className="w-20 h-px bg-gray-200 dark:bg-gray-700 mx-auto lg:ml-auto lg:mr-0" />
          <p className="text-sm md:text-base leading-relaxed text-gray-600 dark:text-gray-400">
            {hadith?.english}
          </p>
        </div>
      </div>
    </div>
  );
};

export default DailyHadithComponent;
