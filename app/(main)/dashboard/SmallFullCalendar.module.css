/* SmallFullCalendar.module.css */

.calendarWrapper {
  position: relative;
  background-color: transparent;
  overflow: hidden;
  min-height: 0;
  height: 100%;
  width: 100%;
  --grid-rows: 6;
  --grid-cols: 7;
  --grid-cell-width: calc(100% / var(--grid-cols));
  --grid-cell-height: calc(100% / var(--grid-rows));
}

.calendarWrapper :global(.fc) {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol";
  font-size: clamp(0.625rem, 1.5vw, 0.875rem);
  line-height: 1.25;
  height: auto !important;
}

.calendarWrapper :global(.fc table) {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  border: none !important;
}

.calendarWrapper :global(.fc-theme-standard td),
.calendarWrapper :global(.fc-theme-standard th) {
  border: none !important;
  padding: 0 !important;
}

.calendarWrapper :global(.fc-theme-standard .fc-scrollgrid) {
  border: none !important;
}

.calendarWrapper :global(.fc-col-header-cell) {
  padding: clamp(2px, 1vh, 8px) 0;
  text-transform: uppercase;
  font-size: clamp(0.5rem, 1.2vw, 0.6875rem);
  line-height: 1rem;
  font-weight: 600;
  color: #6b7280;
  text-align: center;
  letter-spacing: 0.05em;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
}

.calendarWrapper :global(.fc-daygrid-day-frame) {
  min-height: clamp(28px, 8vh, 56px);
  padding: clamp(2px, 0.5vh, 6px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.calendarWrapper :global(.fc-day-today) {
  background: none !important;
}

.calendarWrapper :global(.fc .fc-daygrid-day-events) {
  display: none !important;
}

.dayCellCircle {
  width: clamp(24px, 4.5vh, 36px);
  height: clamp(24px, 4.5vh, 36px);
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: clamp(0.75rem, 1.8vh, 0.875rem);
  line-height: 1;
  font-weight: 500;
  cursor: default;
  transition: all 0.2s ease-in-out;
}

.dayCellCircleToday {
  background-color: #111827;
  color: #ffffff;
  font-weight: 600;
}

.dayCellCircleHasEvents {
  background-color: #f3f4f6;
  color: #374151;
}

.dayCellCircleDefault {
  background-color: transparent;
  color: #4b5563;
}

.dayCellCircleOtherMonth {
  background-color: transparent !important;
  color: #9ca3af !important;
  opacity: 0.8;
}

.calendarWrapper.isHovered
  .dayCellCircle:not(.dayCellCircleOtherMonth, .dayCellCircleToday):hover {
  transform: scale(1.1);
  background-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.calendarWrapper.isHovered .dayCellCircleToday:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
}

.gridContainer {
  position: relative;
  flex: 1 1 0;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  z-index: 1;
}

.gridBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  background-image: linear-gradient(
      to right,
      rgba(229, 231, 235, 0.35) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(229, 231, 235, 0.35) 1px, transparent 1px);
  background-size: var(--grid-cell-width) var(--grid-cell-height);
  background-attachment: local;
}

.gridCellHover {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(229, 231, 235, 0.4);
  border-radius: 6px;
  transition: all 0.15s ease-in-out;
  opacity: 0;
  pointer-events: none;
  z-index: 2;
}

.gridCellHover.active {
  opacity: 1;
}

@media (max-width: 768px) {
  .calendarWrapper :global(.fc-col-header-cell) {
    font-size: 0.625rem;
  }
  .dayCellCircle {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }
}
