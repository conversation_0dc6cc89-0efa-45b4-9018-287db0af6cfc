"use client";

import React, {
  useContext,
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import { EventContext } from "@/app/(main)/schedule/EventContext";
import { CalendarDays, ChevronDown } from "lucide-react";
import styles from "./SmallFullCalendar.module.css";

// Interface definitions (unchanged)
interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  googleEventId?: string;
  calendarLink?: string;
  recurrenceRule?: string;
  jitsiRoomName?: string;
  isMeeting?: boolean;
}

interface DayCellContentArg {
  date: Date;
  dayNumberText: string;
  isOther: boolean;
  isToday: boolean;
}

interface GridDimensions {
  cellWidth: number;
  cellHeight: number;
  offsetX: number;
  offsetY: number;
  headerHeight: number;
  rows: number;
  cols: number;
}

interface GridCell {
  row: number;
  col: number;
  x: number;
  y: number;
  width: number;
  height: number;
}

const SmallFullCalendar: React.FC = () => {
  const { events: contextEvents, loading: contextLoading } =
    useContext(EventContext);
  const [datesWithEvents, setDatesWithEvents] = useState<Set<string>>(
    new Set()
  );
  const [calendarApi, setCalendarApi] = useState<any>(null);
  const [currentDisplayDate, setCurrentDisplayDate] = useState(new Date());
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [hoveredGridCell, setHoveredGridCell] = useState<GridCell | null>(null);
  const [gridDimensions, setGridDimensions] = useState<GridDimensions>({
    cellWidth: 0,
    cellHeight: 0,
    offsetX: 0,
    offsetY: 0,
    headerHeight: 0,
    rows: 6,
    cols: 7,
  });
  const [isGridInitialized, setIsGridInitialized] = useState(false);
  const [isMouseOverCalendar, setIsMouseOverCalendar] = useState(false);
  const calendarRef = useRef<FullCalendar>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const gridContainerRef = useRef<HTMLDivElement>(null);
  const mousePositionRef = useRef({ x: 0, y: 0 });
  const lastUpdateTimeRef = useRef(0);

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const throttle = useCallback((func: Function, limit: number) => {
    return (...args: any[]) => {
      const now = Date.now();
      if (now - lastUpdateTimeRef.current >= limit) {
        lastUpdateTimeRef.current = now;
        func.apply(null, args);
      }
    };
  }, []);

  const calculateGridDimensions = useCallback((): GridDimensions => {
    if (!gridContainerRef.current || !calendarApi) {
      return {
        cellWidth: 0,
        cellHeight: 0,
        offsetX: 0,
        offsetY: 0,
        headerHeight: 0,
        rows: 6,
        cols: 7,
      };
    }

    const container = gridContainerRef.current;
    const headerElement = container.querySelector(".fc-col-header");
    const firstDayCell = container.querySelector(".fc-daygrid-day");

    if (!headerElement || !firstDayCell) {
      return {
        cellWidth: 0,
        cellHeight: 0,
        offsetX: 0,
        offsetY: 0,
        headerHeight: 0,
        rows: 6,
        cols: 7,
      };
    }

    const dayFrames = container.querySelectorAll(".fc-daygrid-day-frame");
    const cellWidth = firstDayCell.getBoundingClientRect().width;
    const cellHeight = firstDayCell.getBoundingClientRect().height;
    const rows = Math.ceil(dayFrames.length / 7);
    const headerHeight = headerElement.getBoundingClientRect().height;
    const offsetX = 0;
    const offsetY = headerHeight;

    return {
      cellWidth,
      cellHeight,
      offsetX,
      offsetY,
      headerHeight,
      rows,
      cols: 7,
    };
  }, [calendarApi]);

  const getGridCellFromCoordinates = useCallback(
    (x: number, y: number): GridCell | null => {
      if (!isGridInitialized || !gridContainerRef.current) return null;

      const container = gridContainerRef.current;
      const rect = container.getBoundingClientRect();
      const scrollTop = container.scrollTop; // Get scroll position

      const relativeX = x - rect.left;
      const relativeY = y - rect.top + scrollTop; // Adjust for scroll

      if (
        relativeX < 0 ||
        relativeY < gridDimensions.headerHeight || // Don't highlight the header
        relativeX > gridDimensions.cols * gridDimensions.cellWidth
      ) {
        return null;
      }

      const col = Math.floor(relativeX / gridDimensions.cellWidth);
      const row = Math.floor(
        (relativeY - gridDimensions.headerHeight) / gridDimensions.cellHeight
      );

      if (
        col < 0 ||
        col >= gridDimensions.cols ||
        row < 0 ||
        row >= gridDimensions.rows
      ) {
        return null;
      }

      return {
        row,
        col,
        x: col * gridDimensions.cellWidth,
        y: row * gridDimensions.cellHeight + gridDimensions.headerHeight,
        width: gridDimensions.cellWidth,
        height: gridDimensions.cellHeight,
      };
    },
    [gridDimensions, isGridInitialized]
  );

  const handleMouseMove = useMemo(
    () =>
      throttle((event: MouseEvent) => {
        mousePositionRef.current = { x: event.clientX, y: event.clientY };
        const gridCell = getGridCellFromCoordinates(
          event.clientX,
          event.clientY
        );

        if (
          gridCell &&
          (!hoveredGridCell ||
            gridCell.row !== hoveredGridCell.row ||
            gridCell.col !== hoveredGridCell.col)
        ) {
          setHoveredGridCell(gridCell);
        } else if (!gridCell && hoveredGridCell) {
          setHoveredGridCell(null);
        }
      }, 16),
    [getGridCellFromCoordinates, hoveredGridCell, throttle]
  );

  const handleMouseLeave = useCallback(() => {
    setHoveredGridCell(null);
    setIsMouseOverCalendar(false);
  }, []);

  const updateGridDimensions = useCallback(() => {
    const newDimensions = calculateGridDimensions();
    setGridDimensions(newDimensions);

    if (newDimensions.cellWidth > 0 && newDimensions.cellHeight > 0) {
      setIsGridInitialized(true);

      if (gridContainerRef.current) {
        const container = gridContainerRef.current;
        container.style.setProperty(
          "--grid-cell-width",
          `${newDimensions.cellWidth}px`
        );
        container.style.setProperty(
          "--grid-cell-height",
          `${newDimensions.cellHeight}px`
        );
      }
    }
  }, [calculateGridDimensions]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerSize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        });
      }
    });

    resizeObserver.observe(container);
    return () => resizeObserver.disconnect();
  }, []);

  useEffect(() => {
    if (containerSize.width > 0 && containerSize.height > 0 && calendarApi) {
      const timeoutId = setTimeout(updateGridDimensions, 150);
      return () => clearTimeout(timeoutId);
    }
  }, [containerSize, calendarApi, updateGridDimensions]);

  useEffect(() => {
    const container = gridContainerRef.current;
    if (!container || !isGridInitialized) return;

    const currentHandleMouseMove = handleMouseMove;
    container.addEventListener("mousemove", currentHandleMouseMove);

    return () => {
      if (container) {
        container.removeEventListener("mousemove", currentHandleMouseMove);
      }
    };
  }, [handleMouseMove, isGridInitialized]);

  const getUniqueEventDates = useCallback(
    (rawEvents: CalendarEvent[]): Set<string> => {
      const eventDates = new Set<string>();
      rawEvents.forEach((event) => {
        if (event.startTime) {
          const date = new Date(event.startTime);
          const dateString = `${date.getFullYear()}-${String(
            date.getMonth() + 1
          ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
          eventDates.add(dateString);
        }
      });
      return eventDates;
    },
    []
  );

  useEffect(() => {
    if (!contextLoading && contextEvents) {
      setDatesWithEvents(getUniqueEventDates(contextEvents as CalendarEvent[]));
    }
  }, [contextEvents, contextLoading, getUniqueEventDates]);

  const handleCalendarRef = useCallback((node: FullCalendar | null) => {
    if (node) {
      setCalendarApi(node.getApi());
    } else {
      setCalendarApi(null);
    }
  }, []);

  const updateCurrentDisplayDate = useCallback(() => {
    if (calendarApi) {
      setCurrentDisplayDate(calendarApi.getDate());
    }
  }, [calendarApi]);

  useEffect(() => {
    if (calendarApi) {
      updateCurrentDisplayDate();
      calendarApi.on("datesSet", () => {
        updateCurrentDisplayDate();
        setTimeout(updateGridDimensions, 100);
      });
    }
  }, [calendarApi, updateGridDimensions, updateCurrentDisplayDate]);

  useEffect(() => {
    if (calendarApi && containerSize.width > 0) {
      setTimeout(() => {
        calendarApi.updateSize();
        setTimeout(updateGridDimensions, 50);
      }, 100);
    }
  }, [calendarApi, containerSize, updateGridDimensions]);

  const handleMonthSelect = (monthIndex: number) => {
    if (calendarApi) {
      calendarApi.gotoDate(
        new Date(currentDisplayDate.getFullYear(), monthIndex, 1)
      );
      setIsDropdownOpen(false);
    }
  };

  const handleYearChange = (year: number) => {
    if (calendarApi) {
      calendarApi.gotoDate(new Date(year, currentDisplayDate.getMonth(), 1));
      setIsDropdownOpen(false);
    }
  };

  const renderDayCell = (arg: DayCellContentArg) => {
    const { date, dayNumberText, isOther, isToday } = arg;
    const dateString = `${date.getFullYear()}-${String(
      date.getMonth() + 1
    ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    const dayHasEvents = datesWithEvents.has(dateString);
    let circleClasses = `${styles.dayCellCircle} ${
      isOther
        ? styles.dayCellCircleOtherMonth
        : isToday
        ? styles.dayCellCircleToday
        : dayHasEvents
        ? styles.dayCellCircleHasEvents
        : styles.dayCellCircleDefault
    }`;

    return <div className={circleClasses}>{dayNumberText}</div>;
  };

  const isSmallContainer =
    containerSize.width < 300 || containerSize.height < 250;
  const displayMonthYear = new Intl.DateTimeFormat("en-US", {
    month: isSmallContainer ? "short" : "long",
    year: "numeric",
  }).format(currentDisplayDate);
  const yearOptions = Array.from(
    { length: 11 },
    (_, i) => new Date().getFullYear() - 5 + i
  );

  const iconSize = isSmallContainer ? "w-3 h-3" : "w-4 h-4";
  const headerText = isSmallContainer ? "text-xs" : "text-sm";
  const buttonPadding = isSmallContainer ? "px-1.5 py-0.5" : "px-2 py-1";
  const buttonText = "text-xs";
  const chevronSize = isSmallContainer ? "w-3 h-3" : "w-3.5 h-3.5";

  return (
    // MODIFIED: Root element now fills the parent and acts as the main flex container.
    <div
      ref={containerRef}
      onMouseEnter={() => setIsMouseOverCalendar(true)}
      onMouseLeave={handleMouseLeave}
      className={`${styles.calendarWrapper} ${
        isMouseOverCalendar ? styles.isHovered : ""
      } flex flex-col h-full w-full`}
    >
      {!contextLoading ? (
        <>
          {/* MODIFIED: Removed padding and border from the header div to allow parent Card to control it. Added margin-bottom for spacing. */}
          <div className="flex-shrink-0 mb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-gray-700">
                <CalendarDays className={`${iconSize} text-black`} />
                <span className={`font-medium ${headerText}`}>Calendar</span>
              </div>
              <div className="relative">
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className={`${buttonPadding} ${buttonText} bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 rounded-md flex items-center gap-1 transition-colors duration-150`}
                >
                  <span className="truncate max-w-[120px]">
                    {displayMonthYear}
                  </span>
                  <ChevronDown
                    className={`${chevronSize} transition-transform duration-200 ${
                      isDropdownOpen ? "rotate-180" : ""
                    }`}
                  />
                </button>
                {isDropdownOpen && (
                  <div className="absolute top-full right-0 mt-1.5 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-48 overflow-y-auto min-w-[180px] flex">
                    <div className="w-1/2 border-r border-gray-100">
                      {months.map((month, index) => (
                        <button
                          key={month}
                          onClick={() => handleMonthSelect(index)}
                          className={`w-full px-2 py-1 text-xs text-left transition-colors duration-150 ${
                            index === currentDisplayDate.getMonth()
                              ? "font-semibold bg-gray-900 text-white"
                              : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          {isSmallContainer ? month.slice(0, 3) : month}
                        </button>
                      ))}
                    </div>
                    <div className="w-1/2">
                      {yearOptions.map((year) => (
                        <button
                          key={year}
                          onClick={() => handleYearChange(year)}
                          className={`w-full px-2 py-1 text-xs text-left transition-colors duration-150 ${
                            year === currentDisplayDate.getFullYear()
                              ? "font-semibold bg-gray-900 text-white"
                              : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          {year}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div ref={gridContainerRef} className={`${styles.gridContainer}`}>
            <div className={styles.gridBackground} />
            {hoveredGridCell && (
              <div
                className={`${styles.gridCellHover} active`}
                style={{
                  transform: `translate(${hoveredGridCell.x}px, ${hoveredGridCell.y}px)`,
                  width: `${hoveredGridCell.width}px`,
                  height: `${hoveredGridCell.height}px`,
                }}
              />
            )}
            <FullCalendar
              ref={handleCalendarRef}
              plugins={[dayGridPlugin]}
              initialView="dayGridMonth"
              headerToolbar={false}
              dayCellContent={renderDayCell}
              height="auto"
              dayHeaderFormat={{
                weekday: isSmallContainer ? "narrow" : "short",
              }}
              fixedWeekCount={false}
              showNonCurrentDates={true}
              expandRows={true}
            />
          </div>
        </>
      ) : (
        <div className="flex items-center justify-center flex-grow">
          <span className={headerText}>Loading...</span>
        </div>
      )}
    </div>
  );
};

export default SmallFullCalendar;
