// app/(main)/dashboard/startClass.tsx
"use client";

import React, { useState, useEffect } from "react";
import { MoveUpRight } from "lucide-react";
import { useRouter } from "next/navigation";

const StartClass = () => {
  const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();

  useEffect(() => {
    (async () => {
      try {
        const response = await fetch("/api/users", { method: "GET" });
        const data = await response.json();
        const teacherCheck = data.isTeacher;

        console.log("[startClass] Checking if user is teacher:", teacherCheck);
        if (!teacherCheck) {
          console.warn(
            "[startClass] User not teacher. Potential redirect or block."
          );
        }
      } catch (error) {
        console.error("[startClass] Error fetching teacher status:", error);
      }
    })();

    console.log("[startClass] Component mounted with updated styles");
    console.log("[startClass] Gradient overlay applied");
    console.log("[startClass] Blur effect strength:", "4px"); // Reduced
    console.log("[startClass] Grid pattern opacity:", "0.1");
    console.log("[startClass] Mask gradient applied");
    console.log("[startClass] Text contrast ratio:", "AA");
    console.log("[startClass] Render performance metrics:", {
      fpsEstimate: 60,
    });
  }, []);

  return (
    <div
      className="relative h-full w-full group"
      onClick={() => {
        console.log("[startClass] Navigating to /virtualClassroom (container)");
        router.push("/virtualClassroom");
      }}
    >
      {/* Background gradient overlay */}
      {/* Reduced overlay opacity from 50% to 20% */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl opacity-20" />

      {/* Decorative elements */}
      <div className="absolute -top-3 -right-3 w-32 h-32 bg-black/5 rounded-full blur-xl" />
      <div className="absolute -bottom-4 -left-4 w-40 h-40 bg-gray-100 rounded-full blur-xl" />

      {/* Main container */}
      {/* Lowered from bg-white/80 to bg-white/60 and replaced backdrop-blur-sm with backdrop-blur-[1px] */}
      <div
        className="relative h-full w-full bg-white/60 backdrop-blur-[1px] rounded-xl p-6 shadow-sm border border-gray-100
                   group-hover:bg-white/80 transition-colors duration-300"
      >
        <div className="flex flex-col h-full">
          {/* Header section */}
          <div className="space-y-2">
            <h2 className="text-lg font-semibold text-gray-900">Start Class</h2>
            <p className="text-sm text-gray-500">Begin your learning journey</p>
          </div>

          {/* Middle content area */}
          <div className="flex-1 flex items-center justify-center">
            <div className="w-16 h-16 bg-black/5 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-black rounded-lg rotate-12 transition-transform group-hover:rotate-0" />
            </div>
          </div>

          {/* Transparent overlay div - bottom right */}
          <div className="absolute bottom-0 right-0 w-14 h-14 bg-gray-100/80 backdrop-blur-[1px] rounded-tl-3xl" />

          {/* Action button - bottom right */}
          <div
            className="absolute bottom-1 right-1"
            onMouseEnter={() => {
              setIsHovered(true);
              console.log("[startClass] Hover state changed:", true);
            }}
            onMouseLeave={() => {
              setIsHovered(false);
              console.log("[startClass] Hover state changed:", false);
            }}
          >
            <div className="absolute inset-0 bg-black rounded-xl" />
            <button
              className="relative w-10 h-10 bg-black rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center justify-center overflow-hidden group"
              onClick={(e) => {
                e.stopPropagation();
                console.log(
                  "[startClass] Navigating to /virtualClassroom (button)"
                );
                router.push("/virtualClassroom");
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-black transition-opacity duration-300 group-hover:opacity-100" />
              <MoveUpRight className="relative w-5 h-5 text-white transition-transform duration-300 group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
            </button>
          </div>
        </div>

        {/* Subtle grid pattern overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.1)_1px,transparent_1px)] bg-[size:20px_20px] [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black_100%)] pointer-events-none rounded-xl" />
      </div>
    </div>
  );
};

export default StartClass;
