"use client";
import React from "react";
import { useUser } from "@clerk/nextjs";
import { AlignJustify, Smartphone } from "lucide-react";
import { Manrop<PERSON>, Newsreader } from "next/font/google";

// Font configuration
const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
});

const newsreader = Newsreader({
  subsets: ["latin"],
  weight: ["200", "400", "500"],
  style: ["normal", "italic"],
  display: "swap",
  variable: "--font-newsreader",
});

const WelcomeMessage: React.FC = () => {
  const { user } = useUser();

  const nameToDisplay = user?.firstName || "Guest";
  const fullMessage = `Your tasks for today, ${nameToDisplay}`;

  const currentCommaIndexInFullMessage = fullMessage.indexOf(",");
  let displayGreetingPart = fullMessage;
  let displayNamePart = "";

  if (currentCommaIndexInFullMessage !== -1) {
    displayGreetingPart = fullMessage.substring(
      0,
      currentCommaIndexInFullMessage + 1
    );
    if (fullMessage.length > currentCommaIndexInFullMessage + 1) {
      displayNamePart = fullMessage.substring(
        currentCommaIndexInFullMessage + 2
      );
    }
  }

  return (
    <div
      className={`relative flex flex-col h-full justify-end ${manrope.variable} ${newsreader.variable} font-sans`}
    >
      <style jsx>{`
        @keyframes subtleGlow {
          0% {
            opacity: 0.7;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0.7;
          }
        }
        .shooting-star::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 0;
          height: 1px;
          background: linear-gradient(
            90deg,
            rgba(26, 26, 24, 0) 0%,
            rgba(26, 26, 24, 1) 50%,
            rgba(26, 26, 24, 0) 100%
          );
          opacity: 0;
          animation: expandLine 3s ease-in-out forwards;
        }
        @keyframes expandLine {
          0% {
            width: 0%;
            opacity: 0;
          }
          20% {
            opacity: 1;
          }
          100% {
            width: 100%;
            opacity: 0.5;
          }
        }
      `}</style>

      <div className="flex items-center justify-between w-full py-4">
        <div className="relative pb-2 shooting-star">
          <h1 className="text-4xl font-extralight tracking-tight h-[44px] flex items-center">
            <span className="text-gray-500 font-serif">
              {displayGreetingPart}
            </span>
            {displayNamePart && (
              <span className="font-canva-sans font-normal text-[#1a1a18] ml-2">
                {displayNamePart}
              </span>
            )}
          </h1>
        </div>

        <div className="flex items-center gap-6">
          <AlignJustify className="h-10 w-6 text-[#1a1a18] transition-opacity hover:opacity-60" />

          <button className="flex items-center justify-center h-10 gap-x-2 bg-[#1a1a18] rounded-full p-4 text-white transition-transform hover:scale-105 shadow-sm">
            <Smartphone className="h-5 w-5" />
            <span className="text-sm font-medium">App</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default WelcomeMessage;
