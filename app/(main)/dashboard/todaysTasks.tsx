"use client";

import React, { useContext, useEffect, useState } from "react";
import { EventContext } from "@/app/(main)/schedule/EventContext";
import {
  Clock,
  Calendar,
  Info,
  Users,
  MapPin,
  ChevronRight,
  List,
  PlusCircle,
} from "lucide-react";
import { Button } from "@/components/shadcn-ui/button";
import { Card, CardContent } from "@/components/shadcn-ui/card";
import { Motion } from "@/components/motion-wrapper";
import { Manrope, Newsreader } from "next/font/google";

// Font configuration
const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
});

const newsreader = Newsreader({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-newsreader",
});

interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  googleEventId?: string;
  calendarLink?: string;
  recurrenceRule?: string;
  location?: string;
  attendees?: number;
  status?: "upcoming" | "in-progress" | "completed";
  progress?: number;
}

// A new type for events after the progress property has been added
interface EventWithProgress extends CalendarEvent {
  progress: number;
}

interface TodaysTasksProps {
  events?: CalendarEvent[];
  loading?: boolean;
}

// A reusable component to render each column with two dashed slots
const StatusColumn = ({
  title,
  events,
  children,
}: {
  title: string;
  events: EventWithProgress[];
  children: React.ReactNode;
}) => {
  const numChildren = React.Children.count(children);

  return (
    <div className="bg-gray-50 rounded-2xl p-4 flex flex-col min-h-0">
      <div className="flex justify-between items-center mb-4 flex-shrink-0">
        <h3
          className={`font-semibold text-[#1a1a18] ${manrope.variable} font-sans`}
        >
          {title}
        </h3>
        <span className="bg-[#1a1a18] text-white text-sm font-bold w-6 h-6 rounded-full flex items-center justify-center">
          {events.length}
        </span>
      </div>
      <div className="flex-1 flex flex-col gap-4 overflow-hidden">
        {/* MODIFICATION: Number of rows is set to 2 */}
        {Array.from({ length: 2 }).map((_, index) => {
          const card = React.Children.toArray(children)[index];
          const isFirstEmptyToDoSlot =
            title === "To Do" && index === numChildren;

          return (
            <div
              key={index}
              // REVERTED: Styling is back to the original version
              className={`group border-2 border-dashed rounded-xl flex-1 min-h-0 transition-colors duration-200 ${
                isFirstEmptyToDoSlot
                  ? "border-[#1a1a18] cursor-pointer"
                  : "border-gray-200"
              }`}
            >
              {card ? (
                <div className="h-full w-full p-1">{card}</div>
              ) : isFirstEmptyToDoSlot ? (
                <div className="h-full w-full flex items-center justify-center">
                  {/* REVERTED: Icon styling is back to original */}
                  <PlusCircle className="w-8 h-8 text-[#1a1a18] transition-colors duration-200" />
                </div>
              ) : (
                <div className="h-full w-full" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const TodaysTasks = (props: TodaysTasksProps) => {
  const { events = [], loading } = props;
  const { events: contextEvents, loading: contextLoading } =
    useContext(EventContext);
  const [expandedEventId, setExpandedEventId] = useState<number | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDate(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    }).format(date);
  };

  const formatCardDate = (dateString?: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(date);
  };

  const finalEvents = contextEvents.length ? contextEvents : events;
  const finalLoading = contextLoading || loading;

  const todayStr = currentDate.toDateString();
  const todaysEvents: EventWithProgress[] = finalEvents
    .filter((event: CalendarEvent) => {
      if (!event.startTime) return false;
      return new Date(event.startTime).toDateString() === todayStr;
    })
    .map((event) => ({
      ...event,
      progress: Math.floor(Math.random() * 80) + 10,
    }));

  const getEventStatus = (
    event: CalendarEvent
  ): "upcoming" | "in-progress" | "completed" => {
    const now = new Date();
    const startTime = event.startTime ? new Date(event.startTime) : null;
    const endTime = event.endTime ? new Date(event.endTime) : null;

    if (!startTime || !endTime) return "upcoming";
    if (now < startTime) return "upcoming";
    if (now > endTime) return "completed";
    return "in-progress";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-emerald-600";
      default:
        return "text-gray-400";
    }
  };

  const getTimeUntil = (startTime: string) => {
    const now = new Date();
    const start = new Date(startTime);
    const diff = start.getTime() - now.getTime();
    if (diff < 0) return "";
    const minutes = Math.round(diff / 60000);
    if (minutes === 0) return "Starting now";
    if (minutes < 60) return `In ${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remMinutes = minutes % 60;
    return `In ${hours}h ${remMinutes}m`;
  };

  const todoEvents = todaysEvents.filter(
    (event) => getEventStatus(event) === "upcoming"
  );
  const inProgressEvents = todaysEvents.filter(
    (event) => getEventStatus(event) === "in-progress"
  );
  const completedEvents = todaysEvents.filter(
    (event) => getEventStatus(event) === "completed"
  );

  const renderEventCard = (event: EventWithProgress) => {
    const status = getEventStatus(event);
    const isExpanded = event.id === expandedEventId;
    const progress = status === "completed" ? 100 : event.progress;

    return (
      <Motion.div
        key={event.id || Math.random()}
        layout
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="group h-full w-full"
      >
        <Card
          className={`relative bg-white border border-gray-300 overflow-hidden transition-all duration-300 rounded-lg h-full flex flex-col ${
            isExpanded
              ? "ring-2 ring-gray-500"
              : "hover:ring-1 hover:ring-gray-400"
          }`}
        >
          <span
            className={`absolute inset-y-0 left-0 w-1.5 rounded-l-md ${
              status === "in-progress"
                ? "bg-blue-500"
                : status === "completed"
                ? "bg-green-500"
                : "bg-sky-500"
            }`}
          ></span>
          <CardContent className="p-3 pl-4 flex-1 flex flex-col min-h-0">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1 pr-2">
                <h3
                  className={`text-base font-semibold text-[#1a1a18] truncate leading-tight ${manrope.variable} font-sans`}
                >
                  {event.title}
                </h3>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="w-7 h-7 -mr-1 -mt-1 text-gray-500 hover:text-[#1a1a18] flex-shrink-0"
                onClick={() =>
                  setExpandedEventId(isExpanded ? null : event.id || null)
                }
              >
                <ChevronRight
                  className={`w-4 h-4 transition-transform duration-200 ${
                    isExpanded ? "rotate-90" : ""
                  }`}
                />
              </Button>
            </div>

            <div className="my-auto space-y-1.5">
              <div className="flex justify-between items-center text-xs">
                <div className="flex items-center text-gray-500 font-medium">
                  <span className={`${newsreader.variable} font-serif`}>
                    Progress
                  </span>
                </div>
                <span
                  className={`font-mono text-gray-600 ${manrope.variable} font-sans`}
                >
                  {Math.round(progress / 10)}/10
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-emerald-400 h-1.5 rounded-full transition-all duration-500"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>

            <Motion.div
              animate={{ height: isExpanded ? "auto" : 0 }}
              className="overflow-hidden"
              transition={{ duration: 0.3 }}
            >
              <div
                className={`space-y-2 pt-2 text-xs border-t border-gray-200 mt-2 ${newsreader.variable} font-serif`}
              >
                {event.location && (
                  <div className="flex items-start space-x-2 text-gray-500">
                    <MapPin className="w-3.5 h-3.5 mt-0.5 flex-shrink-0" />
                    <p>{event.location}</p>
                  </div>
                )}
              </div>
            </Motion.div>

            <div className="mt-auto pt-2 flex justify-between items-center">
              <div
                className={`bg-red-50 text-red-600 text-xs font-semibold px-2 py-1 rounded-md ${manrope.variable} font-sans`}
              >
                {formatCardDate(event.startTime)}
              </div>
              <p
                className={`${getStatusColor(
                  status
                )} text-xs capitalize font-semibold ${
                  newsreader.variable
                } font-serif`}
              >
                {status}
              </p>
            </div>
          </CardContent>
        </Card>
      </Motion.div>
    );
  };

  return (
    <div
      className={`w-full h-full flex flex-col text-gray-400 ${manrope.variable} ${newsreader.variable} font-sans`}
    >
      <div className="mb-6 flex-shrink-0">
        <div className="flex items-center justify-between mb-2"></div>
        <p
          className={`text-sm text-gray-500 font-medium lining-nums tabular-nums ${newsreader.variable} font-serif`}
        >
          {formatDate(currentDate)}
        </p>
      </div>
      {finalLoading ? (
        <div className="flex-1 flex items-center justify-center h-full bg-gray-100 rounded-xl">
          <div className="animate-pulse flex space-x-4">
            <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
            <div className="space-y-3">
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
              <div className="h-4 w-24 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4 min-h-0">
          <StatusColumn title="To Do" events={todoEvents}>
            {todoEvents.map(renderEventCard)}
          </StatusColumn>

          <StatusColumn title="In Progress" events={inProgressEvents}>
            {inProgressEvents.map(renderEventCard)}
          </StatusColumn>

          <StatusColumn title="Completed" events={completedEvents}>
            {completedEvents.map(renderEventCard)}
          </StatusColumn>
        </div>
      )}
    </div>
  );
};

export default TodaysTasks;
