import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { memorizationSessions, memorizedSurahs } from "@/db/schema";
import { eq } from "drizzle-orm";
import SurahGrid from "./SurahGrid";

// Enhanced type to include englishNameTranslation
export type Surah = {
  number: number;
  name: string;
  englishName: string;
  englishNameTranslation: string; // <--- ADDED
  revelationPlace: string;
  numberOfAyahs: number;
  status?: "completed" | "inProgress" | "notStarted";
};

const SurahGridPage = async () => {
  // 1) Check authentication
  const { userId } = await auth();
  if (!userId) {
    redirect("/");
  }

  // 2) Fetch Surah metadata from AlQuranCloud (smaller endpoint)
  const response = await fetch("https://api.alquran.cloud/v1/surah", {
    cache: "no-store", // or any caching strategy you prefer
  });
  if (!response.ok) {
    throw new Error("Failed to fetch Surah metadata from AlQuranCloud");
  }
  const apiData = await response.json();
  const apiSurahs = apiData?.data || [];

  // 3) Fetch user's memorization data from your local DB
  const inProgressSessions = await db.query.memorizationSessions.findMany({
    where: eq(memorizationSessions.userId, userId),
    columns: {
      surahNumber: true,
      // ...any other columns you need...
    },
  });

  const completedSurahs = await db.query.memorizedSurahs.findMany({
    where: eq(memorizedSurahs.userId, userId),
    columns: {
      surahNumber: true,
    },
  });

  // Create sets for quick lookup
  const inProgressSet = new Set<number>(
    inProgressSessions.map((item) => item.surahNumber)
  );
  const completedSet = new Set<number>(
    completedSurahs.map((item) => item.surahNumber)
  );

  // 4) Map AlQuranCloud data + memorization status
  const surahsWithStatus: Surah[] = apiSurahs.map((s: any) => {
    const num = s.number;
    let status: Surah["status"] = "notStarted";
    if (completedSet.has(num)) {
      status = "completed";
    } else if (inProgressSet.has(num)) {
      status = "inProgress";
    }

    return {
      number: s.number,
      name: s.name, // Arabic name
      englishName: s.englishName,
      englishNameTranslation: s.englishNameTranslation, // <--- ADDED
      revelationPlace: s.revelationType, // e.g. "Meccan"/"Medinan"
      numberOfAyahs: s.numberOfAyahs,
      status,
    };
  });

  // 5) Render the grid with the new Surah array
  return <SurahGrid surahs={surahsWithStatus} />;
};

export default SurahGridPage;
