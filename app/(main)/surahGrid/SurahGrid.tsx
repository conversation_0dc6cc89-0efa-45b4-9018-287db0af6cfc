// app/(main)/surahGrid/SurahGrid.tsx

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import type { Surah } from "./page"; // or wherever Surah type is exported

// Extended type to include Juz information
type SurahWithJuz = Surah & {
  juzNumber?: number[];
};

// Type for Juz data structure
type JuzData = {
  number: number;
  surahs: SurahWithJuz[];
};

type SurahGridProps = {
  surahs: Surah[];
};

const SurahGrid: React.FC<SurahGridProps> = ({ surahs }) => {
  const router = useRouter();
  // Changed the default activeTab to "juz"
  const [activeTab, setActiveTab] = useState("juz");
  const [selectedSurah, setSelectedSurah] = useState<Surah | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [hoveredSurah, setHoveredSurah] = useState<{
    surahNumber: number;
    juzNumber?: number;
  } | null>(null);
  const [juzData, setJuzData] = useState<JuzData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Function to fetch and organize Juz data
  const fetchJuzData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Create an array to hold all 30 juz
      const juzArray: JuzData[] = Array.from({ length: 30 }, (_, i) => ({
        number: i + 1,
        surahs: [],
      }));

      // This is a map of which Surahs belong to which Juz
      // Based on the standard Quran arrangement
      const juzSurahMap = [
        // Juz 1: Surah 1:1 -> Surah 2:141
        [1, 2],
        // Juz 2: Surah 2:142 -> Surah 2:252
        [2],
        // Juz 3: Surah 2:253 -> Surah 3:92
        [2, 3],
        // Juz 4: Surah 3:93 -> Surah 4:23
        [3, 4],
        // Juz 5: Surah 4:24 -> Surah 4:147
        [4],
        // Juz 6: Surah 4:148 -> Surah 5:81
        [4, 5],
        // Juz 7: Surah 5:82 -> Surah 6:110
        [5, 6],
        // Juz 8: Surah 6:111 -> Surah 7:87
        [6, 7],
        // Juz 9: Surah 7:88 -> Surah 8:40
        [7, 8],
        // Juz 10: Surah 8:41 -> Surah 9:92
        [8, 9],
        // Juz 11: Surah 9:93 -> Surah 11:5
        [9, 10, 11], // Contains all of Surah 10
        // Juz 12: Surah 11:6 -> Surah 12:52
        [11, 12],
        // Juz 13: Surah 12:53 -> Surah 14:52
        [12, 13, 14], // Contains all of Surah 13
        // Juz 14: Surah 15:1 -> Surah 16:128
        [15, 16],
        // Juz 15: Surah 17:1 -> Surah 18:74
        [17, 18],
        // Juz 16: Surah 18:75 -> Surah 20:135
        [18, 19, 20], // Contains all of Surah 19
        // Juz 17: Surah 21:1 -> Surah 22:78
        [21, 22],
        // Juz 18: Surah 23:1 -> Surah 25:20
        [23, 24, 25], // Contains all of Surah 24
        // Juz 19: Surah 25:21 -> Surah 27:55
        [25, 26, 27], // Contains all of Surah 26
        // Juz 20: Surah 27:56 -> Surah 29:45
        [27, 28, 29], // Contains all of Surah 28
        // Juz 21: Surah 29:46 -> Surah 33:30
        [29, 30, 31, 32, 33], // Contains all of Surahs 30, 31, 32
        // Juz 22: Surah 33:31 -> Surah 36:27
        [33, 34, 35, 36], // Contains all of Surahs 34, 35
        // Juz 23: Surah 36:28 -> Surah 39:31
        [36, 37, 38, 39], // Contains all of Surahs 37, 38
        // Juz 24: Surah 39:32 -> Surah 41:46
        [39, 40, 41], // Contains all of Surah 40
        // Juz 25: Surah 41:47 -> Surah 45:37
        [41, 42, 43, 44, 45], // Contains all of Surahs 42, 43, 44
        // Juz 26: Surah 46:1 -> Surah 51:30
        [46, 47, 48, 49, 50, 51], // Contains all of Surahs 47, 48, 49, 50
        // Juz 27: Surah 51:31 -> Surah 57:29
        [51, 52, 53, 54, 55, 56, 57], // Contains all of Surahs 52, 53, 54, 55, 56
        // Juz 28: Surah 58:1 -> Surah 66:12
        [58, 59, 60, 61, 62, 63, 64, 65, 66], // Contains all of these Surahs
        // Juz 29: Surah 67:1 -> Surah 77:50
        [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77], // Contains all of these Surahs
        // Juz 30: Surah 78:1 -> Surah 114:6
        [
          78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94,
          95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109,
          110, 111, 112, 113, 114,
        ], // Contains all of these Surahs
      ];

      // Map surahs to their respective juz
      juzSurahMap.forEach((surahNumbers, juzIndex) => {
        const juzNumber = juzIndex + 1;

        // Filter the original surahs array to find those in this juz
        const surahsInThisJuz = surahs.filter((s) =>
          surahNumbers.includes(s.number)
        );

        // Add these surahs to the juz
        // Check if juzArray[juzIndex] exists before assigning
        if (juzArray[juzIndex]) {
          juzArray[juzIndex].surahs = surahsInThisJuz.map((surah) => ({
            ...surah,
            juzNumber: [juzNumber],
          }));
        } else {
          console.warn(`Juz index ${juzIndex} out of bounds for juzArray`);
        }
      });

      setJuzData(juzArray);
    } catch (error) {
      console.error("Error organizing Juz data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [surahs]);

  // Fetch Juz data when tab changes to "juz"
  useEffect(() => {
    // This useEffect will now trigger fetchJuzData on initial mount
    // because activeTab is initialized to "juz".
    if (activeTab === "juz" && juzData.length === 0) {
      fetchJuzData();
    }
  }, [activeTab, juzData.length, fetchJuzData]);

  const handleSurahClick = (surah: Surah) => {
    // If surah.status is "inProgress", ask user to continue or start fresh
    if (surah.status === "inProgress") {
      setSelectedSurah(surah);
      setShowModal(true);
    } else {
      // "completed" or "notStarted" just starts fresh
      startMemorization(
        surah.number,
        "startFresh",
        surah.numberOfAyahs,
        surah.englishName
      );
    }
  };

  const startMemorization = (
    surahNumber: number,
    mode: "continue" | "startFresh",
    numberOfAyahs: number,
    englishName: string
  ) => {
    // Original placeholder logic for versesRange
    const versesRange = mode === "continue" ? "1-50" : `1-${numberOfAyahs}`;
    // Build the query string referencing surahNumber
    const url = `/memorization?surahNumber=${encodeURIComponent(
      surahNumber
    )}&versesRange=${encodeURIComponent(
      versesRange
    )}&mode=${mode}&surahName=${encodeURIComponent(englishName)}`;
    router.push(url);
    setShowModal(false); // Close modal after navigation starts
  };

  const handleUserChoice = (choice: "continue" | "startFresh") => {
    if (selectedSurah) {
      startMemorization(
        selectedSurah.number,
        choice,
        selectedSurah.numberOfAyahs,
        selectedSurah.englishName
      );
    }
    // setShowModal(false); // Now handled in startMemorization
  };

  // For reading Juz functionality
  const handleReadJuz = (juzNumber: number) => {
    router.push(`/read-juz/${juzNumber}`);
  };

  // Original background color effect logic
  const getStatusBgColor = (status?: string, isHovered?: boolean) => {
    if (isHovered) {
      switch (status) {
        case "completed":
          return "bg-emerald-500";
        case "inProgress":
          return "bg-amber-500";
        default:
          return "bg-black"; // Original default hover color
      }
    } else {
      switch (status) {
        case "completed":
          return "bg-emerald-50";
        case "inProgress":
          return "bg-amber-50";
        default:
          return "bg-gray-50"; // Original default non-hover color
      }
    }
  };

  // Original border effect logic
  const getStatusBorderColor = (status?: string) => {
    switch (status) {
      case "completed":
        return "border-emerald-500";
      case "inProgress":
        return "border-amber-500";
      default:
        return "border-gray-200";
    }
  };

  // Render a single Surah card (reverted to original simpler style)
  const renderSurahCard = (surah: SurahWithJuz, juzNumber?: number) => {
    const isHovered =
      hoveredSurah?.surahNumber === surah.number &&
      (hoveredSurah.juzNumber === juzNumber ||
        (hoveredSurah.juzNumber === undefined && juzNumber === undefined));
    const statusBorderColor = getStatusBorderColor(surah.status);

    // Use a more compact layout for Juz view
    const isJuzView = activeTab === "juz";

    // --- Reverted Hover Styles ---
    return (
      <div
        key={`${surah.number}-${juzNumber || "surahView"}`}
        onClick={() => handleSurahClick(surah)}
        onMouseEnter={() =>
          setHoveredSurah({ surahNumber: surah.number, juzNumber })
        }
        onMouseLeave={() => setHoveredSurah(null)}
        className={`
          group relative bg-white border rounded-xl ${isJuzView ? "p-3" : "p-4"}
          transition-all duration-300 ease-out cursor-pointer
          hover:shadow-md hover:scale-[1.02]
          ${statusBorderColor}
        `}
        // NOTE: The linter warning about 'hover' conflict on the line above is likely a false positive
        // as multiple different hover effects are being applied, which is valid.
      >
        <div className="flex items-center">
          {/* Number Badge */}
          <div
            className={`relative flex-shrink-0 mr-3 ${
              isJuzView ? "w-8 h-8" : "w-10 h-10"
            }`}
          >
            {/* Reverted Badge Animation */}
            <div
              className={`
                absolute inset-0 rounded-lg
                ${getStatusBgColor(surah.status, isHovered)}
                transition-transform duration-300 delay-200
                ${isHovered ? "rotate-45" : ""}
                group-hover:rotate-45
              `}
            />
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              {/* Reverted Text Color Logic */}
              <span
                className={`
                  font-bold ${
                    isJuzView ? "text-sm" : "text-base"
                  } transition-colors duration-200
                  ${isHovered ? "text-white" : "text-gray-900"}
                  group-hover:text-white
                `}
              >
                {surah.number}
              </span>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start">
              <div>
                {/* Reverted Text Color Logic */}
                <h3
                  className={`${
                    isJuzView ? "text-xs" : "text-sm"
                  } font-semibold text-gray-900 mb-1 group-hover:text-gray-900`} // Ensure text remains dark unless badge hovered
                >
                  {surah.englishName}
                </h3>
                <p
                  className={`${
                    isJuzView ? "text-xs" : "text-xs"
                  } text-gray-500 group-hover:text-gray-500`} // Ensure text remains gray unless badge hovered
                >
                  {surah.englishNameTranslation}
                </p>
              </div>
              <div className="text-right flex-shrink-0 pl-2">
                {/* Reverted Text Color Logic */}
                {/* Ensured 'block' class appears only once for linter */}
                <span
                  className={`${
                    isJuzView ? "text-sm" : "text-base"
                  } font-semibold text-gray-800 font-arabic block group-hover:text-gray-800`} // Ensure text remains dark unless badge hovered
                >
                  {surah.name}
                </span>
                <p className="text-xs text-gray-500 group-hover:text-gray-500">
                  {" "}
                  {/* Ensure text remains gray unless badge hovered */}
                  {surah.numberOfAyahs} Ayahs
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render the Surah view (using the reverted card style)
  const renderSurahView = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {surahs.map((surah) => renderSurahCard(surah, undefined))}
    </div>
  );

  // Render the Juz view (three column layout)
  const renderJuzView = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center py-20">
          {/* Original Loading Spinner Style */}
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
          <span className="ml-2 text-gray-700">Loading Juz data...</span>
        </div>
      );
    }

    if (!juzData || juzData.length === 0) {
      return (
        <div className="text-center py-10 text-gray-500">
          No Juz data available. Try switching tabs or reloading.
        </div>
      );
    }

    // --- UPDATED SLICE LOGIC for desired 19-9-2 split ---
    // Remember slice(start, end) extracts up to, but not including, end.
    // Indices: 0-18 (Juz 1-19), 19-27 (Juz 20-28), 28-29 (Juz 29-30)
    const leftColumnJuz = juzData.slice(0, 19);
    const middleColumnJuz = juzData.slice(19, 28);
    const rightColumnJuz = juzData.slice(28, 30); // or simply slice(28) works too
    // --- END UPDATE ---

    // Function to render a single Juz section
    const renderJuzSection = (juz: JuzData) => {
      if (!juz || !juz.surahs) {
        console.warn("Invalid Juz data detected:", juz);
        return null;
      }
      // --- Reverted Juz Section Style ---
      return (
        <div key={juz.number} className="bg-gray-50 rounded-xl p-4 mb-4">
          {/* Juz Header */}
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-base font-semibold text-gray-900">
              Juz {juz.number}
            </h2>
            {/* Changed to a span for unclickable text */}
            <span
              className="text-xs text-gray-600" // Kept base text styles, removed hover/transition
            >
              Read Juz
            </span>
          </div>

          {/* Juz Surahs */}
          <div className="space-y-3">
            {juz.surahs.length > 0 ? (
              juz.surahs.map((surah) => renderSurahCard(surah, juz.number))
            ) : (
              <p className="text-xs text-gray-400 text-center py-2">
                No Surahs listed for this Juz.
              </p>
            )}
          </div>
        </div>
      );
    };

    // --- Reverted Grid Gap ---
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* --- UPDATED Column Comments --- */}
        {/* Left Column - Juz 1-19 */}
        <div className="space-y-4">{leftColumnJuz.map(renderJuzSection)}</div>

        {/* Middle Column - Juz 20-28 */}
        <div className="space-y-4">{middleColumnJuz.map(renderJuzSection)}</div>

        {/* Right Column - Juz 29-30 */}
        <div className="space-y-4">{rightColumnJuz.map(renderJuzSection)}</div>
        {/* --- END UPDATE --- */}
      </div>
    );
  };

  // --- Main Component Return ---
  return (
    <div
      className="flex flex-col h-screen text-sm"
      // --- Reverted Background Pattern Style ---
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Header Section - Reverted Style */}
      <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          {/* Original Back Button Style */}
          <button
            onClick={() => router.push("/challenges")} // Adjust route if needed
            className="p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
          </button>
          {/* Title - Centered */}
          <h1 className="text-lg font-bold text-neutral-700 whitespace-nowrap">
            Hifz Exercises
          </h1>

          {/* Original Controls Style */}
          <div className="flex items-center space-x-3">
            <nav className="flex space-x-1 border rounded-lg bg-white p-0.5">
              {/* Swapped order of Juz and Surah buttons */}
              <button
                onClick={() => setActiveTab("juz")}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === "juz"
                    ? "bg-black text-gray-100"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Juz
              </button>
              <button
                onClick={() => setActiveTab("surah")}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === "surah"
                    ? "bg-black text-gray-100"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Surah
              </button>
            </nav>
          </div>
        </div>
      </div>

      {/* Main Content Area - Reverted Style */}
      <div className="flex-1 overflow-auto px-3 py-6 md:px-6">
        <div className="max-w-7xl mx-auto">
          {activeTab === "surah" ? renderSurahView() : renderJuzView()}
        </div>
      </div>

      {/* Modal - Reverted Style */}
      {showModal && selectedSurah && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900/40 backdrop-blur-sm z-50">
          {/* Original Modal Box Style */}
          <div className="bg-white rounded-xl shadow-lg p-5 w-full max-w-sm mx-3 transform transition-all duration-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Continue Your Progress {/* Or original title */}
            </h2>
            <div className="w-12 h-px bg-gray-200 mb-3"></div>
            <p className="text-sm text-gray-600 mb-5">
              You have existing progress in Surah {selectedSurah.englishName}.
              Would you like to continue where you left off or start fresh?
            </p>
            {/* Original Button Styles */}
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-white border border-gray-200 hover:bg-gray-50 text-gray-800 font-medium rounded-lg transition-colors duration-200"
                onClick={() => handleUserChoice("startFresh")}
              >
                Start Fresh
              </button>
              <button
                className="px-4 py-2 bg-black hover:bg-gray-800 text-white font-medium rounded-lg transition-colors duration-200"
                onClick={() => handleUserChoice("continue")}
              >
                Continue
              </button>
            </div>
            {/* Removed optional close button */}
          </div>
        </div>
      )}
    </div>
  );
};

export default SurahGrid;
