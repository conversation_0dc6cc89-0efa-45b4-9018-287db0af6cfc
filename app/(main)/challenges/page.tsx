"use client";
import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, ArrowLeft, <PERSON>, Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import React from "react";

interface Tool {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export default function Page() {
  const router = useRouter();
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  // Tools array with "Noorani Qaida" as first card
  const tools: Tool[] = [
    {
      id: 4,
      title: "Noorani Qaida",
      description:
        "Learn the basics of Quranic pronunciation and script through the Noorani Qaida.",
      icon: <Pencil size={24} />,
    },
    {
      id: 1,
      title: "Tajweed",
      description:
        "Master the art of Quranic recitation with proper pronunciation, rhythm, and melody.",
      icon: <Sun size={24} />,
    },
    {
      id: 2,
      title: "Hifz",
      description:
        "Memorize the Holy Quran with structured techniques and personalized guidance.",
      icon: <Brain size={24} />,
    },
    {
      id: 3,
      title: "<PERSON><PERSON><PERSON>",
      description:
        "Explore the meanings and interpretations of the Quran with scholarly explanations.",
      icon: <BookOpen size={24} />,
    },
  ];

  return (
    <main
      className="min-h-screen"
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Sticky Header with back arrow and centered title */}
      <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
        <div className="relative max-w-7xl mx-auto">
          <Link
            href="/learning-path"
            className="absolute left-0 top-1/2 -translate-y-1/2 p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
          </Link>
          <h1 className="text-lg font-bold text-gray-900 text-center">
            Challenges
          </h1>
        </div>
      </div>

      {/* Body Content */}
      <div className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Exercises using the latest AI models
          </h2>

          {/* Grid to hold 4 cards in one row on medium+ screens */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 justify-items-center">
            {tools.map((tool) => (
              <div
                key={tool.id}
                className="group w-full max-w-md"
                onClick={() => {
                  if (tool.id === 4) {
                    router.push("/courses");
                  } else if (tool.id === 1) {
                    router.push("/tajweedGrid");
                  } else if (tool.id === 2) {
                    router.push("/surahGrid");
                  }
                }}
                onMouseEnter={() => setHoveredCard(tool.id)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                <div
                  className="bg-white border border-gray-200 rounded-xl p-5
                  h-72 flex flex-col transition-all duration-300
                  hover:shadow-lg group-hover:border-gray-300 transform
                  hover:scale-105 cursor-pointer"
                >
                  <div className="flex items-center mb-3">
                    <div
                      className={`
                        flex items-center justify-center w-10 h-10 rounded-full
                        ${
                          hoveredCard === tool.id
                            ? "bg-black text-white"
                            : "bg-gray-100 text-gray-600 group-hover:bg-black group-hover:text-white"
                        }
                        transition-colors duration-300
                      `}
                    >
                      {tool.icon}
                    </div>
                    <h3 className="text-lg font-semibold ml-3 text-black group-hover:text-gray-800">
                      {tool.title}
                    </h3>
                  </div>

                  <p className="text-gray-500 text-xs sm:text-sm flex-grow line-clamp-3 sm:line-clamp-4">
                    {tool.description}
                  </p>

                  <div className="mt-3 flex items-center text-gray-400 group-hover:text-black">
                    <span className="text-xs sm:text-sm">Explore Tool</span>
                    <svg
                      className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
