"use client";
import { cn } from "@/lib/utils"; // Make sure this path is correct if not in the same dir
import React, { useEffect, useRef, useState, useCallback } from "react";
import { createNoise3D, NoiseFunction3D } from "simplex-noise";

// It's good practice to define types for props
interface WavyBackgroundProps {
  children?: React.ReactNode;
  className?: string;
  containerClassName?: string;
  colors?: string[];
  waveWidth?: number;
  backgroundFill?: string;
  blur?: number;
  speed?: "slow" | "fast";
  waveOpacity?: number;
  [key: string]: any;
}

export const WavyBackground = ({
  children,
  className,
  containerClassName,
  colors: propColors, // Renamed to avoid conflict with 'colors' variable in render logic
  waveWidth,
  backgroundFill = "#ffffff", // Default to white as often used with light themes
  blur = 10,
  speed = "fast",
  waveOpacity = 0.5,
  ...props
}: WavyBackgroundProps) => {
  const noise3D: NoiseFunction3D = createNoise3D(); // Memoize if createNoise3D is expensive, but usually fine
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // These will be set based on actual canvas size
  let w: number, h: number;
  let nt: number = 0; // Animation time
  let animationId: number | null = null;
  let ctx: CanvasRenderingContext2D | null = null;

  const getSpeedValue = useCallback(() => {
    switch (speed) {
      case "slow":
        return 0.001;
      case "fast":
        return 0.002;
      default:
        return 0.001;
    }
  }, [speed]);

  // Default wave colors if none are provided via props
  const defaultWaveColors = [
    "#1a1a1a",
    "#333333",
    "#4d4d4d",
    "#666666",
    "#808080",
  ];
  const currentWaveColors = propColors ?? defaultWaveColors;

  const drawWave = (numWaves: number) => {
    if (!ctx || w === 0 || h === 0) return;
    nt += getSpeedValue();
    for (let i = 0; i < numWaves; i++) {
      ctx.beginPath();
      ctx.lineWidth = waveWidth || 50;
      ctx.strokeStyle = currentWaveColors[i % currentWaveColors.length];
      for (let x = 0; x < w; x += 5) {
        // Noise function for y value, scaled and centered
        const yNoise = noise3D(x / 800, 0.3 * i, nt) * (h * 0.2); // Adjust amplitude based on height
        ctx.lineTo(x, yNoise + h * 0.5); // Center waves vertically within the canvas height 'h'
      }
      ctx.stroke();
      ctx.closePath();
    }
  };

  const renderCanvas = useCallback(() => {
    if (!ctx || w === 0 || h === 0) {
      // If canvas not ready, request next frame and try again
      animationId = requestAnimationFrame(renderCanvas);
      return;
    }
    ctx.fillStyle = backgroundFill;
    ctx.globalAlpha = 1; // Fill background with full opacity first
    ctx.fillRect(0, 0, w, h);

    ctx.globalAlpha = waveOpacity; // Then set opacity for waves
    drawWave(5); // Draw 5 waves

    animationId = requestAnimationFrame(renderCanvas);
  }, [
    backgroundFill,
    waveOpacity,
    blur,
    getSpeedValue,
    currentWaveColors,
    waveWidth,
    noise3D,
  ]); // Dependencies for renderCanvas

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // This function will be called by ResizeObserver and initially
    const updateCanvasDimensions = () => {
      if (!canvasRef.current) return;
      // Set drawing buffer dimensions to the actual display dimensions of the canvas
      w = canvasRef.current.offsetWidth;
      h = canvasRef.current.offsetHeight;
      canvasRef.current.width = w;
      canvasRef.current.height = h;

      // Re-initialize context if dimensions change (or first time)
      ctx = canvasRef.current.getContext("2d");
      if (ctx) {
        ctx.filter = `blur(${blur}px)`;
      }
    };

    updateCanvasDimensions(); // Initial setup

    const resizeObserver = new ResizeObserver(updateCanvasDimensions);
    resizeObserver.observe(canvas);

    // Start animation only after canvas is sized and context is ready
    if (ctx && w > 0 && h > 0) {
      // Clear any previous animation frame
      if (animationId !== null) {
        cancelAnimationFrame(animationId);
      }
      nt = 0; // Reset animation time
      renderCanvas();
    }

    return () => {
      resizeObserver.unobserve(canvas);
      if (animationId !== null) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    };
    // Re-run effect if these fundamental props change that affect initialization or rendering loop
  }, [blur, renderCanvas]); // renderCanvas callback itself changes if its dependencies change

  // Safari blur fix
  const [isSafari, setIsSafari] = useState(false);
  useEffect(() => {
    setIsSafari(
      typeof window !== "undefined" &&
        navigator.userAgent.includes("Safari") &&
        !navigator.userAgent.includes("Chrome")
    );
  }, []);

  return (
    <div
      className={cn(
        "relative flex items-center justify-center", // Removed h-screen, flex-col. Let containerClassName dictate size.
        containerClassName // e.g., "h-[180px] w-full"
      )}
    >
      <canvas
        className="absolute inset-0 z-0" // Stretches to fill parent
        ref={canvasRef}
        id="canvas" // Make sure id is unique if multiple instances, or remove if not needed
        style={{
          ...(isSafari ? { filter: `blur(${blur}px)` } : {}),
        }}
      ></canvas>
      {/* Children are rendered on top */}
      <div className={cn("relative z-10", className)} {...props}>
        {children}
      </div>
    </div>
  );
};
