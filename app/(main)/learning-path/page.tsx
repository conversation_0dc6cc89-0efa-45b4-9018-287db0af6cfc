"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Spark<PERSON>, NotebookText } from "lucide-react";

const LearningPathSelection: React.FC = () => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  return (
    <div
      className="min-h-screen"
      style={{
        backgroundColor: "#fdfdfd",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Main Content Container */}
      <div className="max-w-6xl mx-auto px-6 py-16">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-2xl sm:text-2xl font-bold text-[#1a1a18] mb-4">
            Choose Your Learning Path
          </h1>
          <p className="text-gray-600 text-base max-w-2xl mx-auto">
            Select your preferred learning method and begin your journey
          </p>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Learn Card */}
          <Link href="/curriculum">
            <div
              className="group bg-white border border-gray-200 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg group-hover:border-gray-300 transform hover:scale-105"
              onMouseEnter={() => setHoveredCard("learn")}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="p-6 sm:p-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl sm:text-xl font-semibold text-[#1a1a18]">
                    My courses
                  </h2>
                  <div
                    className={`
                      w-12 h-12 rounded-full flex items-center justify-center
                      transition-colors duration-300
                      ${
                        hoveredCard === "learn"
                          ? "bg-[#1a1a18] text-white"
                          : "bg-gray-100 text-gray-500"
                      }
                    `}
                  >
                    <NotebookText className="w-6 h-6" />
                  </div>
                </div>
                <p className="text-gray-500 mb-6">
                  Start your learning journey with structured courses and
                  interactive content.
                </p>
                <div
                  className={`
                    flex items-center transition-colors duration-200
                    ${
                      hoveredCard === "learn"
                        ? "text-[#1a1a18]"
                        : "text-gray-400"
                    }
                  `}
                >
                  <span className="text-sm font-medium">
                    Explore curriculum
                  </span>
                  <svg
                    className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </Link>

          {/* Challenges Card */}
          <Link href="/challenges">
            <div
              className="group bg-white border border-gray-200 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg group-hover:border-gray-300 transform hover:scale-105"
              onMouseEnter={() => setHoveredCard("Challenges")}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="p-6 sm:p-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl sm:text-xl font-semibold text-[#1a1a18]">
                    Challenges
                  </h2>
                  <div
                    className={`
                      w-12 h-12 rounded-full flex items-center justify-center
                      transition-colors duration-300
                      ${
                        hoveredCard === "Challenges"
                          ? "bg-[#1a1a18] text-white"
                          : "bg-gray-100 text-gray-500"
                      }
                    `}
                  >
                    <Sparkles className="w-6 h-6" />
                  </div>
                </div>
                <p className="text-gray-500 mb-6">
                  Enhance your memorization with AI-powered voice feedback using
                  Whisper AI.
                </p>
                <div
                  className={`
                    flex items-center transition-colors duration-200
                    ${
                      hoveredCard === "Challenges"
                        ? "text-[#1a1a18]"
                        : "text-gray-400"
                    }
                  `}
                >
                  <span className="text-sm font-medium">Start practicing</span>
                  <svg
                    className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LearningPathSelection;
