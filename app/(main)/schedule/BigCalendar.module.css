/* BigCalendar.module.css */

.calendarWrapper {
  /* MODIFIED: This is the key change. It tells this element to grow and fill the available flex space. */
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.calendarContainer {
  flex: 1 1 auto;
  min-height: 0;
  height: 100%; /* Added so the calendar fills its flexed parent */
}

.calendarWrapper :global(.fc) {
  font-family: -apple-system, BlinkMacSystemFont, "Inter", sans-serif;
}

.calendarWrapper :global(.fc-scrollgrid-sync-table) {
  min-height: 100%;
}

.calendarWrapper :global(.fc-toolbar-title) {
  font-size: 1.5rem !important;
  line-height: 2rem !important;
  font-weight: 600 !important;
  color: #111827;
  letter-spacing: -0.025em !important;
}

.calendarWrapper :global(.fc-button) {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  color: #111827 !important;
  font-weight: 500 !important;
  text-transform: capitalize !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* --- ENHANCED HOVER EFFECTS START --- */

/* Hover effect for a NON-SELECTED button (gray-100) */
.calendarWrapper :global(.fc-button:not(.fc-button-active):hover) {
  background-color: #eeeeee !important; /* Tailwind's gray-100 */
  border-color: #eeeeee !important;
  color: #111827 !important;
}

/* Hover effect for the ALREADY SELECTED button (#141414) */
.calendarWrapper :global(.fc-button-active:hover) {
  background-color: #141414 !important;
  border-color: #141414 !important;
  color: #ffffff !important;
}

/* --- ENHANCED HOVER EFFECTS END --- */

.calendarWrapper :global(.fc-button-active) {
  background-color: #000000 !important;
  border-color: #000000 !important;
  color: #ffffff !important;
}

.calendarWrapper :global(.fc-button:focus) {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(163, 163, 163, 0.5) !important;
}

.calendarWrapper :global(.fc-day) {
  transition: background-color 0.2s ease;
}

.calendarWrapper :global(.fc-day:hover) {
  background-color: rgba(249, 250, 251, 0.5);
}

.calendarWrapper :global(.fc-daygrid-day-number) {
  color: #374151;
  font-weight: 400;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.calendarWrapper :global(.fc-day-today) {
  background-color: rgba(243, 244, 246, 0.6) !important;
}

.calendarWrapper :global(.fc-day-today:hover) {
  background-color: rgba(229, 231, 235, 0.7) !important;
}

.calendarWrapper :global(.fc-day-today .fc-daygrid-day-number) {
  color: #111827;
  font-weight: 600;
}

.calendarWrapper :global(.fc th) {
  padding: 1rem 0 !important;
  font-weight: 500 !important;
  text-transform: capitalize !important;
  font-size: 0.813rem !important;
  letter-spacing: -0.01em !important;
  color: #6b7280 !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.calendarWrapper :global(.fc-header-toolbar) {
  padding: 0 0 1.5rem 0 !important;
  margin-bottom: 0 !important;
  border-bottom: none !important;
  background-color: transparent !important;
}

.calendarWrapper :global(.fc-view) {
  border: none !important;
  background-color: transparent !important;
}

.calendarWrapper :global(.fc td) {
  border-color: #f3f4f6 !important;
}

.calendarWrapper :global(.fc-event) {
  border: none !important;
  background: none !important;
}

.calendarWrapper :global(.fc-day-other .fc-daygrid-day-number) {
  color: #9ca3af;
  opacity: 0.7;
}
.calendarWrapper :global(.fc-day-other) {
  background-color: #f9fafb;
}

.calendarWrapper :global(.fc-scrollgrid) {
  border: none !important;
}

.calendarWrapper :global(.fc-toolbar-chunk) {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.calendarWrapper :global(.fc-direction-ltr .fc-button-group > .fc-button) {
  margin: 0 !important;
}
.calendarWrapper
  :global(.fc-direction-ltr .fc-button-group > .fc-button:not(:first-child)) {
  margin-left: -1px !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.calendarWrapper
  :global(.fc-direction-ltr .fc-button-group > .fc-button:not(:last-child)) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.calendarWrapper :global(.fc-button-group) {
  display: inline-flex;
  border-radius: 0.75rem;
  overflow: hidden;
}

/* --- TimeGrid Specific Styles --- */

.calendarWrapper :global(.fc-timegrid-slot) {
  border-bottom: 1px solid #e5e7eb !important;
}
.calendarWrapper :global(.fc .fc-timegrid-slot-minor) {
  border-bottom-style: dotted !important;
  border-color: #f3f4f6 !important;
}

.calendarWrapper :global(.fc-timegrid-slot-label) {
  font-size: 0.75rem !important;
  color: #6b7280 !important;
  padding: 0 0.5rem !important;
  vertical-align: top;
}

.calendarWrapper :global(.fc-timegrid-event) {
  background: transparent !important;
  border: none !important;
}

.calendarWrapper :global(.fc-timegrid-now-indicator-line) {
  border-color: #ef4444 !important;
  border-width: 2px !important;
  opacity: 1 !important;
  z-index: 35 !important;
}

.calendarWrapper :global(.fc-direction-ltr .fc-timegrid-now-indicator-arrow) {
  margin-top: -3px;
  border-color: transparent !important;
  border-left-color: #ef4444 !important;
  border-width: 6px 0 6px 7px !important;
  opacity: 1 !important;
  z-index: 35 !important;
}

.calendarWrapper :global(.fc-timegrid-now-indicator-dot) {
  display: none;
}

.calendarWrapper :global(.fc-timegrid-col-frame) {
  background-color: transparent !important;
}

/* --- Event Rendering Specific --- */
.calendarWrapper :global(.fc-daygrid-dot-event) {
  background: transparent !important;
  border: none !important;
}
.calendarWrapper :global(.fc-daygrid-event-dot) {
  border-color: #000000;
  background-color: #000000;
}

.calendarWrapper :global(.fc-timegrid-event .fc-event-main) {
  padding: 0 !important;
}

/* Loading state styles */
.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  background-color: #f9fafb;
}

.loadingSpinner {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.loadingDot {
  width: 1rem;
  height: 1rem;
  border-radius: 9999px;
  background-color: #e5e7eb;
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loadingDot:nth-child(1) {
  background-color: #e5e7eb;
  animation-delay: 0ms;
}
.loadingDot:nth-child(2) {
  background-color: #d1d5db;
  animation-delay: 150ms;
}
.loadingDot:nth-child(3) {
  background-color: #9ca3af;
  animation-delay: 300ms;
}
.loadingDot:nth-child(4) {
  background-color: #6b7280;
  animation-delay: 450ms;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.9);
  }
}
