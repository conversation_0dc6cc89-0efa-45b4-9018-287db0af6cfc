"use client";

import React, { useEffect, useState } from "react";

type StudentOption = {
  id: string;
  name: string;
};

interface MultiStudentSelectProps {
  onChange: (selectedIds: string[]) => void;
}

export default function MultiStudentSelect({
  onChange,
}: MultiStudentSelectProps) {
  const [students, setStudents] = useState<StudentOption[]>([]);
  const [selected, setSelected] = useState<string[]>([]);

  async function fetchMyStudents() {
    try {
      const res = await fetch("/api/my-students");
      if (!res.ok) {
        throw new Error("Failed to fetch my students");
      }
      const data = await res.json();
      const mapped = data.map((s: any) => ({
        id: s.id,
        name: s.name,
      }));
      setStudents(mapped);
    } catch (err) {
      console.error(
        "[MultiStudentSelect] Error fetching teacher's students:",
        err
      );
    }
  }

  useEffect(() => {
    fetchMyStudents();
  }, []);

  const toggleSelection = (studentId: string) => {
    let updated: string[] = [];
    if (selected.includes(studentId)) {
      updated = selected.filter((id) => id !== studentId);
    } else {
      updated = [...selected, studentId];
    }
    setSelected(updated);
    onChange(updated);
  };

  return (
    <div className="flex flex-col gap-2">
      <label className="block text-sm font-medium text-neutral-700">
        Invite Students
      </label>
      <div className="border border-neutral-200 rounded-lg p-3 bg-neutral-50 max-h-36 overflow-y-auto space-y-2">
        {students.map((stu) => (
          <div key={stu.id} className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={stu.id}
              checked={selected.includes(stu.id)}
              onChange={() => toggleSelection(stu.id)}
              // MODIFIED: Added bg-gray-50
              className="h-4 w-4 rounded border-neutral-300 bg-gray-50 text-neutral-900 focus:ring-neutral-500"
            />
            <label htmlFor={stu.id} className="text-sm text-neutral-700">
              {stu.name}
            </label>
          </div>
        ))}
        {students.length === 0 && (
          <div className="text-sm text-neutral-500">
            No students found or not a teacher.
          </div>
        )}
      </div>
    </div>
  );
}
