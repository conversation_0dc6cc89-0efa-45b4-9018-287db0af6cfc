// app/(main)/schedule/EventContext.tsx
"use client";

import React, { createContext, useState, useEffect, useCallback } from "react";
import axios from "axios";

// 1) Define your event shape
export interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  googleEventId?: string;
  calendarLink?: string;
  recurrenceRule?: string;
  // ADDED: new optional fields
  jitsiRoomName?: string;
  isMeeting?: boolean;
}

// 2) The shape of our context data
interface EventContextProps {
  events: CalendarEvent[];
  loading: boolean;
  fetchEvents: () => Promise<void>;
  createEvent: (
    newEvent: Omit<CalendarEvent, "id"> & { invitedStudentIds?: string[] }
  ) => Promise<void>;
}

// 3) Create the context with default (stub) values
export const EventContext = createContext<EventContextProps>({
  events: [],
  loading: false,
  fetchEvents: async () => {},
  createEvent: async () => {},
});

// 4) Implement the Provider
export const EventProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);

  // ========== ADDED: We'll store the user role if needed
  const [userRole, setUserRole] = useState<string>("student");

  // ========== ADDED: On mount, fetch the user role from somewhere (e.g. /api/users)
  const fetchUserRole = useCallback(async () => {
    try {
      const res = await fetch("/api/users"); // or a different endpoint
      const data = await res.json();
      // Suppose data has shape { role: "teacher" or "student" } or data.isTeacher
      // We'll set userRole based on data
      if (data.isTeacher) {
        console.log("[EventContext] Detected teacher role from /api/users");
        setUserRole("teacher");
      } else {
        console.log("[EventContext] Detected student role from /api/users");
        setUserRole("student");
      }
    } catch (error) {
      console.error("[EventContext] Error fetching user role:", error);
      setUserRole("student"); // fallback
    }
  }, []);

  // We'll fetch the events from the server
  const fetchEvents = useCallback(async () => {
    console.log("[EventContext] Fetching events...");
    setLoading(true);
    try {
      // ========== ADDED: If user is teacher => scope=all, else => scope=mine
      let url = "/api/events?scope=mine";
      if (userRole === "teacher") {
        url = "/api/events?scope=all";
      }

      console.log(`[EventContext] Using URL: ${url}, userRole=${userRole}`);

      const response = await axios.get(url);
      setEvents(response.data);
      console.log("[EventContext] Events fetched:", response.data);
    } catch (err) {
      console.log("[EventContext] Error fetching events:", err);
    } finally {
      setLoading(false);
    }
  }, [userRole]);

  // A function to create a new event on the backend,
  // and re-fetch to sync the new data
  const createEvent = async (
    newEvent: Omit<CalendarEvent, "id"> & { invitedStudentIds?: string[] }
  ) => {
    console.log("[EventContext] Creating event:", newEvent);
    try {
      const response = await axios.post("/api/events", newEvent);
      console.log(
        "[EventContext] Successfully created event with ID:",
        response.data?.id
      );
      console.log("[EventContext] createEvent response data:", response.data);
      fetchEvents();
    } catch (err) {
      console.log("[EventContext] Error creating event:", err);
    }
  };

  // ========== ADDED: first fetch userRole, then fetchEvents
  useEffect(() => {
    fetchUserRole().then(() => {
      // after userRole is set, we fetchEvents
      fetchEvents();
    });
  }, [fetchEvents, fetchUserRole]);

  // The value we provide to any child that calls useContext(EventContext)
  const value: EventContextProps = {
    events,
    loading,
    fetchEvents,
    createEvent,
  };

  return (
    <EventContext.Provider value={value}>{children}</EventContext.Provider>
  );
};
