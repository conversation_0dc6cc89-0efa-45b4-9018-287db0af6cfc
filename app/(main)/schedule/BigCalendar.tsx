"use client";

import React, {
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
  forwardRef, // ADDED
  useImperativeHandle, // ADDED
} from "react";

import dynamic from "next/dynamic";
import { DateClickArg } from "@fullcalendar/interaction";
import { EventContentArg } from "@fullcalendar/core";

// Dynamically import FullCalendar wrapper with all plugins
const FullCalendarComponent = dynamic(() => import("./FullCalendarWrapper"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <div>Loading calendar...</div>
    </div>
  ),
});

import EventCreationModal from "./EventCreationModal";
import { EventContext } from "@/app/(main)/schedule/EventContext";
import styles from "./BigCalendar.module.css";

interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  googleEventId?: string;
  calendarLink?: string;
  recurrenceRule?: string;
  jitsiRoomName?: string;
  isMeeting?: boolean;
}

interface FullCalendarEvent {
  id?: string;
  title: string;
  start: string;
  end: string;
  description?: string;
  rrule?: Record<string, any>;
  extendedProps?: {
    isMeeting?: boolean;
    jitsiRoomName?: string;
    description?: string;
  };
}

// ADDED: Define the shape of the functions we will expose to the parent
export interface BigCalendarRef {
  openModal: () => void;
}

const parseRRuleString = (rruleString: string): Record<string, string> => {
  const rruleObj: Record<string, string> = {};
  const cleanedRruleString = rruleString.startsWith("RRULE:")
    ? rruleString.substring(6)
    : rruleString;

  cleanedRruleString.split(";").forEach((rule) => {
    const [key, value] = rule.split("=");
    if (key && value) {
      rruleObj[key.toUpperCase()] = value;
    }
  });
  return rruleObj;
};

const formatDateTimeForInput = (date: Date): string => {
  return date.toISOString().slice(0, 16);
};

const formatTime = (date: Date | null): string => {
  if (!date) return "";
  return date
    .toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    })
    .toLowerCase();
};

const formatDate = (date: Date | null): string => {
  if (!date) return "";
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

// MODIFIED: Wrapped component in forwardRef to receive a ref from its parent
const BigCalendar = forwardRef<BigCalendarRef, {}>((props, ref) => {
  const context = useContext(EventContext);

  if (!context) {
    throw new Error("BigCalendar must be used within an EventProvider");
  }

  const {
    events: contextEvents,
    loading: contextLoading,
    createEvent,
  } = context;

  const [events, setEvents] = useState<FullCalendarEvent[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newEventData, setNewEventData] = useState<{
    start: string;
    end: string;
  } | null>(null);
  const calendarRef = useRef<any>(null);

  const currentHour = new Date().getHours();
  const scrollTimeValue = `${String(currentHour).padStart(2, "0")}:00:00`;

  const formatEventsForCalendar = useCallback((rawEvents: CalendarEvent[]) => {
    return rawEvents.map((event) => {
      const formattedEvent: FullCalendarEvent = {
        id: event.id ? event.id.toString() : undefined,
        title: event.title,
        start: event.startTime || "",
        end: event.endTime || "",
        extendedProps: {
          isMeeting: event.isMeeting,
          jitsiRoomName: event.jitsiRoomName,
          description: event.description,
        },
      };

      if (event.recurrenceRule && event.startTime) {
        try {
          const rruleOptions = parseRRuleString(event.recurrenceRule);
          formattedEvent.rrule = {
            ...rruleOptions,
            dtstart: event.startTime,
          };
        } catch (e) {
          console.error("Error parsing RRULE:", event.recurrenceRule, e);
        }
      }
      return formattedEvent;
    });
  }, []);

  useEffect(() => {
    if (!contextLoading) {
      const processedEvents = formatEventsForCalendar(contextEvents);
      setEvents(processedEvents);
    }
  }, [contextEvents, contextLoading, formatEventsForCalendar]);

  const handleCreateEvent = async (eventData: {
    title: string;
    description?: string;
    startTime: string;
    endTime: string;
    recurrenceRule?: string;
    jitsiRoomName?: string;
    isMeeting?: boolean;
    invitedStudentIds?: string[];
  }) => {
    try {
      await createEvent(eventData);
      setIsModalOpen(false);
      setNewEventData(null);
    } catch (error) {
      console.error("Error creating event:", error);
    }
  };

  const handleDateClick = (info: DateClickArg) => {
    let startDate: Date;
    let endDate: Date;

    if (info.allDay) {
      startDate = info.date;
      startDate.setHours(9, 0, 0, 0);
      endDate = new Date(startDate.getTime() + 60 * 60 * 1000);
    } else {
      startDate = info.date;
      endDate = new Date(startDate.getTime() + 60 * 60 * 1000);
    }

    setNewEventData({
      start: formatDateTimeForInput(startDate),
      end: formatDateTimeForInput(endDate),
    });
    setIsModalOpen(true);
  };

  // ADDED: Expose a function to the parent component via the forwarded ref
  useImperativeHandle(ref, () => ({
    openModal: () => {
      // Create default start/end times when opening from the button
      const startDate = new Date();
      // Set start time to the beginning of the next hour
      startDate.setHours(startDate.getHours() + 1, 0, 0, 0);
      // Set end time one hour after the start time
      const endDate = new Date(startDate.getTime() + 60 * 60 * 1000);

      setNewEventData({
        start: formatDateTimeForInput(startDate),
        end: formatDateTimeForInput(endDate),
      });
      setIsModalOpen(true);
    },
  }));

  const renderEventContent = (eventInfo: EventContentArg) => {
    const { event, view } = eventInfo;
    const { extendedProps } = event;
    const start = formatTime(event.start);
    const end = formatTime(event.end);
    const startDate = formatDate(event.start);
    const endDate = formatDate(event.end);
    const isMultiDay = event.end ? startDate !== endDate : false;
    const displayTitle = `${event.title}${
      extendedProps.isMeeting ? " (Meeting)" : ""
    }`;
    const displayDescription = extendedProps.description;

    if (view.type === "dayGridMonth") {
      return (
        <div className="px-1 py-0.5 overflow-hidden whitespace-nowrap text-ellipsis">
          <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
            {displayTitle}
          </span>
        </div>
      );
    }

    return (
      <div className="flex h-full">
        <div className="flex-1 m-1">
          <div className="h-full px-3 py-2 rounded-lg bg-white border border-neutral-200/70 shadow-sm hover:shadow-md transition-all duration-200 relative group backdrop-blur-sm overflow-hidden flex flex-col">
            <div className="flex items-start justify-between mb-1.5 flex-shrink-0">
              <div className="flex items-center gap-1.5">
                <span className="text-[11px] font-medium text-neutral-500 tracking-tight">
                  {isMultiDay
                    ? `${startDate} ${start} - ${endDate} ${end}`
                    : `${start}${end ? ` - ${end}` : ""}`}
                </span>
              </div>
            </div>
            <h3 className="text-sm font-semibold text-neutral-800 leading-tight mb-1 tracking-tight flex-shrink-0">
              {displayTitle}
            </h3>
            {displayDescription && (
              <div className="relative flex-grow min-h-0 overflow-y-auto">
                <p className="text-xs text-neutral-600 leading-normal tracking-tight pr-1">
                  {displayDescription}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.calendarWrapper}>
      {contextLoading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}>
            <div className={styles.loadingDot}></div>
            <div className={styles.loadingDot}></div>
            <div className={styles.loadingDot}></div>
            <div className={styles.loadingDot}></div>
          </div>
        </div>
      ) : (
        <div className={styles.calendarContainer} style={{ height: "100%" }}>
          <FullCalendarComponent
            ref={calendarRef}
            initialView="timeGridWeek"
            headerToolbar={{
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay",
            }}
            events={events}
            eventContent={renderEventContent}
            dateClick={handleDateClick}
            height="100%"
            stickyHeaderDates={true}
            slotMinTime="06:00:00"
            slotMaxTime="21:00:00"
            scrollTime={scrollTimeValue}
            expandRows={true}
            allDaySlot={false}
            nowIndicator={true}
            dayMaxEvents={true}
          />
        </div>
      )}

      {isModalOpen && newEventData && (
        <EventCreationModal
          isOpen={isModalOpen}
          onRequestClose={() => {
            setIsModalOpen(false);
            setNewEventData(null);
          }}
          onSubmit={handleCreateEvent}
          eventDetails={newEventData}
        />
      )}
    </div>
  );
});

// ADDED: A display name for easier debugging in React DevTools
BigCalendar.displayName = "BigCalendar";

export default BigCalendar;
