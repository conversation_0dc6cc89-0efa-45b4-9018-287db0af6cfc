import React, { useState, useEffect } from "react";

interface EventCreationModalProps {
  isOpen: boolean;
  onRequestClose: () => void;
  onSubmit: (eventData: {
    title: string;
    description: string;
    startTime: string;
    endTime: string;
    recurrenceRule?: string;
    isMeeting?: boolean;
    jitsiRoomName?: string;
    invitedStudentIds?: string[];
  }) => Promise<void>;
  eventDetails: { start: string; end: string };
}

import MultiStudentSelect from "./MultiStudentSelect";

function generateRandomJitsiRoomName(): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";
  let result = "vpaas-magic-cookie-";
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

const EventCreationModal: React.FC<EventCreationModalProps> = ({
  isOpen,
  onRequestClose,
  onSubmit,
  eventDetails,
}) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [startTime, setStartTime] = useState(eventDetails.start);
  const [endTime, setEndTime] = useState(eventDetails.end);

  const [isMeeting, setIsMeeting] = useState(false);
  const [jitsiRoomName, setJitsiRoomName] = useState("");

  const [teacherCheck, setTeacherCheck] = useState(false);

  const [invitedStudentIds, setInvitedStudentIds] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen) {
      setTitle("");
      setDescription("");
      setIsMeeting(false);
      setJitsiRoomName("");
      setInvitedStudentIds([]);
      setStartTime(eventDetails.start);
      setEndTime(eventDetails.end);

      (async () => {
        try {
          const response = await fetch("/api/users", { method: "GET" });
          const data = await response.json();
          console.log(
            "[EventCreationModal] Checking if user is teacher:",
            data.isTeacher
          );
          setTeacherCheck(data.isTeacher);

          if (!data.isTeacher) {
            console.warn(
              "[EventCreationModal] Non-teacher tried to create event. Possibly show error or block."
            );
          }
        } catch (error) {
          console.error(
            "[EventCreationModal] Error fetching teacher status:",
            error
          );
          setTeacherCheck(false);
        }
      })();
    }
  }, [isOpen, eventDetails]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("[EventCreationModal] Attempting to create event with data:", {
      title,
      description,
      startTime,
      endTime,
      isMeeting,
      jitsiRoomName,
      invitedStudentIds,
    });

    let finalRoomName = jitsiRoomName.trim();
    if (isMeeting && finalRoomName === "") {
      finalRoomName = generateRandomJitsiRoomName();
      console.log(
        "[EventCreationModal] Generated random room name:",
        finalRoomName
      );
    }

    if (!title || !startTime || !endTime) {
      alert("Please fill in all required fields");
      return;
    }
    if (!teacherCheck) {
      alert("Only teachers can create events.");
      return;
    }

    try {
      await onSubmit({
        title,
        description,
        startTime,
        endTime,
        isMeeting,
        jitsiRoomName: finalRoomName,
        invitedStudentIds,
      });
      console.log("[EventCreationModal] Event creation successful.");

      setTitle("");
      setDescription("");
      setIsMeeting(false);
      setJitsiRoomName("");
      setInvitedStudentIds([]);
    } catch (error) {
      console.error("[EventCreationModal] Error submitting event:", error);
    }
    onRequestClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm transition-opacity"></div>

      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative transform overflow-hidden rounded-2xl bg-neutral-50 dark:bg-neutral-800 shadow-[0_2px_8px_rgba(0,0,0,0.08)] transition-all w-full max-w-lg border border-neutral-100 dark:border-neutral-700">
          <div className="h-1 bg-gradient-to-r from-neutral-500 to-neutral-600"></div>

          <form onSubmit={handleSubmit} className="p-6">
            <div className="mb-6">
              <h2 className="text-2xl font-medium text-neutral-900 mb-1">
                Create Event
              </h2>
              <p className="text-sm text-neutral-500">
                Add a new event to your calendar
              </p>
            </div>

            <div className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-2.5 text-sm text-neutral-900 placeholder:text-neutral-400 focus:border-neutral-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-neutral-500 transition-all"
                  placeholder="Event title"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-2.5 text-sm text-neutral-900 placeholder:text-neutral-400 focus:border-neutral-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-neutral-500 transition-all min-h-[100px] resize-y"
                  placeholder="Add a description (optional)"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Start Time <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="datetime-local"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-2.5 text-sm text-neutral-900 focus:border-neutral-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-neutral-500 transition-all"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    End Time <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="datetime-local"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    className="w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-2.5 text-sm text-neutral-900 focus:border-neutral-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-neutral-500 transition-all"
                    required
                  />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="isMeeting"
                  checked={isMeeting}
                  onChange={(e) => setIsMeeting(e.target.checked)}
                  // MODIFIED: Added bg-gray-50
                  className="h-4 w-4 rounded border-neutral-300 bg-gray-50 text-neutral-900 focus:ring-neutral-500"
                />
                <label htmlFor="isMeeting" className="text-sm text-neutral-700">
                  Is this a Meeting?
                </label>
              </div>

              {isMeeting && (
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Jitsi Room Name (optional)
                  </label>
                  <input
                    type="text"
                    value={jitsiRoomName}
                    onChange={(e) => setJitsiRoomName(e.target.value)}
                    className="w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-2.5 text-sm text-neutral-900 placeholder:text-neutral-400 focus:border-neutral-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-neutral-500 transition-all"
                    placeholder="Leave blank to auto-generate"
                  />
                  <p className="text-xs text-neutral-500 mt-1">
                    If left blank, a random name will be generated. If you
                    specify one, it should be unique to avoid collisions on
                    meet.jit.si.
                  </p>
                </div>
              )}

              {teacherCheck && (
                <MultiStudentSelect
                  onChange={(ids) => {
                    console.log(
                      "[EventCreationModal] MultiStudentSelect changed:",
                      ids
                    );
                    setInvitedStudentIds(ids);
                  }}
                />
              )}
            </div>

            <div className="mt-8 flex justify-end gap-3">
              <button
                type="button"
                onClick={onRequestClose}
                className="inline-flex items-center justify-center rounded-lg px-4 py-2.5 text-sm font-medium text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-neutral-300 focus:ring-offset-2 transition-all"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center justify-center rounded-lg bg-black px-4 py-2.5 text-sm font-medium text-neutral-50 hover:bg-neutral-800 focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2 transition-all"
              >
                Create Event
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EventCreationModal;
