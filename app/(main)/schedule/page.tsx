"use client"; // ADDED: This page now uses client-side hooks (useRef)

import React, { useRef } from "react"; // ADDED: useRef
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
// MODIFIED: Import BigCalendar and the new BigCalendarRef type
import BigCalendar, { BigCalendarRef } from "./BigCalendar";

const SchedulePage: React.FC = () => {
  // ADDED: Create a ref to hold a reference to the BigCalendar component instance
  const calendarRef = useRef<BigCalendarRef>(null);

  // ADDED: A handler function for the button click
  const handleCreateEventClick = () => {
    // Call the openModal function we exposed from the child component
    if (calendarRef.current) {
      calendarRef.current.openModal();
    }
  };

  return (
    // Set a soft, neutral background for the entire page
    <div className="flex flex-col h-full w-full bg-gray-50 p-4 sm:p-6 lg:p-8">
      {/* This is the main "card" container */}
      <div className="flex-1 flex flex-col min-h-0 w-full bg-white rounded-2xl shadow-md overflow-hidden">
        {/* A dedicated header for the card */}
        <header className="flex-none p-4 sm:p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-800">Schedule</h1>
              <p className="text-sm text-gray-500 mt-1">
                Manage your classes, meetings, and events.
              </p>
            </div>
            <div className="flex items-center gap-2">
              {/* MODIFIED: Added the onClick handler to the button */}
              <Button onClick={handleCreateEventClick}>
                <Plus className="mr-2 h-4 w-4" />
                Create Event
              </Button>
            </div>
          </div>
        </header>

        <main className="flex-1 min-h-0 flex flex-col">
          {/* MODIFIED: Passed the ref to the BigCalendar component */}
          <BigCalendar ref={calendarRef} />
        </main>
      </div>
    </div>
  );
};

export default SchedulePage;
