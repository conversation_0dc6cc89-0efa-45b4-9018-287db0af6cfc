"use client";

import { forwardRef } from "react";
import FullCalendarComponent from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import rrulePlugin from "@fullcalendar/rrule";

// Re-export the component with plugins pre-loaded
const FullCalendarWrapper = forwardRef<any, any>((props, ref) => {
  return (
    <FullCalendarComponent
      ref={ref}
      plugins={[
        dayGridPlugin,
        timeGridPlugin,
        interactionPlugin,
        rrulePlugin,
      ]}
      {...props}
    />
  );
});

FullCalendarWrapper.displayName = "FullCalendarWrapper";

export default FullCalendarWrapper;
export { dayGridPlugin, timeGridPlugin, interactionPlugin, rrulePlugin };
