import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import TajweedGrid from "./tajweedGrid";

// Type definition for tajweed rules
export type TajweedRule = {
  id: string;
  name: string;
  arabicName: string;
  description: string;
  color: string;
  category: string; // Category identifier
  examples?: string[];
  status?: "completed" | "inProgress" | "notStarted";
  learningOrder?: number; // New property for sorting by learning progression
};

const TajweedGridPage = async () => {
  // 1) Check authentication
  const { userId } = await auth();
  if (!userId) {
    redirect("/");
  }

  // 2) Define comprehensive tajweed lessons and rules with learningOrder
  const tajweedRules: TajweedRule[] = [
    // 1. Makhar<PERSON>j al-<PERSON> (Points of Articulation) - Orders 1-5
    {
      id: "makharij-jawf",
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON> (Oral Cavity)",
      arabicName: "الجوف",
      description: "Articulation point for long vowels (ا و ي)",
      color: "#EF5350",
      category: "makharij",
      examples: ["ا", "و", "ي"],
      learningOrder: 1,
    },
    {
      id: "makharij-halq",
      name: "Al-Halq (Throat)",
      arabicName: "الحلق",
      description:
        "Articulation points from the throat, including hamzah, ha, ayn, etc.",
      color: "#EC407A",
      category: "makharij",
      examples: ["ء", "ه", "ع", "ح", "غ", "خ"],
      learningOrder: 2,
    },
    {
      id: "makharij-lisan",
      name: "Al-Lisan (Tongue)",
      arabicName: "اللسان",
      description:
        "Articulation points involving the tongue, includes many letters",
      color: "#AB47BC",
      category: "makharij",
      examples: ["ق", "ك", "ج", "ش", "ي", "ض", "ل", "ن", "ر", "ت", "د", "ط"],
      learningOrder: 3,
    },
    {
      id: "makharij-shafatain", // Corrected spelling to match example/lesson key
      name: "Ash-Shafatain (Lips)",
      arabicName: "الشفتان",
      description: "Articulation points involving the lips",
      color: "#7E57C2",
      category: "makharij",
      examples: ["ف", "و", "ب", "م"],
      learningOrder: 4,
    },
    {
      id: "makharij-khayshum",
      name: "Al-Khayshum (Nasal Passage)",
      arabicName: "الخيشوم",
      description: "Nasal articulation for ghunnah sounds",
      color: "#5C6BC0",
      category: "makharij",
      examples: ["نّ", "مّ"],
      learningOrder: 5,
    },

    // 2. Sifaat al-Huruf (Characteristics of Letters) - Orders 6-9
    {
      id: "sifaat-jahr",
      name: "Jahr (Voiced)",
      arabicName: "الجهر",
      description: "Letters pronounced with voice/vibration of vocal cords",
      color: "#42A5F5",
      category: "sifaat",
      examples: [
        "ب",
        "ج",
        "د",
        "ذ",
        "ر",
        "ز",
        "ض",
        "ظ",
        "ع",
        "غ",
        "ل",
        "م",
        "ن",
        "و",
        "ي",
      ],
      learningOrder: 6,
    },
    {
      id: "sifaat-hams",
      name: "Hams (Whispered)",
      arabicName: "الهمس",
      description:
        "Letters pronounced with breath without vibration of vocal cords",
      color: "#26C6DA",
      category: "sifaat",
      examples: ["ت", "ث", "ح", "خ", "س", "ش", "ص", "ط", "ف", "ق", "ك", "ه"],
      learningOrder: 7,
    },
    {
      id: "sifaat-shiddah",
      name: "Shiddah (Intensity)",
      arabicName: "الشدة",
      description: "Letters pronounced with strong airflow restriction",
      color: "#26A69A",
      category: "sifaat",
      examples: ["ء", "ت", "ج", "د", "ط", "ب", "ق", "ك"],
      learningOrder: 8,
    },
    {
      id: "sifaat-rakhawah",
      name: "Rakhawah (Softness)",
      arabicName: "الرخاوة",
      description: "Letters pronounced with gentle airflow",
      color: "#66BB6A",
      category: "sifaat",
      examples: [
        "ث",
        "ح",
        "خ",
        "ذ",
        "ز",
        "س",
        "ش",
        "ص",
        "ض",
        "ظ",
        "غ",
        "ف",
        "ه",
      ],
      learningOrder: 9,
    },

    // 3. Noon Saakin & Tanween Rules - Orders 10-14 (Updated IDs + Added missing Ikhfa)
    {
      id: "izhar", // Was: noon-idhar
      name: "Izhar (Clear Pronunciation)",
      arabicName: "إظهار",
      description:
        "Clear pronunciation of Noon Saakin or Tanween when followed by throat letters",
      color: "#FF7043",
      category: "noon",
      examples: ["مِنْ عِلْمٍ"],
      learningOrder: 10,
    },
    {
      id: "idgham-ghunnah", // Was: noon-idgham-ghunnah
      name: "Idgham with Ghunnah",
      arabicName: "إدغام بغنة",
      description:
        "Merging with nasalization when Noon Saakin or Tanween is followed by (ي,ن,م,و)",
      color: "#5C6BC0",
      category: "noon",
      examples: ["مَن يَقُولُ"],
      learningOrder: 11,
    },
    {
      id: "idgham-without-ghunnah", // Was: noon-idgham-without-ghunnah
      name: "Idgham without Ghunnah",
      arabicName: "إدغام بلا غنة",
      description:
        "Merging without nasalization when Noon Saakin or Tanween is followed by (ل,ر)",
      color: "#7E57C2",
      category: "noon",
      examples: ["مِن رَّبِّهِم"],
      learningOrder: 12,
    },
    {
      id: "iqlab", // Was: noon-iqlab
      name: "Iqlab (Conversion)",
      arabicName: "إقلاب",
      description:
        "Converting Noon Saakin or Tanween to Meem when followed by Ba",
      color: "#EF5350",
      category: "noon",
      examples: ["أَنْبَأَ"],
      learningOrder: 13,
    },
    {
      id: "ikhfa", // Added missing rule
      name: "Ikhfa (Concealment)",
      arabicName: "إخفاء",
      description:
        "Concealment of Noon Saakin or Tanween with nasalization when followed by the remaining 15 letters",
      color: "#9C27B0",
      category: "noon",
      examples: ["مِن صَلْصَالٍ", "رِيْحٌ صَرْصَرٌ"],
      learningOrder: 14,
    },

    // 4. Meem Saakin Rules - Orders 15-17 (Updated IDs)
    {
      id: "idgham-shafawi", // Was: meem-idgham-shafawi
      name: "Idgham Shafawi",
      arabicName: "إدغام شفوي",
      description: "Merging of Meem Saakin when followed by Meem",
      color: "#8D6E63",
      category: "meem",
      examples: ["لَهُمْ مَّا"],
      learningOrder: 15,
    },
    {
      id: "ikhfa-shafawi", // Was: meem-ikhfa-shafawi
      name: "Ikhfa Shafawi",
      arabicName: "إخفاء شفوي",
      description: "Hidden pronunciation of Meem Saakin when followed by Ba",
      color: "#78909C",
      category: "meem",
      examples: ["تَرْمِيهِمْ بِحِجَارَةٍ"],
      learningOrder: 16,
    },
    {
      id: "izhar-shafawi", // Was: meem-idhar-shafawi
      name: "Izhar Shafawi",
      arabicName: "إظهار شفوي",
      description:
        "Clear pronunciation of Meem Saakin when followed by any letter except Meem and Ba",
      color: "#BDBDBD",
      category: "meem",
      examples: ["عَلَيْهِمْ طَائِرًا"],
      learningOrder: 17,
    },

    // 5. Qalqalah Rules - single merged rule
    {
      id: "qalqalah",
      name: "Qalqalah (Echo/Bounce)",
      arabicName: "قلقلة",
      description: "Bounce on ق ط ب ج د – minor in-word, major on stopping",
      color: "#FF9800",
      category: "qalqalah",
      examples: ["يَقْطَعُ", "أَحَدْ"],
      learningOrder: 19,
    },

    // 6. Ghunnah Rules - single core rule
    {
      id: "ghunnah",
      name: "Ghunnah Mushaddadah",
      arabicName: "غنة مشددة",
      description: "Full nasalization on Noon or Meem with Shaddah (2 counts)",
      color: "#66BB6A",
      category: "ghunnah",
      examples: ["إِنَّ", "ثُمَّ"],
      learningOrder: 21,
    },

    // 7. Laam Rules - merged Laam Allah + Qamariyyah/Shamsiyyah (Updated IDs)
    {
      id: "laam-allah",
      name: "Laam of Allah",
      arabicName: "لام لفظ الجلالة",
      description: "Heavy after Fatḥah/Ḍammah; light after Kasrah",
      color: "#FF7043",
      category: "laam",
      examples: ["اللَّهُ", "بِاللَّهِ"],
      learningOrder: 24,
    },
    {
      id: "laam-qamariyyah", // Was: lam-qamariyyah
      name: "Laam Qamariyyah",
      arabicName: "لام قمرية",
      description: "Clear Lam before moon letters",
      color: "#FFA726",
      category: "laam",
      examples: ["الْقَمَر", "الْكِتَاب"],
      learningOrder: 26,
    },
    {
      id: "laam-shamsiyyah", // Was: lam-shamsiyyah
      name: "Laam Shamsiyyah",
      arabicName: "لام شمسية",
      description: "Assimilated Lam before sun letters",
      color: "#FF9800",
      category: "laam",
      examples: ["الشَّمْس", "النَّاس"],
      learningOrder: 27,
    },

    // 8. Madd Rules - condensed into 3 cards
    {
      id: "madd-natural",
      name: "Madd Asli (Natural)",
      arabicName: "مد أصلي",
      description: "Natural elongation – 2 counts",
      color: "#9CCC65",
      category: "madd",
      examples: ["قَالَ", "يَقُولُ"],
      learningOrder: 28,
    },
    {
      id: "madd-hamza",
      name: "Madd by Hamza",
      arabicName: "مد بسبب الهمز",
      description: "Connected/disconnected/Badal – 4–5 counts",
      color: "#8BC34A",
      category: "madd",
      examples: ["جَاءَ", "بِمَا أُنزِلَ"],
      learningOrder: 29,
    },
    {
      id: "madd-sukoon",
      name: "Madd by Sukoon",
      arabicName: "مد بسبب السكون",
      description: "Lāzim 6, ʿĀriḍ/Līn 2–4–6 when stopping",
      color: "#7CB342",
      category: "madd",
      examples: ["الْآنَ", "الرَّحِيمْ"],
      learningOrder: 30,
    },

    // 9. Tafkheem/Tarqeeq - tarqeeq-letters removed
    {
      id: "tafkheem-letters",
      name: "Tafkheem Letters",
      arabicName: "حروف التفخيم",
      description: "Always-heavy letters: خ ص ض غ ط ق ظ",
      color: "#FF5722",
      category: "tafkheem-tarqeeq",
      examples: ["طَاه", "خَلَقَ"],
      learningOrder: 35,
    },
    {
      id: "tafkheem-ra",
      name: "Tafkheem of Ra",
      arabicName: "تفخيم الراء",
      description: "Heavy Ra in specific contexts",
      color: "#F4511E",
      category: "tafkheem-tarqeeq",
      examples: ["رَبّ"],
      learningOrder: 36,
    },
    {
      id: "tarqeeq-ra",
      name: "Tarqeeq of Ra",
      arabicName: "ترقيق الراء",
      description: "Light Ra in specific contexts",
      color: "#FF8A65",
      category: "tafkheem-tarqeeq",
      examples: ["فِرْعَوْن"],
      learningOrder: 37,
    },

    // 10. Hamzah Rules
    {
      id: "hamzat-wasl",
      name: "Hamzatul Wasl",
      arabicName: "همزة الوصل",
      description: "Dropped when connecting, pronounced when starting",
      color: "#29B6F6",
      category: "hamzah",
      examples: ["اسْمُ", "انْطَلِقْ"],
      learningOrder: 39,
    },
    {
      id: "hamzat-qat",
      name: "Hamzatul Qat'",
      arabicName: "همزة القطع",
      description: "Always pronounced",
      color: "#03A9F4",
      category: "hamzah",
      examples: ["أَكْبَر", "إِيمَان"],
      learningOrder: 40,
    },

    // 11. Waqf Rules
    {
      id: "waqf-taam",
      name: "Waqf Taam",
      arabicName: "وقف تام",
      description: "Complete stop (meaning finished)",
      color: "#9575CD",
      category: "waqf",
      examples: ["العَالَمِينَ ۝"],
      learningOrder: 42,
    },
    {
      id: "waqf-kaafi",
      name: "Waqf Kaafi",
      arabicName: "وقف كافي",
      description: "Sufficient stop (meaning complete, linked)",
      color: "#7E57C2",
      category: "waqf",
      examples: ["يَوْمِ الدِّينِ ۙ"],
      learningOrder: 43,
    },
    {
      id: "waqf-hasan",
      name: "Waqf Hasan",
      arabicName: "وقف حسن",
      description: "Good stop (some connection remains)",
      color: "#673AB7",
      category: "waqf",
      examples: ["الرَّحْمٰنِ ۖ"],
      learningOrder: 44,
    },
    {
      id: "waqf-qabih",
      name: "Waqf Qabih",
      arabicName: "وقف قبيح",
      description: "Bad stop (meaning distorted)",
      color: "#5E35B1",
      category: "waqf",
      examples: ["اهْدِنَا"],
      learningOrder: 45,
    },
  ];

  // 3) Add default status to all rules, narrowing to the union
  const tajweedRulesWithStatus: TajweedRule[] = tajweedRules.map(
    (r): TajweedRule => ({
      ...r,
      status: "notStarted" as const,
    })
  );

  // 4️⃣  Render grid with enhanced landing page functionality
  return <TajweedGrid tajweedRules={tajweedRulesWithStatus} />;
};

export default TajweedGridPage;
