import Link from "next/link";
import { ArrowLef<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

type Props = {
  title: string; // This prop will receive the englishTitle from page.tsx
};

export const Header = ({ title }: Props) => {
  return (
    <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Left Item: Back Button */}
        <Link href="/courses">
          <Button variant="ghost" size="sm" className="p-1.5 h-auto">
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
          </Button>
        </Link>

        {/* Center Item: Title */}
        {/* English Title */}
        <h3 className="text-lg font-semibold text-neutral-400 leading-tight">
          {title}
        </h3>
        <div className="w-7 md:w-8" aria-hidden="true" />
      </div>
    </div>
  );
};
