// app/(main)/learn/page.tsx
import { lessons, units as unitsSchema } from "@/db/schema";
import { Unit } from "./unit";
import { Header } from "./header"; // Learn page specific header
import { fetchLearnPageData, type LearnPageData } from "./page-server";
import Link from "next/link"; // Added import for Link

// NEW: Define props to accept searchParams, including 'embedded'
type LearnPageProps = {
  searchParams?: {
    curriculumId?: string; // From CoursesPlayerPage
    targetUnitId?: string; // The specific unit ID for quiz/exercise
    embedded?: string; // NEW: To indicate if the page is embedded
  };
};

const LearnPage = async ({ searchParams }: LearnPageProps) => {
  // Enhanced embedded detection with proper string comparison
  const isEmbedded = searchParams?.embedded === "true";

  // Pass the targetUnitId from searchParams to the data fetching function
  const data = await fetchLearnPageData(searchParams?.targetUnitId);

  // Destructure all necessary data. Assert non-null where appropriate based on fetchLearnPageData's logic.
  const {
    userProgress,
    units,
    courseProgress,
    lessonPercentage,
    userSubscription, // Make sure this is always returned, even if null/undefined
    isSingleUnitView,
    singleUnitTitle,
  } = data;

  // isPro check - ensure userSubscription is handled if it can be null
  const isPro = !!userSubscription?.isActive;

  // Determine header title for the non-embedded version
  const headerTitle =
    isSingleUnitView && singleUnitTitle
      ? singleUnitTitle
      : userProgress?.activeCourse?.englishTitle || "Learn"; // Added optional chaining for userProgress

  // ----- RENDER DIFFERENTLY IF EMBEDDED -----
  if (isEmbedded) {
    return (
      // Enhanced wrapper optimized for iframe display with better responsive design
      <div className="h-full w-full overflow-auto bg-white">
        <div className="min-h-full w-full">
          {units.length > 0 ? (
            <div className="space-y-4 p-3 pb-8 sm:space-y-6 sm:p-4 sm:pb-12">
              {units.map((unit) => (
                <div key={unit.id} className="w-full">
                  <Unit
                    id={unit.id}
                    order={unit.order}
                    description={unit.description}
                    title={unit.title}
                    lessons={unit.lessons}
                    activeLesson={
                      courseProgress?.activeLesson as
                        | (typeof lessons.$inferSelect & {
                            unit: typeof unitsSchema.$inferSelect;
                          })
                        | undefined
                    }
                    activeLessonPercentage={lessonPercentage}
                    isEmbedded={isEmbedded} // Enhanced: Pass embedded flag to Unit component
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center text-center min-h-full p-6">
              <div className="max-w-sm mx-auto space-y-3">
                <h2 className="text-lg font-semibold text-neutral-700">
                  Content Not Found
                </h2>
                <p className="text-neutral-500 text-sm leading-relaxed">
                  The learning content for this section could not be loaded.
                  Please try refreshing or contact support.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // ----- DEFAULT RENDERING WHEN NOT EMBEDDED (uses its own header, intended for direct navigation) -----
  return (
    <div
      className="flex flex-col h-screen text-sm"
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      <Header title={headerTitle} />

      <div
        className={`flex-1 overflow-auto px-3 py-6 md:px-6 ${
          isSingleUnitView ? "pt-2" : "" // This padding adjustment is for when Header above is shown/hidden
        }`}
      >
        <div className="max-w-7xl mx-auto">
          {units.length > 0 ? (
            <div className="space-y-10 pb-20">
              {units.map((unit) => (
                <div key={unit.id}>
                  <Unit
                    id={unit.id}
                    order={unit.order}
                    description={unit.description}
                    title={unit.title}
                    lessons={unit.lessons}
                    activeLesson={
                      courseProgress?.activeLesson as
                        | (typeof lessons.$inferSelect & {
                            unit: typeof unitsSchema.$inferSelect;
                          })
                        | undefined
                    }
                    activeLessonPercentage={lessonPercentage}
                    isEmbedded={isEmbedded} // Enhanced: Pass embedded flag to Unit component
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center text-center py-10">
              <h2 className="text-2xl font-semibold text-neutral-700 mb-2">
                {isSingleUnitView
                  ? "Unit Content Not Found" // This is when targetUnitId was given, but no units found for it
                  : "No Units Available"}{" "}
              </h2>
              <p className="text-neutral-500">
                {isSingleUnitView
                  ? "The content for this specific unit could not be loaded."
                  : "There are currently no units available for this course. Please check back later."}
              </p>
              {!isSingleUnitView && ( // Only show "Back to Courses" if not in a targeted single unit view
                <Link
                  href="/courses"
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Back to Courses
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LearnPage;
