"use client";

import Link from "next/link";
import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

type Props = {
  id: number;
  index: number;
  totalCount: number;
  locked?: boolean;
  current?: boolean;
  percentage: number;
  title: string;
  isEmbedded?: boolean; // Flag to indicate if component is embedded
  lessonCompleted?: boolean; // Shared completion status from learn page
};

export const LessonButton = ({
  id,
  index,
  // totalCount, // Not used in this specific styling iteration
  locked,
  current,
  percentage,
  title,
  isEmbedded = false, // Default to false for backward compatibility
  lessonCompleted = false, // Default to false
}: Props) => {
  const [isHovered, setIsHovered] = useState(false);
  const [referrerInfo, setReferrerInfo] = useState<string>("");

  useEffect(() => {
    if (typeof window === "undefined") return;
    let referrerParams = "";

    if (isEmbedded) {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const curriculumId = urlParams.get("curriculumId");
        referrerParams = `from=coursesplayer${
          curriculumId ? `&curriculumId=${curriculumId}` : ""
        }`;
      } catch {
        referrerParams = "from=coursesplayer";
      }
    } else {
      referrerParams = "from=learn";
    }

    setReferrerInfo(referrerParams);
  }, [isEmbedded]);

  const isCompleted = lessonCompleted;

  const href = locked
    ? "#"
    : `/lesson/${id}${referrerInfo ? `?${referrerInfo}` : ""}`;

  let lessonStatus: "completed" | "inProgress" | "locked" | "default" =
    "default";
  if (locked) lessonStatus = "locked";
  else if (current) lessonStatus = "inProgress";
  else if (isCompleted) lessonStatus = "completed";

  const handleEmbeddedClick = (e: React.MouseEvent) => {
    if (!isEmbedded || locked) return;
    e.preventDefault();
    e.stopPropagation();
    const fullScreenUrl = `/lesson/${id}${
      referrerInfo ? `?${referrerInfo}` : ""
    }`;
    try {
      if (window.parent && window.parent !== window) {
        window.parent.location.href = fullScreenUrl;
      } else if (window.top && window.top !== window) {
        window.top.location.href = fullScreenUrl;
      } else {
        window.location.href = fullScreenUrl;
      }
    } catch {
      console.warn("Cross-origin, navigating current window");
      window.location.href = fullScreenUrl;
    }
  };

  // border for card itself
  const getStatusBorderColor = (status: typeof lessonStatus) => {
    switch (status) {
      case "completed":
        return "border-emerald-500";
      case "inProgress":
        return "border-amber-500";
      case "locked":
        return "border-gray-200";
      default:
        return "border-gray-200";
    }
  };
  const statusBorderColor = getStatusBorderColor(lessonStatus);

  // initial badge background
  const getInitialBadgeBgColor = (status: typeof lessonStatus) => {
    switch (status) {
      case "completed":
        return "bg-emerald-50";
      case "inProgress":
        return "bg-amber-50";
      case "locked":
        return "bg-gray-100";
      default:
        return "bg-gray-100";
    }
  };
  const initialBadgeBg = getInitialBadgeBgColor(lessonStatus);

  // initial badge text color
  const getInitialBadgeTextColor = (status: typeof lessonStatus) => {
    switch (status) {
      case "completed":
        return "text-emerald-700";
      case "inProgress":
        return "text-black";
      case "locked":
        return "text-gray-500";
      default:
        return "text-gray-600";
    }
  };
  const initialBadgeTextColor = getInitialBadgeTextColor(lessonStatus);

  // NEW: hover badge bg matching status
  const getHoverBadgeBg = (status: typeof lessonStatus) => {
    switch (status) {
      case "completed":
        return "group-hover:bg-emerald-500";
      case "inProgress":
        return "group-hover:bg-amber-500";
      case "locked":
        return "";
      default:
        return "group-hover:bg-gray-300";
    }
  };
  const hoverBadgeBg = getHoverBadgeBg(lessonStatus);

  // NEW: hover badge text color matching status
  const getHoverBadgeTextColor = (status: typeof lessonStatus) => {
    switch (status) {
      case "completed":
        return "group-hover:text-white";
      case "inProgress":
        return "group-hover:text-white";
      case "locked":
        return "";
      default:
        return "group-hover:text-white";
    }
  };
  const hoverBadgeText = getHoverBadgeTextColor(lessonStatus);

  // status subtitle text
  let statusSubtitle = "";
  if (locked) statusSubtitle = "Locked";
  else if (current)
    statusSubtitle = isEmbedded ? "In Progress" : `${percentage}% Complete`;
  else if (isCompleted) statusSubtitle = "Completed";
  else statusSubtitle = "Available";

  const badgeNumber = index + 1;
  const isLargerCardStyle = true;

  // if embedded mode and unlocked — render div + click handler
  if (isEmbedded && !locked) {
    return (
      <div
        className="block w-full"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleEmbeddedClick}
      >
        <div
          className={cn(
            "group relative bg-white border rounded-xl w-full",
            isLargerCardStyle ? "p-4" : "p-3",
            "transition-all duration-300 ease-out",
            locked
              ? "cursor-not-allowed opacity-60"
              : "cursor-pointer hover:shadow-md hover:scale-[1.02]",
            statusBorderColor
          )}
        >
          <div className="flex items-center">
            {/* Number Badge */}
            <div
              className={cn(
                "relative flex-shrink-0 mr-3 flex items-center justify-center",
                isLargerCardStyle ? "w-10 h-10" : "w-8 h-8"
              )}
            >
              <div
                className={cn(
                  "absolute inset-0 rounded-lg",
                  initialBadgeBg,
                  "transition-all duration-300 ease-out delay-100",
                  !locked && cn(hoverBadgeBg, "group-hover:rotate-45")
                )}
              />
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <span
                  className={cn(
                    "font-bold",
                    isLargerCardStyle ? "text-base" : "text-sm",
                    initialBadgeTextColor,
                    "transition-colors duration-200",
                    !locked && hoverBadgeText
                  )}
                >
                  {badgeNumber}
                </span>
              </div>
            </div>
            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-start">
                <div>
                  <h3
                    className={cn(
                      isLargerCardStyle ? "text-sm" : "text-xs",
                      "font-semibold mb-0.5",
                      locked ? "text-gray-500" : "text-gray-900"
                    )}
                    title={title}
                  >
                    {title}
                  </h3>
                  <p className={cn("text-xs", "text-gray-500")}>
                    {statusSubtitle}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // default mode: Link
  return (
    <Link
      href={href}
      aria-disabled={locked}
      style={{ pointerEvents: locked ? "none" : "auto" }}
      className="block w-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={cn(
          "group relative bg-white border rounded-xl w-full",
          isLargerCardStyle ? "p-4" : "p-3",
          "transition-all duration-300 ease-out",
          locked
            ? "cursor-not-allowed opacity-60"
            : "cursor-pointer hover:shadow-md hover:scale-[1.02]",
          statusBorderColor
        )}
      >
        <div className="flex items-center">
          {/* Number Badge */}
          <div
            className={cn(
              "relative flex-shrink-0 mr-3 flex items-center justify-center",
              isLargerCardStyle ? "w-10 h-10" : "w-8 h-8"
            )}
          >
            <div
              className={cn(
                "absolute inset-0 rounded-lg",
                initialBadgeBg,
                "transition-all duration-300 ease-out delay-100",
                !locked && cn(hoverBadgeBg, "group-hover:rotate-45")
              )}
            />
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <span
                className={cn(
                  "font-bold",
                  isLargerCardStyle ? "text-base" : "text-sm",
                  initialBadgeTextColor,
                  "transition-colors duration-200",
                  !locked && hoverBadgeText
                )}
              >
                {badgeNumber}
              </span>
            </div>
          </div>
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start">
              <div>
                <h3
                  className={cn(
                    isLargerCardStyle ? "text-sm" : "text-xs",
                    "font-semibold mb-0.5",
                    locked ? "text-gray-500" : "text-gray-900"
                  )}
                  title={title}
                >
                  {title}
                </h3>
                <p className={cn("text-xs", "text-gray-500")}>
                  {statusSubtitle}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};
