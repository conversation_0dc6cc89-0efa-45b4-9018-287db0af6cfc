import { type InferSelectModel } from "drizzle-orm";
import { lessons, units as unitsSchema } from "@/db/schema";
import { LessonButton } from "./lesson-button";

// Type definitions for clarity based on your provided schema structure
type LessonSchemaType = InferSelectModel<typeof lessons>;
type UnitSchemaType = InferSelectModel<typeof unitsSchema>;

type Props = {
  id: number; // Unit ID
  order: number; // Unit order
  title: string; // Unit title, e.g., "The Arabic Alphabet"
  description: string; // Unit description
  lessons: (LessonSchemaType & {
    completed: boolean; // This should come from your data query that populates lessons
  })[];
  activeLesson:
    | (LessonSchemaType & {
        unit: UnitSchemaType; // unit property within activeLesson
      })
    | undefined;
  activeLessonPercentage: number;
  isEmbedded?: boolean; // Flag to indicate if component is embedded
  // isPro?: boolean; // Optional: if you need to pass pro status for lesson locking logic
};

const MAX_LESSONS_PER_GROUP = 5; // Defines how many lessons per "Challenge" group box

export const Unit = ({
  title, // Unit title
  description, // Unit description
  lessons,
  activeLesson,
  activeLessonPercentage,
  isEmbedded = false, // Default to false for backward compatibility
}: // id, // Unit ID is available
// order, // Unit order is available
// isPro, // available if passed
Props) => {
  const lessonChunks: (LessonSchemaType & { completed: boolean })[][] = [];
  if (lessons && lessons.length > 0) {
    for (let i = 0; i < lessons.length; i += MAX_LESSONS_PER_GROUP) {
      lessonChunks.push(lessons.slice(i, i + MAX_LESSONS_PER_GROUP));
    }
  }

  if (lessonChunks.length === 0) {
    return (
      <div className="mb-12">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-neutral-700 mb-1">{title}</h2>
          <p className="text-sm text-neutral-500">{description}</p>
        </div>
        <div className="text-center text-neutral-500 my-4 p-6 bg-gray-50 border border-dashed border-gray-300 rounded-lg">
          No lessons (exercises/quizzes) in this unit yet.
        </div>
      </div>
    );
  }

  return (
    <div className="mb-12">
      {/* Unit Title and Description, now consistently shown above the chunks */}
      <div className="mb-6">
        {" "}
        {/* Added more bottom margin */}
        <h2 className="text-2xl font-bold text-neutral-700 mb-1">{title}</h2>
        <p className="text-sm text-neutral-500">{description}</p>
      </div>

      <div className="space-y-8">
        {lessonChunks.map((chunk, chunkIndex) => {
          return (
            <div
              key={`chunk-${chunkIndex}`}
              className="flex flex-col" // Simpler chunk container
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4 flex-grow">
                {chunk.map((lesson) => {
                  const originalIndex = lessons.findIndex(
                    (l) => l.id === lesson.id
                  );

                  // Determine lesson state based on context (embedded vs learn page)
                  let isCurrent: boolean;
                  let isLocked: boolean;

                  if (isEmbedded) {
                    // For coursesplayer: use sequential unlocking based on shared progress
                    isCurrent = lesson.id === activeLesson?.id;

                    if (originalIndex === 0) {
                      // First lesson is always unlocked
                      isLocked = false;
                    } else {
                      // Check if previous lesson is completed (using shared progress)
                      const previousLesson = lessons[originalIndex - 1];
                      isLocked = !previousLesson.completed;
                    }
                  } else {
                    // Use learn page logic (original logic)
                    isCurrent = lesson.id === activeLesson?.id;
                    isLocked = !lesson.completed && !isCurrent;
                  }

                  return (
                    <LessonButton
                      key={lesson.id}
                      id={lesson.id} // This is lesson.id
                      index={originalIndex} // For badge number (1, 2, 3...)
                      totalCount={lessons.length}
                      current={isCurrent}
                      locked={isLocked}
                      percentage={isCurrent ? activeLessonPercentage : 0}
                      title={lesson.title} // Title of the individual lesson/exercise
                      isEmbedded={isEmbedded} // Pass embedded flag to LessonButton
                      lessonCompleted={lesson.completed} // Pass shared completion status
                    />
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
