// app/(main)/learn/page-server.tsx
import { redirect } from "next/navigation";
import {
  getCourseProgress,
  getLessonPercentage,
  getUnits, // This getUnits now accepts an optional courseId
  getUserProgress,
  getUserSubscription,
  getCourseById, // Used to fetch the title of the targeted course/unit
} from "@/db/queries";

export type LearnPageData = {
  userProgress: NonNullable<Awaited<ReturnType<typeof getUserProgress>>> | null; // Allows the object or null
  units: Awaited<ReturnType<typeof getUnits>>;
  courseProgress: NonNullable<
    Awaited<ReturnType<typeof getCourseProgress>>
  > | null; // Allows the object or null
  lessonPercentage: Awaited<ReturnType<typeof getLessonPercentage>>;
  userSubscription: Awaited<ReturnType<typeof getUserSubscription>> | null; // Allows the object or null
  isSingleUnitView: boolean;
  singleUnitTitle?: string;
};

export async function fetchLearnPageData(
  targetUnitIdParam?: string
): Promise<LearnPageData> {
  console.log(
    `[LearnPageServer] fetchLearnPageData called. targetUnitIdParam: '${targetUnitIdParam}'`
  );

  const userProgressDataPromise = getUserProgress();
  const courseProgressPromise = getCourseProgress();
  const lessonPercentagePromise = getLessonPercentage();
  const userSubscriptionPromise = getUserSubscription();

  const targetCourseIdFromParam = targetUnitIdParam
    ? parseInt(targetUnitIdParam, 10)
    : undefined;
  console.log(
    `[LearnPageServer] Parsed targetCourseIdFromParam: ${targetCourseIdFromParam}`
  );

  let unitsPromise: ReturnType<typeof getUnits>;
  let singleUnitTitle: string | undefined = undefined;
  let isSingleUnitView = false;

  if (targetCourseIdFromParam && !isNaN(targetCourseIdFromParam)) {
    console.log(
      `[LearnPageServer] Valid targetCourseIdFromParam (${targetCourseIdFromParam}). Fetching units for this specific course ID.`
    );
    unitsPromise = getUnits(targetCourseIdFromParam);

    console.log(
      `[LearnPageServer] Attempting to fetch course info for ID: ${targetCourseIdFromParam} to get title.`
    );
    // Await here as courseInfo is needed to set isSingleUnitView and singleUnitTitle before Promise.all for other data
    const courseInfo = await getCourseById(targetCourseIdFromParam);
    if (courseInfo) {
      singleUnitTitle = courseInfo.englishTitle;
      isSingleUnitView = true;
      console.log(
        `[LearnPageServer] Course info found for ID ${targetCourseIdFromParam}. Title: '${singleUnitTitle}'. isSingleUnitView SET to TRUE.`
      );
    } else {
      console.warn(
        `[LearnPageServer] Course info NOT FOUND for ID ${targetCourseIdFromParam} by getCourseById. isSingleUnitView REMAINS FALSE. This might lead to issues if units are expected.`
      );
    }
  } else {
    console.log(
      "[LearnPageServer] No valid targetCourseIdFromParam. Fetching units based on user's active course."
    );
    unitsPromise = getUnits(); // Relies on userProgress.activeCourseId inside getUnits (which getUserProgress provides)
  }

  // Await all promises concurrently
  const [
    userProgressResult, // Renamed to avoid conflict and indicate it's the direct result
    unitsResult, // Renamed
    courseProgressResult, // Renamed
    lessonPercentageResult, // Renamed
    userSubscriptionResult, // Renamed
  ] = await Promise.all([
    userProgressDataPromise,
    unitsPromise,
    courseProgressPromise, // Using the promise defined above
    lessonPercentagePromise, // Using the promise defined above
    userSubscriptionPromise, // Using the promise defined above
  ]);

  // Convert potential undefined to null for stricter typing in LearnPageData
  const userProgress =
    userProgressResult === undefined ? null : userProgressResult;
  const units = unitsResult; // getUnits should return [] if no units, not undefined
  const courseProgress =
    courseProgressResult === undefined ? null : courseProgressResult;
  const lessonPercentage = lessonPercentageResult; // Assuming this always returns a number
  const userSubscription =
    userSubscriptionResult === undefined ? null : userSubscriptionResult;

  console.log("[LearnPageServer] After Promise.all & null conversion:");
  console.log(
    `  userProgress: ${
      userProgress
        ? `userId: ${userProgress.userId}, activeCourseId: ${userProgress.activeCourseId}`
        : "null"
    }`
  );
  console.log(
    `  units fetched: ${units.length} (for courseId: ${
      targetCourseIdFromParam ?? userProgress?.activeCourseId
    })`
  );
  units.forEach((u) =>
    console.log(
      `    Unit ID: ${u.id}, Title: ${u.title}, Lessons: ${u.lessons.length}`
    )
  );
  console.log(
    `  courseProgress: ${
      courseProgress
        ? `activeLessonId: ${courseProgress.activeLessonId}`
        : "null"
    }`
  );
  console.log(`  lessonPercentage: ${lessonPercentage}`);
  console.log(
    `  userSubscription: ${
      userSubscription ? `isActive: ${userSubscription.isActive}` : "null"
    }`
  );
  console.log(
    `  isSingleUnitView (determined before Promise.all): ${isSingleUnitView}`
  );
  console.log(
    `  singleUnitTitle (determined before Promise.all): '${singleUnitTitle}'`
  );

  // --- Redirect Logic ---
  // This logic ensures proper navigation for both embedded and non-embedded scenarios

  if (
    !targetCourseIdFromParam &&
    (!userProgress || !userProgress.activeCourseId)
  ) {
    console.log(
      "[LearnPageServer] REDIRECTING to /courses: No targetCourseIdFromParam AND (userProgress is null or no activeCourseId)."
    );
    redirect("/courses");
  }

  if (isSingleUnitView && units.length === 0) {
    console.warn(
      `[LearnPageServer] REDIRECTING to /courses: Targeted course ID ${targetCourseIdFromParam} (isSingleUnitView=true), but getUnits found 0 units for it.`
    );
    redirect("/courses");
  }

  if (targetCourseIdFromParam && !isSingleUnitView && units.length === 0) {
    console.warn(
      `[LearnPageServer] REDIRECTING to /courses: targetCourseIdFromParam (${targetCourseIdFromParam}) was present, but isSingleUnitView is FALSE (getCourseById failed) AND units.length is 0.`
    );
    redirect("/courses");
  }

  if (!isSingleUnitView && !courseProgress) {
    if (units.length === 0) {
      console.log(
        "[LearnPageServer] REDIRECTING to /courses: Not isSingleUnitView, no courseProgress, AND no units for active course."
      );
      redirect("/courses");
    } else {
      console.log(
        "[LearnPageServer] Not isSingleUnitView and no courseProgress, but units ARE present for active course. Proceeding with courseProgress as null."
      );
    }
  }

  console.log(
    "[LearnPageServer] Data fetching complete. Proceeding to render page."
  );

  // Data structure supports both embedded and non-embedded rendering
  // The embedded flag is handled client-side in the React components
  return {
    userProgress, // Now correctly typed as object | null
    units,
    courseProgress, // Now correctly typed as object | null
    lessonPercentage,
    userSubscription, // Now correctly typed as object | null
    isSingleUnitView,
    singleUnitTitle,
  };
}
