"use client";

import Image from "next/image";
import Script from "next/script";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link"; // Added Link import

import {
  Search,
  Plus,
  ChevronRight,
  MoreVertical,
  RefreshCw,
  MessageSquare,
  ArrowLeft, // Added ArrowLeft import
} from "lucide-react";

// TYPES remain the same
type Student = {
  id: string;
  name: string;
  avatar: string;
  quizzesCompleted: number;
  score: string;
  lastActive?: string;
  email?: string;
  subjects?: string[];
  status?: "online" | "offline";
};

interface UserProgress {
  userId?: string;
  userName?: string;
  userImageSrc?: string;
  activeCourseId?: number | null;
  hearts?: number;
  points?: number;
  activeCourse?: {
    id: number;
    englishTitle: string;
    description: string;
    arabicTitle: string;
  } | null;
}

interface UserSubscription {
  isActive?: boolean;
}

interface LeaderboardEntry {
  userId: string;
  userName: string;
  userImageSrc: string;
  points: number;
}

interface MyStudentsClientProps {
  userProgress?: UserProgress | null;
  userSubscription: UserSubscription | null;
  leaderboard: LeaderboardEntry[];
  isTeacher?: boolean;
}

export default function MyStudentsClient({
  userSubscription,
  leaderboard,
  isTeacher,
}: MyStudentsClientProps) {
  const router = useRouter();

  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [debugMode, setDebugMode] = useState(false);
  const [rawApiResponse, setRawApiResponse] = useState<any>(null);

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedView, setSelectedView] = useState<"grid" | "list">("grid");

  async function fetchUserAvatar(userId: string): Promise<string> {
    try {
      console.log(`[my-students-client] Fetching avatar for user ${userId}`);
      const response = await fetch(`/api/users/${userId}`);
      if (response.ok) {
        const userData = await response.json();
        if (userData.avatarSrc) {
          console.log(
            `[my-students-client] Got avatar for ${userId}: ${userData.avatarSrc}`
          );
          return userData.avatarSrc;
        }
      }
      console.log(
        `[my-students-client] No avatar found for ${userId}, using default`
      );
      return "/mascot.svg";
    } catch (error) {
      console.error(
        `[my-students-client] Error fetching avatar for ${userId}:`,
        error
      );
      return "/mascot.svg";
    }
  }

  async function fetchStudents() {
    setLoading(true);
    setError(null);
    console.log("[my-students-client] Starting student fetch...");

    try {
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/my-students?t=${timestamp}`, {
        headers: { "Cache-Control": "no-cache, no-store" },
      });

      console.log(
        "[my-students-client] Fetch response status:",
        response.status
      );

      const responseText = await response.text();
      console.log("[my-students-client] Raw API response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        setRawApiResponse(data);
      } catch (parseError) {
        console.error(
          "[my-students-client] Failed to parse JSON response:",
          parseError
        );
        throw new Error(`Failed to parse server response: ${responseText}`);
      }

      if (!response.ok) {
        throw new Error(
          `Failed to fetch students: ${response.status} ${JSON.stringify(data)}`
        );
      }

      console.log("[my-students-client] Parsed students data:", data);

      if (!Array.isArray(data)) {
        console.error(
          "[my-students-client] API did not return an array:",
          data
        );
        throw new Error(
          "Expected an array of students, but received something else"
        );
      }

      console.log(
        "[my-students-client] Enhancing student data with avatars..."
      );
      const enhancedData = await Promise.all(
        data.map(async (student: Student) => {
          if (
            student.avatar &&
            !student.avatar.includes("mascot.svg") &&
            !student.avatar.includes("/woman.svg") &&
            !student.avatar.includes("/man.svg")
          ) {
            console.log(
              `[my-students-client] Student ${student.id} already has custom avatar: ${student.avatar}`
            );
            return student;
          }

          const avatarSrc = await fetchUserAvatar(student.id);
          console.log(
            `[my-students-client] Enhanced student ${student.id} with avatar: ${avatarSrc}`
          );
          return {
            ...student,
            avatar: avatarSrc,
          };
        })
      );

      console.log(
        "[my-students-client] Setting enhanced student data:",
        enhancedData
      );
      setStudents(enhancedData);
      setLastRefresh(new Date());
    } catch (err) {
      console.error("[my-students-client] Error fetching students:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  }

  const handleForceRefresh = async () => {
    console.log("[my-students-client] Force refreshing student data...");
    try {
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/my-students?nocache=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      console.log(
        "[my-students-client] Force refresh status:",
        response.status
      );

      const data = await response.json();
      console.log("[my-students-client] Force refresh data:", data);

      const enhancedData = await Promise.all(
        data.map(async (student: Student) => {
          if (
            student.avatar &&
            !student.avatar.includes("mascot.svg") &&
            !student.avatar.includes("/woman.svg") &&
            !student.avatar.includes("/man.svg")
          ) {
            return student;
          }
          const avatarSrc = await fetchUserAvatar(student.id);
          return {
            ...student,
            avatar: avatarSrc,
          };
        })
      );

      setStudents(Array.isArray(enhancedData) ? enhancedData : []);
      setLastRefresh(new Date());
      setRawApiResponse(data);
    } catch (err) {
      console.error("[my-students-client] Error during force refresh:", err);
      setError(
        err instanceof Error ? err.message : "Unknown error during refresh"
      );
    }
  };

  const handleSendMessage = (studentId: string, studentName: string) => {
    console.log(
      `[my-students-client] Opening message with student: ${studentName} (${studentId})`
    );
    router.push(`/messages?recipientId=${studentId}`);
  };

  useEffect(() => {
    console.log("[my-students-client] Component mounted, fetching students...");
    fetchStudents();

    const intervalId = setInterval(() => {
      console.log("[my-students-client] Auto-refreshing student data");
      fetchStudents();
    }, 30000);

    console.log("[my-students-client] Browser info:", {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
    });

    return () => clearInterval(intervalId);
  }, []);

  const filteredStudents = students.filter((student) =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const isPro = !!userSubscription?.isActive;

  const addSampleStudent = () => {
    const sampleStudent = {
      id: `sample_${Date.now()}`,
      name: "Sample Student",
      avatar: "/woman.svg",
      quizzesCompleted: 5,
      score: "85%",
      lastActive: "Just now",
      email: "<EMAIL>",
      subjects: ["Basic", "Speaking"],
      status: "online" as const,
    };
    setStudents((prev) => [...prev, sampleStudent]);
  };

  return (
    <>
      <Script id="student-debug" strategy="afterInteractive">
        {`
          console.log('[my-students-client] Page initialized with script');
          window.refreshStudents = function() {
            console.log('Manual refresh triggered from console');
            document.querySelector('[data-testid="refresh-button"]')?.click();
          };
          window.debugStudents = function() {
            // Updated to get students from a global function if you set one, or log a message.
            if (window.getMyStudentsClientState) {
              console.log('Current students:', window.getMyStudentsClientState().students);
            } else {
              console.log('To debug students, expose a global state accessor or log them directly in the component.');
            }
          };
        `}
      </Script>

      {/* Sticky Header with back arrow and centered title */}
      <div className="sticky top-0 z-40 bg-white px-3 py-2 md:px-4 md:py-3 border-b border-gray-200">
        <div className="relative max-w-7xl mx-auto">
          <Link
            href="/learning-path" // Consider making this dynamic or using router.back() if more appropriate
            className="absolute left-0 top-1/2 -translate-y-1/2 p-1.5 rounded-full hover:bg-gray-100 transition-all duration-200"
            aria-label="Go back"
          >
            <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
          </Link>
          <h1 className="text-xl font-bold text-gray-900 text-center">
            My Students
          </h1>
        </div>
      </div>

      {/* Main content area */}
      <div
        className="p-4 md:p-6 lg:p-8"
        style={{
          backgroundColor: "#ffffff",
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundRepeat: "repeat",
        }}
      >
        {/* Controls Bar: Search, Add Student, View Toggle, Refresh, Count */}
        <div className="mb-6 max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-3">
            <div className="flex-grow w-full md:flex-initial md:max-w-md relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-black/40" />
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-9 pr-4 py-2 rounded-lg border border-black/10 bg-black/5 text-sm text-black placeholder-black/40 focus:outline-none focus:ring-1 focus:ring-black/50 focus:border-transparent transition-all duration-300"
              />
            </div>

            <div className="flex items-center gap-2 flex-shrink-0">
              <button
                onClick={() => setSelectedView("grid")}
                className={`p-2 rounded-lg border ${
                  selectedView === "grid"
                    ? "bg-black text-white border-black"
                    : "bg-white text-black border-black/10 hover:bg-black/5"
                } transition-all duration-300`}
                aria-label="Grid View"
                title="Grid View"
              >
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M10 3a1 1 0 011 1v4a1 1 0 11-2 0V4a1 1 0 011-1zM4 3a1 1 0 011 1v4a1 1 0 11-2 0V4a1 1 0 011-1zM16 3a1 1 0 011 1v4a1 1 0 11-2 0V4a1 1 0 011-1zM10 11a1 1 0 011 1v4a1 1 0 11-2 0v-4a1 1 0 011-1zM4 11a1 1 0 011 1v4a1 1 0 11-2 0v-4a1 1 0 011-1zM16 11a1 1 0 011 1v4a1 1 0 11-2 0v-4a1 1 0 011-1z"></path>
                </svg>
              </button>
              <button
                onClick={() => setSelectedView("list")}
                className={`p-2 rounded-lg border ${
                  selectedView === "list"
                    ? "bg-black text-white border-black"
                    : "bg-white text-black border-black/10 hover:bg-black/5"
                } transition-all duration-300`}
                aria-label="List View"
                title="List View"
              >
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
              <button className="flex items-center px-3 py-2 bg-black text-white text-sm rounded-lg shadow-sm hover:bg-black/90 active:bg-black/80 transition-all duration-300 whitespace-nowrap">
                <Plus className="w-4 h-4 mr-1.5" />
                Add Student
              </button>
            </div>
          </div>

          <div className="flex flex-wrap items-center justify-between gap-x-4 gap-y-1 text-xs text-black/50">
            <div className="flex items-center gap-2">
              <span className="px-2 py-0.5 bg-black/10 rounded-full text-xs font-medium">
                {students.length} students
              </span>
              <button
                onClick={() => fetchStudents()}
                disabled={loading}
                data-testid="refresh-button"
                className="p-1 rounded-full text-black/60 hover:text-black hover:bg-black/5 transition-all duration-200 disabled:opacity-50"
                title="Refresh student list"
              >
                <RefreshCw
                  className={`w-3.5 h-3.5 ${loading ? "animate-spin" : ""}`}
                />
              </button>
            </div>
            <div className="text-right">
              Last updated: {lastRefresh.toLocaleTimeString()}
              {loading && <span className="ml-2">(Refreshing...)</span>}
            </div>
          </div>
          {error &&
            !loading && ( // Show error only if not loading
              <div className="mt-2 text-xs text-red-500 text-left md:text-right">
                Error: {error}
              </div>
            )}
        </div>

        {/* Student Grid/List Area */}
        <div className="max-w-7xl mx-auto">
          <div
            className={
              selectedView === "grid"
                ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
                : "space-y-4"
            }
          >
            {loading && students.length === 0 && (
              <div className="col-span-full bg-white/80 backdrop-blur-sm p-6 rounded-xl text-center border border-gray-200">
                <p className="text-black/60">Loading students...</p>
                {/* You could add a spinner icon here */}
              </div>
            )}
            {!loading && students.length === 0 && !error && (
              <div className="col-span-full bg-white/80 backdrop-blur-sm p-6 rounded-xl text-center border border-gray-200">
                <p className="text-black/70 font-medium mb-1">
                  No students yet
                </p>
                <p className="text-sm text-black/50 mb-3">
                  Students you add or who join your class will appear here.
                </p>
                <button
                  onClick={addSampleStudent} // Example: or link to "Add Student" modal
                  className="px-4 py-1.5 bg-black text-white rounded-lg text-sm hover:bg-black/80"
                >
                  Add Sample Student
                </button>
              </div>
            )}
            {/* This error display is now part of the status bar above
            {!loading && error && students.length === 0 && (
              <div className="col-span-full bg-red-50 p-6 rounded-xl text-center border border-red-200">
                  <p className="text-red-700 font-semibold mb-1">Could not load students</p>
                  <p className="text-sm text-red-600 mb-3">{error}</p>
                  <button
                    onClick={() => fetchStudents()}
                    className="px-4 py-1.5 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700"
                  >
                    Try Again
                  </button>
              </div>
            )} */}

            {filteredStudents.map((student) => (
              <div
                key={student.id}
                className="bg-white rounded-xl shadow-sm border border-black/5 hover:shadow-md transition-all duration-300"
              >
                <div className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center min-w-0">
                      {" "}
                      {/* Added min-w-0 for text truncation */}
                      <div className="relative flex-shrink-0">
                        <Image
                          src={student.avatar}
                          alt={student.name}
                          className="w-12 h-12 rounded-lg object-cover"
                          width={48}
                          height={48}
                        />
                        <span
                          className={`absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 rounded-full border-2 border-white ${
                            student.status === "online"
                              ? "bg-green-500"
                              : "bg-gray-400" // Changed offline to gray
                          }`}
                          title={
                            student.status === "online" ? "Online" : "Offline"
                          }
                        />
                      </div>
                      <div className="ml-3 min-w-0">
                        {" "}
                        {/* Added min-w-0 */}
                        <h3
                          className="font-semibold text-sm text-black truncate"
                          title={student.name}
                        >
                          {student.name}
                        </h3>
                        <p
                          className="text-xs text-black/60 truncate"
                          title={student.email}
                        >
                          {student.email || "No email"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center flex-shrink-0 ml-2">
                      <button
                        onClick={() =>
                          handleSendMessage(student.id, student.name)
                        }
                        className="p-1.5 text-black/40 hover:text-black hover:bg-gray-100 rounded-md transition-colors duration-200"
                        title={`Message ${student.name}`}
                      >
                        <MessageSquare className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1.5 ml-1 text-black/40 hover:text-black hover:bg-gray-100 rounded-md transition-colors duration-200"
                        title="More options"
                      >
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <div className="mt-4 grid grid-cols-2 gap-3">
                    <div className="bg-black/5 p-3 rounded-lg">
                      <p className="text-xs text-black/60">Quizzes</p>
                      <p className="text-base font-semibold text-black mt-0.5">
                        {student.quizzesCompleted}
                      </p>
                    </div>
                    <div className="bg-black/5 p-3 rounded-lg">
                      <p className="text-xs text-black/60">Avg. Score</p>
                      <p className="text-base font-semibold text-black mt-0.5">
                        {student.score}
                      </p>
                    </div>
                  </div>

                  {student.subjects && student.subjects.length > 0 && (
                    <div className="mt-3">
                      <p className="text-xs text-black/60 mb-1">Subjects</p>
                      <div className="flex flex-wrap gap-1.5">
                        {student.subjects.slice(0, 3).map(
                          (
                            subject // Show max 3 subjects initially
                          ) => (
                            <span
                              key={subject}
                              className="px-2 py-1 bg-black/5 text-black text-xs rounded-md font-medium"
                            >
                              {subject}
                            </span>
                          )
                        )}
                        {student.subjects.length > 3 && (
                          <span className="px-2 py-1 bg-black/5 text-black text-xs rounded-md font-medium">
                            +{student.subjects.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="mt-4 pt-3 border-t border-gray-100 flex items-center justify-between">
                    <span className="text-xs text-black/60">
                      Active: {student.lastActive || "N/A"}
                    </span>
                    <Link
                      href={`/students/${student.id}`}
                      legacyBehavior={false}
                    >
                      {" "}
                      {/* Assuming a profile page route */}
                      <a className="flex items-center text-black hover:text-black/70 group transition-colors duration-300">
                        <span className="text-xs font-semibold group-hover:underline">
                          View Profile
                        </span>
                        <ChevronRight className="w-3.5 h-3.5 ml-0.5" />
                      </a>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
