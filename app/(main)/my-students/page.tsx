import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import {
  getTopTenUsers,
  getUserProgress,
  getUserSubscription,
} from "@/db/queries";
import db from "@/db/drizzle";
import { eq } from "drizzle-orm";
import { users } from "@/db/schema";

import MyStudentsClient from "./my-students-client";

export default async function MyStudentsPage() {
  // Get the current user's auth ID
  const { userId } = auth();
  console.log("[my-students-page] Server-side auth check");
  console.log("[my-students-page] Current auth userId:", userId);

  if (!userId) {
    return redirect("/");
  }

  // Check if the user is a teacher
  const userRecord = await db.query.users.findFirst({
    where: eq(users.userId, userId),
  });

  const isTeacher = userRecord?.role === "teacher";
  console.log("[my-students-page] User record found:", !!userRecord);
  console.log("[my-students-page] User role:", userRecord?.role);
  console.log("[my-students-page] Is user a teacher?", isTeacher);

  // 1) Fetch data on the server
  const [userProgress, userSubscription, leaderboard] = await Promise.all([
    getUserProgress(),
    getUserSubscription(),
    getTopTenUsers(),
  ]);

  // 2) Only redirect students to courses if they're not enrolled
  // Teachers should always be able to see the my-students page
  if (!isTeacher && !userProgress?.activeCourse) {
    console.log(
      "[my-students-page] User is not a teacher and has no active course. Redirecting to /courses"
    );
    return redirect("/courses");
  }

  // 3) Render the client component, passing props
  console.log("[my-students-page] Rendering My Students page");
  return (
    <MyStudentsClient
      userProgress={userProgress}
      userSubscription={userSubscription}
      leaderboard={leaderboard}
      isTeacher={isTeacher}
    />
  );
}
