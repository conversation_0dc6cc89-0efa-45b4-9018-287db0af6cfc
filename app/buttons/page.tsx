"use client";

import React, { useEffect, useState } from "react";

const Thinking: React.FC = () => {
  const [gradientPosition, setGradientPosition] = useState("0% 50%");

  useEffect(() => {
    const interval = setInterval(() => {
      // Cycle through gradient positions
      setGradientPosition((prev) => {
        if (prev === "0% 50%") return "100% 50%";
        return "0% 50%";
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center justify-center space-x-4">
      <div className="relative w-20 h-20 rounded-full shadow-lg">
        <div
          className="absolute inset-0 rounded-full animate-pulse"
          style={{
            background: `radial-gradient(circle at ${gradientPosition}, #FF6B6B, #4F86C6, #87CEEB, #FFD700, #FFA07A)`,
            transition: "background-position 2s ease-in-out", // Smooth transition
            backgroundSize: "200% 200%", // Increased size for animation
          }}
        ></div>
      </div>
      <span className="text-gray-600 font-semibold text-lg">Thinking...</span>
    </div>
  );
};

export default Thinking;
