"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

type UnreadCountsContextType = {
  unreadCounts: { [senderId: string]: number };
  setUnreadCounts: React.Dispatch<
    React.SetStateAction<{ [senderId: string]: number }>
  >;
};

const UnreadCountsContext = createContext<UnreadCountsContextType | undefined>(
  undefined
);

export const UnreadCountsProvider = ({ children }: { children: ReactNode }) => {
  const [unreadCounts, setUnreadCounts] = useState<{
    [senderId: string]: number;
  }>({});
  return (
    <UnreadCountsContext.Provider value={{ unreadCounts, setUnreadCounts }}>
      {children}
    </UnreadCountsContext.Provider>
  );
};

export const useUnreadCounts = () => {
  const context = useContext(UnreadCountsContext);
  if (!context) {
    throw new Error(
      "useUnreadCounts must be used within an UnreadCountsProvider"
    );
  }
  return context;
};
