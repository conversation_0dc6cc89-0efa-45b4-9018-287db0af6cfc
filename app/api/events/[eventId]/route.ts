// app/api/events/[eventId]/route.ts
import { NextResponse } from "next/server";
import { getEventById, updateEventById, deleteEventById } from "@/db/queries"; // Use functions from queries.ts
import { isAdmin } from "@/lib/admin";

// Fetch a specific event by ID
export const GET = async (
  req: Request,
  { params }: { params: { eventId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  try {
    const event = await getEventById(params.eventId); // Use getEventById from queries.ts
    if (!event) {
      return new NextResponse("Event not found", { status: 404 });
    }
    return NextResponse.json(event);
  } catch (error) {
    console.error("Error fetching event:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch event" }),
      { status: 500 }
    );
  }
};

// Update a specific event by ID
export const PUT = async (
  req: Request,
  { params }: { params: { eventId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  try {
    const body = await req.json();
    const updatedEvent = await updateEventById(params.eventId, body); // Use updateEventById from queries.ts
    if (!updatedEvent) {
      return new NextResponse("Event not found", { status: 404 });
    }
    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error("Error updating event:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to update event" }),
      { status: 500 }
    );
  }
};

// Delete a specific event by ID
export const DELETE = async (
  req: Request,
  { params }: { params: { eventId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  try {
    const deletedEvent = await deleteEventById(params.eventId); // Use deleteEventById from queries.ts
    if (!deletedEvent) {
      return new NextResponse("Event not found", { status: 404 });
    }
    return NextResponse.json(deletedEvent);
  } catch (error) {
    console.error("Error deleting event:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to delete event" }),
      { status: 500 }
    );
  }
};
