import { NextResponse } from "next/server";
import { getEvents, createEvent } from "@/db/queries";
import { isAdmin } from "@/lib/admin";
import { v4 as uuidv4 } from "uuid";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { eventParticipants } from "@/db/schema";
import { eq } from "drizzle-orm";

// We'll add the AppID so we can prefix it to the jitsiRoomName
// whenever we generate random meeting rooms. This ensures we stay
// within the JaaS domain for non-auth participants.
const JAAS_APP_ID = "vpaas-magic-cookie-80ac716282f04f7ab743f2a09b32c6f2";

// function to generate random Jitsi room names, now with a direct
// prefix that includes your JaaS App ID. This is crucial if you want
// the "Allow meeting participants to join unauthenticated" toggle
// to apply to your rooms.
function generateRandomJitsiRoomNameServerSide(): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";
  let result = "IqraRoom_";
  for (let i = 0; i < 10; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  // Add the JaaS App ID namespace to the final name:
  return `${JAAS_APP_ID}/${result}`;
}

export const GET = async (req: Request) => {
  try {
    const { searchParams } = new URL(req.url);
    const scope = searchParams.get("scope");

    if (scope === "mine") {
      const { userId } = auth();
      if (!userId) {
        return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
      }
      const epRows = await db.query.eventParticipants.findMany({
        where: eq(eventParticipants.userId, userId),
      });
      const invitedEventIds = epRows.map((row) => row.eventId);

      if (invitedEventIds.length === 0) {
        return NextResponse.json([]);
      }
      const myEvents = await db.query.events.findMany({
        where: (table, { inArray }) => inArray(table.id, invitedEventIds),
      });
      const formatted = myEvents.map((evt) => ({
        ...evt,
        startTime: new Date(evt.startTime).toISOString(),
        endTime: new Date(evt.endTime).toISOString(),
      }));
      console.log("[GET /api/events?scope=mine] returning events:", formatted);
      return NextResponse.json(formatted);
    }

    if (scope === "all" || !scope) {
      if (!isAdmin()) {
        return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
      }
      const eventsData = await getEvents();
      const formattedEvents = eventsData.map((event) => ({
        ...event,
        startTime: new Date(event.startTime).toISOString(),
        endTime: new Date(event.endTime).toISOString(),
      }));
      console.log("Fetched events (teacher):", formattedEvents);
      return NextResponse.json(formattedEvents);
    }

    return NextResponse.json(
      { message: `Scope '${scope}' not recognized.` },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error fetching events:", error);
    return NextResponse.json(
      { message: "Failed to fetch events" },
      { status: 500 }
    );
  }
};

export const POST = async (req: Request) => {
  if (!isAdmin()) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await req.json();
    console.log("Received event data:", body);

    const startTime = new Date(body.startTime);
    const endTime = new Date(body.endTime);

    if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
      return NextResponse.json(
        { message: "Invalid date format" },
        { status: 400 }
      );
    }

    const googleEventId = uuidv4();

    const invitedStudentIds = body.invitedStudentIds || [];
    const isMeeting = body.isMeeting ?? false;
    let jitsiRoomName = body.jitsiRoomName ?? null;

    // If you didn't provide a title, set a basic fallback
    // so there's always a user-friendly name to display.
    if (!body.title || body.title.trim() === "") {
      body.title = "Untitled Meeting";
      console.log("[POST /api/events] No title provided, using fallback.");
    }

    // If user requests a meeting but no name is provided,
    // or if a name was provided but lacks the JaaS AppID prefix,
    // we ensure we add the prefix so the room is recognized by JaaS.
    if (isMeeting) {
      if (!jitsiRoomName || jitsiRoomName.trim() === "") {
        jitsiRoomName = generateRandomJitsiRoomNameServerSide();
        console.log(
          "[POST /api/events] Generating random jitsiRoomName server-side:",
          jitsiRoomName
        );
      } else if (!jitsiRoomName.startsWith(`${JAAS_APP_ID}/`)) {
        // We only apply this if you want *all* meeting rooms to belong
        // to your JaaS AppID. Otherwise, remove this condition.
        jitsiRoomName = `${JAAS_APP_ID}/${jitsiRoomName}`;
        console.log(
          "[POST /api/events] Prefixing user-provided jitsiRoomName with AppID:",
          jitsiRoomName
        );
      }
    }

    console.log(
      "[POST /api/events] Passing isMeeting and jitsiRoomName to createEvent:",
      isMeeting,
      jitsiRoomName
    );

    const newEvent = await createEvent({
      title: body.title,
      description: body.description,
      startTime,
      endTime,
      googleEventId,
      calendarLink: body.calendarLink || null,
      recurrenceRule: body.recurrenceRule || null,
      invitedStudentIds,
      isMeeting,
      jitsiRoomName,
    });

    console.log("Event created successfully:", newEvent);
    return NextResponse.json(newEvent);
  } catch (error) {
    console.error("Error creating event:", error);
    return NextResponse.json(
      { message: "Failed to create event" },
      { status: 500 }
    );
  }
};
