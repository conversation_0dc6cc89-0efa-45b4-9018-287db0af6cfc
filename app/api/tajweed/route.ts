// app/api/tajweed/route.ts
import { NextRequest, NextResponse } from "next/server";
import {
  convertToPhonemes,
  simpleAlign,
  diffFromAlignment,
  checkQalqalah,
  checkNoonTanweenRules,
  checkMeemRules,
  checkMaddRules,
  checkLaamRules,
  checkTafkheemTarqeeq,
  checkGhunnahRules,
  checkMakharijRules,
  detectRecitationStyle,
  generateLetterPositionReport,
  computeScore,
  Violation,
} from "@/app/tajweed/tajweedFeedback";

// --- Environment Variables & Constants ---
const HF_TOKEN = `Bearer ${process.env.HF_TOKEN}`;
const HF_TAJWEED_URL = process.env.HF_TAJWEED_URL!;

const DEFAULT_REF_TEXT = "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ";
const SAMPLE_RATE_HZ = 16000; // Assuming 16kHz sample rate for the audio
const BYTES_PER_SAMPLE = 2; // Assuming 16-bit audio (2 bytes per sample)

/**
 * @description API endpoint to assess Tajweed recitation from an audio file.
 * It receives an audio file, sends it to a Hugging Face model for phoneme transcription,
 * compares the transcription against a reference text, and returns a detailed
 * analysis of Tajweed rule violations.
 *
 * @param {NextRequest} request The incoming request object, expected to contain
 * multipart/form-data with an 'audio' file and optional 'verseText'.
 * @returns {NextResponse} A JSON response with the score, violations, and other details.
 */
export async function POST(request: NextRequest) {
  console.log("POST /api/tajweed - Assessment request received.");

  // --- 1. Validate Server Configuration ---
  if (!HF_TAJWEED_URL) {
    const errorMessage =
      "Server configuration error: HF_TAJWEED_URL environment variable is not set.";
    console.error(errorMessage);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
  if (!process.env.HF_TOKEN) {
    const errorMessage =
      "Server configuration error: HF_TOKEN environment variable is not set.";
    console.error(errorMessage);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }

  try {
    // --- 2. Parse Incoming Request Data ---
    const formData = await request.formData();
    const file = formData.get("audio") as Blob | null;
    const ruleId = formData.get("ruleId") as string | null; // TODO: Implement filtering based on ruleId if needed.
    const verseText = formData.get("verseText") as string | null;
    const REF_ARABIC_TEXT = verseText || DEFAULT_REF_TEXT;

    console.log("Using reference Arabic text:", REF_ARABIC_TEXT);

    if (!file) {
      console.error("No audio file received in form data.");
      return NextResponse.json(
        { error: "No audio file uploaded." },
        { status: 400 }
      );
    }

    console.log(
      `Received audio file: name=${(file as File).name || "blob"}, size=${
        file.size
      } bytes, type=${file.type}`
    );

    // --- 3. Transcribe Audio using Hugging Face Model ---
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    console.log(
      `Sending ${buffer.length} bytes to HF Endpoint: ${HF_TAJWEED_URL}`
    );

    // Wrap Node Buffer in Uint8Array so fetch accepts it as BodyInit
    const wavBody = new Uint8Array(buffer);

    const hfRes = await fetch(HF_TAJWEED_URL, {
      method: "POST",
      headers: {
        Authorization: HF_TOKEN,
        "Content-Type": "audio/wav",
      },
      body: wavBody,
    });

    console.log(`Hugging Face API Response Status: ${hfRes.status}`);
    const responseText = await hfRes.text();

    if (!hfRes.ok) {
      console.error(
        "Hugging Face inference error:",
        hfRes.status,
        responseText
      );
      let errorDetail = responseText;
      try {
        const jsonError = JSON.parse(responseText);
        errorDetail = jsonError.error || JSON.stringify(jsonError);
      } catch {
        // Response is not JSON, use the raw text
      }
      return NextResponse.json(
        { error: `Hugging Face API Error (${hfRes.status}): ${errorDetail}` },
        { status: hfRes.status }
      );
    }

    console.log("Raw model response (text):", responseText);
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
    } catch {
      // If response is not JSON, treat the whole string as the transcription
      parsedResponse = { text: responseText };
    }

    const phonemeText = (parsedResponse.text || "").trim();
    const predictedPhonemes = phonemeText.split(" ").filter(Boolean);

    if (predictedPhonemes.length === 0) {
      console.error("Model returned empty or invalid phoneme string.");
      return NextResponse.json(
        {
          error:
            "Could not transcribe audio. The model returned an empty result.",
        },
        { status: 500 }
      );
    }

    console.log("Predicted phonemes array:", predictedPhonemes);

    // --- 4. Process and Align Phonemes ---
    const {
      phonemes: refPhonemes,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
    } = convertToPhonemes(REF_ARABIC_TEXT);

    const alignment = simpleAlign(refPhonemes, predictedPhonemes);
    const { subs, ins, dels } = diffFromAlignment(
      alignment,
      refPhonemes,
      predictedPhonemes,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals
    );

    // --- 5. Analyze Recitation and Check Tajweed Rules ---
    const audioDuration = file.size / (SAMPLE_RATE_HZ * BYTES_PER_SAMPLE);
    const recitationContext = detectRecitationStyle(
      predictedPhonemes,
      audioDuration
    );
    console.log(`Detected recitation style: ${recitationContext.speed} speed`);

    // Use spread operator for cleaner violation aggregation
    const violations: Violation[] = [
      ...checkQalqalah(
        refPhonemes,
        subs,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkNoonTanweenRules(
        refPhonemes,
        predictedPhonemes,
        subs,
        dels,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkMeemRules(
        refPhonemes,
        predictedPhonemes,
        subs,
        dels,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkMaddRules(
        refPhonemes,
        predictedPhonemes,
        subs,
        dels,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkLaamRules(
        refPhonemes,
        predictedPhonemes,
        subs,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkTafkheemTarqeeq(
        refPhonemes,
        predictedPhonemes,
        subs,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkGhunnahRules(
        refPhonemes,
        predictedPhonemes,
        subs,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
      ...checkMakharijRules(
        refPhonemes,
        predictedPhonemes,
        subs,
        refArabicMap,
        wordMap,
        letterPositions,
        wordOriginals,
        recitationContext
      ),
    ];

    // --- 6. Finalize Score and Report ---
    const score = computeScore(violations);

    if (violations.length === 0 && (subs.length > 0 || dels.length > 0)) {
      const missedPhonemes = dels.map((d) => d[1]).join(", ");
      violations.push({
        rule: "general-pronunciation",
        message: `Your pronunciation differs slightly from the reference. Focus on: ${
          missedPhonemes || "overall rhythm and clarity"
        }`,
        severity: "mild",
        position: 0,
        ref_phoneme: null,
        pred_phoneme: null,
        arabic_char: "",
        word_idx: 0,
        word: "",
        letter_position: "various",
      });
    }

    console.log(`Assessment Score: ${score}`);
    console.log(`Detected ${violations.length} violations.`);

    const detailedReport = generateLetterPositionReport(
      violations,
      wordOriginals
    );

    // --- 7. Return Final Response ---
    return NextResponse.json({
      score: score,
      violations: violations,
      phonemes: predictedPhonemes,
      referencePhonemes: refPhonemes,
      report: detailedReport,
      recitationContext: recitationContext,
      success: true,
    });
  } catch (err: any) {
    console.error("--- Critical Error in /api/tajweed Route ---");
    console.error(err);
    console.error("------------------------------------------");
    return NextResponse.json(
      {
        error:
          err.message ||
          "An unknown server error occurred during the assessment.",
        success: false,
      },
      { status: 500 }
    );
  }
}
