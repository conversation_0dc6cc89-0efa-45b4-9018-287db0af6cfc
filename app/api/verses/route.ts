// app/api/verses/route.ts

import { NextRequest, NextResponse } from "next/server";

// Define the expected structure for the response data
type VerseData = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

// Helper function to remove Basmala
function removeBasmala(verseText: string): string {
  // *** CORRECTED BASMALA STRING ***
  // Reconstructed based on the split words logged: "بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ"
  // We also check for a potential trailing space just in case the API adds one before the next part.
  const basmalaExact = "بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ";
  const basmalaWithSpace = basmalaExact + " "; // Check with a space too

  console.log(
    `Server: Checking verse 1 text [${verseText.substring(0, 50)}...]`
  ); // Log start of text

  if (verseText.startsWith(basmalaWithSpace)) {
    console.log(`Server: Basmala (with space) found. Removing...`);
    // Remove the version with the space
    return verseText.slice(basmalaWithSpace.length).trimStart();
  } else if (verseText.startsWith(basmalaExact)) {
    console.log(`Server: Basmala (exact) found. Removing...`);
    // Remove the exact version
    return verseText.slice(basmalaExact.length).trimStart();
  } else {
    console.log(
      `Server: Basmala string constant did not match start of verse text.`
    );
  }
  // Return original text if no match
  return verseText;
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const surahNumberStr = searchParams.get("surah");
  const startVerseStr = searchParams.get("start");
  const endVerseStr = searchParams.get("end");

  // 1. Validate Input Parameters
  // ... (validation remains the same) ...
  if (!surahNumberStr || !startVerseStr || !endVerseStr)
    return NextResponse.json({ error: "Missing parameters" }, { status: 400 });
  const surahNumber = parseInt(surahNumberStr, 10);
  const startVerse = parseInt(startVerseStr, 10);
  const endVerse = parseInt(endVerseStr, 10);
  if (
    isNaN(surahNumber) ||
    isNaN(startVerse) ||
    isNaN(endVerse) ||
    startVerse <= 0 ||
    endVerse <= 0 ||
    surahNumber <= 0 ||
    startVerse > endVerse
  ) {
    return NextResponse.json({ error: "Invalid parameters" }, { status: 400 });
  }

  // 2. Fetch Full Surah Text from External API
  const apiUrl = `https://api.alquran.cloud/v1/surah/${surahNumber}/quran-uthmani`;
  console.log(`Server: Fetching from AlQuranCloud: ${apiUrl}`);

  try {
    const response = await fetch(apiUrl);

    if (!response.ok) {
      // ... (error handling remains the same) ...
      console.error(
        `Server: AlQuranCloud API error! Status: ${response.status}, URL: ${apiUrl}`
      );
      const errorBody = await response.text();
      return NextResponse.json(
        {
          error: `Failed API fetch. Status: ${response.status}`,
          details: errorBody,
        },
        { status: response.status === 404 ? 404 : 502 }
      );
    }

    const data = await response.json();

    // 3. Process and Filter the Data
    const allAyahs = data?.data?.ayahs;

    if (!allAyahs || !Array.isArray(allAyahs)) {
      // ... (error handling remains the same) ...
      console.error(`Server: Invalid data structure for Surah ${surahNumber}.`);
      return NextResponse.json(
        { error: "Invalid upstream data" },
        { status: 502 }
      );
    }

    const filteredVerses: VerseData[] = allAyahs
      .filter(
        (ayah: any) =>
          typeof ayah.numberInSurah === "number" &&
          ayah.numberInSurah >= startVerse &&
          ayah.numberInSurah <= endVerse
      )
      .map((ayah: any) => {
        let verseText = ayah.text;

        // Apply Basmala removal check (unchanged logic, relies on updated removeBasmala function)
        if (startVerse === 1 && ayah.numberInSurah === 1 && surahNumber !== 1) {
          verseText = removeBasmala(verseText);
        }

        return {
          surahNumber: surahNumber,
          verseNumber: ayah.numberInSurah,
          text: verseText,
        };
      });

    if (filteredVerses.length === 0 && endVerse >= startVerse) {
      console.warn(
        `Server: No verses found for range ${startVerse}-${endVerse} for Surah ${surahNumber}.`
      );
    }

    // 4. Return Successful Response
    return NextResponse.json(filteredVerses, { status: 200 });
  } catch (error: any) {
    // ... (error handling remains the same) ...
    console.error("Server: Internal error:", error);
    return NextResponse.json(
      { error: "Internal Server Error", details: error.message || "Unknown" },
      { status: 500 }
    );
  }
}
