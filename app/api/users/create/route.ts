import { NextRequest, NextResponse } from "next/server";
import { auth, clerkClient } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user already exists
    const existing = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId));

    if (existing.length > 0) {
      const row = existing[0];
      return NextResponse.json({
        userId: row.userId,
        email: row.email,
        role: row.role,
        displayName: row.displayName ?? undefined,
        createdAt: row.createdAt ?? undefined,
        schoolUsername: row.schoolUsername ?? null,
        schoolPassword: row.schoolPassword ?? null,
      });
    }

    // Get user data from Clerk
    const clerkUser = await clerkClient.users.getUser(userId);
    const email = clerkUser?.emailAddresses?.[0]?.emailAddress || "<EMAIL>";
    const displayName = clerkUser?.fullName || "";

    // Create new user
    const inserted = await db
      .insert(users)
      .values({ 
        userId, 
        email, 
        role: "student", 
        displayName 
      })
      .returning();

    const [created] = inserted;
    
    return NextResponse.json({
      userId: created.userId,
      email: created.email,
      role: created.role,
      displayName: created.displayName ?? undefined,
      createdAt: created.createdAt ?? undefined,
      schoolUsername: created.schoolUsername ?? null,
      schoolPassword: created.schoolPassword ?? null,
    });

  } catch (error) {
    console.error("[API] Error creating user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
