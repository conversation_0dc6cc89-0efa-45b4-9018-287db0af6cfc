// app/api/users/route.ts
import { NextResponse } from "next/server";
import { isTeacher } from "@/lib/users"; // This is server-side only—safe here

/**
 * This route simply checks if the current user is a teacher
 * and returns { isTeacher: boolean }
 */
export async function GET() {
  try {
    const teacherStatus = await isTeacher();
    return NextResponse.json({ isTeacher: teacherStatus });
  } catch (error) {
    console.error("[api/users/route.ts] Error checking teacher status:", error);
    // Optionally return a 401 or 500 if there's an issue
    return NextResponse.json({ isTeacher: false }, { status: 200 });
  }
}
