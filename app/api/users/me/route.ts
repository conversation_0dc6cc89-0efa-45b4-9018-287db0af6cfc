// app/api/users/me/route.ts
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";

export async function GET() {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized. No user session." },
        { status: 401 }
      );
    }

    // Query the user record from your database
    const userRecord = await db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    if (!userRecord) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      userId: userRecord.userId,
      role: userRecord.role,
      displayName: userRecord.displayName,
      avatarSrc: userRecord.avatarSrc,
    });
  } catch (error) {
    console.error("[api/users/me] Error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
