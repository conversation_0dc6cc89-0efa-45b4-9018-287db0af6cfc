// app/api/auth/sign-out/route.ts
import { NextResponse } from "next/server";
import { auth, clerkClient } from "@clerk/nextjs/server";

export async function POST() {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: "No active session" }, { status: 400 });
    }

    // Clerk doesn't support server-side sign-out directly in Next.js API routes
    // This is a placeholder to acknowledge the request
    // The actual sign-out will be handled client-side

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[api/auth/sign-out] Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
