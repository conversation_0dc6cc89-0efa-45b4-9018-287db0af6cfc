import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { messages } from "@/db/schema";
import { and, eq, sql } from "drizzle-orm";

// This endpoint returns unread message counts for the logged-in user,
// grouped by sender (i.e. conversation partner).
export async function GET(request: Request) {
  try {
    // 1) Identify the current user
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 2) Query the DB for unread messages where recipientId is the current user
    // and read_at is NULL. Group results by senderId.
    const unreadCounts = await db
      .select({
        senderId: messages.senderId,
        unreadCount: sql<number>`COUNT(*)`,
      })
      .from(messages)
      .where(
        and(eq(messages.recipientId, userId), sql`${messages.readAt} IS NULL`)
      )
      .groupBy(messages.senderId);

    // 3) Return the unread counts in JSON format
    return NextResponse.json({ unreadCounts });
  } catch (error) {
    // In a production application, you might want to log this error
    // to an external logging service (e.g., Sentry, DataDog, CloudWatch)
    // instead of console.error.
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
