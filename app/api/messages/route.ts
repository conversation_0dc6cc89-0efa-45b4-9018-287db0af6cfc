import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { and, eq } from "drizzle-orm";
import { messages } from "@/db/schema";

export async function GET(request: Request) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const recipientId = searchParams.get("recipientId");
    if (!recipientId) {
      return NextResponse.json(
        { error: "Missing recipientId query parameter" },
        { status: 400 }
      );
    }

    const chat = await db.query.messages.findMany({
      where: (table, { or }) =>
        or(
          and(eq(table.senderId, userId), eq(table.recipientId, recipientId)),
          and(eq(table.senderId, recipientId), eq(table.recipientId, userId))
        ),
      orderBy: (table, { asc }) => [asc(table.createdAt)],
    });

    return NextResponse.json(chat);
  } catch (error) {
    // In a production application, you might want to log this error
    // to an external logging service (e.g., Sentry, DataDog, CloudWatch)
    // instead of console.error.
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { recipientId, content } = body;

    if (!recipientId || !content) {
      return NextResponse.json(
        { error: "Missing recipientId or content" },
        { status: 400 }
      );
    }

    const now = new Date();
    const inserted = await db
      .insert(messages)
      .values({
        senderId: userId,
        recipientId,
        content,
        createdAt: now,
      })
      .returning();

    return NextResponse.json(inserted[0]);
  } catch (error) {
    // In a production application, you might want to log this error
    // to an external logging service (e.g., Sentry, DataDog, CloudWatch)
    // instead of console.error.
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
