import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { and, eq, sql } from "drizzle-orm";
import { messages } from "@/db/schema";

export async function POST(request: Request) {
  try {
    // 1) Identify the current user
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 2) Parse the request body to get the conversation partner's id.
    // We assume the client sends a JSON body with a "conversationPartnerId" field.
    const body = await request.json();
    const { conversationPartnerId } = body;
    if (!conversationPartnerId) {
      return NextResponse.json(
        { error: "Missing conversationPartnerId" },
        { status: 400 }
      );
    }

    // 3) Update all messages from the conversation partner to the current user that are still unread (read_at is null)
    const now = new Date();
    const updated = await db
      .update(messages)
      .set({ readAt: now })
      .where(
        and(
          eq(messages.recipientId, userId),
          eq(messages.senderId, conversationPartnerId),
          sql`${messages.readAt} IS NULL`
        )
      )
      .returning();

    // 4) Return the result
    return NextResponse.json({
      updatedCount: updated.length,
      message: "Messages marked as read",
    });
  } catch (error) {
    // In a production application, you might want to log this error
    // to an external logging service (e.g., Sentry, DataDog, CloudWatch)
    // instead of console.error.
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
