// app/api/my-students/route.ts
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { eq, sql, desc, asc } from "drizzle-orm";
import { teacherStudents, users } from "@/db/schema";

/**
 * This route returns only the students that belong to a teacher.
 * Enhanced with extensive debugging.
 */
export async function GET() {
  console.log("========= /api/my-students ENDPOINT CALLED =========");
  try {
    // 1) Identify the current user
    const { userId } = auth();
    console.log("[my-students] Auth userId:", userId);

    if (!userId) {
      console.log("[my-students] No authenticated user found");
      return NextResponse.json(
        { error: "Unauthorized. No user session." },
        { status: 401 }
      );
    }

    // 2) Check this user's row in 'users' to see if teacher or student
    console.log("[my-students] Querying user record for", userId);
    const userRow = await db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    console.log(
      "[my-students] User record found:",
      userRow ? JSON.stringify(userRow) : "null"
    );

    if (!userRow) {
      console.log("[my-students] User record not found in database");
      return NextResponse.json(
        { error: "User record not found." },
        { status: 404 }
      );
    }

    // Only teachers can see their students
    if (userRow.role !== "teacher") {
      console.log(
        "[my-students] Current user is not a teacher. Role:",
        userRow.role
      );
      return NextResponse.json([]);
    }

    console.log(
      "[my-students] Confirmed user is a teacher. Searching for students..."
    );

    // DEBUG: Check raw SQL access to pivot table
    console.log("[my-students] DEBUG: Direct SQL query to check pivot table");
    try {
      const rawPivotQuery = await db.execute(
        sql`SELECT * FROM teacher_students WHERE "teacher_id" = ${userId} LIMIT 10`
      );
      console.log(
        "[my-students] Raw SQL pivot results:",
        JSON.stringify(rawPivotQuery.rows)
      );
    } catch (sqlError) {
      console.error("[my-students] Error executing raw SQL query:", sqlError);
    }

    // 3) Query pivot table with teacherId = userId
    console.log(
      "[my-students] Querying teacher_students table for teacherId:",
      userId
    );
    const pivotRows = await db.query.teacherStudents.findMany({
      where: eq(teacherStudents.teacherId, userId),
    });

    console.log("[my-students] Found pivot rows:", pivotRows.length);
    console.log("[my-students] Pivot rows details:", JSON.stringify(pivotRows));

    // If none exist
    if (pivotRows.length === 0) {
      console.log("[my-students] No students found for this teacher");
      return NextResponse.json([]);
    }

    // 4) Collect studentIds from pivot
    const studentIds = pivotRows.map((p) => p.studentId);
    console.log("[my-students] Student IDs found:", JSON.stringify(studentIds));

    // 5) Query the users table for these studentIds
    console.log("[my-students] Querying users table for student records");
    const studentRecords = await db.query.users.findMany({
      where: (table, { inArray }) => inArray(table.userId, studentIds),
    });

    console.log("[my-students] Student records found:", studentRecords.length);
    console.log(
      "[my-students] Student records:",
      JSON.stringify(studentRecords)
    );

    // If we found fewer students than expected, log the discrepancy
    if (studentRecords.length < studentIds.length) {
      console.log(
        "[my-students] Warning: Some student IDs not found in users table"
      );
      console.log(
        "[my-students] Missing IDs:",
        studentIds.filter(
          (id) => !studentRecords.some((record) => record.userId === id)
        )
      );
    }

    // 6) Format the data so front-end can display
    const studentData = studentRecords.map((stu) => {
      return {
        id: stu.userId, // must be string to match the 'id' in my-students-client
        name: stu.displayName || "Unknown Student",
        avatar: "/woman.svg",
        quizzesCompleted: 0,
        score: "0%",
        lastActive: "N/A",
        email: stu.email,
        subjects: [],
        status: "offline",
      };
    });

    console.log(
      "[my-students] Final formatted data:",
      JSON.stringify(studentData)
    );

    // 7) Return JSON
    return NextResponse.json(studentData);
  } catch (error) {
    console.error("[my-students] Uncaught error in endpoint:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  } finally {
    console.log("========= /api/my-students ENDPOINT COMPLETE =========");
  }
}
