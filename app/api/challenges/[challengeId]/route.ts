import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import db from "@/db/drizzle";
import {
  challenges,
  challengeOptions as challengeOptionsTable,
} from "@/db/schema";
import { isAdmin } from "@/lib/admin";

// GET a specific challenge by ID
export const GET = async (
  req: Request,
  { params }: { params: { challengeId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, params.challengeId),
    with: {
      challengeOptions: true,
    },
  });

  if (!challenge) {
    return new NextResponse("Challenge not found", { status: 404 });
  }

  // Ensure that the required fields for IMAGE_AUDIO_SELECT are present
  if (
    challenge.type === "IMAGE_AUDIO_SELECT" &&
    (!challenge.question ||
      !challenge.type ||
      !challenge.lessonId ||
      challenge.order === undefined ||
      !challenge.mediaType)
  ) {
    return new NextResponse(
      "Missing required fields for 'Image Audio Select'",
      {
        status: 400,
      }
    );
  }

  const response: any = {
    id: challenge.id,
    question: challenge.question,
    type: challenge.type,
    lessonId: challenge.lessonId,
    order: challenge.order,
    mediaType: challenge.mediaType,
    mediaUrl: challenge.mediaUrl,
    topCardText: challenge.topCardText,
    topCardAudio: challenge.topCardAudio,
    challengeOptions: challenge.challengeOptions.map((option: any) => ({
      id: option.id,
      text: option.text,
      imageSrc: option.imageSrc,
      audioSrc: option.audioSrc,
      matchPairId: option.matchPairId,
      correct: option.correct,
      sequence: option.sequence,
      side: option.side,
    })),
  };

  if (challenge.audioSrc) {
    response.audioSrc = challenge.audioSrc;
  }

  if (challenge.sentence) {
    response.sentence = challenge.sentence;
  }

  return NextResponse.json(response);
};

// PUT to update a specific challenge by ID
export const PUT = async (
  req: Request,
  { params }: { params: { challengeId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const body = await req.json();
  const { challengeOptions, ...challengeData } = body;

  // Validation rules
  if (
    challengeData.type === "TAP_WHAT_YOU_HEAR" &&
    challengeOptions &&
    !challengeOptions.every((option: any) => option.audioSrc)
  ) {
    return new NextResponse("Missing audio source for 'Tap What You Hear'", {
      status: 400,
    });
  }

  if (
    challengeData.type === "IMAGE_AUDIO_SELECT" &&
    (!challengeData.mediaType || !challengeData.mediaUrl)
  ) {
    return new NextResponse(
      "Missing media type or media URL for 'Image Audio Select'",
      {
        status: 400,
      }
    );
  }

  // Remove validation for topCardText and topCardAudio
  // These fields are now optional, regardless of the type and mediaType

  if (challengeData.type === "DRAG_AND_DROP" && !challengeData.audioSrc) {
    return new NextResponse("Missing audio source for 'Drag and Drop'", {
      status: 400,
    });
  }

  // Updated validation for ASSIST type
  if (
    challengeData.type === "ASSIST" &&
    challengeData.sentence && // Check if a sentence is provided
    !challengeData.sentence.includes("blank") &&
    !challengeData.sentence.includes("فارغ")
  ) {
    return new NextResponse(
      "Invalid sentence for 'ASSIST'. If a sentence is provided, it must include 'blank' or 'فارغ'.",
      {
        status: 400,
      }
    );
  }

  // ADDED: "SPEAK_THIS_ADVANCED" check (no additional validations)
  if (challengeData.type === "SPEAK_THIS_ADVANCED") {
    console.log("Updating SPEAK_THIS_ADVANCED challenge. No extra checks.");
  }

  // Update the challenge
  const updatedChallenge = await db
    .update(challenges)
    .set({
      ...challengeData,
    })
    .where(eq(challenges.id, params.challengeId))
    .returning();

  // Update challenge options
  if (challengeOptions) {
    await db
      .delete(challengeOptionsTable)
      .where(eq(challengeOptionsTable.challengeId, params.challengeId));
    await db.insert(challengeOptionsTable).values(
      challengeOptions.map((option: any) => ({
        ...option,
        challengeId: params.challengeId,
        sequence: option.sequence,
        side: option.side,
      }))
    );
  }

  return NextResponse.json(updatedChallenge[0]);
};

// DELETE a specific challenge by ID
export const DELETE = async (
  req: Request,
  { params }: { params: { challengeId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const deletedChallenge = await db
    .delete(challenges)
    .where(eq(challenges.id, params.challengeId))
    .returning();

  return NextResponse.json(deletedChallenge[0]);
};
