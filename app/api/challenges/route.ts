import { NextResponse } from "next/server";
import db from "@/db/drizzle";
import { isAdmin } from "@/lib/admin";
import { challenges } from "@/db/schema";

// GET all challenges
export const GET = async () => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const data = await db.query.challenges.findMany();
  return NextResponse.json(data);
};

// POST a new challenge
export const POST = async (req: Request) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  try {
    const body = await req.json();

    // Validate required fields for challenge
    if (
      !body.question ||
      !body.type ||
      !body.lessonId ||
      body.order === undefined
    ) {
      return new NextResponse("Missing required challenge fields", {
        status: 400,
      });
    }

    // Validate required fields for "IMAGE_AUDIO_SELECT" type
    if (body.type === "IMAGE_AUDIO_SELECT" && !body.mediaType) {
      return new NextResponse("Missing media type for 'Image Audio Select'", {
        status: 400,
      });
    }

    // Validate audioSrc for "Tap What You Hear" type
    if (body.type === "TAP_WHAT_YOU_HEAR" && !body.audioSrc) {
      return new NextResponse("Missing audio source for 'Tap What You Hear'", {
        status: 400,
      });
    }

    // Validate audioSrc for "Drag and Drop" type
    if (body.type === "DRAG_AND_DROP" && !body.audioSrc) {
      return new NextResponse("Missing audio source for 'Drag and Drop'", {
        status: 400,
      });
    }

    // Validate sentence for "Fill in the Blank" type
    if (
      body.type === "FILL_IN_THE_BLANK" &&
      (!body.sentence ||
        (!body.sentence.includes("blank") && !body.sentence.includes("فارغ")))
    ) {
      return new NextResponse(
        "Missing or invalid sentence for 'Fill in the Blank'. Sentence must include 'blank' or 'فارغ'.",
        {
          status: 400,
        }
      );
    }

    // Validate fields for ASSIST type
    if (
      body.type === "ASSIST" &&
      body.sentence && // Check if a sentence is provided
      !body.sentence.includes("blank") &&
      !body.sentence.includes("فارغ")
    ) {
      return new NextResponse(
        "Invalid sentence for 'ASSIST'. If a sentence is provided, it must include 'blank' or 'فارغ'.",
        {
          status: 400,
        }
      );
    }

    // ADDED: "SPEAK_THIS_ADVANCED" handling (no special validations here)
    if (body.type === "SPEAK_THIS_ADVANCED") {
      console.log("Creating a new SPEAK_THIS_ADVANCED challenge");
    }

    // Insert challenge data
    const data = await db
      .insert(challenges)
      .values({
        question: body.question,
        type: body.type,
        lessonId: body.lessonId,
        order: body.order,
        audioSrc: body.audioSrc || null, // Set audioSrc to null if not provided
        mediaType: body.mediaType || null, // Set mediaType to null if not provided
        mediaUrl: body.mediaUrl || null, // Set mediaUrl to null if not provided
        topCardText: body.topCardText || null, // Set topCardText to null if not provided
        topCardAudio: body.topCardAudio || null, // Set topCardAudio to null if not provided
        sentence: body.sentence || null, // Set sentence to null if not provided
      })
      .returning();

    return NextResponse.json(data[0]);
  } catch (error: any) {
    console.error("Error in POST /api/challenges:", error);
    return new NextResponse(error.message || "Server Error", { status: 500 });
  }
};
