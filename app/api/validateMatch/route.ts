import { eq, isNotNull, and } from "drizzle-orm";
import { NextResponse } from "next/server";
import db from "@/db/drizzle";
import { auth } from "@clerk/nextjs";
import {
  challengeOptions,
  challengeProgress,
  challenges,
  userProgress,
} from "@/db/schema";
import { getUserProgress, getUserSubscription } from "@/db/queries";
import { revalidatePath } from "next/cache";

// Utility function to revalidate multiple paths
const revalidatePaths = (paths: string[]) => {
  paths.forEach((path) => revalidatePath(path));
};

export const POST = async (req: Request) => {
  const { userId } = await auth();

  if (!userId) {
    console.error("Unauthorized access attempt");
    return new NextResponse("Unauthorized", { status: 403 });
  }

  let requestBody;
  try {
    requestBody = await req.json();
    console.log("Request Body:", requestBody);
  } catch (error) {
    console.error("Invalid request body:", error);
    return new NextResponse("Invalid request body", { status: 400 });
  }

  const { challengeId, selectedOptionId, matchingPairId } = requestBody;

  if (!challengeId || !selectedOptionId || !matchingPairId) {
    console.error("Missing required fields:", {
      challengeId,
      selectedOptionId,
      matchingPairId,
    });
    return new NextResponse("Missing required fields", { status: 400 });
  }

  try {
    const challenge = await db.query.challenges.findFirst({
      where: eq(challenges.id, challengeId),
    });

    if (!challenge || challenge.type !== "MATCHING") {
      console.error("Challenge not found or invalid type:", challenge);
      return new NextResponse("Challenge not found or invalid type", {
        status: 404,
      });
    }

    const currentUserProgress = await getUserProgress();
    const userSubscription = await getUserSubscription();

    if (!currentUserProgress) {
      console.error("User progress not found");
      return new NextResponse("User progress not found", { status: 404 });
    }

    const [selectedOption, matchingPairOption] = await Promise.all([
      db.query.challengeOptions.findFirst({
        where: eq(challengeOptions.id, selectedOptionId),
      }),
      db.query.challengeOptions.findFirst({
        where: eq(challengeOptions.id, matchingPairId),
      }),
    ]);

    if (!selectedOption || !matchingPairOption) {
      console.error("Option not found:", {
        selectedOption,
        matchingPairOption,
      });
      return new NextResponse("Option not found", { status: 404 });
    }

    const isCorrectMatch =
      selectedOption.matchPairId === matchingPairOption.matchPairId;

    if (isCorrectMatch) {
      await db.insert(challengeProgress).values({
        challengeId,
        userId,
        completed: true,
      });

      await db
        .update(userProgress)
        .set({
          points: currentUserProgress.points + 10,
        })
        .where(eq(userProgress.userId, userId));

      // Check if all pairs are matched
      const allPairsMatched = await db.query.challengeOptions.findMany({
        where: and(
          eq(challengeOptions.challengeId, challengeId),
          isNotNull(challengeOptions.matchPairId)
        ),
      });

      const matchedPairsSet = new Set(
        allPairsMatched.map((option) => option.matchPairId)
      );

      if (matchedPairsSet.size === allPairsMatched.length / 2) {
        console.log("All pairs matched");
        revalidatePaths([
          "/learn",
          `/lesson/${challenge.lessonId}`,
          "/quests",
          "/leaderboard",
        ]);

        return NextResponse.json({ correct: true });
      } else {
        console.log("Not all pairs matched yet");
        return NextResponse.json({ correct: true, allPairsMatched: false });
      }
    } else {
      if (currentUserProgress.hearts === 0 && !userSubscription?.isActive) {
        console.error("Out of hearts");
        return NextResponse.json({ error: "hearts" });
      }

      await db
        .update(userProgress)
        .set({
          hearts: Math.max(currentUserProgress.hearts - 1, 0),
        })
        .where(eq(userProgress.userId, userId));

      revalidatePaths([
        "/learn",
        `/lesson/${challenge.lessonId}`,
        "/quests",
        "/leaderboard",
      ]);

      console.log("Incorrect match");
      return NextResponse.json({ correct: false });
    }
  } catch (error) {
    console.error("Error validating match:", error);
    return new NextResponse("Internal server error", { status: 500 });
  }
};
