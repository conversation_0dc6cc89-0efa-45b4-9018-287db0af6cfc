// app/api/my-contacts/route.ts

import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { and, desc, eq, or } from "drizzle-orm";
import { teacherStudents, users, messages } from "@/db/schema";

/**
 * This API returns the contacts list for the messaging page:
 * - For teachers, it returns their students
 * - For students, it returns their teachers
 *
 * Each contact includes their last message (if any) for display in the sidebar.
 */
export async function GET() {
  try {
    // 1) Identify the current user
    const { userId } = auth();
    console.log("[my-contacts] Auth userId:", userId);

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized. No user session." },
        { status: 401 }
      );
    }

    // 2) Check if user is a teacher or student
    const userRecord = await db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    if (!userRecord) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    console.log("[my-contacts] User role:", userRecord.role);

    // 3) Find contacts based on the user's role
    const isTeacher = userRecord.role === "teacher";
    let contactIds: string[] = [];

    if (isTeacher) {
      // Teachers see their students
      const studentRelations = await db.query.teacherStudents.findMany({
        where: eq(teacherStudents.teacherId, userId),
      });

      contactIds = studentRelations.map((relation) => relation.studentId);
      console.log("[my-contacts] Found student contacts:", contactIds.length);
    } else {
      // Students see their teachers
      const teacherRelations = await db.query.teacherStudents.findMany({
        where: eq(teacherStudents.studentId, userId),
      });

      contactIds = teacherRelations.map((relation) => relation.teacherId);
      console.log("[my-contacts] Found teacher contacts:", contactIds.length);
    }

    // 4) If no contacts, return empty array
    if (contactIds.length === 0) {
      console.log("[my-contacts] No contacts found. Returning empty array.");
      return NextResponse.json([]);
    }

    // 5) Get contact details (name, email, etc.) from user records
    const contactRecords = await db.query.users.findMany({
      where: (table, { inArray }) => inArray(table.userId, contactIds),
    });

    console.log(
      "[my-contacts] Fetched contact details for",
      contactRecords.length,
      "users"
    );

    // 6) For each contact, find the most recent message (if any)
    const contactsWithMessages = await Promise.all(
      contactRecords.map(async (contact) => {
        // Find the most recent message between these users (in either direction)
        const latestMessages = await db.query.messages.findMany({
          where: (table, { and, or }) =>
            or(
              and(
                eq(table.senderId, userId),
                eq(table.recipientId, contact.userId)
              ),
              and(
                eq(table.senderId, contact.userId),
                eq(table.recipientId, userId)
              )
            ),
          orderBy: [desc(messages.createdAt)],
          limit: 1,
        });

        // Get the most recent message or set placeholder values
        const lastMessage =
          latestMessages.length > 0 ? latestMessages[0] : null;

        // Format to match the expected structure in the Messages UI
        return {
          id: contact.userId,
          name: contact.displayName || "Unknown User",
          avatar: "/mascot.svg", // Default avatar
          lastMessage: lastMessage ? lastMessage.content : "No messages yet",
          timestamp: lastMessage
            ? new Date(lastMessage.createdAt).toLocaleDateString()
            : "Never",
          email: contact.email,
        };
      })
    );

    console.log(
      "[my-contacts] Returning",
      contactsWithMessages.length,
      "contacts with messages"
    );

    // 7) Return the formatted contacts
    return NextResponse.json(contactsWithMessages);
  } catch (error) {
    console.error("[my-contacts] Error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
