import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  const body = await req.json();
  const { userMessage, pdfUrl } = body;

  if (!userMessage) {
    return NextResponse.json(
      { error: "No user message provided" },
      { status: 400 }
    );
  }

  try {
    // If you already have an assistant ID, you can skip creating one each time.
    // Here we use the provided assistant ID for demonstration:
    const assistantId = "asst_KmBnyywJ5vkFTqyJ5yNO3eDR";

    // Create a new thread
    const thread = await openai.beta.threads.create();

    // Add the user message to the thread
    await openai.beta.threads.messages.create(thread.id, {
      role: "user",
      content: userMessage,
    });

    // Create a run to process the messages in the thread
    let run = await openai.beta.threads.runs.create(thread.id, {
      assistant_id: assistantId,
    });

    // Wait for the run to finish
    run = await waitOnRun(run, thread);

    // Get the messages in ascending order
    const messages = await openai.beta.threads.messages.list(thread.id, {
      order: "asc",
    });

    // Find the last assistant message
    const assistantMessage = [...messages.data]
      .reverse()
      .find((m) => m.role === "assistant");

    // Extract text content from the assistant message
    let assistantResponse = "No assistant response found.";
    if (assistantMessage && assistantMessage.content) {
      const textBlock = assistantMessage.content.find(
        (block) => block.type === "text"
      );
      if (textBlock?.text?.value) {
        assistantResponse = textBlock.text.value;
      }
    }

    return NextResponse.json({ assistantResponse }, { status: 200 });
  } catch (error: any) {
    console.error("[Assistant API] Error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

async function waitOnRun(run: any, thread: any) {
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  while (run.status === "queued" || run.status === "in_progress") {
    run = await openai.beta.threads.runs.retrieve(thread.id, run.id);
    await new Promise((resolve) => setTimeout(resolve, 500));
  }
  return run;
}
