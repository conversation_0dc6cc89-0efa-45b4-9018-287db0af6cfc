// app/api/hf-transcribe/route.ts
import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import { randomUUID } from "crypto";
import path from "path";

const HF_TOKEN = "Bearer *************************************";
const HF_MODEL_URL =
  "https://eia0srs00ma9qijv.eu-west-1.aws.endpoints.huggingface.cloud";

/**
 * Accepts multipart-form audio (`file`), stores a temp copy,
 * streams the raw bytes to a private HF Inference Endpoint,
 * and returns the transcription.
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File | Blob | null;

    if (!file) {
      return NextResponse.json(
        { error: "No audio file provided." },
        { status: 400 }
      );
    }

    /* ---------- save a temp copy (optional) ---------- */
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const tmpName = `student-${randomUUID()}.webm`;
    const tmpPath = path.join("/tmp", tmpName);
    fs.writeFileSync(tmpPath, buffer);
    console.log("[hf-transcribe] saved to", tmpPath);

    /* ---------- send to HF ---------- */
    const contentType = (file as File).type || "application/octet-stream";
    const audioBlob = new Blob([arrayBuffer], { type: contentType }); // ✅ Blob ∈ BodyInit

    const hfResponse = await fetch(HF_MODEL_URL, {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: HF_TOKEN,
        "Content-Type": contentType,
      },
      body: audioBlob, // <-- TS-safe BodyInit
    });

    if (!hfResponse.ok) {
      const errText = await hfResponse.text();
      return NextResponse.json(
        { error: `HF inference error: ${errText}` },
        { status: hfResponse.status }
      );
    }

    const result = await hfResponse.json();
    console.log("[hf-transcribe] HF result:", result);

    const finalText =
      Array.isArray(result) && result[0]?.text
        ? result[0].text
        : result.text ?? "(no transcription found)";

    /* ---------- cleanup ---------- */
    fs.unlink(tmpPath, () => {});

    return NextResponse.json({ text: finalText });
  } catch (err) {
    console.error("[hf-transcribe] route error:", err);
    return NextResponse.json(
      {
        error:
          err instanceof Error
            ? err.message
            : "Failed to transcribe audio with HF.",
      },
      { status: 500 }
    );
  }
}
