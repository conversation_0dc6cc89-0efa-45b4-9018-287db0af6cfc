// api/chapters/[chapterOrder]/route.ts

import db from "@/db/drizzle"; // your Drizzle DB instance
import { chapters, squares } from "@/db/schema";
import { eq, asc } from "drizzle-orm";

// Force dynamic rendering to prevent caching
export const dynamic = "force-dynamic";
export const revalidate = 0;

// ================================================================== //
// LETTER METADATA TYPES AND INTERFACES
// ================================================================== //

interface LetterMetadata {
  complexity: "basic" | "intermediate" | "advanced";
  complexityScore: number;
  position: {
    index: number;
    total: number;
    isFirst: boolean;
    isLast: boolean;
    relativePosition: "beginning" | "middle" | "end";
  };
  phonetic: {
    family: string;
    group: string;
    similarLetters: string[];
  };
  search: {
    keywords: string[];
    variations: string[];
    commonMisspellings: string[];
    descriptions: string[];
  };
  visual: {
    characterCount: number;
    hasSpecialMarks: boolean;
    visualComplexity: number;
    shapeFamily: string;
  };
}

// UPDATED: The 'transliteration' field has been removed from the interface.
interface EnhancedSquare {
  id: number;
  squareNumber: number;
  content: string;
  chapterId: number;
  // Enhanced metadata
  metadata: LetterMetadata;
}

// ================================================================== //
// ARABIC LETTER ANALYSIS UTILITIES
// ================================================================== //

// NEW: Map from Arabic character to its common English name for search keyword generation
const ARABIC_TO_ENGLISH_NAME: Record<string, string> = {
  ا: "alif",
  أ: "alif",
  إ: "alif",
  آ: "alif",
  ب: "ba",
  ت: "ta",
  ث: "tha",
  ج: "jim",
  ح: "ha", // heavy ha
  خ: "kha",
  د: "dal",
  ذ: "dhal",
  ر: "ra",
  ز: "za",
  س: "sin",
  ش: "shin",
  ص: "sad",
  ض: "dad",
  ط: "ta2", // heavy ta
  ظ: "za2", // heavy za
  ع: "ain",
  غ: "ghain",
  ف: "fa",
  ق: "qaf",
  ك: "kaf",
  ل: "lam",
  م: "mim",
  ن: "nun",
  ه: "ha2", // light ha
  و: "waw",
  ي: "ya",
  ة: "ha", // ta marbuta often pronounced as ha
};

const ARABIC_LETTER_FAMILIES = {
  alif: { family: "Alif Group", similar: ["ا", "أ", "إ", "آ"] },
  ba: { family: "Ba Group", similar: ["ب", "ت", "ث", "ن", "ي"] },
  jim: { family: "Jim Group", similar: ["ج", "ح", "خ"] },
  dal: { family: "Dal Group", similar: ["د", "ذ"] },
  ra: { family: "Ra Group", similar: ["ر", "ز"] },
  sin: { family: "Sin Group", similar: ["س", "ش"] },
  sad: { family: "Sad Group", similar: ["ص", "ض"] },
  ta: { family: "Ta Group", similar: ["ط", "ظ"] },
  ain: { family: "Ain Group", similar: ["ع", "غ"] },
  fa: { family: "Fa Group", similar: ["ف", "ق"] },
  kaf: { family: "Kaf Group", similar: ["ك"] },
  lam: { family: "Lam Group", similar: ["ل"] },
  mim: { family: "Mim Group", similar: ["م"] },
  ha: { family: "Ha Group", similar: ["ه", "ة"] },
  waw: { family: "Waw Group", similar: ["و"] },
  ya: { family: "Ya Group", similar: ["ي"] },
};

const PHONETIC_GROUPS = {
  stops: ["ب", "ت", "ط", "د", "ض", "ك", "ق"],
  fricatives: ["ث", "ج", "ح", "خ", "ذ", "ز", "س", "ش", "ص", "ظ", "ف", "غ", "ه"],
  nasals: ["م", "ن"],
  liquids: ["ل", "ر"],
  glides: ["و", "ي"],
  vowels: ["ا", "أ", "إ", "آ", "ع"],
};

const COMPLEXITY_RULES = {
  basic: {
    maxCharacterCount: 1,
    noSpecialMarks: true,
    simpleShapes: ["ا", "د", "ذ", "ر", "ز", "و"],
    visualComplexityMax: 3,
  },
  intermediate: {
    maxCharacterCount: 2,
    allowSpecialMarks: true,
    moderateShapes: ["ب", "ت", "ث", "ن", "ي", "ف", "ق", "ل", "م"],
    visualComplexityMax: 6,
  },
  advanced: {
    complexShapes: [
      "ج",
      "ح",
      "خ",
      "س",
      "ش",
      "ص",
      "ض",
      "ط",
      "ظ",
      "ع",
      "غ",
      "ك",
      "ه",
    ],
    hasConnections: true,
    visualComplexityMin: 5,
  },
};

const COMMON_TRANSLITERATION_VARIATIONS: Record<string, string[]> = {
  alif: ["a", "aa", "alef", "aleph"],
  ba: ["b", "baa", "bee"],
  ta: ["t", "taa", "tee"],
  tha: ["th", "thaa", "theta"],
  jim: ["j", "jeem", "gym"],
  ha: ["h", "haa", "hee"],
  kha: ["kh", "khaa", "chi"],
  dal: ["d", "daal", "dee"],
  dhal: ["dh", "dhaal", "zee"],
  ra: ["r", "raa", "ree"],
  za: ["z", "zaa", "zee"],
  sin: ["s", "seen", "sin"],
  shin: ["sh", "sheen", "shin"],
  sad: ["s", "saad", "sad"],
  dad: ["d", "daad", "dad"],
  ta2: ["t", "taa", "taw"],
  za2: ["z", "zaa", "zaw"],
  ain: ["a", "ayn", "ain"],
  ghain: ["gh", "ghain", "ghayn"],
  fa: ["f", "faa", "fee"],
  qaf: ["q", "qaaf", "qee"],
  kaf: ["k", "kaaf", "kee"],
  lam: ["l", "laam", "lee"],
  mim: ["m", "meem", "mee"],
  nun: ["n", "noon", "nee"],
  ha2: ["h", "haa", "hee"],
  waw: ["w", "waaw", "wee"],
  ya: ["y", "yaa", "yee"],
};

// ================================================================== //
// METADATA CALCULATION FUNCTIONS
// ================================================================== //

// UPDATED: Removed unused 'transliteration' parameter
function calculateComplexity(content: string): {
  complexity: "basic" | "intermediate" | "advanced";
  score: number;
} {
  const cleanContent = content.replace(/\s+/g, "");
  const characterCount = cleanContent.length;
  const hasSpecialMarks = /[\u064B-\u065F\u0670\u0640]/.test(content);

  let complexityScore = 0;

  // Character count factor
  complexityScore += characterCount;

  // Special marks add complexity
  if (hasSpecialMarks) complexityScore += 2;

  // Visual complexity based on letter shapes
  const visualComplexity = calculateVisualComplexity(cleanContent);
  complexityScore += visualComplexity;

  // Determine complexity level
  if (
    complexityScore <= 3 &&
    COMPLEXITY_RULES.basic.simpleShapes.some((shape) =>
      cleanContent.includes(shape)
    )
  ) {
    return { complexity: "basic", score: complexityScore };
  } else if (complexityScore <= 6) {
    return { complexity: "intermediate", score: complexityScore };
  } else {
    return { complexity: "advanced", score: complexityScore };
  }
}

function calculateVisualComplexity(content: string): number {
  let complexity = 0;

  // Base complexity per character
  complexity += content.length;

  // Additional complexity for certain letter shapes
  const complexShapes = [
    "ج",
    "ح",
    "خ",
    "س",
    "ش",
    "ص",
    "ض",
    "ط",
    "ظ",
    "ع",
    "غ",
    "ك",
    "ه",
  ];
  complexShapes.forEach((shape) => {
    if (content.includes(shape)) complexity += 2;
  });

  return complexity;
}

// UPDATED: Removed unused 'transliteration' parameter
function determinePhoneticGroup(content: string): {
  family: string;
  group: string;
  similarLetters: string[];
} {
  const cleanContent = content.replace(/\s+/g, "");

  // Find phonetic group
  let phoneticGroup = "misc";
  for (const [groupName, letters] of Object.entries(PHONETIC_GROUPS)) {
    if (letters.some((letter) => cleanContent.includes(letter))) {
      phoneticGroup = groupName;
      break;
    }
  }

  // Find letter family
  let letterFamily = "unknown";
  let similarLetters: string[] = [];

  for (const [familyName, familyData] of Object.entries(
    ARABIC_LETTER_FAMILIES
  )) {
    if (familyData.similar.some((letter) => cleanContent.includes(letter))) {
      letterFamily = familyData.family;
      similarLetters = familyData.similar.filter(
        (letter) => letter !== cleanContent
      );
      break;
    }
  }

  return {
    family: letterFamily,
    group: phoneticGroup,
    similarLetters,
  };
}

// UPDATED: Generates keywords from Arabic content, not transliteration
function generateSearchKeywords(
  content: string,
  complexity: string
): {
  keywords: string[];
  variations: string[];
  commonMisspellings: string[];
  descriptions: string[];
} {
  const cleanContent = content.replace(/\s+/g, "").trim();
  const mainChar = cleanContent.charAt(0);
  const englishName = ARABIC_TO_ENGLISH_NAME[mainChar] || "";

  // Base keywords
  const keywords = [
    cleanContent,
    englishName,
    `letter ${englishName}`,
    `arabic ${englishName}`,
  ].filter(Boolean); // Filter out empty strings if a name isn't found

  // Transliteration variations
  const variations: string[] =
    COMMON_TRANSLITERATION_VARIATIONS[englishName] || [];

  // Common misspellings (simplified approach)
  const commonMisspellings = englishName
    ? [
        englishName.replace(/aa/g, "a"),
        englishName.replace(/ee/g, "i"),
        englishName.replace(/oo/g, "u"),
        englishName + "h",
        englishName.slice(0, -1),
      ].filter((word) => word !== englishName && word.length > 0)
    : [];

  // Descriptive keywords
  const descriptions = [
    `${complexity} letter`,
    `${complexity} arabic letter`,
    `letter ${englishName}`,
    `arabic alphabet ${englishName}`,
  ].filter((d) => d.includes(englishName)); // Only include if englishName exists

  // Add positional descriptions
  descriptions.push("arabic letter", "quranic letter", "alphabet letter");

  return {
    keywords: [...new Set(keywords)],
    variations: [...new Set(variations)],
    commonMisspellings: [...new Set(commonMisspellings)],
    descriptions: [...new Set(descriptions)],
  };
}

function calculatePosition(
  index: number,
  total: number
): {
  index: number;
  total: number;
  isFirst: boolean;
  isLast: boolean;
  relativePosition: "beginning" | "middle" | "end";
} {
  const isFirst = index === 0;
  const isLast = index === total - 1;

  let relativePosition: "beginning" | "middle" | "end";
  if (index < total / 3) {
    relativePosition = "beginning";
  } else if (index > (2 * total) / 3) {
    relativePosition = "end";
  } else {
    relativePosition = "middle";
  }

  return {
    index,
    total,
    isFirst,
    isLast,
    relativePosition,
  };
}

// UPDATED: Metadata generation no longer uses transliteration
function generateLetterMetadata(
  square: any,
  index: number,
  total: number
): LetterMetadata {
  const complexity = calculateComplexity(square.content);
  const phonetic = determinePhoneticGroup(square.content);
  const search = generateSearchKeywords(square.content, complexity.complexity);
  const position = calculatePosition(index, total);

  const cleanContent = square.content.replace(/\s+/g, "");
  const hasSpecialMarks = /[\u064B-\u065F\u0670\u0640]/.test(square.content);

  return {
    complexity: complexity.complexity,
    complexityScore: complexity.score,
    position,
    phonetic,
    search,
    visual: {
      characterCount: cleanContent.length,
      hasSpecialMarks,
      visualComplexity: calculateVisualComplexity(cleanContent),
      shapeFamily: phonetic.family,
    },
  };
}

// ================================================================== //
// CACHING UTILITIES
// ================================================================== //

const metadataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

function getCachedMetadata(cacheKey: string) {
  const cached = metadataCache.get(cacheKey);
  if (!cached) return null;

  const isExpired = Date.now() - cached.timestamp > CACHE_TTL;
  if (isExpired) {
    metadataCache.delete(cacheKey);
    return null;
  }

  return cached.data;
}

function setCachedMetadata(cacheKey: string, data: any) {
  metadataCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
  });
}

// ================================================================== //
// MAIN API FUNCTION (ENHANCED)
// ================================================================== //

export async function GET(
  request: Request,
  { params }: { params: { chapterOrder: string } }
) {
  try {
    const order = parseInt(params.chapterOrder, 10);
    if (isNaN(order)) {
      return new Response(JSON.stringify({ error: "Invalid chapter order" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control":
            "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
    }

    console.log(
      `[API] Fetching chapter with order=${order} at ${new Date().toISOString()}`
    );

    // Check cache first
    const cacheKey = `chapter-${order}-enhanced`;
    const cachedData = getCachedMetadata(cacheKey);
    if (cachedData) {
      console.log(`[API] Returning cached data for chapter ${order}`);
      return new Response(JSON.stringify(cachedData), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=300", // 5 minutes
          "X-Cache": "HIT",
        },
      });
    }

    // Query the chapters table for the given order
    const [chapter] = await db
      .select()
      .from(chapters)
      .where(eq(chapters.order, order))
      .limit(1);

    if (!chapter) {
      return new Response(JSON.stringify({ error: "Chapter not found" }), {
        status: 404,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control":
            "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
    }

    // Fetch squares for that chapter
    const squaresResult = await db.query.squares.findMany({
      where: eq(squares.chapterId, chapter.id),
      orderBy: (sq) => [asc(sq.squareNumber)],
    });

    console.log(
      `[API] Processing ${squaresResult.length} squares for metadata analysis`
    );

    // ENHANCED: Add metadata to each square, focusing on Arabic content
    const enhancedSquares: EnhancedSquare[] = squaresResult.map(
      (square, index): EnhancedSquare => {
        const metadata = generateLetterMetadata(
          square,
          index,
          squaresResult.length
        );

        // UPDATED: The 'transliteration' key has been removed from the returned object.
        return {
          id: square.id,
          squareNumber: square.squareNumber,
          content: square.content,
          chapterId: square.chapterId,
          metadata,
        };
      }
    );

    // Generate chapter-level analytics
    const chapterAnalytics = {
      totalLetters: enhancedSquares.length,
      complexityDistribution: {
        basic: enhancedSquares.filter((s) => s.metadata.complexity === "basic")
          .length,
        intermediate: enhancedSquares.filter(
          (s) => s.metadata.complexity === "intermediate"
        ).length,
        advanced: enhancedSquares.filter(
          (s) => s.metadata.complexity === "advanced"
        ).length,
      },
      phoneticGroups: [
        ...new Set(enhancedSquares.map((s) => s.metadata.phonetic.group)),
      ],
      averageComplexityScore:
        enhancedSquares.reduce(
          (sum, s) => sum + s.metadata.complexityScore,
          0
        ) / (enhancedSquares.length || 1), // Avoid division by zero
      searchableTerms: enhancedSquares.reduce(
        (total, s) => total + s.metadata.search.keywords.length,
        0
      ),
    };

    // Combine chapter with enhanced squares and analytics
    const enhancedChapterData = {
      ...chapter,
      squares: enhancedSquares,
      analytics: chapterAnalytics,
      metadata: {
        enhancedAt: new Date().toISOString(),
        version: "1.1.0",
        features: [
          "complexity_analysis",
          "phonetic_grouping",
          "search_optimization",
          "positional_metadata",
          "arabic_first_logic",
        ],
      },
    };

    // Cache the enhanced data
    setCachedMetadata(cacheKey, enhancedChapterData);

    console.log(`[API] Returning enhanced chapter data:`, {
      id: enhancedChapterData.id,
      title: enhancedChapterData.title,
      order: enhancedChapterData.order,
      squareCount: enhancedChapterData.squares.length,
      complexityDistribution: chapterAnalytics.complexityDistribution,
      phoneticGroups: chapterAnalytics.phoneticGroups.length,
      averageComplexity: chapterAnalytics.averageComplexityScore.toFixed(2),
      cacheStatus: "MISS",
    });

    return new Response(JSON.stringify(enhancedChapterData), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300", // 5 minutes
        "X-Cache": "MISS",
        "X-Processing-Time": Date.now().toString(),
        Vary: "*",
      },
    });
  } catch (err) {
    console.error("[API] Error fetching chapter:", err);
    return new Response(JSON.stringify({ error: "Server error" }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control":
          "no-store, no-cache, must-revalidate, proxy-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  }
}
