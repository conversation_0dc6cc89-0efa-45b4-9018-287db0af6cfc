// deeseek-chat/route.ts

import { NextResponse, NextRequest } from "next/server";

// ================================================================== //
// TYPE DEFINITIONS                                                   //
// ================================================================== //

/**
 * The structure of a single message in the conversation history.
 * Aligns with the format required by the DeepSeek API.
 */
interface ChatMessage {
  role: "system" | "user" | "assistant" | "tool";
  content: string;
  tool_call_id?: string; // For tool responses
}

/**
 * Context about the student's current lesson progress.
 * This will be sent from the frontend with each request.
 */
interface LessonContext {
  currentChapter: number;
  currentSquareId?: number;
  lessonType: "pronunciation" | "writing" | "both";
  difficultyLevel: "beginner" | "intermediate" | "advanced";
  studentProgress: {
    pronunciationScore: number;
    writingScore: number;
    completedSquares: number[];
    strugglingAreas: string[];
  };
}

/**
 * The expected JSON body of the POST request from the frontend.
 */
interface ApiRequestBody {
  conversationHistory: ChatMessage[];
  lessonContext: LessonContext;
}

// ================================================================== //
// HELPER FUNCTIONS                                                   //
// ================================================================== //

/**
 * Dynamically creates the system prompt to guide the AI's behavior
 * based on the student's current lesson context.
 * @param {LessonContext} context - The student's current lesson state.
 * @returns {string} A detailed system prompt for DeepSeek-Chat.
 */
// *** FIXED HERE ***
function createSystemPrompt(context: LessonContext): string {
  // Find the last user message to see what they said.
  // const lastUserMessage = conversationHistory.findLast(msg => msg.role === 'user')?.content || '';

  return `You are Ustad AI, an expert, patient, and encouraging Arabic tutor for the Noorani Qaida.

**Core Instruction:** Your primary role is to act as a friendly conversational guide and a controller for the application's tools. You must decide which tool to call based on the user's message.

**YOUR BEHAVIOR:**
1.  **ALWAYS BE CONVERSATIONAL:** Your text responses should be short (1-2 sentences), friendly, and encouraging. Never output code, JSON, or markdown in your text responses.
2.  **IMMEDIATELY USE TOOLS:** When the user provides a pronunciation or drawing, you MUST call the appropriate tool. Your only job is to decide which tool to use.
    - If the user says an Arabic letter/word, you **MUST** call the \`gradePronunciation\` tool.
    - If the user draws something, you **MUST** call the \`evaluateDrawing\` tool.
3.  **DO NOT GRADE YOURSELF:** You are not allowed to evaluate the user's input directly. Your job is to call the tool and let the application handle the grading. For example, if the user says "الم", do not say "You pronounced it well." Instead, say something like "Good attempt, let me check that for you," and then immediately call the \`gradePronunciation\` tool.
4.  **STAY ON TOPIC:** If the user asks an off-topic question, gently guide them back to the lesson. Example: "That's a great question! For now, let's focus on this letter."
5.  **Be Encouraging:** Always maintain a positive, supportive tone.

**CURRENT LESSON:**
- Chapter: ${context.currentChapter}
- Current Focus: ${context.currentSquareId || "General practice"}
- Student's Level: ${context.difficultyLevel}

You must only respond in a way that can be spoken aloud.`;
}

/**
 * Defines the available tools that DeepSeek-Chat can call.
 * This structure follows the OpenAI/DeepSeek function calling specification.
 * @returns {Array<object>} A list of tool definitions.
 */
function getAvailableTools() {
  return [
    {
      type: "function",
      function: {
        name: "gradePronunciation",
        description:
          "Analyzes a student's spoken audio of an Arabic letter or word against the expected text and provides a score and feedback. Call this immediately when the user provides speech for evaluation.",
        parameters: {
          type: "object",
          properties: {
            expectedText: {
              type: "string",
              description:
                "The specific Arabic text from the lesson that the student was supposed to pronounce.",
            },
            // *** FIXED HERE ***
            // The AI doesn't have access to the audio blob URL. The app does.
            // The AI only knows the *transcribed text* from the user.
            // We are instructing the AI to call this function *because* the user spoke.
            // The frontend will handle attaching the audio.
            userTranscription: {
              type: "string",
              description:
                "The text transcribed from the user's audio recording.",
            },
          },
          required: ["expectedText", "userTranscription"],
        },
      },
    },
    {
      type: "function",
      function: {
        name: "evaluateDrawing",
        description:
          "Evaluates a student's handwritten Arabic letter from the canvas and provides a score and improvement tips. Call this when the student has finished drawing a letter.",
        parameters: {
          type: "object",
          properties: {
            expectedLetter: {
              type: "string",
              description: "The target Arabic letter, e.g., 'ب', 'ج', 'ض'.",
            },
            drawingStrokes: {
              type: "object", // Could be an array of points, SVG path, etc.
              description: "The stroke data from the canvas.",
            },
          },
          required: ["expectedLetter", "drawingStrokes"],
        },
      },
    },
    {
      type: "function",
      function: {
        name: "playExampleAudio",
        description:
          "Plays a perfect, teacher-model audio of a specific letter or word.",
        parameters: {
          type: "object",
          properties: {
            textToPlay: {
              type: "string",
              description:
                "The Arabic letter or word to be pronounced, e.g., 'ض' or 'صِرَاطَ'.",
            },
          },
          required: ["textToPlay"],
        },
      },
    },
    {
      type: "function",
      function: {
        name: "changeChapter",
        description: "Navigates the lesson interface to a different chapter.",
        parameters: {
          type: "object",
          properties: {
            chapterNumber: {
              type: "number",
              description: "The chapter number to navigate to (e.g., 1, 2, 3).",
            },
          },
          required: ["chapterNumber"],
        },
      },
    },
  ];
}

// ================================================================== //
// API ROUTE HANDLER                                                  //
// ================================================================== //

export async function POST(request: NextRequest) {
  // 1. --- API Key and Configuration ---
  const apiKey = process.env.DEEPSEEK_API_KEY;
  if (!apiKey) {
    console.error("DEEPSEEK_API_KEY is not set in environment variables.");
    return NextResponse.json(
      { error: "Server configuration error." },
      { status: 500 }
    );
  }
  const DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";

  // 2. --- Parse and Validate Incoming Request ---
  let body: ApiRequestBody;
  try {
    body = await request.json();
  } catch (error) {
    return NextResponse.json(
      { error: "Invalid JSON in request body." },
      { status: 400 }
    );
  }

  const { conversationHistory, lessonContext } = body;

  if (!Array.isArray(conversationHistory) || !lessonContext) {
    return NextResponse.json(
      {
        error:
          "Missing 'conversationHistory' or 'lessonContext' in request body.",
      },
      { status: 400 }
    );
  }

  // 3. --- Construct the Payload for DeepSeek API ---
  try {
    const systemPrompt = createSystemPrompt(lessonContext);
    const messages: ChatMessage[] = [
      { role: "system", content: systemPrompt },
      ...conversationHistory, // Append the ongoing conversation
    ];

    const payload = {
      model: "deepseek-chat",
      messages: messages,
      tools: getAvailableTools(),
      tool_choice: "auto",
      temperature: 0.7,
      max_tokens: 4096, // A reasonable limit
      stream: false, // We want a complete response with potential tool calls
    };

    // 4. --- Make the API Call to DeepSeek ---
    const response = await fetch(DEEPSEEK_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });

    // 5. --- Handle API Response and Errors ---
    if (!response.ok) {
      const errorBody = await response.text();
      console.error(
        `DeepSeek API error: ${response.status} ${response.statusText}`,
        { errorBody }
      );
      return NextResponse.json(
        {
          error: "Failed to communicate with the AI model.",
          details: errorBody,
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    const assistantResponse = data.choices?.[0]?.message;

    if (!assistantResponse) {
      console.error("Invalid response structure from DeepSeek API:", data);
      return NextResponse.json(
        { error: "Received an invalid response from the AI model." },
        { status: 500 }
      );
    }

    // 6. --- Return the Structured Response to the Frontend ---
    // The frontend will receive the complete assistant message, including
    // any text content and tool calls it needs to execute.
    return NextResponse.json(assistantResponse, { status: 200 });
  } catch (error) {
    console.error("An unexpected error occurred in /api/deepseek-chat:", error);
    if (error instanceof Error) {
      return NextResponse.json(
        { error: "An internal server error occurred.", details: error.message },
        { status: 500 }
      );
    }
    return NextResponse.json(
      { error: "An unknown internal server error occurred." },
      { status: 500 }
    );
  }
}
