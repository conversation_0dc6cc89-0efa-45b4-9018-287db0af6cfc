import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";

/**
 * POST /api/setupTeacher
 * Body: { schoolUsername: string, schoolPassword: string, avatarSrc: string }
 *
 * Steps:
 * 1) Ensure local user is authenticated.
 * 2) Fetch user row from `users`.
 * 3) Decide if we allow overwriting teacher credentials if user is already teacher.
 * 4) Update `users` row with role = "teacher", plus the provided school credentials.
 */
export async function POST(request: Request) {
  try {
    console.log("[setupTeacher] POST request received.");
    // 1) Clerk sees who is logged in
    const { userId } = auth();
    console.log("[setupTeacher] Authenticated userId:", userId);
    if (!userId) {
      console.error("[setupTeacher] Unauthorized: No user session.");
      return NextResponse.json(
        { error: "Unauthorized. No user session." },
        { status: 401 }
      );
    }

    // 2) Fetch user row
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId))
      .limit(1);
    console.log("[setupTeacher] Existing user record:", existingUser);

    if (!existingUser) {
      console.error("[setupTeacher] User record not found.");
      return NextResponse.json(
        { error: "User record not found." },
        { status: 404 }
      );
    }

    // 3) Read body for teacher credentials
    const body = await request.json();
    console.log("[setupTeacher] Request body:", body);
    const { schoolUsername, schoolPassword, avatarSrc } = body;

    if (!schoolUsername || !schoolPassword) {
      console.error(
        "[setupTeacher] Missing teacher credentials in request body."
      );
      return NextResponse.json(
        { error: "Missing teacher credentials." },
        { status: 400 }
      );
    }

    // Optional: decide how to handle if user is already teacher with existing credentials
    // For now, we allow them to set or overwrite their credentials.
    console.log(
      "[setupTeacher] Updating user record with teacher credentials..."
    );

    // 4) Update user row with avatar
    await db
      .update(users)
      .set({
        role: "teacher",
        schoolUsername,
        schoolPassword,
        avatarSrc: avatarSrc || existingUser.avatarSrc, // Use new avatar or keep existing one
      })
      .where(eq(users.userId, userId));

    console.log(
      "[setupTeacher] User promoted to teacher with new credentials."
    );

    // After updating the user record, fetch the updated record to confirm
    const updatedUserArr = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId))
      .limit(1);
    const updatedUser = updatedUserArr[0];

    console.log("[setupTeacher] Complete updated user record:", updatedUser);
    console.log(
      "[setupTeacher] User updated successfully. New role:",
      updatedUser.role
    );

    return NextResponse.json({
      success: true,
      message: "Teacher credentials saved successfully.",
      user: {
        role: updatedUser.role,
        schoolUsername: updatedUser.schoolUsername,
        schoolPassword: updatedUser.schoolPassword,
        avatarSrc: updatedUser.avatarSrc,
      },
    });
  } catch (error) {
    console.error("[setupTeacher] Error setting teacher credentials:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
