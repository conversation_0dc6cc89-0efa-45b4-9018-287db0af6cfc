// api/whisper/route.ts

import ffmpeg from "fluent-ffmpeg";
import { Readable, PassThrough } from "stream";
import WavDecoder from "wav-decoder"; // NEW – for in-memory WAV decoding
import {
  constructAudioUrl,
  getBestAudioUrl,
} from "@/app/memorization/Audio/audioUtils";

export const runtime = "nodejs";

/* ================================================================== */
/*  ENHANCED TYPE DEFINITIONS                                          */
/* ================================================================== */

interface CorrectWordInfo {
  text: string;
  surah: number;
  ayah: number;
  wordInAyahIndex: number;
  audioUrl: string | null;
}

interface BaseWordTimestamp {
  word: string;
  start: number;
  end: number;
}

interface EnhancedWordTimestamp extends BaseWordTimestamp {
  correctWordInfo?: CorrectWordInfo;
}

interface WordMetadata {
  text: string;
  surah: number;
  ayah: number;
  wordInAyahIndex: number;
}

// Environment for assess model
const HF_TOKEN = `Bearer ${process.env.HF_TOKEN}`;
const HF_WHISPER_URL = process.env.HF_WHISPER_URL!;

/* ------------------------------------------------------------------ */
/*  🆕 UTILITY FUNCTIONS                                              */
/* ------------------------------------------------------------------ */

/** Pad Helper Function: Define a utility function for padding numbers with leading zeros */
const pad = (num: number, size: number): string => ("000" + num).slice(-size);

/* ------------------------------------------------------------------ */
/*  🆕 ENERGY-BASED TIMING HELPERS (from Teacher)                      */
/* ------------------------------------------------------------------ */

/** Extract an RMS envelope (mono 16k WAV) at `frameMs` resolution */
async function extractRmsEnvelope(
  wavBuffer: Buffer,
  frameMs = 10
): Promise<number[]> {
  console.log(
    `[ENERGY] Decoding WAV buffer of size ${wavBuffer.length} for RMS extraction.`
  );
  const decoded = WavDecoder.decode.sync(wavBuffer); // Can throw error if not valid WAV
  const { sampleRate, channelData } = decoded;
  if (sampleRate !== 16000) {
    // This check is important as convertWebmToWav aims for 16kHz
    console.warn(
      `[ENERGY] Expected 16kHz WAV, got ${sampleRate}. Proceeding, but quality might be affected.`
    );
    // Potentially, resampling could be added here if frequent mismatches occur
  }
  if (!channelData || channelData.length === 0) {
    throw new Error("[ENERGY] No channel data found in WAV buffer.");
  }
  const pcm = channelData[0]; // Float32Array -1..1, assumes mono
  const step = Math.floor((sampleRate * frameMs) / 1000);
  const rms: number[] = [];
  for (let i = 0; i < pcm.length; i += step) {
    let sum = 0;
    const end = Math.min(i + step, pcm.length);
    if (end === i) continue; // Avoid division by zero if step is too large or pcm is short
    for (let j = i; j < end; j++) sum += pcm[j] * pcm[j];
    rms.push(Math.sqrt(sum / (end - i)));
  }
  console.log(
    `[ENERGY] Extracted RMS envelope with ${rms.length} data points.`
  );
  return rms;
}

/** Very simple VAD on the RMS "envelope" */
function detectSpeechSegments(
  rms: number[],
  threshold = 0.02,
  minGap = 3 // minGap in terms of `frameMs` units
): { start: number; end: number }[] {
  const segs: { start: number; end: number }[] = [];
  let inSeg = false;
  let s = 0;
  for (let i = 0; i < rms.length; i++) {
    if (!inSeg && rms[i] >= threshold) {
      inSeg = true;
      s = i;
    } else if (inSeg && rms[i] < threshold) {
      // check next frames for sustained silence
      let silence = true;
      for (let j = 1; j <= minGap && i + j < rms.length; j++) {
        if (rms[i + j] >= threshold) {
          silence = false;
          break;
        }
      }
      if (silence) {
        segs.push({ start: s, end: i }); // end is the frame before sustained silence
        inSeg = false;
      }
    }
  }
  if (inSeg) segs.push({ start: s, end: rms.length - 1 }); // If still in segment at the end
  console.log(
    `[ENERGY] Detected ${segs.length} speech segments from RMS data.`
  );
  return segs;
}

/** Distribute words in each speech block by energy proportion (or rather, by segment frame count proportion) */
function segmentIntoWords(
  transcriptionText: string,
  rms: number[], // rms is not directly used here, but implies context
  segments: { start: number; end: number }[],
  frameMs = 10
): Array<{ word: string; start: number; end: number }> {
  const words = transcriptionText
    .trim()
    .split(/\s+/)
    .filter((w) => w.length > 0);
  if (words.length === 0) return [];

  const totalFramesInSegments = segments.reduce(
    (acc, seg) => acc + (seg.end - seg.start + 1),
    0
  );
  if (totalFramesInSegments === 0) {
    console.warn(
      "[ENERGY] No frames in speech segments, cannot distribute words."
    );
    return [];
  }

  const energyTimestamps: { word: string; start: number; end: number }[] = [];
  let wordIndex = 0;

  segments.forEach((seg, segIdx) => {
    if (wordIndex >= words.length) return; // All words assigned

    const framesInThisSegment = seg.end - seg.start + 1;
    // Distribute a proportional number of the *remaining* words to this segment
    const remainingWords = words.length - wordIndex;
    const remainingFrames = segments
      .slice(segIdx)
      .reduce((acc, s) => acc + (s.end - s.start + 1), 0);

    let wordsForThisSegment: number;
    if (remainingFrames > 0) {
      wordsForThisSegment = Math.round(
        (framesInThisSegment / remainingFrames) * remainingWords
      );
    } else {
      wordsForThisSegment = 0; // Should not happen if totalFramesInSegments > 0
    }

    // Ensure at least one word if segment is not empty and words remain,
    // and don't assign more words than available.
    wordsForThisSegment = Math.max(1, wordsForThisSegment);
    wordsForThisSegment = Math.min(wordsForThisSegment, remainingWords);

    // If it's the last segment, assign all remaining words
    if (segIdx === segments.length - 1) {
      wordsForThisSegment = remainingWords;
    }

    for (let k = 0; k < wordsForThisSegment; k++) {
      if (wordIndex >= words.length) break; // Safety break

      const wordStartTimeInSegment =
        (k / wordsForThisSegment) * framesInThisSegment;
      const wordEndTimeInSegment =
        ((k + 1) / wordsForThisSegment) * framesInThisSegment;

      const startFrame = seg.start + Math.floor(wordStartTimeInSegment);
      const endFrame = seg.start + Math.floor(wordEndTimeInSegment); // Use floor to ensure end is not before start for very short segments/words

      energyTimestamps.push({
        word: words[wordIndex++],
        start: (startFrame * frameMs) / 1000,
        end: Math.max(
          (startFrame * frameMs) / 1000 + frameMs / 1000,
          (endFrame * frameMs) / 1000
        ), // Ensure end is at least one frameMs after start
      });
    }
  });

  // If words remain due to rounding, assign them to the last timestamp
  if (wordIndex < words.length && energyTimestamps.length > 0) {
    const lastTimestamp = energyTimestamps[energyTimestamps.length - 1];
    while (wordIndex < words.length) {
      lastTimestamp.word += " " + words[wordIndex++];
    }
  }

  console.log(
    `[ENERGY] Segmented transcription into ${energyTimestamps.length} energy-based word timestamps.`
  );
  return energyTimestamps;
}

/* ------------------------------------------------------------------ */
/*  🆕 ENHANCED ALIGNMENT HELPER FUNCTIONS                            */
/* ------------------------------------------------------------------ */

/**
 * Enhanced metadata validation function
 */
function validateWordMetadata(metadata: any): metadata is WordMetadata {
  if (!metadata || typeof metadata !== "object") {
    console.error("[METADATA VALIDATION] Invalid metadata object:", metadata);
    return false;
  }

  const { text, surah, ayah, wordInAyahIndex } = metadata;

  if (!text || typeof text !== "string" || text.trim().length === 0) {
    console.error("[METADATA VALIDATION] Invalid or missing text field:", text);
    return false;
  }

  if (!surah || typeof surah !== "number" || surah < 1 || surah > 114) {
    console.error("[METADATA VALIDATION] Invalid surah number:", surah);
    return false;
  }

  if (!ayah || typeof ayah !== "number" || ayah < 1) {
    console.error("[METADATA VALIDATION] Invalid ayah number:", ayah);
    return false;
  }

  if (
    !wordInAyahIndex ||
    typeof wordInAyahIndex !== "number" ||
    wordInAyahIndex < 1
  ) {
    console.error(
      "[METADATA VALIDATION] Invalid wordInAyahIndex:",
      wordInAyahIndex
    );
    return false;
  }

  return true;
}

/**
 * Enhanced word alignment with metadata and audio URL construction
 */
async function alignWordsWithMetadata(
  userWords: BaseWordTimestamp[],
  wordMetadataArray: WordMetadata[]
): Promise<EnhancedWordTimestamp[]> {
  console.log(
    `[ENHANCED ALIGNMENT] Aligning ${userWords.length} user words with ${wordMetadataArray.length} metadata entries`
  );

  // Validate all metadata entries first
  const validMetadataCount =
    wordMetadataArray.filter(validateWordMetadata).length;
  if (validMetadataCount !== wordMetadataArray.length) {
    console.warn(
      `[ENHANCED ALIGNMENT] ${
        wordMetadataArray.length - validMetadataCount
      } metadata entries failed validation`
    );
  }

  const alignedWords = await Promise.all(
    userWords.map(async (userWord, index): Promise<EnhancedWordTimestamp> => {
      // Simple alignment by index - in production, you might want more sophisticated alignment
      const alignedCorrectWordIndex = Math.min(
        index,
        wordMetadataArray.length - 1
      );
      const correctWordMeta = wordMetadataArray[alignedCorrectWordIndex];

      if (correctWordMeta && validateWordMetadata(correctWordMeta)) {
        try {
          // 🆕 ENHANCED: Use audio utility for URL construction
          const audioUrl = constructAudioUrl(
            correctWordMeta.surah,
            correctWordMeta.ayah,
            correctWordMeta.wordInAyahIndex
          );

          const correctWordInfo: CorrectWordInfo = {
            text: correctWordMeta.text,
            surah: correctWordMeta.surah,
            ayah: correctWordMeta.ayah,
            wordInAyahIndex: correctWordMeta.wordInAyahIndex,
            audioUrl: audioUrl,
          };

          console.log(
            `[ENHANCED ALIGNMENT] Successfully created correctWordInfo for word "${userWord.word}":`,
            {
              text: correctWordInfo.text,
              audioUrl: correctWordInfo.audioUrl,
              userWordIndex: index,
              metadataIndex: alignedCorrectWordIndex,
            }
          );

          return {
            ...userWord,
            correctWordInfo: correctWordInfo,
          };
        } catch (error: any) {
          console.error(
            `[ENHANCED ALIGNMENT] Failed to create audio URL for word "${userWord.word}":`,
            {
              error: error.message,
              metadata: correctWordMeta,
              userWordIndex: index,
            }
          );

          // Create correctWordInfo without audioUrl as fallback
          const correctWordInfo: CorrectWordInfo = {
            text: correctWordMeta.text,
            surah: correctWordMeta.surah,
            ayah: correctWordMeta.ayah,
            wordInAyahIndex: correctWordMeta.wordInAyahIndex,
            audioUrl: null, // Explicitly set to null when construction fails
          };

          return {
            ...userWord,
            correctWordInfo: correctWordInfo,
          };
        }
      } else {
        console.warn(
          `[ENHANCED ALIGNMENT] No valid metadata for user word "${userWord.word}" at index ${index}`
        );
        return userWord;
      }
    })
  );

  const successfulAudioUrls = alignedWords.filter(
    (word) => word.correctWordInfo?.audioUrl
  ).length;

  console.log(
    `[ENHANCED ALIGNMENT] Successfully aligned words with metadata. ${successfulAudioUrls}/${alignedWords.length} words have audio URLs`
  );

  return alignedWords;
}

/* ------------------------------------------------------------------ */
/*  🆕 AUDIO PROXY ENDPOINT                                           */
/* ------------------------------------------------------------------ */

/**
 * Handle GET requests for audio proxy
 */
async function handleAudioProxy(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const surah = url.searchParams.get("surah");
  const ayah = url.searchParams.get("ayah");
  const word = url.searchParams.get("word");

  console.log(
    `[AUDIO PROXY] Request received for surah:${surah}, ayah:${ayah}, word:${word}`
  );

  // Validate parameters
  if (!surah || !ayah || !word) {
    console.error("[AUDIO PROXY] Missing required parameters");
    return new Response(
      JSON.stringify({
        error: "Missing required parameters: surah, ayah, word",
      }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  const surahNum = parseInt(surah);
  const ayahNum = parseInt(ayah);
  const wordNum = parseInt(word);

  // Validate parameter ranges
  if (surahNum < 1 || surahNum > 114) {
    console.error(`[AUDIO PROXY] Invalid surah number: ${surahNum}`);
    return new Response(
      JSON.stringify({
        error: "Invalid surah number. Must be between 1 and 114.",
      }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  if (ayahNum < 1) {
    console.error(`[AUDIO PROXY] Invalid ayah number: ${ayahNum}`);
    return new Response(
      JSON.stringify({ error: "Invalid ayah number. Must be greater than 0." }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  if (wordNum < 1) {
    console.error(`[AUDIO PROXY] Invalid word number: ${wordNum}`);
    return new Response(
      JSON.stringify({ error: "Invalid word number. Must be greater than 0." }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  try {
    // 🆕 Use audio utility to get best URL
    const audioUrl = await getBestAudioUrl(surahNum, ayahNum, wordNum);

    if (!audioUrl) {
      console.error(
        `[AUDIO PROXY] No audio URL available for ${surahNum}:${ayahNum}:${wordNum}`
      );
      return new Response(
        JSON.stringify({ error: "Audio file not available" }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // If it's already a proxy URL, avoid infinite recursion
    if (audioUrl.startsWith("/api/audio-proxy")) {
      console.error(
        `[AUDIO PROXY] Infinite recursion detected for ${surahNum}:${ayahNum}:${wordNum}`
      );
      return new Response(
        JSON.stringify({ error: "Audio proxy recursion detected" }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    console.log(`[AUDIO PROXY] Fetching audio from upstream: ${audioUrl}`);

    // Fetch the audio file from upstream
    const response = await fetch(audioUrl, {
      method: "GET",
      headers: {
        "User-Agent": "QuranMemorizationApp/1.0",
      },
    });

    if (!response.ok) {
      console.error(
        `[AUDIO PROXY] Upstream fetch failed: ${response.status} ${response.statusText}`
      );
      return new Response(
        JSON.stringify({
          error: `Upstream audio fetch failed: ${response.status}`,
        }),
        {
          status: response.status,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Validate content type
    const contentType = response.headers.get("content-type");
    if (
      contentType &&
      !contentType.includes("audio") &&
      !contentType.includes("mpeg")
    ) {
      console.error(`[AUDIO PROXY] Invalid content type: ${contentType}`);
      return new Response(
        JSON.stringify({ error: "Invalid audio file format" }),
        {
          status: 415,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Get content length for validation
    const contentLength = response.headers.get("content-length");
    const audioSize = contentLength ? parseInt(contentLength) : 0;

    // Validate file size (reasonable limits for audio files)
    if (audioSize > 5 * 1024 * 1024) {
      // 5MB limit
      console.error(`[AUDIO PROXY] Audio file too large: ${audioSize} bytes`);
      return new Response(JSON.stringify({ error: "Audio file too large" }), {
        status: 413,
        headers: { "Content-Type": "application/json" },
      });
    }

    console.log(
      `[AUDIO PROXY] Successfully proxying audio file: ${audioSize} bytes, type: ${contentType}`
    );

    // Stream the audio file with appropriate headers
    const headers = new Headers();
    headers.set("Content-Type", contentType || "audio/mpeg");
    headers.set("Cache-Control", "public, max-age=86400"); // Cache for 24 hours
    headers.set("Access-Control-Allow-Origin", "*");
    headers.set("Access-Control-Allow-Methods", "GET");

    if (contentLength) {
      headers.set("Content-Length", contentLength);
    }

    return new Response(response.body, {
      status: 200,
      headers: headers,
    });
  } catch (error: any) {
    console.error(`[AUDIO PROXY] Error processing request:`, error);
    return new Response(
      JSON.stringify({ error: "Internal server error during audio proxy" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

/* ------------------------------------------------------------------ */
/*  MAIN /api/whisper POST (Enhanced by Teacher's suggestions)        */
/* ------------------------------------------------------------------ */
export const POST = async (req: Request) => {
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    console.log("[WHISPER] Starting enhanced processing with metadata support");

    // 1. 🆕 ENHANCED: Load FormData for both audio and metadata
    const formData = await req.formData();
    const audioBlob = formData.get("audio") as File;
    const wordMetadataString = formData.get("wordMetadata") as string;

    // Validate audio
    if (!audioBlob) {
      console.error("[WHISPER] No audio file received in FormData.");
      return new Response(
        JSON.stringify({ error: "No audio file received." }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Convert audio blob to buffer
    const audioBuffer = Buffer.from(await audioBlob.arrayBuffer());
    if (!audioBuffer || audioBuffer.length === 0) {
      console.error("[WHISPER] No audio received.");
      return new Response(JSON.stringify({ error: "No audio received." }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
    console.log(
      `[WHISPER] Received audio buffer of size: ${audioBuffer.length} bytes`
    );

    // 🆕 ENHANCED: Parse and validate word metadata
    let wordMetadataArray: WordMetadata[] = [];

    if (wordMetadataString) {
      try {
        const parsedMetadata = JSON.parse(wordMetadataString);
        if (Array.isArray(parsedMetadata)) {
          // Validate each metadata entry
          wordMetadataArray = parsedMetadata.filter(validateWordMetadata);
          console.log(
            `[WHISPER] Parsed and validated ${wordMetadataArray.length}/${parsedMetadata.length} word metadata entries`
          );

          if (wordMetadataArray.length !== parsedMetadata.length) {
            console.warn(
              `[WHISPER] ${
                parsedMetadata.length - wordMetadataArray.length
              } metadata entries were invalid and filtered out`
            );
          }
        } else {
          console.warn(
            "[WHISPER] Word metadata is not an array, proceeding without metadata"
          );
        }
      } catch (error: any) {
        console.warn(
          "[WHISPER] Failed to parse word metadata, proceeding without metadata:",
          error.message
        );
      }
    } else {
      console.log("[WHISPER] No word metadata provided");
    }

    // Convert WebM to FLAC for Whisper and to WAV for energy/playback, in parallel
    console.log(
      "[WHISPER] Converting audio to FLAC and WAV formats in parallel."
    );
    const [flacConversionResult, wavConversionResult] =
      await Promise.allSettled([
        convertWebmToFlac(audioBuffer),
        convertWebmToWav(audioBuffer),
      ]);

    let flacBuffer: Buffer;
    if (
      flacConversionResult.status === "fulfilled" &&
      flacConversionResult.value &&
      flacConversionResult.value.length > 0
    ) {
      flacBuffer = flacConversionResult.value;
      console.log(
        `[WHISPER] Audio converted to FLAC, size: ${flacBuffer.length} bytes`
      );
    } else {
      const reason =
        flacConversionResult.status === "rejected"
          ? flacConversionResult.reason
          : "Empty FLAC buffer";
      console.error("[WHISPER] Failed to convert audio to FLAC:", reason);
      return new Response(
        JSON.stringify({ error: "Failed to convert audio to FLAC." }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    let wavBuffer: Buffer;
    if (
      wavConversionResult.status === "fulfilled" &&
      wavConversionResult.value &&
      wavConversionResult.value.length > 0
    ) {
      wavBuffer = wavConversionResult.value;
      console.log(
        `[WHISPER] Audio converted to WAV, size: ${wavBuffer.length} bytes`
      );
    } else {
      const reason =
        wavConversionResult.status === "rejected"
          ? wavConversionResult.reason
          : "Empty WAV buffer";
      console.error("[WHISPER] Failed to convert audio to WAV:", reason);
      return new Response(
        JSON.stringify({ error: "Failed to convert audio to WAV." }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    // 2. Parallel STT (Whisper) + Phoneme timing model (Assess)
    console.log(
      "[WHISPER] Starting parallel processing for transcription (Whisper) and raw timing data (Assess model)"
    );
    const [sttRes, assessRes] = await Promise.allSettled([
      getTranscriptionFromWhisper(flacBuffer), // Uses FLAC
      getTimingDataFromAssessModel(audioBuffer), // Uses original WebM
    ]);
    console.log(
      "[WHISPER] Parallel STT and Assess model processing completed."
    );

    // Handle STT result
    let transcriptionText = "";
    let whisperWordTimestamps: Array<{
      word: string;
      start: number;
      end: number;
    }> = [];

    if (sttRes.status === "fulfilled" && sttRes.value) {
      transcriptionText = sttRes.value.text;
      whisperWordTimestamps = sttRes.value.words || [];
      console.log(
        `[WHISPER] Successfully got transcription: "${transcriptionText.substring(
          0,
          100
        )}..."`
      );
      console.log(
        `[WHISPER] Whisper provided ${whisperWordTimestamps.length} word timestamps.`
      );
    } else {
      const reason =
        sttRes.status === "rejected"
          ? sttRes.reason
          : "Unknown error or empty value from Whisper";
      console.error(
        "[WHISPER] Failed to get transcription from Whisper:",
        reason
      );
      return new Response(
        JSON.stringify({ error: "Failed to get transcription from Whisper." }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    // 3. Energy-based timestamps
    let energyWordTimestamps: Array<{
      word: string;
      start: number;
      end: number;
    }> = [];
    if (transcriptionText.trim() && wavBuffer) {
      try {
        console.log(
          "[ENERGY] Attempting to extract RMS envelope from WAV buffer for energy-based timestamps."
        );
        const rms = await extractRmsEnvelope(wavBuffer);
        const speechSegments = detectSpeechSegments(rms);
        if (speechSegments.length > 0) {
          energyWordTimestamps = segmentIntoWords(
            transcriptionText,
            rms,
            speechSegments
          );
          console.log(
            `[ENERGY] Generated ${energyWordTimestamps.length} energy-based word timestamps.`
          );
        } else {
          console.log(
            "[ENERGY] No speech segments detected from RMS, cannot generate energy-based timestamps."
          );
        }
      } catch (e: any) {
        console.warn(
          "[ENERGY] Energy-based timestamp generation failed:",
          e.message
        );
      }
    } else {
      console.log(
        "[ENERGY] Skipping energy-based timestamps (transcription text is empty or WAV buffer missing)."
      );
    }

    // 4. Phoneme timings (Raw from assess model)
    let phonemeTimings: Array<{
      phoneme: string;
      start: number;
      end: number;
      word_boundary?: boolean;
    }> = [];
    if (assessRes.status === "fulfilled" && assessRes.value) {
      phonemeTimings = assessRes.value;
      console.log(
        `[WHISPER] Successfully got raw timing data with ${phonemeTimings.length} phoneme segments from assess model.`
      );
    } else {
      console.warn(
        "[WHISPER] Failed to get raw timing data from assess model."
      );
    }

    // 5. Pick best source of timestamps
    console.log("[WHISPER] Selecting best timestamp source...");
    const words = transcriptionText.trim()
      ? transcriptionText
          .trim()
          .split(/\s+/)
          .filter((w) => w.length > 0)
      : [];

    const plausible = (arr: BaseWordTimestamp[]): boolean =>
      Array.isArray(arr) &&
      arr.length > 0 &&
      arr.length === words.length &&
      arr.every(
        (w) =>
          typeof w.word === "string" &&
          typeof w.start === "number" &&
          typeof w.end === "number" &&
          w.end >= w.start
      );

    let chosenTimestamps: BaseWordTimestamp[] = [];
    let timestampSourceDescription = "unknown_source";

    if (plausible(whisperWordTimestamps)) {
      chosenTimestamps = whisperWordTimestamps;
      timestampSourceDescription = "whisper_word_timestamps";
      console.log(
        `[WHISPER] Using plausible Whisper word timestamps (${chosenTimestamps.length} words).`
      );
    } else if (plausible(energyWordTimestamps)) {
      chosenTimestamps = energyWordTimestamps;
      timestampSourceDescription = "energy_based_timestamps";
      console.log(
        `[WHISPER] Using plausible Energy-based word timestamps (${chosenTimestamps.length} words).`
      );
    } else if (
      phonemeTimings &&
      phonemeTimings.length > 0 &&
      words.length > 0
    ) {
      console.log(
        "[WHISPER] Attempting to correlate phoneme timings with words as Whisper/Energy timestamps were not plausible."
      );
      const correlatedFromPhonemes = correlateTimingWithWords(
        transcriptionText,
        whisperWordTimestamps,
        phonemeTimings
      );
      if (plausible(correlatedFromPhonemes)) {
        chosenTimestamps = correlatedFromPhonemes;
        timestampSourceDescription = "correlated_phoneme_timings";
        console.log(
          `[WHISPER] Using plausible Correlated phoneme timings (${chosenTimestamps.length} words).`
        );
      } else {
        console.log(
          "[WHISPER] Correlated phoneme timings were not plausible or did not match word count."
        );
      }
    }

    // Fallback if no plausible source found and there are words to timestamp
    if (chosenTimestamps.length === 0 && words.length > 0) {
      console.log(
        "[WHISPER] No plausible timestamps found from Whisper, Energy, or Phoneme correlation. Creating fallback estimations based on audio duration."
      );
      const audioDuration = estimateAudioDuration(audioBuffer);
      chosenTimestamps = words.map((word, i) => {
        const start = (i / words.length) * audioDuration;
        const end = ((i + 1) / words.length) * audioDuration;
        return {
          word: word,
          start: Math.round(start * 100) / 100,
          end: Math.round(end * 100) / 100,
        };
      });
      timestampSourceDescription = "fallback_estimated_duration";
      console.log(
        `[WHISPER] Created ${chosenTimestamps.length} fallback estimated timestamps.`
      );
    }

    console.log(
      `[WHISPER] Final chosen timestamp source: ${timestampSourceDescription}. Number of timestamps: ${chosenTimestamps.length}`
    );

    // 🆕 ENHANCED: Align and augment response words with metadata using enhanced function
    let finalWords: EnhancedWordTimestamp[] = chosenTimestamps;
    if (wordMetadataArray.length > 0 && chosenTimestamps.length > 0) {
      console.log(
        "[WHISPER] Aligning user words with correct word metadata using enhanced alignment"
      );
      finalWords = await alignWordsWithMetadata(
        chosenTimestamps,
        wordMetadataArray
      );
    }

    // 🆕 ENHANCED: Validate final response structure
    const wordsWithAudio = finalWords.filter(
      (w) => w.correctWordInfo?.audioUrl
    ).length;
    const wordsWithMetadata = finalWords.filter(
      (w) => w.correctWordInfo
    ).length;

    console.log(`[WHISPER] Final response validation:`, {
      totalWords: finalWords.length,
      wordsWithMetadata: wordsWithMetadata,
      wordsWithAudioUrls: wordsWithAudio,
      timestampSource: timestampSourceDescription,
    });

    // 6. Build response using the chosen timestamps and the WAV buffer for playback
    console.log("[WHISPER] Creating final processed audio response.");
    const processedResponse = await createProcessedAudioResponse(
      transcriptionText,
      finalWords,
      wavBuffer
    );

    // Ensure the timestamp source is part of the response for clarity
    if (processedResponse && !processedResponse.timestampSource) {
      (processedResponse as any).timestampSource = timestampSourceDescription;
    } else if (processedResponse) {
      (processedResponse as any).detailedTimestampSourceChoice =
        timestampSourceDescription;
    }

    console.log(
      `[WHISPER] Processing complete. Returning response with ${
        processedResponse.words.length
      } timestamps. Source: ${
        processedResponse.timestampSource || timestampSourceDescription
      }`
    );

    return new Response(JSON.stringify(processedResponse), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (err: any) {
    console.error(
      "[WHISPER] Fatal error in POST handler:",
      err.message,
      err.stack
    );
    return new Response(
      JSON.stringify({ error: err.message || "Internal server error." }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
};

/* ------------------------------------------------------------------ */
/*  🆕 AUDIO PROXY GET HANDLER                                        */
/* ------------------------------------------------------------------ */
export const GET = async (req: Request) => {
  return handleAudioProxy(req);
};

/* ------------------------------------------------------------------ */
/*  ORIGINAL HELPERS (Unchanged - Copied from your existing code)    */
/* ------------------------------------------------------------------ */

// 🎯 STRATEGY 1: Create processed audio response with perfect timestamp alignment
async function createProcessedAudioResponse(
  transcriptionText: string,
  timestamps: EnhancedWordTimestamp[],
  wavBuffer: Buffer
): Promise<{
  text: string;
  words: EnhancedWordTimestamp[];
  processedAudioUrl?: string;
  audioFormat?: string;
  timestampSource?: string;
}> {
  console.log(
    "[STRATEGY1] Creating processed audio response for perfect timestamp alignment"
  );

  try {
    // Convert WAV buffer to base64 data URL
    const base64Audio = wavBuffer.toString("base64");
    const dataUrl = `data:audio/wav;base64,${base64Audio}`;

    console.log(
      `[STRATEGY1] Generated processed audio data URL: ${Math.floor(
        dataUrl.length / 1024
      )}KB`
    );

    // Validate data URL size (warn if too large)
    if (dataUrl.length > 10 * 1024 * 1024) {
      console.warn(
        `[STRATEGY1] Large audio data URL: ${Math.floor(
          dataUrl.length / 1024 / 1024
        )}MB - may cause performance issues`
      );
    }

    return {
      text: transcriptionText,
      words: timestamps,
      processedAudioUrl: dataUrl,
      audioFormat: "wav",
      timestampSource: "processed_audio_aligned",
    };
  } catch (error) {
    console.error(
      "[STRATEGY1] Error creating processed audio response:",
      error
    );

    return {
      text: transcriptionText,
      words: timestamps,
      timestampSource: "fallback_no_processed_audio",
    };
  }
}

// ENHANCED: Get transcription from original whisper model
async function getTranscriptionFromWhisper(convertedBuffer: Buffer): Promise<{
  text: string;
  words?: BaseWordTimestamp[];
} | null> {
  console.log("[WHISPER] Attempting to get transcription from Whisper model");

  let result: any = null;

  // Approach 1: Try with word-level timestamp parameters
  try {
    console.log("[WHISPER] Attempting word-level timestamps from Whisper...");

    const response1 = await fetch(
      "https://e8ihs31fmq52oymd.eu-west-1.aws.endpoints.huggingface.cloud",
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: "Bearer *************************************",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inputs: {
            audio: convertedBuffer.toString("base64"),
          },
          parameters: {
            return_timestamps: "word",
            word_timestamps: true,
          },
        }),
      }
    );

    if (response1.ok) {
      result = await response1.json();
      console.log(
        "[WHISPER] Word-level timestamp response received from Whisper"
      );
    }
  } catch (error) {
    console.log("[WHISPER] Word-level timestamp approach failed:", error);
  }

  // Approach 2: Try with direct audio and URL parameters
  if (!result) {
    try {
      console.log("[WHISPER] Trying direct audio with URL parameters...");

      const response2 = await fetch(
        "https://e8ihs31fmq52oymd.eu-west-1.aws.endpoints.huggingface.cloud?return_timestamps=word",
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            Authorization: "Bearer *************************************",
            "Content-Type": "audio/flac",
          },
          //  टीचर द्वारा किया गया सुधार 1
          body: new Uint8Array(convertedBuffer),
        }
      );

      if (response2.ok) {
        result = await response2.json();
        console.log("[WHISPER] URL parameter response received from Whisper");
      }
    } catch (error) {
      console.log("[WHISPER] URL parameter approach failed:", error);
    }
  }

  // Approach 3: Fall back to original method (text only)
  if (!result) {
    console.log("[WHISPER] Falling back to original Whisper method...");

    const response3 = await fetch(
      "https://e8ihs31fmq52oymd.eu-west-1.aws.endpoints.huggingface.cloud",
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: "Bearer *************************************",
          "Content-Type": "audio/flac",
        },
        // 📢 ADJUSTMENT 2 BY TEACHER
        body: new Uint8Array(convertedBuffer),
      }
    );

    if (!response3.ok) {
      const errorText = await response3.text();
      console.error("[WHISPER] Whisper API error:", errorText);
      throw new Error("Error transcribing audio with Whisper");
    }

    result = await response3.json();
    console.log("[WHISPER] Fallback response received from Whisper");
  }

  // Extract text and word timestamps from various response formats
  let transcriptionText = "";
  let wordTimestamps: BaseWordTimestamp[] = [];

  // Extract text
  if (result.text) {
    transcriptionText = result.text;
  } else if (result.generated_text) {
    transcriptionText = result.generated_text;
  } else if (typeof result === "string") {
    transcriptionText = result;
  }

  console.log(`[WHISPER] Extracted text from Whisper: "${transcriptionText}"`);

  // Extract word-level timestamps from various possible formats
  if (result.chunks && Array.isArray(result.chunks)) {
    result.chunks.forEach((chunk: any) => {
      if (chunk.text && chunk.timestamp) {
        const [start, end] = chunk.timestamp;
        wordTimestamps.push({
          word: chunk.text.trim(),
          start,
          end,
        });
      }
    });
    console.log(
      `[WHISPER] Extracted ${wordTimestamps.length} word timestamps from chunks format`
    );
  } else if (result.words && Array.isArray(result.words)) {
    wordTimestamps = result.words.map((w: any) => ({
      word: w.word || w.text || w.token,
      start: w.start || w.timestamp?.[0] || 0,
      end: w.end || w.timestamp?.[1] || 0,
    }));
    console.log(
      `[WHISPER] Extracted ${wordTimestamps.length} word timestamps from words format`
    );
  } else if (result.segments && Array.isArray(result.segments)) {
    result.segments.forEach((segment: any) => {
      if (segment.words && Array.isArray(segment.words)) {
        segment.words.forEach((w: any) => {
          wordTimestamps.push({
            word: w.word || w.text || w.token,
            start: w.start || w.timestamp?.[0] || 0,
            end: w.end || w.timestamp?.[1] || 0,
          });
        });
      }
    });
    console.log(
      `[WHISPER] Extracted ${wordTimestamps.length} word timestamps from segments format`
    );
  }

  return {
    text: transcriptionText,
    words: wordTimestamps,
  };
}

// ENHANCED: Get timing data from assess model
async function getTimingDataFromAssessModel(
  audioBuffer: Buffer
): Promise<Array<{
  phoneme: string;
  start: number;
  end: number;
  word_boundary?: boolean;
}> | null> {
  console.log("[WHISPER] Getting timing data from assess model");

  if (!HF_WHISPER_URL || !process.env.HF_TOKEN) {
    console.warn(
      "[WHISPER] HF_WHISPER_URL or HF_TOKEN not configured for assess model, skipping timing enhancement"
    );
    return null;
  }

  try {
    console.log(
      "[ASSESS_MODEL] Converting input audio to WAV for assess model (if not already WAV)."
    );
    const wavForAssessModel = await convertWebmToWav(audioBuffer);

    console.log(
      `[ASSESS_MODEL] Sending ${wavForAssessModel.length} bytes (WAV) to assess model at ${HF_WHISPER_URL}`
    );

    const hfRes = await fetch(HF_WHISPER_URL, {
      method: "POST",
      headers: {
        Authorization: HF_TOKEN,
        "Content-Type": "audio/wav",
      },
      // 📢 ADJUSTMENT 3 BY TEACHER
      body: new Uint8Array(wavForAssessModel),
    });

    console.log(`[ASSESS_MODEL] Assess model response status: ${hfRes.status}`);

    if (!hfRes.ok) {
      const errorText = await hfRes.text();
      console.error(
        "[ASSESS_MODEL] Assess model error:",
        hfRes.status,
        errorText
      );
      return null;
    }

    const responseText = await hfRes.text();
    console.log(
      `[ASSESS_MODEL] Raw assess model response: ${responseText.substring(
        0,
        100
      )}...`
    );

    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
      console.log("[ASSESS_MODEL] Successfully parsed JSON from assess model");
    } catch (e) {
      parsedResponse = { text: responseText };
      console.log(
        "[ASSESS_MODEL] Treating assess model response as text (potential phonemes)"
      );
    }

    const phonemeText =
      parsedResponse.text ||
      (typeof parsedResponse === "string" ? parsedResponse : "");
    const phonemes = phonemeText
      .trim()
      .split(/\s+/)
      .filter((p: string) => p);

    console.log(
      `[ASSESS_MODEL] Extracted ${phonemes.length} phonemes from assess model response`
    );

    if (phonemes.length === 0) {
      console.log("[ASSESS_MODEL] No phonemes extracted from assess model.");
      return [];
    }

    const estimatedDuration = estimateAudioDuration(wavForAssessModel);
    const timingSegments = phonemes.map((phoneme: string, index: number) => {
      const start = (index / phonemes.length) * estimatedDuration;
      const end = ((index + 1) / phonemes.length) * estimatedDuration;

      return {
        phoneme: phoneme,
        start: Math.round(start * 1000) / 1000,
        end: Math.round(end * 1000) / 1000,
        word_boundary: false,
      };
    });

    console.log(
      `[ASSESS_MODEL] Created ${timingSegments.length} raw timing segments from assess model phonemes`
    );

    return timingSegments;
  } catch (error) {
    console.error(
      "[ASSESS_MODEL] Error getting timing data from assess model:",
      error
    );
    return null;
  }
}

// ENHANCED: Correlate timing data with transcribed words
function correlateTimingWithWords(
  transcriptionText: string,
  whisperTimestamps: BaseWordTimestamp[],
  phonemeTimings: Array<{
    phoneme: string;
    start: number;
    end: number;
    word_boundary?: boolean;
  }>
): BaseWordTimestamp[] {
  console.log(
    "[WHISPER_CORRELATE] Starting correlation of phoneme timing data with transcribed words"
  );

  if (!transcriptionText || !transcriptionText.trim()) {
    console.warn(
      "[WHISPER_CORRELATE] No transcription text for correlation, returning whisper timestamps or empty."
    );
    return whisperTimestamps || [];
  }

  const words = transcriptionText
    .trim()
    .split(/\s+/)
    .filter((w) => w.length > 0);
  console.log(
    `[WHISPER_CORRELATE] Correlating ${words.length} words with ${
      phonemeTimings?.length || 0
    } phoneme timings`
  );

  if (!phonemeTimings || phonemeTimings.length === 0 || words.length === 0) {
    console.log(
      "[WHISPER_CORRELATE] No phoneme timings or no words to correlate, using Whisper timestamps as fallback."
    );
    return whisperTimestamps || [];
  }

  const correlatedTimestamps: BaseWordTimestamp[] = [];

  let currentPhonemeIndex = 0;
  for (let i = 0; i < words.length; i++) {
    if (currentPhonemeIndex >= phonemeTimings.length) {
      console.warn(
        `[WHISPER_CORRELATE] Ran out of phonemes at word ${i + 1} ('${
          words[i]
        }').`
      );
      if (
        whisperTimestamps &&
        whisperTimestamps.length === words.length &&
        whisperTimestamps[i]
      ) {
        correlatedTimestamps.push(whisperTimestamps[i]);
      } else if (correlatedTimestamps.length > 0) {
        const lastEndTime =
          correlatedTimestamps[correlatedTimestamps.length - 1].end;
        correlatedTimestamps.push({
          word: words[i],
          start: lastEndTime,
          end: lastEndTime + 0.1,
        });
      } else {
        correlatedTimestamps.push({ word: words[i], start: 0, end: 0.1 });
      }
      continue;
    }

    const wordStartPhoneme = phonemeTimings[currentPhonemeIndex];
    let phonemesForThisWord = Math.max(
      1,
      Math.floor(phonemeTimings.length / words.length)
    );

    if (i === words.length - 1) {
      phonemesForThisWord = phonemeTimings.length - currentPhonemeIndex;
    }

    const wordEndPhonemeIndex = Math.min(
      currentPhonemeIndex + phonemesForThisWord - 1,
      phonemeTimings.length - 1
    );
    const wordEndPhoneme = phonemeTimings[wordEndPhonemeIndex];

    correlatedTimestamps.push({
      word: words[i].trim(),
      start: wordStartPhoneme.start,
      end: wordEndPhoneme.end,
    });
    currentPhonemeIndex = wordEndPhonemeIndex + 1;
  }

  console.log(
    `[WHISPER_CORRELATE] Naive correlation produced ${correlatedTimestamps.length} word timestamps.`
  );

  if (correlatedTimestamps.length === words.length) {
    console.log(
      "[WHISPER_CORRELATE] Correlation count matches word count, using these enhanced timestamps."
    );
    return correlatedTimestamps;
  } else {
    console.warn(
      `[WHISPER_CORRELATE] Correlation produced ${correlatedTimestamps.length} timestamps for ${words.length} words. This is a mismatch. Falling back to whisper timestamps.`
    );
    return whisperTimestamps || [];
  }
}

// ENHANCED: Estimate audio duration from buffer
function estimateAudioDuration(audioBuffer: Buffer): number {
  let estimatedDuration: number;
  if (
    audioBuffer.length > 44 &&
    audioBuffer.toString("ascii", 0, 4) === "RIFF" &&
    audioBuffer.toString("ascii", 8, 12) === "WAVE"
  ) {
    const bytesPerSecond = 16000 * 1 * 2;
    estimatedDuration = audioBuffer.length / bytesPerSecond;
  } else {
    const assumedBytesPerSecond = 3000;
    estimatedDuration = audioBuffer.length / assumedBytesPerSecond;
  }

  estimatedDuration = Math.max(0.1, estimatedDuration);
  console.log(
    `[WHISPER_DURATION] Estimated audio duration: ${estimatedDuration.toFixed(
      2
    )}s from ${audioBuffer.length} byte buffer`
  );
  return estimatedDuration;
}

// ENHANCED: Convert WebM to WAV for assess model AND Strategy 1 playback
async function convertWebmToWav(webmBuffer: Buffer): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    console.log(
      "[FFMPEG_WAV] Converting WebM to WAV (16kHz, mono) for assess model and/or Strategy 1 playback"
    );

    const input = new Readable();
    input.push(webmBuffer);
    input.push(null);

    const output = new PassThrough();
    const wavChunks: Uint8Array[] = [];

    output.on("data", (chunk) => wavChunks.push(chunk));
    output.on("end", () => {
      const wavBuffer = Buffer.concat(wavChunks);
      console.log(
        `[FFMPEG_WAV] WebM to WAV conversion complete, output size: ${wavBuffer.length} bytes`
      );
      resolve(wavBuffer);
    });
    output.on("error", (err) => {
      console.error(
        "[FFMPEG_WAV] Error in PassThrough stream for WAV conversion:",
        err
      );
      reject(err);
    });

    ffmpeg(input)
      .inputFormat("webm")
      .toFormat("wav")
      .audioFrequency(16000)
      .audioChannels(1)
      .on("start", (commandLine) => {
        console.log("[FFMPEG_WAV] Spawned Ffmpeg with command: " + commandLine);
      })
      .on("error", (err, stdout, stderr) => {
        console.error(
          "[FFMPEG_WAV] FFmpeg error in WAV conversion:",
          err.message
        );
        console.error("[FFMPEG_WAV] FFmpeg stdout:", stdout);
        console.error("[FFMPEG_WAV] FFmpeg stderr:", stderr);
        reject(err);
      })
      .pipe(output, { end: true });
  });
}

// Original convertWebmToFlac function (keeping for whisper model)
const convertWebmToFlac = (webmBuffer: Buffer): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    console.log("[FFMPEG_FLAC] Converting WebM to FLAC for whisper model");

    const input = new Readable();
    input.push(webmBuffer);
    input.push(null);

    const output = new PassThrough();
    const flacChunks: Uint8Array[] = [];

    output.on("data", (chunk) => flacChunks.push(chunk));
    output.on("end", () => {
      const flacBuffer = Buffer.concat(flacChunks);
      console.log(
        `[FFMPEG_FLAC] WebM to FLAC conversion complete, output size: ${flacBuffer.length} bytes`
      );
      resolve(flacBuffer);
    });
    output.on("error", (err) => {
      console.error(
        "[FFMPEG_FLAC] Error in PassThrough stream for FLAC conversion:",
        err
      );
      reject(err);
    });

    ffmpeg(input)
      .inputFormat("webm")
      .toFormat("flac")
      .on("start", (commandLine) => {
        console.log(
          "[FFMPEG_FLAC] Spawned Ffmpeg with command: " + commandLine
        );
      })
      .on("error", (err, stdout, stderr) => {
        console.error(
          "[FFMPEG_FLAC] FFmpeg error in FLAC conversion:",
          err.message
        );
        console.error("[FFMPEG_FLAC] FFmpeg stdout:", stdout);
        console.error("[FFMPEG_FLAC] FFmpeg stderr:", stderr);
        reject(err);
      })
      .pipe(output, { end: true });
  });
};
