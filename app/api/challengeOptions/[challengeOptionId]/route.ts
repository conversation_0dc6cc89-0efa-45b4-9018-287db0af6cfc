import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import db from "@/db/drizzle";
import { challengeOptions, challenges } from "@/db/schema";
import { isAdmin } from "@/lib/admin";

export const GET = async (
  req: Request,
  { params }: { params: { challengeOptionId: number } }
) => {
  console.log(
    "GET request received for challengeOptionId:",
    params.challengeOptionId
  );

  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const data = await db.query.challengeOptions.findFirst({
    where: eq(challengeOptions.id, params.challengeOptionId),
  });

  if (!data) {
    return new NextResponse("Challenge option not found", { status: 404 });
  }

  return NextResponse.json(data);
};

export const PUT = async (
  req: Request,
  { params }: { params: { challengeOptionId: number } }
) => {
  console.log(
    "PUT request received for challengeOptionId:",
    params.challengeOptionId
  );

  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const body = await req.json();
  console.log("Request body:", body);

  // Validate required fields
  if (body.text === undefined || body.correct === undefined) {
    return new NextResponse("Missing required fields", { status: 400 });
  }

  // Fetch the challenge to determine its type
  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, body.challengeId),
  });

  if (!challenge) {
    return new NextResponse("Challenge not found", { status: 404 });
  }

  // Validate for "FILL_IN_THE_BLANK" type
  if (challenge.type === "FILL_IN_THE_BLANK" && !body.audioSrc) {
    return new NextResponse("Missing audio source for 'Fill in the Blank'", {
      status: 400,
    });
  }

  // Validate audioSrc and imageSrc for "ASSIST" type
  if (challenge.type === "ASSIST" && (!body.audioSrc || !body.imageSrc)) {
    return new NextResponse(
      "Missing audio source or image source for 'Assist'",
      {
        status: 400,
      }
    );
  }

  // Conditionally require the sequence field only for "Drag and Drop" type
  if (challenge.type === "DRAG_AND_DROP" && body.sequence === undefined) {
    return new NextResponse("Missing sequence for 'Drag and Drop'", {
      status: 400,
    });
  }

  // ADDED: "SPEAK_THIS_ADVANCED" check (no extra validation needed)
  if (challenge.type === "SPEAK_THIS_ADVANCED") {
    console.log("Handling option update for SPEAK_THIS_ADVANCED type.");
  }

  const data = await db
    .update(challengeOptions)
    .set({
      text: body.text,
      correct: body.correct,
      side: body.side || null, // Set side to null if not provided
      audioSrc: body.audioSrc || null, // Set audioSrc to null if not provided
      imageSrc: body.imageSrc || null, // Set imageSrc to null if not provided
      matchPairId: body.matchPairId || null, // Set matchPairId to null if not provided
      sequence:
        challenge.type === "DRAG_AND_DROP"
          ? body.sequence // Ensure sequence is required for Drag and Drop
          : body.sequence ?? null, // Allow null for other challenge types
    })
    .where(eq(challengeOptions.id, params.challengeOptionId))
    .returning();

  if (data.length === 0) {
    return new NextResponse("Challenge option not found", { status: 404 });
  }

  return NextResponse.json(data[0]);
};

export const DELETE = async (
  req: Request,
  { params }: { params: { challengeOptionId: number } }
) => {
  console.log(
    "DELETE request received for challengeOptionId:",
    params.challengeOptionId
  );

  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const data = await db
    .delete(challengeOptions)
    .where(eq(challengeOptions.id, params.challengeOptionId))
    .returning();

  if (data.length === 0) {
    return new NextResponse("Challenge option not found", { status: 404 });
  }

  return NextResponse.json(data[0]);
};
