import { NextResponse } from "next/server";
import db from "@/db/drizzle";
import { isAdmin } from "@/lib/admin";
import { eq } from "drizzle-orm";
import { challengeOptions, challenges } from "@/db/schema";

export const GET = async () => {
  console.log("GET request received for challengeOptions");

  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const data = await db.query.challengeOptions.findMany();
  console.log("Fetched challengeOptions:", data);

  return NextResponse.json(data);
};

export const POST = async (req: Request) => {
  console.log("POST request received for challengeOptions");

  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const body = await req.json();
  console.log("Request body:", body);

  // Validate required fields
  if (!body.text || body.correct === undefined || !body.challengeId) {
    return new NextResponse("Missing required fields", { status: 400 });
  }

  // Fetch the challenge to determine its type
  const challenge = await db.query.challenges.findFirst({
    where: eq(challenges.id, body.challengeId),
  });

  if (!challenge) {
    return new NextResponse("Challenge not found", { status: 404 });
  }

  // Conditionally validate the sequence field for "Drag and Drop" type
  if (challenge.type === "DRAG_AND_DROP" && body.sequence === undefined) {
    return new NextResponse("Missing sequence for 'Drag and Drop'", {
      status: 400,
    });
  }

  // Validate for "FILL_IN_THE_BLANK" type
  if (challenge.type === "FILL_IN_THE_BLANK" && !body.text) {
    return new NextResponse("Missing text for 'Fill in the Blank'", {
      status: 400,
    });
  }

  // Validate audioSrc and imageSrc for "ASSIST" type
  if (challenge.type === "ASSIST" && (!body.audioSrc || !body.imageSrc)) {
    return new NextResponse(
      "Missing audio source or image source for 'Assist'",
      {
        status: 400,
      }
    );
  }

  // ADDED: "SPEAK_THIS_ADVANCED" check (no extra validation needed)
  if (challenge.type === "SPEAK_THIS_ADVANCED") {
    console.log("Handling new option creation for SPEAK_THIS_ADVANCED type.");
  }

  const data = await db
    .insert(challengeOptions)
    .values({
      challengeId: body.challengeId,
      text: body.text,
      correct: body.correct,
      side: body.side || null, // Set side to null if not provided
      audioSrc: body.audioSrc || null, // Set audioSrc to null if not provided
      imageSrc: body.imageSrc || null, // Set imageSrc to null if not provided
      matchPairId: body.matchPairId || null, // Set matchPairId to null if not provided
      sequence:
        challenge.type === "DRAG_AND_DROP"
          ? body.sequence // Ensure sequence is required for Drag and Drop
          : body.sequence ?? null, // Allow null for other challenge types
    })
    .returning();

  console.log("Inserted challengeOption:", data);

  return NextResponse.json(data[0]);
};
