import { NextResponse } from "next/server";

import db from "@/db/drizzle";
import { isAdmin } from "@/lib/admin";
import { lessons } from "@/db/schema";

export const GET = async () => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const data = await db.query.lessons.findMany({
    with: {
      challenges: {
        orderBy: (challenges, { asc }) => [asc(challenges.order)],
        with: {
          challengeOptions: true,
        },
      },
    },
  });

  // Include sentence, mediaType, mediaUrl, topCardText, topCardAudio, and sequence in the response
  const response = data.map((lesson) => ({
    ...lesson,
    challenges: lesson.challenges.map((challenge) => ({
      ...challenge,
      sentence: challenge.sentence, // Include sentence for FILL_IN_THE_BLANK
      mediaType: challenge.mediaType,
      mediaUrl: challenge.mediaUrl,
      topCardText: challenge.topCardText,
      topCardAudio: challenge.topCardAudio,
      challengeOptions: challenge.challengeOptions.map((option) => ({
        id: option.id,
        text: option.text,
        imageSrc: option.imageSrc,
        audioSrc: option.audioSrc,
        matchPairId: option.matchPairId,
        correct: option.correct,
        sequence: option.sequence, // Include sequence for DRAG_AND_DROP
      })),
    })),
  }));

  return NextResponse.json(response);
};

export const POST = async (req: Request) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const body = await req.json();

  // Validate required fields for lessons
  if (!body.title || !body.unitId || body.order === undefined) {
    return new NextResponse("Missing required fields", { status: 400 });
  }

  const data = await db
    .insert(lessons)
    .values({
      ...body,
    })
    .returning();

  return NextResponse.json(data[0]);
};
