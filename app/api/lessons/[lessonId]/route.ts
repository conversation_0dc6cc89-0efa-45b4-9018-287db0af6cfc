import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import db from "@/db/drizzle";
import { lessons } from "@/db/schema";
import { isAdmin } from "@/lib/admin";

export const GET = async (
  req: Request,
  { params }: { params: { lessonId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const data = await db.query.lessons.findFirst({
    where: eq(lessons.id, params.lessonId),
    with: {
      challenges: {
        orderBy: (challenges, { asc }) => [asc(challenges.order)],
        with: {
          challengeOptions: true,
        },
      },
    },
  });

  if (!data) {
    return new NextResponse("Lesson not found", { status: 404 });
  }

  // Include sentence, mediaType, mediaUrl, topCardText, topCardAudio, and sequence in the response
  const response = {
    ...data,
    challenges: data.challenges.map((challenge) => ({
      ...challenge,
      sentence: challenge.sentence, // Include sentence for FILL_IN_THE_BLANK
      mediaType: challenge.mediaType,
      mediaUrl: challenge.mediaUrl,
      topCardText: challenge.topCardText,
      topCardAudio: challenge.topCardAudio,
      challengeOptions: challenge.challengeOptions.map((option) => ({
        id: option.id,
        text: option.text,
        imageSrc: option.imageSrc,
        audioSrc: option.audioSrc,
        matchPairId: option.matchPairId,
        correct: option.correct,
        sequence: option.sequence, // Include sequence for DRAG_AND_DROP
      })),
    })),
  };

  return NextResponse.json(response);
};

export const PUT = async (
  req: Request,
  { params }: { params: { lessonId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const body = await req.json();
  const data = await db
    .update(lessons)
    .set({
      ...body,
    })
    .where(eq(lessons.id, params.lessonId))
    .returning();

  if (data.length === 0) {
    return new NextResponse("Lesson not found", { status: 404 });
  }

  return NextResponse.json(data[0]);
};

export const DELETE = async (
  req: Request,
  { params }: { params: { lessonId: number } }
) => {
  if (!isAdmin()) {
    return new NextResponse("Unauthorized", { status: 403 });
  }

  const data = await db
    .delete(lessons)
    .where(eq(lessons.id, params.lessonId))
    .returning();

  if (data.length === 0) {
    return new NextResponse("Lesson not found", { status: 404 });
  }

  return NextResponse.json(data[0]);
};
