// app/api/classCredentials/route.ts

import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { and, eq } from "drizzle-orm";
import { teacherStudents, users, messages } from "@/db/schema";

// OPTIONAL: a local helper function to create or find the user in your local DB.
import { getOrCreateUser } from "@/lib/users";

/**
 * POST /api/classCredentials
 * Body: { schoolUsername: string, schoolPassword: string, avatarSrc: string }
 *
 * Steps:
 * 1) Ensure local user exists in `users`.
 * 2) Lookup teacher by (school_username, school_password).
 * 3) Insert a pivot row in `teacher_students`.
 * 4) Create a welcome message from teacher to student.
 */
export async function POST(request: Request) {
  try {
    // (1) Clerk sees who is logged in
    const { userId } = auth();
    console.log("[classCredentials] Auth userId:", userId);

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized. No user session." },
        { status: 401 }
      );
    }

    // (2) Insert or find the local user row so there's no foreign key error
    // If you have a function getOrCreateUser() that already does this, call it:
    const currentUserRecord = await getOrCreateUser();
    console.log("[classCredentials] Current user record:", currentUserRecord);

    if (!currentUserRecord) {
      return NextResponse.json(
        { error: "Unable to find or create user record." },
        { status: 500 }
      );
    }

    // If this user is already a teacher, they shouldn't be joining another class.
    if (currentUserRecord.role === "teacher") {
      return NextResponse.json(
        { error: "Teachers cannot join a class as a student." },
        { status: 400 }
      );
    }

    // (3) Parse the body for class credentials
    const body = await request.json();
    const { schoolUsername, schoolPassword, avatarSrc } = body;
    console.log(
      "[classCredentials] Received credentials, username:",
      schoolUsername
    );

    if (!schoolUsername || !schoolPassword) {
      return NextResponse.json(
        { error: "Missing credentials." },
        { status: 400 }
      );
    }

    // (4) Lookup teacher user who has these class credentials
    const teacher = await db.query.users.findFirst({
      where: and(
        eq(users.schoolUsername, schoolUsername),
        eq(users.schoolPassword, schoolPassword)
      ),
    });

    console.log(
      "[classCredentials] Teacher lookup result:",
      teacher ? "found" : "not found"
    );

    if (!teacher) {
      return NextResponse.json(
        { error: "Invalid class credentials." },
        { status: 400 }
      );
    }

    // Log teacher details for debugging
    console.log("[classCredentials] Teacher details:", {
      teacherId: teacher.userId,
      teacherEmail: teacher.email,
      teacherRole: teacher.role,
    });

    // Double-check that the found user is actually a teacher
    if (teacher.role !== "teacher") {
      console.log(
        "[classCredentials] Warning: Found user with credentials is not a teacher!"
      );
      return NextResponse.json(
        { error: "Found credentials belong to a non-teacher account." },
        { status: 400 }
      );
    }

    // Update the user record with the avatar if provided
    if (avatarSrc) {
      await db.update(users).set({ avatarSrc }).where(eq(users.userId, userId));

      console.log("[classCredentials] Updated user with avatar:", avatarSrc);
    }

    // (5) Insert pivot row linking teacher and current student
    // At this point, we know userId is in `users` (from step #2).
    // Check if a pivot row already exists to avoid duplicates
    const existingPivot = await db.query.teacherStudents.findFirst({
      where: and(
        eq(teacherStudents.teacherId, teacher.userId),
        eq(teacherStudents.studentId, userId)
      ),
    });

    console.log(
      "[classCredentials] Existing pivot:",
      existingPivot ? "found" : "not found"
    );

    if (existingPivot) {
      // Even if already linked, we'll return a success message with teacher info for the UI
      console.log("[classCredentials] Student already linked to this teacher");
      return NextResponse.json({
        success: true,
        teacherId: teacher.userId,
        teacherName: teacher.displayName || "Your Teacher",
        message: "You are already linked to this teacher.",
      });
    }

    // Create the teacher-student relationship
    const insertResult = await db
      .insert(teacherStudents)
      .values({
        teacherId: teacher.userId,
        studentId: userId,
      })
      .returning();

    console.log(
      "[classCredentials] Link created result:",
      insertResult.length > 0 ? "success" : "failed",
      "details:",
      JSON.stringify(insertResult)
    );

    // (6) Create a welcome message from teacher to student
    try {
      const welcomeMessage = await db
        .insert(messages)
        .values({
          senderId: teacher.userId,
          recipientId: userId,
          content: `Welcome to my class! I'm glad you've joined. Let me know if you have any questions.`,
          createdAt: new Date(),
        })
        .returning();

      console.log(
        "[classCredentials] Created welcome message id:",
        welcomeMessage.length > 0 ? welcomeMessage[0]?.id : "unknown"
      );
    } catch (messageError) {
      // Log the error but don't fail the entire operation if message creation fails
      console.error(
        "[classCredentials] Error creating welcome message:",
        messageError
      );
    }

    // (7) For verification, retrieve the pivot row we just created
    const verifyPivot = await db.query.teacherStudents.findFirst({
      where: and(
        eq(teacherStudents.teacherId, teacher.userId),
        eq(teacherStudents.studentId, userId)
      ),
    });

    console.log(
      "[classCredentials] Verification - pivot found after insert:",
      !!verifyPivot
    );

    // (8) Return success with teacher info
    return NextResponse.json({
      success: true,
      teacherId: teacher.userId,
      teacherName: teacher.displayName || "Your Teacher",
    });
  } catch (error) {
    console.error("[classCredentials] Error linking teacher & student:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
