"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  User,
  Users,
  AlertCircle,
  Loader2,
  Check,
} from "lucide-react";
import { GraduationCap } from "lucide-react";
import Image from "next/image";

// Define available avatars
const avatarOptions = [
  { id: "blue", src: "/BlueAvatar.jpg", alt: "Blue Avatar" },
  { id: "green", src: "/GreenAvatar.jpg", alt: "Green Avatar" },
  { id: "pink", src: "/PinkAvatar.jpg", alt: "Pink Avatar" },
  { id: "purple", src: "/PurpleAvatar.jpg", alt: "Purple Avatar" },
  { id: "red", src: "/RedAvatar.jpg", alt: "Red Avatar" },
  { id: "yellow", src: "/YellowAvatar.jpg", alt: "Yellow Avatar" },
];

export default function JoinSchoolPage() {
  const router = useRouter();
  const [schoolUsername, setSchoolUsername] = useState("");
  const [schoolPassword, setSchoolPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  // The default role is "student", but user can toggle to "teacher".
  const [role, setRole] = useState<"student" | "teacher">("student");

  // If the user chooses to be a teacher, they might set up their own teacher credentials
  const [teacherUsername, setTeacherUsername] = useState("");
  const [teacherPassword, setTeacherPassword] = useState("");
  const [teacherError, setTeacherError] = useState("");
  const [teacherLoading, setTeacherLoading] = useState(false);

  // New state variables for handling teacher setup transition
  const [setupPhase, setSetupPhase] = useState<
    "input" | "avatar" | "processing" | "complete"
  >("input");
  const [setupMessage, setSetupMessage] = useState("");
  const [setupProgress, setSetupProgress] = useState(0);

  // Avatar selection state
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(null);
  const [avatarError, setAvatarError] = useState("");

  // Animation state
  const [fadeIn, setFadeIn] = useState(false);

  useEffect(() => {
    // Trigger fade-in animation on component mount
    setFadeIn(true);
  }, []);

  // Function to handle moving to avatar selection
  const handleMoveToAvatarSelection = () => {
    // Basic validation
    if (role === "student" && (!schoolUsername || !schoolPassword)) {
      setError("Please enter both class code and password");
      return;
    }

    if (role === "teacher" && (!teacherUsername || !teacherPassword)) {
      setTeacherError("Please enter both class code and password");
      return;
    }

    // Clear any existing errors and move to avatar selection phase
    setError("");
    setTeacherError("");
    setAvatarError("");
    setSetupPhase("avatar");
  };

  // Enhanced function for student to link class credentials
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if an avatar has been selected
    if (!selectedAvatar) {
      setAvatarError("Please select an avatar");
      return;
    }

    setError("");
    setLoading(true);
    setSetupPhase("processing");
    setSetupMessage("Connecting to your class...");
    setSetupProgress(30);

    try {
      // First API call - Connect with class credentials
      const res = await fetch("/api/classCredentials", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          schoolUsername,
          schoolPassword,
          avatarSrc: selectedAvatar, // Include selected avatar in the request
        }),
      });

      setSetupProgress(70);

      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.error || "Error linking to teacher");
      }

      console.log("[JoinSchool] Link success. Teacher ID:", data.teacherId);

      // Show success state before redirecting
      setSetupPhase("complete");
      setSetupMessage(
        `Successfully joined ${
          data.teacherName || "your teacher"
        }'s class! Redirecting to dashboard...`
      );
      setSetupProgress(100);

      // Add a small delay before redirecting to ensure state updates and user sees success message
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 1500);
    } catch (err: any) {
      console.error("[JoinSchool] Error submitting class credentials:", err);
      setError(err.message || "Invalid credentials");
      setSetupPhase("avatar"); // Go back to avatar selection on error
    } finally {
      setLoading(false);
    }
  };

  // Enhanced teacher setup with inline polling
  const handleTeacherSetup = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if an avatar has been selected
    if (!selectedAvatar) {
      setAvatarError("Please select an avatar");
      return;
    }

    console.log("[JoinSchool] Teacher setup initiated");

    setTeacherError("");
    setTeacherLoading(true);
    setSetupPhase("processing");
    setSetupMessage("Setting up your teacher account...");
    setSetupProgress(10);

    try {
      console.log("[JoinSchool] Sending data:", {
        schoolUsername: teacherUsername,
        schoolPassword: teacherPassword,
        avatarSrc: selectedAvatar,
      });

      // Step 1: Call the setupTeacher API with avatar information
      const res = await fetch("/api/setupTeacher", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          schoolUsername: teacherUsername,
          schoolPassword: teacherPassword,
          avatarSrc: selectedAvatar, // Include selected avatar in the request
        }),
      });

      console.log("[JoinSchool] Response status:", res.status);

      const data = await res.json();
      console.log("[JoinSchool] Response data:", data);

      if (!res.ok) {
        throw new Error(data.error || "Error setting teacher credentials");
      }

      console.log("[JoinSchool] Teacher setup success:", data);
      setSetupProgress(40);
      setSetupMessage("Teacher role assigned. Verifying account status...");

      // Step 2: Poll the status until we confirm the user is a teacher
      let attempts = 0;
      const maxAttempts = 10;

      const checkTeacherStatus = async () => {
        try {
          const statusRes = await fetch("/api/users");
          const statusData = await statusRes.json();
          attempts++;

          console.log(
            `[JoinSchool] Role check attempt ${attempts}:`,
            statusData
          );
          setSetupProgress(40 + attempts * 6); // Progress from 40% to 100%

          if (statusData.isTeacher) {
            // Success! User is now a teacher
            console.log("[JoinSchool] Teacher role confirmed!");
            setSetupMessage("Setup complete! Redirecting to dashboard...");
            setSetupPhase("complete");
            setSetupProgress(100);

            setTimeout(() => {
              window.location.href = "/dashboard";
            }, 1000);
            return;
          }

          if (attempts >= maxAttempts) {
            // Too many attempts
            throw new Error(
              "Could not confirm teacher status. Please try again."
            );
          }

          // Try again after a delay
          setSetupMessage(
            `Verifying account status... (${attempts}/${maxAttempts})`
          );
          setTimeout(checkTeacherStatus, 1000);
        } catch (error: any) {
          console.error("[JoinSchool] Error checking teacher status:", error);
          setTeacherError(error.message || "Failed to verify teacher status");
          setTeacherLoading(false);
          setSetupPhase("avatar"); // Go back to avatar selection on error
        }
      };

      // Start polling
      checkTeacherStatus();
    } catch (err: any) {
      console.error("[JoinSchool] Error setting teacher credentials:", err);
      setTeacherError(err.message || "Invalid teacher setup");
      setTeacherLoading(false);
      setSetupPhase("avatar"); // Go back to avatar selection on error
    }
  };

  // Helper function to render the appropriate action button based on setup phase
  const renderActionButton = () => {
    // Avatar selection phase buttons
    if (setupPhase === "avatar") {
      return (
        <div className="flex flex-col space-y-4 w-full">
          {avatarError && (
            <div className="p-3 bg-red-50 text-red-700 rounded-lg flex items-center text-sm">
              <AlertCircle size={20} className="w-5 h-5 mr-2 text-red-500" />
              {avatarError}
            </div>
          )}
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={() => setSetupPhase("input")}
              className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-xl font-medium hover:bg-gray-300 transition-all duration-200"
            >
              Back
            </button>
            <button
              type="submit"
              form={role === "student" ? "studentForm" : "teacherForm"}
              disabled={loading || !selectedAvatar}
              className="flex-1 bg-black text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {loading ? (
                <span className="flex items-center justify-center">
                  <Loader2
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    size={24}
                  />
                  Processing...
                </span>
              ) : (
                `${role === "student" ? "Join" : "Create"} Quran Class`
              )}
            </button>
          </div>
        </div>
      );
    }

    // Input phase (credentials entry)
    if (setupPhase === "input") {
      return (
        <button
          type="button"
          onClick={handleMoveToAvatarSelection}
          className="w-full bg-black text-white py-3 px-4 rounded-xl font-medium hover:bg-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transform hover:scale-[1.01] active:scale-[0.99]"
        >
          Next: Choose Avatar
        </button>
      );
    }

    // Progress indicator for processing/complete phases
    if (setupPhase === "processing" || setupPhase === "complete") {
      return (
        <div className="w-full">
          <div className="flex items-center justify-center mb-2">
            <p className="text-sm font-medium text-gray-700">{setupMessage}</p>
          </div>
          <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="bg-black h-full transition-all duration-300 ease-in-out"
              style={{ width: `${setupProgress}%` }}
            ></div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 flex items-stretch">
      <div
        className={`flex-1 p-6 flex items-stretch transition-opacity duration-500 ${
          fadeIn ? "opacity-100" : "opacity-0"
        }`}
      >
        <div className="bg-white rounded-3xl w-full lg:w-2/5 overflow-hidden flex shadow-lg border border-gray-100">
          <div className="w-full p-8 flex flex-col">
            {/* Back navigation */}
            <div className="mb-6">
              <button
                onClick={() => router.back()}
                className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-black transition-all duration-200 transform hover:scale-105"
                aria-label="Go back"
              >
                <ArrowLeft size={18} />
              </button>
            </div>

            {/* Main content */}
            <div className="flex-1 flex flex-col">
              <div className="mb-auto">
                {/* Dynamic title based on the current phase */}
                <h1 className="text-3xl font-bold text-black mb-2 bg-clip-text text-transparent bg-gradient-to-r from-black to-gray-700">
                  {setupPhase === "avatar"
                    ? "Choose Your Avatar"
                    : role === "student"
                    ? "Join Your Quran Class"
                    : "Create Your Quran Class"}
                </h1>

                {/* Dynamic description based on the current phase */}
                <p className="text-sm text-black/70 mb-8 font-light">
                  {setupPhase === "avatar"
                    ? "Pick an avatar that will represent you in discussions with your teacher and classmates."
                    : role === "student"
                    ? "Please enter the class credentials provided by your teacher to begin your Quran learning journey."
                    : "Set up your class credentials to share with your students for Quran learning and memorization."}
                </p>

                {/* Only show role toggle if we're in input phase */}
                {setupPhase === "input" && (
                  <div className="mb-6">
                    <label className="block mb-3 text-sm font-medium text-gray-700">
                      I am a:
                    </label>
                    <div className="relative p-1 bg-gray-50 rounded-xl flex items-stretch shadow-inner">
                      <div
                        className={`absolute transition-all duration-300 bg-white rounded-lg shadow-md top-1 bottom-1 ${
                          role === "student"
                            ? "left-1 right-[calc(50%+1px)]"
                            : "right-1 left-[calc(50%+1px)]"
                        }`}
                      ></div>
                      <label
                        className={`relative flex-1 flex items-center justify-center p-3 rounded-lg cursor-pointer z-10 transition-all duration-200`}
                      >
                        <input
                          type="radio"
                          value="student"
                          checked={role === "student"}
                          onChange={() => setRole("student")}
                          className="sr-only"
                        />
                        <div className="flex items-center">
                          <GraduationCap
                            size={20}
                            className={`h-5 w-5 mr-2 transition-colors ${
                              role === "student"
                                ? "text-black"
                                : "text-gray-400"
                            }`}
                          />
                          <span
                            className={`text-sm font-medium transition-colors ${
                              role === "student"
                                ? "text-gray-800"
                                : "text-gray-500"
                            }`}
                          >
                            Student
                          </span>
                        </div>
                      </label>
                      <label
                        className={`relative flex-1 flex items-center justify-center p-3 rounded-lg cursor-pointer z-10 transition-all duration-200`}
                      >
                        <input
                          type="radio"
                          value="teacher"
                          checked={role === "teacher"}
                          onChange={() => setRole("teacher")}
                          className="sr-only"
                        />
                        <div className="flex items-center">
                          <Users
                            size={20}
                            className={`h-5 w-5 mr-2 transition-colors ${
                              role === "teacher"
                                ? "text-black"
                                : "text-gray-400"
                            }`}
                          />
                          <span
                            className={`text-sm font-medium transition-colors ${
                              role === "teacher"
                                ? "text-gray-800"
                                : "text-gray-500"
                            }`}
                          >
                            Teacher
                          </span>
                        </div>
                      </label>
                    </div>
                  </div>
                )}

                {/* Content container with fixed height to prevent jumping */}
                <div className="h-80">
                  {/* STUDENT FORM: only show if role === "student" and we're in input phase */}
                  <div
                    className={`transition-all duration-300 ${
                      role === "student" && setupPhase === "input"
                        ? "opacity-100 visible"
                        : "opacity-0 invisible absolute"
                    }`}
                  >
                    <form
                      id="studentForm"
                      onSubmit={handleSubmit}
                      className="space-y-5"
                    >
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Class Code
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={schoolUsername}
                            onChange={(e) => setSchoolUsername(e.target.value)}
                            placeholder="Enter the code from your teacher"
                            className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black/20 transition-all py-3 px-4 border bg-white text-gray-800 group-hover:border-gray-400"
                            required
                          />
                        </div>
                      </div>
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Class Password
                        </label>
                        <div className="relative">
                          <input
                            type="password"
                            value={schoolPassword}
                            onChange={(e) => setSchoolPassword(e.target.value)}
                            placeholder="Enter the password from your teacher"
                            className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black/20 transition-all py-3 px-4 border bg-white text-gray-800 group-hover:border-gray-400"
                            required
                          />
                        </div>
                      </div>
                      {error && (
                        <div className="p-3 bg-red-50 text-red-700 rounded-lg flex items-center text-sm">
                          <AlertCircle
                            size={20}
                            className="w-5 h-5 mr-2 text-red-500"
                          />
                          {error}
                        </div>
                      )}
                    </form>
                  </div>

                  {/* TEACHER FORM: only show if role === "teacher" and we're in input phase */}
                  <div
                    className={`transition-all duration-300 ${
                      role === "teacher" && setupPhase === "input"
                        ? "opacity-100 visible"
                        : "opacity-0 invisible absolute"
                    }`}
                  >
                    <form
                      id="teacherForm"
                      onSubmit={handleTeacherSetup}
                      className="space-y-5"
                    >
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Create Class Code
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={teacherUsername}
                            onChange={(e) => setTeacherUsername(e.target.value)}
                            placeholder="Choose a unique class code to share"
                            className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black/20 transition-all py-3 px-4 border bg-white text-gray-800 group-hover:border-gray-400"
                            required
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-2 flex items-center">
                          You will share this code with your students
                        </p>
                      </div>
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Create Class Password
                        </label>
                        <div className="relative">
                          <input
                            type="password"
                            value={teacherPassword}
                            onChange={(e) => setTeacherPassword(e.target.value)}
                            placeholder="Choose a secure password to share"
                            className="mt-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-black focus:ring focus:ring-black/20 transition-all py-3 px-4 border bg-white text-gray-800 group-hover:border-gray-400"
                            required
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-2 flex items-center">
                          <AlertCircle
                            size={16}
                            className="h-4 w-4 mr-1 text-gray-400"
                          />
                          This password will be shared with your students
                        </p>
                      </div>
                      {teacherError && (
                        <div className="p-3 bg-red-50 text-red-700 rounded-lg flex items-center text-sm">
                          <AlertCircle
                            size={20}
                            className="w-5 h-5 mr-2 text-red-500"
                          />
                          {teacherError}
                        </div>
                      )}
                    </form>
                  </div>

                  {/* AVATAR SELECTION: Show in avatar phase for both roles */}
                  <div
                    className={`transition-all duration-300 ${
                      setupPhase === "avatar"
                        ? "opacity-100 visible"
                        : "opacity-0 invisible absolute"
                    }`}
                  >
                    <div className="grid grid-cols-3 gap-4">
                      {avatarOptions.map((avatar) => (
                        <div
                          key={avatar.id}
                          onClick={() => setSelectedAvatar(avatar.src)}
                          className={`
                            relative cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-200
                            ${
                              selectedAvatar === avatar.src
                                ? "border-black shadow-md scale-105"
                                : "border-transparent hover:border-gray-300"
                            }
                          `}
                        >
                          <Image
                            src={avatar.src}
                            alt={avatar.alt}
                            width={80}
                            height={80}
                            className="w-full h-auto aspect-square object-cover"
                          />
                          {selectedAvatar === avatar.src && (
                            <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                              <div className="bg-white rounded-full p-1">
                                <Check className="w-5 h-5 text-black" />
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    <p className="text-sm text-gray-500 mt-4 text-center">
                      Select an avatar that represents you
                    </p>
                  </div>

                  {/* Student Success UI */}
                  <div
                    className={`transition-all duration-300 ${
                      setupPhase === "complete"
                        ? "opacity-100 visible"
                        : "opacity-0 invisible absolute"
                    }`}
                  >
                    <div className="flex flex-col items-center justify-center h-full space-y-6">
                      <div className="h-16 w-16 bg-green-500 rounded-full flex items-center justify-center text-white">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>

                      <h2 className="text-xl font-semibold text-gray-800">
                        {role === "student"
                          ? "Successfully Joined!"
                          : "Teacher Account Ready!"}
                      </h2>

                      <p className="text-gray-600 text-center max-w-xs">
                        {setupMessage ||
                          `You've successfully ${
                            role === "student"
                              ? "joined the class"
                              : "created your teacher account"
                          }. Redirecting to your dashboard...`}
                      </p>
                    </div>
                  </div>

                  {/* Processing UI */}
                  <div
                    className={`transition-all duration-300 ${
                      setupPhase === "processing"
                        ? "opacity-100 visible"
                        : "opacity-0 invisible absolute"
                    }`}
                  >
                    <div className="flex flex-col items-center justify-center h-full space-y-6">
                      <Loader2 className="h-16 w-16 text-black animate-spin" />
                      <h2 className="text-xl font-semibold text-gray-800">
                        {role === "student"
                          ? "Joining Your Class..."
                          : "Setting Up Teacher Account..."}
                      </h2>
                      <p className="text-gray-600 text-center max-w-xs">
                        Please wait while we process your information...
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fixed Action Button Container at bottom */}
              <div className="mt-8">{renderActionButton()}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
