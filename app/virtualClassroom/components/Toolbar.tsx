"use client";

import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import { ChromePicker } from "react-color";

// ----------------------------------------------------------------------
// (A) Interface for Toolbar props
// ----------------------------------------------------------------------
interface ToolbarProps {
  selectedButton?: string | null;
  setSelectedButton?: (btn: string | null) => void;
  externalColor?: string;
  externalLineWidth?: number;
  externalTool?: "pen" | "eraser";
  onClearCanvas?: () => void;

  // Provide these callbacks so we can call them directly:
  onSelectMarker?: (
    buttonId: string,
    newTool: "pen" | "eraser",
    newColor: string,
    newLineWidth: number,
    newOpacity?: number
  ) => void;
  onColorChange?: (newColor: string) => void;

  // If you want to position inside the white canvas container:
  containerRef?: React.RefObject<HTMLDivElement>;
}

// ----------------------------------------------------------------------
// Toolbar Component
// ----------------------------------------------------------------------
function ToolbarComponent(
  {
    selectedButton: propSelectedButton,
    setSelectedButton: propSetSelectedButton,
    externalColor,
    externalLineWidth,
    externalTool,
    containerRef,
    onSelectMarker,
    onColorChange,
  }: ToolbarProps,
  ref: React.Ref<any>
) {
  // ----------------------------------------------------------------------
  // 1. States for color, lineWidth, tool, and optional opacity
  // ----------------------------------------------------------------------
  const [selectedButton, setSelectedButtonState] = useState<string | null>(
    propSelectedButton || null
  );
  const [color, setColor] = useState(externalColor || "#000000");
  const [lineWidth, setLineWidth] = useState(externalLineWidth || 3);
  const [tool, setTool] = useState<"pen" | "eraser">(externalTool || "pen");
  const [opacity, setOpacity] = useState(1.0);

  // New: Marker-specific colors for marker1, marker2, marker3
  const [markerColors, setMarkerColors] = useState({
    marker1: "#0000FF", // default blue
    marker2: "#FF0000", // default red
    marker3: "#FFFF00", // default yellow
  });

  // ----------------------------------------------------------------------
  // 2. Tooltip & toolbar references
  // ----------------------------------------------------------------------
  // const [showTooltip, setShowTooltip] = useState<string | null>(null);
  const [toolbarVisible, setToolbarVisible] = useState(true);
  const toolbarRef = useRef<HTMLDivElement | null>(null);

  // ----------------------------------------------------------------------
  // 3. Layout and scaling logic
  // ----------------------------------------------------------------------
  const [scale, setScale] = useState(1);
  const [toolbarLayout, setToolbarLayout] = useState({
    buttonSize: "w-20",
    orientation: "horizontal",
    containerClass: "flex-row",
  });

  // Absolute positioning for the toolbar
  const [toolbarPosition, setToolbarPosition] = useState({ x: 0, y: 0 });

  // ----------------------------------------------------------------------
  // 4. "Select Button" with optional newOpacity
  // ----------------------------------------------------------------------
  const selectButton = (
    buttonId: string,
    newTool: "pen" | "eraser",
    newColor: string,
    newLineWidth: number,
    newOpacity?: number
  ) => {
    if (selectedButton === buttonId) {
      // Unselect if clicked again
      setSelectedButtonState(null);
      propSetSelectedButton?.(null);
    } else {
      setTool(newTool);
      setColor(newColor);
      setLineWidth(newLineWidth);
      if (typeof newOpacity === "number") {
        setOpacity(newOpacity);
      }
      setSelectedButtonState(buttonId);
      propSetSelectedButton?.(buttonId);

      if (onSelectMarker) {
        onSelectMarker(buttonId, newTool, newColor, newLineWidth, newOpacity);
      }
    }
  };

  // ----------------------------------------------------------------------
  // 6. Scale logic for toolbar
  // ----------------------------------------------------------------------
  useEffect(() => {
    const toolbarEl = toolbarRef.current;
    if (!toolbarEl) return;

    const parentEl = toolbarEl.parentElement;
    if (!parentEl) return;

    const updateScale = () => {
      const parentWidth = parentEl.clientWidth;
      const toolbarWidth = toolbarEl.scrollWidth;
      if (toolbarWidth > parentWidth) {
        const newScale = parentWidth / toolbarWidth;
        setScale(newScale);
      } else {
        setScale(1);
      }
    };

    const ro = new ResizeObserver(() => {
      updateScale();
    });
    ro.observe(parentEl);

    updateScale();
    return () => {
      ro.disconnect();
    };
  }, []);

  // ----------------------------------------------------------------------
  // 7. Toolbar size management & orientation
  // ----------------------------------------------------------------------
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 480) {
        setToolbarLayout({
          buttonSize: "w-16",
          orientation: "vertical",
          containerClass: "flex-col",
        });
      } else if (width < 768) {
        setToolbarLayout({
          buttonSize: "w-18",
          orientation: "horizontal",
          containerClass: "flex-row",
        });
      } else {
        setToolbarLayout({
          buttonSize: "w-20",
          orientation: "horizontal",
          containerClass: "flex-row",
        });
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // ----------------------------------------------------------------------
  // 8. Show/Hide toolbar (placeholder logic)
  // ----------------------------------------------------------------------
  useEffect(() => {
    setToolbarVisible(true);
  }, []);

  // ----------------------------------------------------------------------
  // 9. Bottom-center logic inside containerRef
  // ----------------------------------------------------------------------
  useEffect(() => {
    const toolbarEl = toolbarRef.current;
    if (!toolbarEl) return;
    if (!containerRef?.current) return;

    const updateToolbarPosition = () => {
      const container = containerRef.current;
      if (!container) return;

      const parentRect = container.getBoundingClientRect();
      const toolbarRect = toolbarEl.getBoundingClientRect();

      // Position bottom-center inside the white container
      const newX = (parentRect.width - toolbarRect.width) / 2;
      const newY = parentRect.height - toolbarRect.height - 20;

      setToolbarPosition({ x: newX, y: newY });
    };

    const ro = new ResizeObserver(() => {
      updateToolbarPosition();
    });
    ro.observe(containerRef.current);
    ro.observe(toolbarEl);

    updateToolbarPosition();
    return () => {
      ro.disconnect();
    };
  }, [toolbarLayout.orientation, containerRef]);

  // ----------------------------------------------------------------------
  // 10. Forward ref if needed
  // ----------------------------------------------------------------------
  useImperativeHandle(ref, () => ({}));

  // ----------------------------------------------------------------------
  // 11. (Optional) Color override from the palette
  // ----------------------------------------------------------------------
  function handleColorClick(newColor: string) {
    if (
      selectedButton === "marker1" ||
      selectedButton === "marker2" ||
      selectedButton === "marker3"
    ) {
      setMarkerColors((prev) => ({
        ...prev,
        [selectedButton]: newColor,
      }));
      setColor(newColor);
      if (onColorChange) {
        onColorChange(newColor);
      }
    } else {
      setColor(newColor);
      if (onColorChange) {
        onColorChange(newColor);
      }
    }
  }

  // ----------------------------------------------------------------------
  // 12. Render the actual toolbar
  // ----------------------------------------------------------------------
  const renderToolbar = () => (
    <div
      ref={toolbarRef}
      className={`flex ${toolbarLayout.containerClass} gap-2 p-2 bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl border border-gray-200/50 transition-all duration-300 ease-in-out z-10`}
    >
      {/* Wrap marker buttons + shelf overlay in a relative container */}
      <div className="relative flex gap-2">
        {/* Marker #1 => default color Blue, 3 px, opacity=1.0 */}
        <button
          onMouseEnter={(e) => {
            if (selectedButton !== "marker1") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(7px)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onMouseLeave={(e) => {
            if (selectedButton !== "marker1") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(0)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onClick={() =>
            selectButton("marker1", "pen", markerColors.marker1, 3, 1.0)
          }
          className={`
            ${toolbarLayout.buttonSize}
            h-20
            flex
            items-center
            justify-center
            focus:outline-none
            hover:bg-transparent
            rounded
            overflow-visible
            transition-all
            duration-300
            ease-in-out
            ${selectedButton === "marker1" ? "scale-105" : "scale-100"}
            ${selectedButton === "marker1" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              {/* Marker #1 SVG */}
              <svg
                width="24"
                height="85"
                viewBox="0 0 185 51"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: 0,
                  transform: `translate(-50%, 0) rotate(-90deg) scale(4) ${
                    selectedButton === "marker1"
                      ? "translateX(10px)"
                      : "translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition: "transform 0.3s ease-in-out",
                }}
              >
                <path
                  d="M182.49 28.48c1.44-.22 2.51-1.44 2.51-2.87 0-1.42-1.05-2.64-2.48-2.87l-22.85-3.65c-4.02-.64-7.67 2.41-7.67 6.41 0 3.98 3.61 7.03 7.61 6.42l22.88-3.44Z"
                  fill={markerColors.marker1}
                ></path>
                <path
                  d="M159.554 31.55h-.001c-3.779.576-7.178-2.306-7.178-6.05 0-3.761 3.436-6.645 7.236-6.04l22.849 3.65c1.253.202 2.165 1.268 2.165 2.5 0 1.239-.928 2.306-2.191 2.5h-.001l-22.879 3.44Z"
                  stroke="#000"
                  strokeOpacity=".1"
                  strokeWidth=".75"
                ></path>
                <path
                  d="M163 18.57 108.92 4.31C106.5 3.5 104 3 104 1v49c0-2 2.5-2.5 4.51-3.08L163 33.06V18.57Z"
                  fill="#EEE"
                  fillOpacity="0.3"
                ></path>
                <path
                  d="M163 18.57 108.92 4.31C106.5 3.5 104 3 104 1v49c0-2 2.5-2.5 4.51-3.08L163 33.06V18.57Z"
                  fill="url(#_868415490__a)"
                ></path>
                <path
                  d="M163 18.57 108.92 4.31C106.5 3.5 104 3 104 1v49c0-2 2.5-2.5 4.51-3.08L163 33.06V18.57Z"
                  fill="url(#_868415490__b)"
                  fillOpacity=".7"
                ></path>
                <path d="M81 1H0v49h81V1Z" fill="#EEE" fillOpacity="0.4"></path>
                <path d="M81 1H0v49h81V1Z" fill="url(#_868415490__c)"></path>
                <path
                  d="M81 1H0v49h81V1Z"
                  fill="url(#_868415490__d)"
                  fillOpacity=".7"
                ></path>
                <path
                  d="M104 50V1c0-.55-.45-1-1-1H82c-.55 0-1 .45-1 1v49c0 .55.45 1 1 1h21c.55 0 1-.45 1-1Z"
                  fill="#EEE"
                  fillOpacity="0.7"
                ></path>
                <path
                  d="M104 50V1c0-.55-.45-1-1-1H82c-.55 0-1 .45-1 1v49c0 .55.45 1 1 1h21c.55 0 1-.45 1-1Z"
                  fill="url(#_868415490__e)"
                ></path>
                <path
                  d="M104 50V1c0-.55-.45-1-1-1H82c-.55 0-1 .45-1 1v49c0 .55.45 1 1 1h21c.55 0 1-.45 1-1Z"
                  fill="url(#_868415490__f)"
                  fillOpacity=".7"
                ></path>
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M163 18.57 108.92 4.31c-.31-.1-.61-.2-.92-.29-2.08-.66-3.97-1.26-4-2.97V1c0-.55-.45-1-1-1H82c-.55 0-1 .45-1 1H0v49h81c0 .55.45 1 1 1h21c.55 0 1-.45 1-1v-.04c.03-1.79 2.08-2.36 3.95-2.89.19-.05.38-.11.56-.16L163 33.06V18.57Z"
                  fill="url(#_868415490__g)"
                  fillOpacity=".4"
                ></path>
                <path
                  d="M41.16 32.94c.81-.21 1.32-1.24 1.03-2.1-.1-.37-.41-.73-.59-1.05-.81-1.31-1.64-2.62-2.5-3.89l-.25-.41 3.44 2.36c1.6 1.37 3.29 2.62 5 3.86 1 .82 2.47.11 2.58-1.27.05-.49-.1-.99-.39-1.35l-.24-.32c-.32-.41-.64-.81-1-1.18-.02-.02-.03-.04-.03-.06l-2.75-4.51 4.39 2.23c.76.52 1.54.97 2.33 **********.***********.68.21 1.45.15 1.77-.86.46-.88.07-2.08-.78-2.45a69.8 69.8 0 0 0-1.22-.6c-1.62-.79-3.26-1.55-4.95-2.21l-2.99-1.52c-.62.37-1.32.79-1.96 1.14l-.68.37c-.51.26-1.17.15-1.59-.28-.56-.54-.68-1.54-.25-2.19.14-.22.49-.79.64-1.01.83-1.29 1.76-2.7 2.65-3.93L39.1 22.2c-.83-.71-1.69-1.37-2.55-2-.42-.32-.86-.64-1.3-.95-.49-.41-1.17-.51-1.71-.19-.69.39-1.03 1.39-.73 2.19.17.41.46.75.68 1.12.61.94 1.23 1.85 1.91 2.73l1.65 2.71-4.47-2.25c-.73-.54-1.49-1.01-2.26-1.46-.29-.15-.54-.34-.84-.45-.71-.22-1.54.17-1.87.9-.46.9-.08 2.13.76 2.55 0 0 .42.22.46.22 1.89.94 3.8 1.83 5.79 2.53-.03-.02-.07-.04-.08-.06l2.75 1.39c.88.58 1.81 1.1 2.72 1.59.24.19.78.3 1.15.17Z"
                  stroke="#000"
                  strokeOpacity=".07"
                  strokeWidth=".75"
                ></path>
                <path d="M95 0h-6v51h6V0Z" fill={markerColors.marker1}></path>
                <path
                  d="M89.375 50.625V.375h5.25v50.25h-5.25Z"
                  stroke="#000"
                  strokeOpacity=".1"
                  strokeWidth=".75"
                ></path>
                <defs>
                  <linearGradient
                    id="_868415490__a"
                    x1="0"
                    y1="0"
                    x2="185"
                    y2="51"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                  <linearGradient
                    id="_868415490__b"
                    x1="104"
                    y1="50"
                    x2="104"
                    y2="1"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#C4C4C4" />
                    <stop offset=".725" stopColor="#fff" />
                    <stop offset="1" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_868415490__c"
                    x1="134.358"
                    y1="53.324"
                    x2="141.358"
                    y2="2.324"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#C4C4C4" />
                    <stop offset=".748" stopColor="#fff" />
                    <stop offset=".889" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_868415490__d"
                    x1="41.15"
                    y1="24.778"
                    x2="32.65"
                    y2="21.278"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset=".177" stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                  <linearGradient
                    id="_868415490__e"
                    x1="56"
                    y1="26"
                    x2="29"
                    y2="26"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                  <linearGradient
                    id="_868415490__f"
                    x1="0"
                    y1="0"
                    x2="185"
                    y2="51"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                  <linearGradient
                    id="_868415490__g"
                    x1="0"
                    y1="0"
                    x2="185"
                    y2="51"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </button>

        {/* Marker #2 => default color Red, lineWidth ~12, opacity=1.0 */}
        <button
          onMouseEnter={(e) => {
            if (selectedButton !== "marker2") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(7px)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onMouseLeave={(e) => {
            if (selectedButton !== "marker2") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(0)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onClick={() =>
            selectButton("marker2", "pen", markerColors.marker2, 12, 1.0)
          }
          className={`
            ${toolbarLayout.buttonSize}
            h-20
            flex
            items-center
            justify-center
            focus:outline-none
            hover:bg-transparent
            rounded
            overflow-visible
            transition-all
            duration-300
            ease-in-out
            ${selectedButton === "marker2" ? "scale-105" : "scale-100"}
            ${selectedButton === "marker2" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          {/* Marker #2 SVG */}
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 185 51"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: 0,
                  transform: `translate(-50%, 0) rotate(-90deg) scale(4) ${
                    selectedButton === "marker2"
                      ? "translateX(10px)"
                      : "translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition: "transform 0.3s ease-in-out",
                }}
              >
                <path
                  d="M161.96 35.07h-.5v-21h.5c11.5 0 21.5 4.7 21.5 10.5s-10 10.5-21.5 10.5Z"
                  fill={markerColors.marker2}
                ></path>
                <path
                  d="M161.85 34.695h-.015V14.445h.125c5.704 0 11.022 1.166 14.9 3.038 3.907 1.885 6.225 4.416 6.225 7.087 0 2.671-2.318 5.202-6.225 7.087-3.878 1.872-9.196 3.038-14.9 3.038H161.85Z"
                  stroke={markerColors.marker2}
                  strokeOpacity=".1"
                  strokeWidth=".85"
                ></path>
                <path d="M89 0H0v49h89V0Z" fill="#EEE"></path>
                <path
                  d="M89 0H0v49h89V0Z"
                  fill="url(#_1063316517__a)"
                  fillOpacity=".08"
                ></path>
                <path d="M89 44H0v3h89v-3Z" fill="#D9D9D9"></path>
                <path d="M95 0h-6v49h6V0Z" fill={markerColors.marker2}></path>
                <path
                  d="M89.375 48.625V.375h5.25v48.25h-5.25Z"
                  stroke={markerColors.marker2}
                  strokeOpacity=".1"
                  strokeWidth=".85"
                ></path>
                <path
                  d="M124 49H95V0h29a2 2 0 0 1 2 2v45a2 2 0 0 1-2 2Z"
                  fill="#EEE"
                ></path>
                <path
                  d="M124 49H95V0h29a2 2 0 0 1 2 2v45a2 2 0 0 1-2 2Z"
                  fill="url(#_1063316517__b)"
                ></path>
                <path
                  d="M126 47c.35 0 26-10 26-10V11.5L126 2v45Z"
                  fill="#EEE"
                ></path>
                <path
                  d="M126 47c.35 0 26-10 26-10V11.5L126 2v45Z"
                  fill="url(#_1063316517__c)"
                ></path>
                <path
                  d="M163 13.4V35c0 1.1-.9 2-2 2h-9V11.4h9a2 2 0 0 1 2 2Z"
                  fill="#EEE"
                ></path>
                <path
                  d="M163 13.4V35c0 1.1-.9 2-2 2h-9V11.4h9a2 2 0 0 1 2 2Z"
                  fill="url(#_1063316517__d)"
                ></path>
                <path
                  d="M163 13.4V35c0 1.1-.9 2-2 2V11.4a2 2 0 0 1 2 2Z"
                  fill="#EEE"
                ></path>
                <path d="M126 44H95v3h31v-3Z" fill="#D9D9D9"></path>
                <path
                  d="m126 46 26-10v-1l-26 10v1ZM161 35h-9v1h9v-1Z"
                  fill="#C9C9C9"
                  fillOpacity=".5"
                ></path>
                <path
                  d="M29.38 22.98c0-.47-.2.47 0 0l2.25-3.29 7.24 3.25-.91-3.25a3.694 3.694 0 0 1 4.89-4.44l10.88 4.21c1.91.74 2.85 2.88 2.11 4.78a3.697 3.697 0 0 1-4.78 2.11l-4-1.55.98 3.5c.38 1.36-.05 2.83-1.11 3.77a3.681 3.681 0 0 1-3.87.64L28 26l1.38-3.02Z"
                  fill={markerColors.marker2}
                ></path>
                <path
                  d="m46.699 24.901.98 3.5a3.338 3.338 0 0 1-.998 3.388 3.306 3.306 0 0 1-3.472.577L28.5 25.812l1.186-2.596a1.53 1.53 0 0 0 .025-.054l2.052-3.001 6.954 3.121.731.328-.216-.771-.91-3.25a3.318 3.318 0 0 1 4.393-3.99l10.88 4.21a3.313 3.313 0 0 1 1.897 4.295A3.322 3.322 0 0 1 51.196 26l-4-1.55-.7-.271.203.721Zm-17.06-1.811-.259-.11h.266a.34.34 0 0 1 .018.074l-.004.006a.325.325 0 0 1-.022.03Zm.027.003Z"
                  stroke={markerColors.marker2}
                  strokeOpacity=".07"
                  strokeWidth=".85"
                ></path>

                <defs>
                  <linearGradient
                    id="_1063316517__a"
                    x1="44.5"
                    y1="0"
                    x2="44.5"
                    y2="54.5"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                  <linearGradient
                    id="_1063316517__b"
                    x1="110.5"
                    y1="49"
                    x2="110.5"
                    y2="0"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#C4C4C4" />
                    <stop offset=".725" stopColor="#fff" />
                    <stop offset="1" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_1063316517__c"
                    x1="134.358"
                    y1="53.324"
                    x2="141.358"
                    y2="2.324"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#C4C4C4" />
                    <stop offset=".748" stopColor="#fff" />
                    <stop offset=".889" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_1063316517__d"
                    x1="157.5"
                    y1="43.5"
                    x2="157.5"
                    y2="11"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset=".214" stopColor="#FFD0D8" stopOpacity="0" />
                    <stop offset=".495" stopColor="#FFD0D8" />
                    <stop offset=".893" stopColor="#FFD0D8" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_1063316517__e"
                    x1="40.383"
                    y1="22.496"
                    x2="29.883"
                    y2="17.496"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset=".209" stopColor="red" />
                    <stop offset=".632" stopColor="red" stopOpacity="0" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </button>

        {/* Marker #3 => default color Yellow, lineWidth ~16, partial opacity=0.5 */}
        <button
          onMouseEnter={(e) => {
            if (selectedButton !== "marker3") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(7px)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onMouseLeave={(e) => {
            if (selectedButton !== "marker3") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(0)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onClick={() =>
            selectButton("marker3", "pen", markerColors.marker3, 16, 0.5)
          }
          className={`
            ${toolbarLayout.buttonSize}
            h-20
            flex
            items-center
            justify-center
            focus:outline-none
            hover:bg-transparent
            rounded
            overflow-visible
            transition-all
            duration-300
            ease-in-out
            ${selectedButton === "marker3" ? "scale-105" : "scale-100"}
            ${selectedButton === "marker3" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          {/* Marker #3 SVG */}
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 185 51"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: 0,
                  transform: `translate(-50%, 0) rotate(-90deg) scale(4) ${
                    selectedButton === "marker3"
                      ? "translateX(10px)"
                      : "translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition: "transform 0.3s ease-in-out",
                }}
              >
                <path d="M89 1H0v49h89V1Z" fill="#EEE"></path>
                <path
                  d="M89 1H0v49h89V1Z"
                  fill="url(#_1675523858__a)"
                  fillOpacity=".08"
                ></path>
                <path
                  d="M125.39 50H89V1h36.39c.4 0 .78.12 1.11.34l9.43 6.3c4.93 3.3 10.74 5.06 16.67 5.06h8.4c1.1 0 2 .9 2 2v21.6c0 1.1-.9 2-2 2h-8.4a29.96 29.96 0 0 0-16.67 5.06l-9.43 6.3c-.32.22-.71.34-1.11.34Z"
                  fill="url(#_1675523858__b)"
                ></path>
                <path
                  d="M176.25 15c.45 0 .84.3.96.73l5.43 19c.18.64-.3 1.27-.96 1.27H163V15h13.25Z"
                  fill={markerColors.marker3}
                ></path>
                <path
                  d="M176.849 15.83v.003l5.43 18.998v.001a.623.623 0 0 1-.599.793h-18.305v-20.25h12.875c.28 0 .524.186.599.456Z"
                  stroke={markerColors.marker3}
                  strokeOpacity=".1"
                  strokeWidth="1"
                ></path>
                <path d="M89 45H0v3h89v-3Z" fill="#D9D9D9"></path>
                <path
                  d="M115.07 9c4.42 0 8.65 1.83 11.67 5.06 5.77 6.15 5.77 15.73 0 21.89a15.98 15.98 0 0 1-11.67 5.06H95V25c0-8.84 7.16-16 16-16h4.07Z"
                  fill="url(#_1675523858__c)"
                  fillOpacity=".5"
                ></path>
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M89 48h35.42c.38 0 .75-.11 1.07-.31l10.67-6.74c4.8-3.03 10.36-4.64 16.03-4.64H161c1.1 0 2-.9 2-2v1c0 1.1-.9 2-2 2h-8.4a29.96 29.96 0 0 0-16.67 5.06l-9.43 6.3c-.33.22-.72.34-1.11.34H89V48Zm36.39-48Z"
                  fill="#C9C9C9"
                  fillOpacity=".5"
                ></path>
                <path d="M95 1h-6v49h6V1Z" fill={markerColors.marker3}></path>
                <path
                  d="M89.375 49.625V1.375h5.25v48.25h-5.25Z"
                  stroke={markerColors.marker3}
                  strokeOpacity=".1"
                  strokeWidth="1"
                ></path>
                <path
                  d="m29 28.44 2.82-6.74 6.96 2.91-.9-3.21 4.83-4.4L56 22.14l-2.64 6.81-6.48-2.51.97 3.46-4.93 4.35L29 28.44Z"
                  fill={markerColors.marker3}
                />
                <path
                  d="m39.141 24.509-.837-2.988 4.487-4.088 12.724 4.921-2.37 6.11-6.13-2.374-.698-.27.202.721.906 3.234-4.577 4.038-13.357-5.575 2.53-6.047 6.614 2.765.715.299-.209-.746Z"
                  stroke={markerColors.marker3}
                  strokeOpacity=".07"
                  strokeWidth="1"
                />

                <defs>
                  <linearGradient
                    id="_1675523858__a"
                    x1="44.5"
                    y1="1"
                    x2="44.5"
                    y2="55.5"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".245" stopColor="#fff" stopOpacity="0.5" />
                    <stop offset="1" stopColor="#C4C4C4" />
                  </linearGradient>
                  <linearGradient
                    id="_1675523858__b"
                    x1="126"
                    y1="50"
                    x2="126"
                    y2="1"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#C4C4C4" />
                    <stop offset=".725" stopColor="#fff" />
                    <stop offset="1" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_1675523858__c"
                    x1="134.358"
                    y1="53.324"
                    x2="141.358"
                    y2="2.324"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#C4C4C4" />
                    <stop offset=".748" stopColor="#fff" />
                    <stop offset=".889" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_1675523858__d"
                    x1="41.15"
                    y1="24.778"
                    x2="32.65"
                    y2="21.278"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset=".177" stopColor="yellow" />
                    <stop offset="1" stopColor="yellow" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_1675523858__e"
                    x1="56"
                    y1="26"
                    x2="29"
                    y2="26"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="yellow" />
                    <stop offset="1" stopColor="yellow" stopOpacity="0" />
                  </linearGradient>
                  <filter
                    id="_4113241068__a"
                    x="0"
                    y="0"
                    width="163"
                    height="49"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feBlend
                      in="SourceGraphic"
                      in2="BackgroundImageFix"
                      result="shape"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset />
                    <feGaussianBlur stdDeviation="4.5" />
                    <feComposite
                      in2="hardAlpha"
                      operator="arithmetic"
                      k2="-1"
                      k3="1"
                    />
                    <feColorMatrix values="0 0 0 0 0.988235 0 0 0 0 0.486275 0 0 0 0 0.564706 0 0 0 1 0" />
                    <feBlend
                      in2="shape"
                      result="effect1_innerShadow_2075_309080"
                    />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </button>

        {/* Eraser => default color #ffffff, lineWidth=10, opacity=1.0 */}
        <button
          onMouseEnter={(e) => {
            if (selectedButton !== "eraser") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(7px)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onMouseLeave={(e) => {
            if (selectedButton !== "eraser") {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(0)";
            } else {
              e.currentTarget.querySelector("svg")!.style.transform =
                "translate(-50%, 0) rotate(-90deg) scale(4) translateX(10px)";
            }
          }}
          onClick={() => selectButton("eraser", "eraser", "#ffffff", 10, 1.0)}
          className={`
            ${toolbarLayout.buttonSize}
            h-20
            flex
            items-center
            justify-center
            focus:outline-none
            hover:bg-transparent
            rounded
            overflow-visible
            transition-all
            duration-300
            ease-in-out
            ${selectedButton === "eraser" ? "scale-105" : "scale-100"}
            ${selectedButton === "eraser" ? "z-20" : "z-20"}
            hover:z-20
          `}
        >
          {/* Eraser SVG */}
          <div className="relative w-full h-full pointer-events-auto">
            <div className="pointer-events-none absolute w-full h-full overflow-visible top-0 left-0">
              <svg
                width="24"
                height="85"
                viewBox="0 0 185 51"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  position: "absolute",
                  left: "50%",
                  bottom: 0,
                  transform: `translate(-50%, 0) rotate(-90deg) scale(4) ${
                    selectedButton === "eraser"
                      ? "translateX(10px)"
                      : "translateX(0)"
                  }`,
                  transformOrigin: "center center",
                  transition: "transform 0.3s ease-in-out",
                }}
              >
                <g filter="url(#_4113241068__a)">
                  <path
                    d="M157.569 2.394A6 6 0 0 1 163 8.367v32.266a6 6 0 0 1-5.431 5.973L132.45 49H0V0h132.45l25.119 2.394Z"
                    fill="#FF9EAD"
                  ></path>
                  <path
                    d="M157.569 2.394A6 6 0 0 1 163 8.367v32.266a6 6 0 0 1-5.431 5.973L132.45 49H0V0h132.45l25.119 2.394Z"
                    fill="url(#_4113241068__b)"
                  ></path>
                </g>
                <path
                  d="M160.762 2.654a3 3 0 0 1 2.717 2.987v37.718a3 3 0 0 1-2.717 2.987l-24.711 2.343a3 3 0 0 1-3.283-2.987V3.298A3 3 0 0 1 136.051.31l24.711 2.343Z"
                  fill="#8CC8FF"
                ></path>
                <path
                  d="M160.762 2.654a3 3 0 0 1 2.717 2.987v37.718a3 3 0 0 1-2.717 2.987l-24.711 2.343a3 3 0 0 1-3.283-2.987V3.298A3 3 0 0 1 136.051.31l24.711 2.343Z"
                  fill="url(#_4113241068__c)"
                ></path>
                <path
                  d="M146.753.72a6 6 0 0 1 5.684 5.992v35.576a6 6 0 0 1-5.684 5.991l-13.64.721h-13.324a6 6 0 0 1-6-6V6a6 6 0 0 1 6-6h13.324l13.64.72Z"
                  fill="url(#_4113241068__d)"
                ></path>
                <path
                  d="M153.127 1.726v45.563l-9.662.676V1.035l9.662.69Z"
                  fill="url(#_4113241068__e)"
                ></path>
                <rect
                  x="33"
                  y="16"
                  width="5.667"
                  height="5.667"
                  rx="1"
                  fill="#fff"
                ></rect>
                <rect
                  x="33"
                  y="27.333"
                  width="5.667"
                  height="5.667"
                  rx="1"
                  fill="#fff"
                ></rect>
                <rect
                  x="38.667"
                  y="21.667"
                  width="5.667"
                  height="5.667"
                  rx="1"
                  fill="#fff"
                ></rect>
                <rect
                  x="44.333"
                  y="16"
                  width="5.667"
                  height="5.667"
                  rx="1"
                  fill="#fff"
                ></rect>
                <rect
                  x="44.333"
                  y="27.333"
                  width="5.667"
                  height="5.667"
                  rx="1"
                  fill="#fff"
                ></rect>
                <defs>
                  <linearGradient
                    id="_4113241068__b"
                    x1="-26.476"
                    y1="49"
                    x2="148.947"
                    y2="-29.22"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#FC7C90"></stop>
                    <stop offset=".65" stopColor="#FF9EAD"></stop>
                  </linearGradient>
                  <linearGradient
                    id="_4113241068__c"
                    x1="186.599"
                    y1="24.5"
                    x2="132.768"
                    y2="24.5"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#FFD0D8"></stop>
                    <stop offset="1" stopColor="#FFD0D8"></stop>
                  </linearGradient>
                  <linearGradient
                    id="_4113241068__d"
                    x1="152.437"
                    y1="24.5"
                    x2="113.789"
                    y2="24.5"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset=".214" stopColor="#FFD0D8" stopOpacity="0" />
                    <stop offset=".495" stopColor="#FFD0D8" />
                    <stop offset=".893" stopColor="#FFD0D8" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="_4113241068__e"
                    x1="153.127"
                    y1="24.5"
                    x2="143.465"
                    y2="24.5"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#fff" stopOpacity="0" />
                    <stop offset=".094" stopColor="#fff" />
                    <stop offset=".88" stopColor="#fff" />
                    <stop offset="1" stopColor="#fff" stopOpacity="0" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </button>

        {/* (Optional) shelf overlay if you need it */}
        <div
          className="
            absolute
            bottom-0
            left-0
            w-full
            h-2/5
            bg-white/95
            backdrop-blur-sm
            pointer-events-none
            z-30
          "
        ></div>
      </div>

      {/* Divider */}
      <div className="border-l border-gray-200 h-24 mx-6"></div>

      {/* Color Palette Grid */}
      <div className="grid grid-cols-3 gap-4">
        <button
          onClick={() => handleColorClick("#000000")}
          className="w-8 h-8 rounded-full bg-black transform transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
          aria-label="Black color"
        />
        <button
          onClick={() => handleColorClick("#007AFF")}
          className="w-8 h-8 rounded-full bg-blue-500 transform transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400"
          aria-label="Blue color"
        />
        <button
          onClick={() => handleColorClick("#34C759")}
          className="w-8 h-8 rounded-full bg-green-500 transform transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-400"
          aria-label="Green color"
        />

        <button
          onClick={() => handleColorClick("#FF0000")}
          className="w-8 h-8 rounded-full bg-red-500 transform transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-400"
          aria-label="Red color"
        />
        <button
          onClick={() => handleColorClick("#FFCC00")}
          className="w-8 h-8 rounded-full bg-yellow-400 transform transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-300"
          aria-label="Yellow color"
        />
        <button
          onClick={() => handleColorClick("#A020F0")}
          className="w-8 h-8 rounded-full bg-purple-400 transform transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-400"
          aria-label="Purple color"
        />
      </div>
    </div>
  );

  // ----------------------------------------------------------------------
  // 13. Return with style + conditional rendering
  // ----------------------------------------------------------------------
  return (
    <>
      <style>
        {`
          @media (max-width: 480px) {
            .toolbar-vertical {
              flex-direction: column;
              right: 1rem;
              top: 50%;
              transform: translateY(-50%);
            }
          }

          .toolbar-button {
            transition: all 0.3s ease-in-out;
          }

          .toolbar-button:hover {
            transform: scale(1.1);
          }
        `}
      </style>

      {toolbarVisible && renderToolbar()}
    </>
  );
}

// ----------------------------------------------------------------------
// Forward Ref
// ----------------------------------------------------------------------
const Toolbar = forwardRef(ToolbarComponent);
export default Toolbar;
