"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  Camera,
  Mic,
  MessageSquare,
  Settings,
  Users,
  Monitor,
  Phone,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

// Updated: We'll ensure the default domain is "8x8.vc" for JaaS.
// We'll keep the code referencing `process.env.JITSI || "8x8.vc"` so that
// you can override it in .env if you wish.
const domainEnv = process.env.JITSI || "8x8.vc";
// NOTE: If you want a fully hardcoded domain, you can remove the `process.env.JITSI` usage.

declare global {
  interface Window {
    JitsiMeetExternalAPI: any;
  }
}

interface JitsiMeetProps {
  roomName: string;
  userName?: string;
  onLeave?: () => void;
  // NEW: Allow a custom subject to replace the “vpaas...” label in Jitsi
  subject?: string;
}

const JitsiMeet: React.FC<JitsiMeetProps> = ({
  roomName,
  userName = "User",
  onLeave,
  subject, // newly added
}) => {
  const jitsiContainerRef = useRef<HTMLDivElement | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [participantCount, setParticipantCount] = useState(1);
  const [meetingTime, setMeetingTime] = useState(0);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [inMeeting, setInMeeting] = useState(true); // Track meeting state
  const apiRef = useRef<any>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Start meeting timer
    if (inMeeting) {
      timerRef.current = setInterval(() => {
        setMeetingTime((prev) => prev + 1);
      }, 1000);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [inMeeting]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      window.JitsiMeetExternalAPI &&
      inMeeting
    ) {
      try {
        console.log("[JitsiMeet] Using domain:", domainEnv);
        console.log("[JitsiMeet] Using roomName:", roomName);

        const domain = domainEnv;
        const options = {
          roomName: roomName,
          parentNode: jitsiContainerRef.current,
          width: "100%",
          height: "100%",
          configOverwrite: {
            displayName: userName,
            prejoinPageEnabled: false,
            startWithAudioMuted: false,
            startWithVideoMuted: false,
            disableDeepLinking: true,
          },
          interfaceConfigOverwrite: {
            TOOLBAR_BUTTONS: [
              "microphone",
              "camera",
              "closedcaptions",
              "desktop",
              "fullscreen",
              "fodeviceselection",
              "hangup",
              "profile",
              "recording",
              "livestreaming",
              "etherpad",
              "sharedvideo",
              "settings",
              "raisehand",
              "videoquality",
              "filmstrip",
              "invite",
              "feedback",
              "stats",
              "shortcuts",
              "tileview",
              "videobackgroundblur",
              "download",
              "help",
              "mute-everyone",
              "security",
            ],
            SHOW_BRAND_WATERMARK: false,
            SHOW_WATERMARK_FOR_GUESTS: false,
            SHOW_JITSI_WATERMARK: false,
            DEFAULT_LOGO_URL: "",
            DEFAULT_WELCOME_PAGE_LOGO_URL: "",
            SHOW_POWERED_BY: false,
            HIDE_DEEP_LINKING_LOGO: true,
            SHOW_ROOM_NAME: false,
          },
        };

        console.log(
          "[JitsiMeet] Initializing Jitsi with options:",
          JSON.stringify({ domain, roomName })
        );

        apiRef.current = new window.JitsiMeetExternalAPI(domain, options);

        // Add event listeners including videoConferenceJoined
        apiRef.current.addEventListeners({
          participantJoined: () => {
            setParticipantCount((prev) => prev + 1);
            console.log("[JitsiMeet] Participant joined, updated count.");
          },
          participantLeft: () => {
            setParticipantCount((prev) => Math.max(1, prev - 1));
            console.log("[JitsiMeet] Participant left, updated count.");
          },
          audioMuteStatusChanged: ({ muted }: { muted: boolean }) => {
            setIsAudioMuted(muted);
            console.log("[JitsiMeet] Audio mute status changed:", muted);
          },
          videoMuteStatusChanged: ({ muted }: { muted: boolean }) => {
            setIsVideoMuted(muted);
            console.log("[JitsiMeet] Video mute status changed:", muted);
          },
          screenSharingStatusChanged: ({ on }: { on: boolean }) => {
            setIsScreenSharing(on);
            console.log("[JitsiMeet] Screen sharing status changed:", on);
          },
          readyToClose: () => {
            console.log("[JitsiMeet] Meeting is ready to close.");
            handleLeave();
          },
          videoConferenceJoined: () => {
            // Set the subject when the conference is joined (most reliable point)
            console.log(
              "[JitsiMeet] Conference joined, setting subject:",
              subject ?? "Lesson Session"
            );
            if (apiRef.current) {
              apiRef.current.executeCommand(
                "subject",
                subject ?? "Lesson Session"
              );
            }
          },
        });

        // Multiple attempts to set the subject to handle timing issues

        // First attempt - set subject as early as possible
        setTimeout(() => {
          if (apiRef.current) {
            console.log(
              "[JitsiMeet] Setting initial subject:",
              subject ?? "Lesson Session"
            );
            apiRef.current.executeCommand(
              "subject",
              subject ?? "Lesson Session"
            );
          }
        }, 1000);

        // Second attempt - backup after the UI is likely loaded
        setTimeout(() => {
          if (apiRef.current) {
            console.log(
              "[JitsiMeet] Setting subject again (backup):",
              subject ?? "Lesson Session"
            );
            apiRef.current.executeCommand(
              "subject",
              subject ?? "Lesson Session"
            );
          }
        }, 3000);

        setIsLoading(false);
        console.log("[JitsiMeet] Meeting initialization complete.");
      } catch (err) {
        console.error("[JitsiMeet] Failed to initialize meeting:", err);
        setError("Failed to initialize meeting");
        setIsLoading(false);
      }
    } else {
      console.error("[JitsiMeet] Jitsi API not found or inMeeting is false.");
      setError("Jitsi API not found. Ensure the script is included globally.");
      setIsLoading(false);
    }

    return () => {
      if (apiRef.current) {
        console.log("[JitsiMeet] Disposing Jitsi API instance.");
        apiRef.current.dispose();
      }
      if (jitsiContainerRef.current) {
        jitsiContainerRef.current.innerHTML = "";
      }
    };
  }, [roomName, userName, inMeeting, subject]);

  const handleLeave = () => {
    console.log("[JitsiMeet] handleLeave triggered, ending meeting.");
    setInMeeting(false);
    if (onLeave) {
      onLeave();
    }
    apiRef.current?.executeCommand("hangup");
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-[700px] bg-gray-50 rounded-lg">
        <div className="text-center p-8">
          <div className="flex justify-center mb-4">
            <AlertCircle className="text-red-500 w-8 h-8" />
          </div>
          <p className="text-red-500 text-xl mb-4 font-semibold">{error}</p>
          <p className="text-gray-600 mb-6">
            There was a problem initializing your meeting.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!inMeeting) {
    return (
      <div className="flex items-center justify-center h-[700px] bg-gray-50 rounded-lg">
        <div className="text-center p-8">
          <div className="flex justify-center mb-4">
            <CheckCircle className="text-green-500 w-8 h-8" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Thank you for joining!</h2>
          <p className="text-gray-600 mb-6">You have left the meeting.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
          >
            Rejoin
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-xl overflow-hidden h-full flex flex-col">
      {/* Meeting Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            {/* Replace the hardcoded "Meeting" with the subject prop */}
            <h2 className="text-lg font-semibold">{subject || "Meeting"}</h2>
            <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
              <Users className="w-4 h-4" />
              <span>
                {participantCount} participant
                {participantCount !== 1 ? "s" : ""}
              </span>
              <span>•</span>
              <span>{formatTime(meetingTime)}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Settings className="w-5 h-5 text-gray-600" />
            </button>
            <button
              onClick={handleLeave}
              className="flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              <Phone className="w-4 h-4 mr-2" />
              Leave
            </button>
          </div>
        </div>
      </div>

      {/* Meeting Container fills remainder of parent */}
      <div className="relative flex-1">
        {isLoading && (
          <div className="absolute inset-0 bg-gray-50 flex items-center justify-center z-10">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-gray-600">Joining meeting...</p>
            </div>
          </div>
        )}
        <div
          ref={jitsiContainerRef}
          className="h-full w-full"
          style={{ overflow: "hidden" }}
        />
      </div>
    </div>
  );
};

export default JitsiMeet;
