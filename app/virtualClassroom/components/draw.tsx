"use client";

import React, {
  useState,
  useRef,
  forwardRef,
  useImperative<PERSON>andle,
  useEffect, // <-- Added useEffect to imports
} from "react";
import Canvas, { CanvasComponentRef } from "./Canvas"; // <-- Import Canvas and its Ref type
import Toolbar from "./Toolbar"; // <-- Our Toolbar component

// --- Configuration ---
const MIN_STROKES_FOR_DONE = 1; // A drawing is valid for completion if it has at least one stroke.

// ------------------------------
// Stroke object interface - ENHANCED
// ------------------------------
interface StrokeObject {
  userId: string;
  tool: "pen" | "eraser";
  color: string;
  lineWidth: number;
  opacity: number;
  points: { x: number; y: number }[];
  // NEW: Step 5.2 - Add quality metrics to each stroke object
  quality?: {
    pointCount: number;
    boundingBox: {
      minX: number;
      minY: number;
      maxX: number;
      maxY: number;
    };
    length: number;
  };
}

// ------------------------------
// (NEW) Exposed Ref Methods Interface
// ------------------------------
export interface DrawRefHandles {
  clearCanvas: () => void;
  getFullStrokeHistory: () => StrokeObject[];
}

// ------------------------------
// (A) Interface & Additional Props
// ------------------------------
interface DrawProps {
  canDraw?: boolean;
  userId?: string;
  externalColor?: string;
  externalLineWidth?: number;
  externalTool?: "pen" | "eraser";
  onClearCanvas?: () => void;
  onDrawingComplete?: (strokes: StrokeObject[]) => void;
  onDone?: (strokes: StrokeObject[]) => void; // MODIFIED: Prop now accepts stroke data
}

// ------------------------------
// (B) Types for Markers
// ------------------------------
type MarkerKey = "marker1" | "marker2" | "marker3";
interface MarkerConfig {
  color: string;
  lineWidth: number;
  opacity: number;
  tool: "pen" | "eraser";
}
type MarkerCollection = Record<MarkerKey, MarkerConfig>;

// NOTE: The CanvasComponentRef interface is now imported directly from Canvas.tsx

// ======================================================================
// Helper Function - Step 5.2
// ======================================================================

/**
 * Iterates through strokes and adds quality metrics for analysis.
 */
const enrichStrokesWithQualityMetrics = (
  strokes: StrokeObject[]
): StrokeObject[] => {
  return strokes.map((stroke) => {
    if (stroke.quality || stroke.points.length < 2) {
      return stroke; // Already enriched or not a valid line
    }

    let minX = 1,
      minY = 1,
      maxX = 0,
      maxY = 0,
      length = 0;
    for (let i = 0; i < stroke.points.length; i++) {
      const p = stroke.points[i];
      minX = Math.min(minX, p.x);
      minY = Math.min(minY, p.y);
      maxX = Math.max(maxX, p.y);
      maxY = Math.max(maxY, p.y);

      if (i > 0) {
        const prevP = stroke.points[i - 1];
        length += Math.sqrt(
          Math.pow(p.x - prevP.x, 2) + Math.pow(p.y - prevP.y, 2)
        );
      }
    }

    return {
      ...stroke,
      quality: {
        pointCount: stroke.points.length,
        boundingBox: { minX, minY, maxX, maxY },
        length,
      },
    };
  });
};

// ----------------------------------------------------------------------
// Draw Component
// ----------------------------------------------------------------------
function DrawComponent(
  {
    canDraw = true,
    userId,
    externalColor,
    externalLineWidth,
    externalTool,
    onClearCanvas,
    onDrawingComplete,
    onDone,
  }: DrawProps,
  ref: React.Ref<DrawRefHandles>
) {
  // ----------------------------------------------------------------------
  // 1) Initialize the Active Marker on Page Load
  // ----------------------------------------------------------------------
  const [color, setColor] = useState(externalColor ?? "#0000FF");
  const [lineWidth, setLineWidth] = useState(externalLineWidth ?? 3);
  const [tool, setTool] = useState<"pen" | "eraser">(externalTool ?? "pen");
  const [opacity, setOpacity] = useState(1.0); // default full opacity
  const [selectedButton, setSelectedButton] = useState<string | null>(
    "marker1"
  );

  // ----------------------------------------------------------------------
  // 2) Manage Marker State
  // ----------------------------------------------------------------------
  const [markers, setMarkers] = useState<MarkerCollection>({
    marker1: { color: "#0000FF", lineWidth: 3, opacity: 1.0, tool: "pen" },
    marker2: { color: "#FF0000", lineWidth: 5, opacity: 1.0, tool: "pen" },
    marker3: { color: "#FFFF00", lineWidth: 8, opacity: 0.5, tool: "pen" },
  });

  const canvasRef = useRef<CanvasComponentRef>(null);
  const canvasContainerRef = useRef<HTMLDivElement | null>(null);

  const [strokeHistory, setStrokeHistory] = useState<StrokeObject[]>([]);
  // Step 1.1: Introduce a new, temporary state variable for incoming strokes.
  const [incomingStrokes, setIncomingStrokes] = useState<StrokeObject[] | null>(
    null
  );

  // Step 1.3 & 1.4: Create a new useEffect to process strokes safely after render.
  useEffect(() => {
    // Ensure the effect only runs when there are new strokes to process.
    if (incomingStrokes && incomingStrokes.length > 0) {
      // Logic moved from handleStrokesFromCanvas
      const enrichedStrokes = enrichStrokesWithQualityMetrics(incomingStrokes);
      setStrokeHistory(enrichedStrokes);
      onDrawingComplete?.(enrichedStrokes);
    }
  }, [incomingStrokes, onDrawingComplete]); // FIXED: Added onDrawingComplete to dependency array

  useImperativeHandle(ref, () => ({
    clearCanvas: () => {
      canvasRef.current?.clearCanvas();
      setStrokeHistory([]);
    },
    getFullStrokeHistory: () => {
      return strokeHistory;
    },
  }));

  const handleSelectButton = (
    buttonId: string,
    newTool: "pen" | "eraser",
    newColor: string,
    newLineWidth: number,
    newOpacity?: number
  ) => {
    if (selectedButton === buttonId) {
      setSelectedButton(null);
    } else {
      if (buttonId in markers) {
        const {
          color: storedColor,
          lineWidth: storedWidth,
          opacity: storedOpacity,
          tool: storedTool,
        } = markers[buttonId as MarkerKey];

        setTool(storedTool);
        setColor(storedColor);
        setLineWidth(storedWidth);
        setOpacity(storedOpacity);
      } else {
        setTool(newTool);
        setColor(newColor);
        setLineWidth(newLineWidth);
        if (typeof newOpacity === "number") {
          setOpacity(newOpacity);
        }
      }
      setSelectedButton(buttonId);
    }
  };

  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    if (selectedButton && selectedButton in markers) {
      setMarkers((prevMarkers) => ({
        ...prevMarkers,
        [selectedButton as MarkerKey]: {
          ...prevMarkers[selectedButton as MarkerKey],
          color: newColor,
        },
      }));
    }
  };

  // Step 1.2: Modify the handler to only perform a single, safe state update.
  const handleStrokesFromCanvas = (newStrokesBatch: StrokeObject[]) => {
    setIncomingStrokes(newStrokesBatch);
  };

  const handleClearCanvas = () => {
    canvasRef.current?.clearCanvas();
    setStrokeHistory([]);
    onClearCanvas?.();
  };

  // MODIFIED: This function now directly passes the stored history to the parent.
  const handleDoneClick = () => {
    onDone?.(strokeHistory);
  };

  // Step 5.1: Determine if the drawing is valid for completion
  const isDrawingValid = strokeHistory.length >= MIN_STROKES_FOR_DONE; // FIXED: Corrected typo from MIN_STROKES_for_DONE

  // ----------------------------------------------------------------------
  // (L) Render
  // ----------------------------------------------------------------------
  return (
    <>
      <style>
        {`
          @media (max-width: 480px) {
            .toolbar-vertical {
              flex-direction: column;
              right: 1rem;
              top: 50%;
              transform: translateY(-50%);
            }
          }

          .toolbar-button {
            transition: all 0.3s ease-in-out;
          }

          .toolbar-button:hover {
            transform: scale(1.1);
          }
        `}
      </style>

      <div className="flex flex-col w-full h-full bg-white relative">
        <div className="flex-1 relative p-4 bg-gray-50">
          <div
            className="absolute inset-0 m-4 border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm transition-shadow duration-200 hover:shadow-md"
            ref={canvasContainerRef}
          >
            <Canvas
              ref={canvasRef}
              canDraw={canDraw}
              userId={userId}
              color={color}
              lineWidth={lineWidth}
              tool={tool}
              opacity={opacity}
              onClearCanvas={onClearCanvas}
              onDrawingComplete={handleStrokesFromCanvas}
            />

            <div
              style={{
                position: "absolute",
                bottom: 0,
                left: "50%",
                transform: "translateX(-50%)",
                width: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                gap: "1rem",
                paddingBottom: "10px",
              }}
            >
              <Toolbar
                selectedButton={selectedButton}
                setSelectedButton={setSelectedButton}
                externalColor={color}
                externalLineWidth={lineWidth}
                externalTool={tool}
                onClearCanvas={handleClearCanvas}
                containerRef={canvasContainerRef}
                onSelectMarker={handleSelectButton}
                onColorChange={handleColorChange}
              />
              {onDone && (
                <button
                  onClick={handleDoneClick}
                  // Step 5.1: Button is disabled if the drawing is not valid
                  disabled={!canDraw || !isDrawingValid}
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full shadow-lg transition-transform transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none"
                  aria-label="I'm done drawing"
                >
                  Done
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

const Draw = forwardRef<DrawRefHandles, DrawProps>(DrawComponent);
export default Draw;
