// chapterpage.tsx

"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  ChevronLeft,
  ChevronRight,
  X,
  Info,
  Search,
  Zap,
  Maximize2,
  Loader, // A simple loader icon
} from "lucide-react";
import { useRouter } from "next/navigation";
import Toolbar from "./Toolbar";
import Canvas from "./Canvas";

/*
  Types
  ----------------------------------------
*/
export interface Square {
  id: number;
  squareNumber: number;
  content: string;
  metadata?: {
    complexity: "basic" | "intermediate" | "advanced";
    complexityScore: number;
    position: {
      index: number;
      total: number;
      isFirst: boolean;
      isLast: boolean;
      relativePosition: "beginning" | "middle" | "end";
    };
    phonetic: {
      family: string;
      group: string;
      similarLetters: string[];
    };
    search: {
      keywords: string[];
      variations: string[];
      commonMisspellings: string[];
      descriptions: string[];
    };
    visual: {
      characterCount: number;
      hasSpecialMarks: boolean;
      visualComplexity: number;
      shapeFamily: string;
    };
  };
}

interface AnnotationState {
  tool: "pen" | "eraser" | undefined;
  color: string;
  lineWidth: number;
  opacity: number;
}

interface AISelectionContext {
  squareId: number;
  confidence?: number;
  matchType?:
    | "exact_id"
    | "exact_content"
    | "keyword"
    | "description"
    | "position";
  matchedTerm?: string;
  alternatives?: Array<{
    id: number;
    content: string;
    confidence: number;
  }>;
  source: "ai" | "user";
  timestamp: number;
}

export interface ChapterPageProps {
  title: string;
  squares: Square[];
  order?: number;
  onSquareHighlighted?: (squareId: number) => void;
  highlightedSquareId?: number | null;
  isTeacher?: boolean;
  hoveredSquareId?: number | null;
  selectedSquareId?: number | null;
  onSquareHover?: (squareId: number | null) => void;
  onSquareSelected?: (squareId: number | null) => void;
  onChapterChange?: (newChapter: number) => void;
  showNavigation?: boolean;
  restrictNavigation?: boolean;
  allowDrawing?: boolean;
  requireIconClick?: boolean;
  analytics?: {
    totalLetters: number;
    complexityDistribution: {
      basic: number;
      intermediate: number;
      advanced: number;
    };
    phoneticGroups: string[];
    averageComplexityScore: number;
    searchableTerms: number;
  };
}

type AudioLoadStatus = "loading" | "loaded" | "error" | "idle";

const getAudioSrc = (lesson: number, position: number): string => {
  return `/${lesson}.${position}.wav`;
};

/*
  Combined CSS (Original + Senior Dev + Enhanced)
  ----------------------------------------
*/
const arabicStyle = `
  .arabic-text {
    font-feature-settings: "kern";
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes scaleIn {
    from { transform: scale(0.95); }
    to { transform: scale(1); }
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .audio-loader-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
  }

  @keyframes smartSelectionPulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
      border-color: rgba(34, 197, 94, 0.8);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
      border-color: rgba(34, 197, 94, 1);
    }
  }

  @keyframes fuzzyMatchPulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
      border-color: rgba(251, 191, 36, 0.8);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(251, 191, 36, 0);
      border-color: rgba(251, 191, 36, 1);
    }
  }

  @keyframes multiMatchIndicator {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.7);
      border-color: rgba(168, 85, 247, 0.8);
    }
    50% {
      box-shadow: 0 0 0 12px rgba(168, 85, 247, 0);
      border-color: rgba(168, 85, 247, 1);
    }
  }

  .fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out forwards;
  }

  .card-hover {
    transition: all 0.2s ease-out;
  }

  .ai-selected {
    animation: pulseGlow 2s ease-in-out 3;
    border: 2px solid rgba(59, 130, 246, 0.8) !important;
  }

  .smart-selection {
    animation: smartSelectionPulse 1.5s ease-in-out 4;
  }

  .fuzzy-match {
    animation: fuzzyMatchPulse 2s ease-in-out 3;
  }

  .multi-match {
    animation: multiMatchIndicator 2.5s ease-in-out 2;
  }

  .complexity-basic {
    border-left: 4px solid #22c55e;
  }

  .complexity-intermediate {
    border-left: 4px solid #f59e0b;
  }

  .complexity-advanced {
    border-left: 4px solid #ef4444;
  }

  .glassmorphism-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1.5px solid rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .metadata-tooltip {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(8px);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-size: 11px;
    max-width: 200px;
    z-index: 1000;
  }
`;

/*
  Component
  ----------------------------------------
*/
export default function ChapterPage({
  title,
  squares,
  order = 1,
  onSquareHighlighted,
  highlightedSquareId,
  isTeacher = false,
  hoveredSquareId,
  selectedSquareId,
  onSquareHover,
  onSquareSelected,
  onChapterChange,
  showNavigation = true,
  restrictNavigation = false,
  allowDrawing = true,
  requireIconClick = false,
}: ChapterPageProps) {
  const DEBUG_MODE =
    process.env.NODE_ENV === "development" &&
    typeof window !== "undefined" &&
    (window as any).DEBUG_CHAPTER_PAGE === true;

  const router = useRouter();

  const socketRef = useRef<any>(null);
  if (!socketRef.current && typeof window !== "undefined") {
    socketRef.current = (window as any).socket || null;
  }
  const socket = socketRef.current;

  // --- AUDIO REFACTOR: Start ---
  // A ref to hold a map of preloaded Audio elements.
  const audioMapRef = useRef<Map<number, HTMLAudioElement>>(new Map());
  // A state to track the loading status of each audio file to update the UI.
  const [audioStatuses, setAudioStatuses] = useState<
    Map<number, AudioLoadStatus>
  >(new Map());
  const currentlyPlayingRef = useRef<HTMLAudioElement | null>(null);

  // Effect for preloading all audio files for the current lesson (squares).
  useEffect(() => {
    const newStatuses = new Map<number, AudioLoadStatus>();
    const audioMap = audioMapRef.current;

    // Filter out squares that shouldn't be preloaded (example: locked squares)
    // For now, we assume all squares are valid.
    const validSquares = squares; // .filter(sq => !sq.isLocked);

    validSquares.forEach((sq) => {
      // If audio element already exists, don't re-create it.
      if (audioMap.has(sq.id)) {
        newStatuses.set(sq.id, audioStatuses.get(sq.id) || "loaded");
        return;
      }

      const audio = new Audio();
      audio.src = getAudioSrc(order, sq.squareNumber);

      newStatuses.set(sq.id, "loading");

      audio.oncanplaythrough = () => {
        setAudioStatuses((prev) => new Map(prev).set(sq.id, "loaded"));
        if (DEBUG_MODE) console.log(`Audio for square ${sq.id} preloaded.`);
      };

      audio.onerror = (e) => {
        setAudioStatuses((prev) => new Map(prev).set(sq.id, "error"));
        if (DEBUG_MODE)
          console.error(`Failed to load audio for square ${sq.id}:`, e);
      };

      audio.load(); // Start downloading
      audioMap.set(sq.id, audio);
    });

    setAudioStatuses(newStatuses);

    // Cleanup function: runs when component unmounts or `squares`/`order` changes.
    return () => {
      const currentAudioMap = audioMapRef.current;
      if (DEBUG_MODE)
        console.log(`Cleaning up ${currentAudioMap.size} audio elements.`);

      currentAudioMap.forEach((audio, id) => {
        audio.pause();
        // Remove event listeners to prevent memory leaks
        audio.oncanplaythrough = null;
        audio.onerror = null;
        // Release the resource
        audio.src = "";
      });

      currentAudioMap.clear();
      if (currentlyPlayingRef.current) {
        currentlyPlayingRef.current = null;
      }
    };
  }, [squares, order, DEBUG_MODE]); // Keyed on squares and order
  // --- AUDIO REFACTOR: End ---

  useEffect(() => {
    if (!socket) return;
    function handleChapterChanged(data: any) {
      if (data && typeof data.newChapter === "number") {
        if (onChapterChange) {
          onChapterChange(data.newChapter);
        }
      }
    }
    socket.on("chapter-changed", handleChapterChanged);
    return () => {
      socket.off("chapter-changed", handleChapterChanged);
    };
  }, [socket, onChapterChange]);

  const [localHoverSquareId, setLocalHoverSquareId] = useState<number | null>(
    null
  );
  const [localCurrentSquare, setLocalCurrentSquare] = useState<number | null>(
    null
  );
  const [currentPage, setCurrentPage] = useState(1);

  const [aiSelectionContext, setAiSelectionContext] =
    useState<AISelectionContext | null>(null);
  const [lastSelectedSource, setLastSelectedSource] = useState<
    "user" | "ai" | null
  >(null);

  useEffect(() => {
    setCurrentPage(1);
    setLocalCurrentSquare(null);
    setLocalHoverSquareId(null);
    setAiSelectionContext(null);
    setLastSelectedSource(null);
    setAnnotationState({
      tool: undefined,
      color: "#000000",
      lineWidth: 3,
      opacity: 1.0,
    });
    setSelectedButton(null);
    if (canvasRef.current && canvasRef.current.clearCanvas) {
      canvasRef.current.clearCanvas();
    }
  }, [squares, order, title]);

  const getSquareById = (id: number | null): Square | undefined => {
    if (id === null) return undefined;
    return squares.find((sq) => sq.id === id);
  };

  useEffect(() => {
    if (selectedSquareId !== null && selectedSquareId !== undefined) {
      if (lastSelectedSource !== "user") {
        const newAiContext: AISelectionContext = {
          squareId: selectedSquareId,
          source: "ai",
          timestamp: Date.now(),
        };

        setAiSelectionContext(newAiContext);
        setLastSelectedSource("ai");
        setTimeout(() => setAiSelectionContext(null), 8000);
      }
    } else {
      setAiSelectionContext(null);
    }
  }, [selectedSquareId, lastSelectedSource]);

  const maxSquares = 30;
  const totalPages = Math.ceil(squares.length / maxSquares);

  const displayedSquares = React.useMemo(() => {
    const startIndex = (currentPage - 1) * maxSquares;
    return squares.slice(startIndex, startIndex + maxSquares);
  }, [squares, currentPage, maxSquares]);

  const isFirstPage = currentPage === 1;
  const isLastPage = totalPages === 0 || currentPage === totalPages;
  const needsNavigation = totalPages > 1;

  const canGoToNextChapter =
    !restrictNavigation && onChapterChange && order < 21;
  const canGoToPrevChapter =
    !restrictNavigation && onChapterChange && order > 1;

  const shouldShowPageLevelNavigation =
    showNavigation &&
    (needsNavigation || canGoToNextChapter || canGoToPrevChapter);

  const effectiveCurrentSquareId = selectedSquareId ?? localCurrentSquare;
  const effectiveCurrentSquare = effectiveCurrentSquareId
    ? getSquareById(effectiveCurrentSquareId)
    : null;

  const getFontSizeClass = (
    content: string,
    context: "grid" | "fullscreen"
  ): string => {
    const letterCount = content.replace(/\s+/g, "").length;

    if (context === "grid") {
      if (letterCount >= 9) return "text-2xl";
      if (letterCount >= 5) return "text-3xl";
      return "text-4xl ";
    }

    if (letterCount >= 9) return "text-6xl";
    if (letterCount >= 5) return "text-7xl";
    return "text-8xl";
  };

  const getComplexityClass = (square: Square): string => {
    if (!square.metadata) return "";
    switch (square.metadata.complexity) {
      case "basic":
        return "complexity-basic";
      case "intermediate":
        return "complexity-intermediate";
      case "advanced":
        return "complexity-advanced";
      default:
        return "";
    }
  };

  // --- AUDIO REFACTOR: Updated click handler ---
  const handleCardAudio = (sq: Square) => {
    if (DEBUG_MODE) {
      console.log(
        `[ChapterPage] Card clicked. Square #${sq.id}: "${sq.content}"`
      );
    }
    if (onSquareHighlighted) onSquareHighlighted(sq.id);

    const audioEl = audioMapRef.current.get(sq.id);
    const status = audioStatuses.get(sq.id);

    if (!audioEl || status !== "loaded") {
      if (DEBUG_MODE)
        console.warn(
          `Audio for square ${sq.id} not ready. Status: ${status || "unknown"}`
        );
      return; // Guard against missing or not-yet-loaded audio
    }

    // Stop any other sound that might be playing
    if (
      currentlyPlayingRef.current &&
      currentlyPlayingRef.current !== audioEl
    ) {
      currentlyPlayingRef.current.pause();
      currentlyPlayingRef.current.currentTime = 0;
    }

    // Rewind and play the requested audio
    audioEl.currentTime = 0;
    const playPromise = audioEl.play();

    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          // Keep track of the currently playing audio instance
          currentlyPlayingRef.current = audioEl;
        })
        .catch((error) => {
          if (DEBUG_MODE) console.warn("Audio play failed:", error);
          currentlyPlayingRef.current = null;
        });
    }
  };

  const handleMaximizeClick = (sq: Square) => {
    if (DEBUG_MODE) {
      console.log(
        `[ChapterPage] Maximize icon clicked. Opening square #${sq.id}: "${sq.content}"`
      );
    }
    if (onSquareHighlighted) onSquareHighlighted(sq.id);
    if (onSquareSelected) onSquareSelected(sq.id);

    // --- AUDIO REFACTOR: Stop any playing audio when maximizing ---
    if (currentlyPlayingRef.current) {
      currentlyPlayingRef.current.pause();
      currentlyPlayingRef.current = null;
    }

    setLocalCurrentSquare(sq.id);
    setLastSelectedSource("user");
    setAiSelectionContext(null);

    socketRef.current?.emit("debug-log", {
      component: "ChapterPage",
      timestamp: new Date().toISOString(),
      message: `User fullscreen selection: ${sq.content}`,
    });
  };

  const navigateSquare = (direction: "previous" | "next") => {
    if (canvasRef.current && canvasRef.current.clearCanvas) {
      canvasRef.current.clearCanvas();
    }

    const currentId = effectiveCurrentSquareId;
    if (currentId === null) return;

    const currentIndex = squares.findIndex((sq) => sq.id === currentId);
    if (currentIndex === -1) return;

    const newIndex =
      direction === "previous"
        ? (currentIndex - 1 + squares.length) % squares.length
        : (currentIndex + 1) % squares.length;

    const newSquareToSelect = squares[newIndex];
    if (newSquareToSelect && onSquareSelected) {
      onSquareSelected(newSquareToSelect.id);
      setLastSelectedSource("user");
      setAiSelectionContext(null);
    }
  };

  const handleCloseFullscreen = () => {
    if (onSquareSelected) onSquareSelected(null);
    setLocalCurrentSquare(null);
    setAiSelectionContext(null);
    setLastSelectedSource(null);

    // --- AUDIO REFACTOR: Stop audio when closing fullscreen ---
    if (currentlyPlayingRef.current) {
      currentlyPlayingRef.current.pause();
      currentlyPlayingRef.current = null;
    }
  };

  const handleMouseEnter = (sqId: number) => {
    setLocalHoverSquareId(sqId);
    if (onSquareHover) {
      onSquareHover(sqId);
    }
  };

  const handleMouseLeave = () => {
    setLocalHoverSquareId(null);
    if (onSquareHover) {
      onSquareHover(null);
    }
  };

  const goToNextChapter = () => {
    if (restrictNavigation) {
      if (DEBUG_MODE)
        console.warn("[ChapterPage] Navigation to next chapter is restricted.");
      return;
    }
    if (order < 21 && onChapterChange) {
      onChapterChange(order + 1);
    }
  };

  const goToPreviousChapter = () => {
    if (restrictNavigation) {
      if (DEBUG_MODE)
        console.warn(
          "[ChapterPage] Navigation to previous chapter is restricted."
        );
      return;
    }
    if (order > 1 && onChapterChange) {
      onChapterChange(order - 1);
    }
  };

  const handleNextAction = () => {
    if (!isLastPage) {
      setCurrentPage(currentPage + 1);
    } else if (canGoToNextChapter) {
      goToNextChapter();
    }
  };

  const handlePreviousAction = () => {
    if (!isFirstPage) {
      setCurrentPage(currentPage - 1);
    } else if (canGoToPrevChapter) {
      goToPreviousChapter();
    }
  };

  const showNextFooterButton = !isLastPage || canGoToNextChapter;
  const showPreviousFooterButton = !isFirstPage || canGoToPrevChapter;

  const [annotationState, setAnnotationState] = useState<AnnotationState>({
    tool: undefined,
    color: "#000000",
    lineWidth: 3,
    opacity: 1.0,
  });
  const [selectedButton, setSelectedButton] = useState<string | null>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<any>(null);

  const handleToolSelection = (
    buttonId: string,
    newTool: "pen" | "eraser",
    newColor: string,
    newLineWidth: number,
    newOpacity?: number
  ) => {
    setAnnotationState({
      tool: newTool,
      color: newColor,
      lineWidth: newLineWidth,
      opacity: newOpacity || 1.0,
    });
    setSelectedButton(buttonId);
  };

  const handleColorChange = (newColor: string) => {
    setAnnotationState((prev) => ({ ...prev, color: newColor }));
  };

  const getAISelectionStyling = (squareId: number) => {
    if (!aiSelectionContext || aiSelectionContext.squareId !== squareId) {
      return { className: "", style: {} };
    }
    const confidence = aiSelectionContext.confidence || 1;
    let className = "";
    if (confidence >= 0.9) className = "smart-selection";
    else if (confidence >= 0.7) className = "fuzzy-match";
    else if (aiSelectionContext.alternatives?.length) className = "multi-match";
    else className = "ai-selected";
    return { className, style: {} };
  };

  const getMatchTypeIcon = (matchType?: string) => {
    switch (matchType) {
      case "exact_id":
      case "exact_content":
        return <Search className="w-3 h-3 text-green-500" />;
      case "keyword":
        return <Zap className="w-3 h-3 text-yellow-500" />;
      case "description":
      case "position":
        return <Info className="w-3 h-3 text-blue-500" />;
      default:
        return null;
    }
  };

  const renderTitle = () => {
    const parts = title.split(/(\d+)/g).filter(Boolean);

    return parts.map((part, index) => {
      if (/^\d+$/.test(part)) {
        return (
          <span key={index} className="proportional-nums">
            {part}
          </span>
        );
      }
      return part;
    });
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-white to-neutral-50 overflow-hidden">
      <style jsx>{arabicStyle}</style>

      <div className="flex justify-center w-full mb-8 pt-6">
        <div className="text-white bg-black text-center py-3 px-8 rounded-xl shadow-sm backdrop-blur-sm">
          <h1 className="text-2xl font-medium tracking-tight leading-tight">
            {renderTitle()}
          </h1>
        </div>
      </div>

      <div className="flex-1 flex flex-col min-h-0 relative">
        {!effectiveCurrentSquare ? (
          <div className="flex-1 min-h-0 fade-in">
            <div
              className="grid grid-cols-5 auto-rows-fr justify-items-stretch items-stretch p-2 w-full h-full gap-3"
              dir="rtl"
            >
              {displayedSquares.map((sq) => {
                const isHighlighted = highlightedSquareId === sq.id;
                const { className: aiClassName } = getAISelectionStyling(sq.id);
                const complexityClass = getComplexityClass(sq);
                const audioStatus = audioStatuses.get(sq.id);

                let cardIsHovered =
                  (onSquareHover ? hoveredSquareId : localHoverSquareId) ===
                  sq.id;
                if (isTeacher) cardIsHovered = localHoverSquareId === sq.id;

                return (
                  <div
                    key={sq.id}
                    className="relative group cursor-pointer"
                    onMouseEnter={() => handleMouseEnter(sq.id)}
                    onMouseLeave={handleMouseLeave}
                    onClick={() =>
                      audioStatus === "loaded" ? handleCardAudio(sq) : undefined
                    }
                  >
                    <div
                      className={`
                        rounded-xl p-4 text-center h-full flex flex-col justify-center relative
                        card-hover glassmorphism-card 
                        ${complexityClass} 
                        ${
                          isHighlighted || selectedSquareId === sq.id
                            ? "ring-2 ring-neutral-400 shadow-md"
                            : ""
                        }
                        ${aiClassName}
                        ${audioStatus === "loading" ? "opacity-70" : ""}
                      `}
                      style={{
                        transform: cardIsHovered
                          ? "scale(1.02) translateY(-2px)"
                          : "scale(1.0)",
                        boxShadow: cardIsHovered
                          ? "0 8px 24px rgba(0,0,0,0.12)"
                          : "0 2px 12px rgba(0,0,0,0.08)",
                        transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
                      }}
                    >
                      {/* Audio Loading Indicator */}
                      {audioStatus === "loading" && (
                        <div className="absolute top-2 left-2 z-20">
                          <Loader className="w-4 h-4 text-neutral-500 audio-loader-spinner" />
                        </div>
                      )}

                      <button
                        onClick={(event) => {
                          event.stopPropagation();
                          handleMaximizeClick(sq);
                        }}
                        className="opacity-0 group-hover:opacity-100 absolute top-2 right-2 p-1 rounded bg-white/70 hover:bg-white transition-opacity duration-200 z-20"
                        aria-label={`Maximize ${sq.content}`}
                      >
                        <Maximize2 className="w-5 h-5 text-gray-400" />
                      </button>

                      <div
                        className={`font-arabic text-neutral-900 arabic-text font-medium mb-2 flex items-center justify-center flex-1 ${getFontSizeClass(
                          sq.content,
                          "grid"
                        )}`}
                        dangerouslySetInnerHTML={{ __html: sq.content }}
                      />

                      {isHighlighted && (
                        <ChevronRight className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 animate-pulse" />
                      )}

                      {aiSelectionContext?.squareId === sq.id && (
                        <div className="absolute top-1 right-1 flex items-center space-x-1">
                          {getMatchTypeIcon(aiSelectionContext.matchType)}
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div
            className={`
              absolute inset-0 bg-neutral-50/90 backdrop-blur-md z-10 border border-neutral-200
              rounded-xl p-8 shadow-lg scale-in flex flex-col items-center justify-center overflow-visible
              ${
                effectiveCurrentSquareId &&
                aiSelectionContext?.squareId === effectiveCurrentSquareId
                  ? "ring-4 ring-blue-400 ring-opacity-50"
                  : ""
              }
            `}
          >
            {effectiveCurrentSquareId &&
              aiSelectionContext?.squareId === effectiveCurrentSquareId && (
                <div className="absolute px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-2">
                  {getMatchTypeIcon(aiSelectionContext.matchType)}
                </div>
              )}

            <button
              onClick={handleCloseFullscreen}
              className="absolute top-6 right-6 group p-2 rounded-full hover:bg-neutral-200/80 transition-all duration-300"
            >
              <X className="w-6 h-6 text-neutral-600 group-hover:text-neutral-900" />
            </button>
            <button
              onClick={() => navigateSquare("next")}
              className="absolute left-6 top-1/2 -translate-y-1/2 p-4 rounded-full bg-neutral-200/80 backdrop-blur-sm hover:bg-neutral-300/80 transition-all duration-300 shadow-sm hover:shadow z-20"
            >
              <ChevronLeft className="w-8 h-8 text-neutral-700" />
            </button>
            <button
              onClick={() => navigateSquare("previous")}
              className="absolute right-6 top-1/2 -translate-y-1/2 p-4 rounded-full bg-neutral-200/80 backdrop-blur-sm hover:bg-neutral-300/80 transition-all duration-300 shadow-sm hover:shadow z-20"
            >
              <ChevronRight className="w-8 h-8 text-neutral-700" />
            </button>
            <div
              ref={canvasContainerRef}
              className="relative w-[80%] h-[80%] bg-white border border-gray-200 rounded-lg shadow-md"
            >
              <div className="absolute inset-0 pointer-events-none flex items-center justify-center z-0">
                <div
                  className={`font-arabic antialiased text-neutral-900 arabic-text text-center font-light ${
                    effectiveCurrentSquare
                      ? getFontSizeClass(
                          effectiveCurrentSquare.content,
                          "fullscreen"
                        )
                      : "text-8xl"
                  }`}
                  dangerouslySetInnerHTML={{
                    __html: effectiveCurrentSquare
                      ? effectiveCurrentSquare.content
                      : "",
                  }}
                />
              </div>
              <div className="absolute inset-0 z-10">
                <Canvas
                  ref={canvasRef}
                  canDraw={allowDrawing}
                  color={annotationState.color}
                  lineWidth={annotationState.lineWidth}
                  tool={annotationState.tool ?? "pen"}
                  opacity={annotationState.opacity}
                />
              </div>
            </div>
            {allowDrawing && (
              <div className="absolute bottom-0 left-0 w-full flex justify-center pb-4 pt-2 bg-transparent z-20 pointer-events-auto">
                <Toolbar
                  selectedButton={selectedButton}
                  setSelectedButton={setSelectedButton}
                  externalColor={annotationState.color}
                  externalLineWidth={annotationState.lineWidth}
                  externalTool={annotationState.tool}
                  onSelectMarker={handleToolSelection}
                  onColorChange={handleColorChange}
                  containerRef={canvasContainerRef}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {!effectiveCurrentSquare && shouldShowPageLevelNavigation && (
        <div className="border-t border-neutral-200 bg-neutral-50/80 backdrop-blur-sm mt-auto">
          <div className="px-6 py-4 flex items-center relative">
            <div className="flex-1 flex justify-start">
              {showNextFooterButton && (
                <button
                  className="flex items-center gap-2 px-4 py-2 text-base text-neutral-600 hover:text-neutral-900 hover:bg-neutral-200/80 rounded-full transition-all duration-300 group"
                  onClick={handleNextAction}
                >
                  <ChevronLeft className="w-4 h-4 group-hover:transform group-hover:-translate-x-1 transition-transform" />
                  <span>
                    {isLastPage && canGoToNextChapter ? "Next Chapter" : "Next"}
                  </span>
                </button>
              )}
            </div>
            <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center">
              {totalPages > 1 && (
                <div className="text-base text-neutral-500">
                  Page {currentPage} of {totalPages}
                </div>
              )}
            </div>
            <div className="flex-1 flex justify-end">
              {showPreviousFooterButton && (
                <button
                  className="flex items-center gap-2 px-4 py-2 text-base text-neutral-600 hover:text-neutral-900 hover:bg-neutral-200/80 rounded-full transition-all duration-300 group"
                  onClick={handlePreviousAction}
                >
                  <span>
                    {isFirstPage && canGoToPrevChapter
                      ? "Previous Chapter"
                      : "Previous"}
                  </span>
                  <ChevronRight className="w-4 h-4 group-hover:transform group-hover:translate-x-1 transition-transform" />
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
