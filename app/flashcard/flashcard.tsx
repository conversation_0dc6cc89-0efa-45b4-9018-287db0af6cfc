// app/flashcard/flashcard.tsx
"use client";

import { useState } from "react";
import { RotateCcw } from "lucide-react";

export default function Flashcard() {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <div className="w-[32rem] mx-auto rounded-2xl shadow-xl overflow-hidden bg-white">
      {/* Card container */}
      <div className="h-80" style={{ perspective: "1000px" }}>
        <div
          className="relative w-full h-full text-center transition-transform duration-700"
          style={{
            transformStyle: "preserve-3d",
            transform: isFlipped ? "rotateY(180deg)" : "rotateY(0deg)",
          }}
        >
          {/* Front of the Card */}

          <div
            className="absolute w-full h-full bg-white rounded-t-2xl flex flex-col justify-center items-center p-8"
            style={{ backfaceVisibility: "hidden" }}
          >
            <button
              onClick={handleFlip}
              className="absolute top-6 right-6 z-10 p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors duration-200 border border-gray-200"
              aria-label="Flip card"
            >
              <RotateCcw className="w-5 h-5 text-gray-600 hover:text-black transition-colors duration-200" />
            </button>
            <div className="flex flex-col items-center space-y-4">
              <h2 className="text-3xl font-semibold text-black tracking-tight">
                Front of Card
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed max-w-xs text-center">
                This is the question side.
              </p>
            </div>
          </div>

          {/* Back of the Card */}

          <div
            className="absolute w-full h-full bg-black rounded-t-2xl flex flex-col justify-center items-center p-8"
            style={{
              backfaceVisibility: "hidden",
              transform: "rotateY(180deg)",
            }}
          >
            <button
              onClick={handleFlip}
              className="absolute top-6 right-6 z-10 p-2 rounded-full bg-gray-900 hover:bg-gray-800 transition-colors duration-200 border border-gray-700"
              aria-label="Flip card"
            >
              <RotateCcw className="w-5 h-5 text-gray-400 hover:text-white transition-colors duration-200" />
            </button>
            <div className="flex flex-col items-center space-y-4">
              <h2 className="text-3xl font-semibold text-white tracking-tight">
                Back of Card
              </h2>
              <p className="text-lg text-gray-300 leading-relaxed max-w-xs text-center">
                This is the answer side.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Answer options - below the card */}

      <div className="w-full h-12 bg-gray-50 border-t border-gray-200 flex">
        <div className="flex-1 flex items-center justify-center"></div>
        <div className="w-px bg-gray-200"></div>
        <div className="flex-1 flex items-center justify-center"></div>
        <div className="w-px bg-gray-200"></div>
        <div className="flex-1 flex items-center justify-center"></div>
      </div>
    </div>
  );
}
