// app/flashcard/page.tsx

import Flashcard from "./flashcard"; // Import the flashcard component

export default async function FlashcardPage() {
  // Simulate a network delay to demonstrate the loading.tsx component
  await new Promise((resolve) => setTimeout(resolve, 2000));

  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-24">
      <Flashcard />
    </main>
  );
}
