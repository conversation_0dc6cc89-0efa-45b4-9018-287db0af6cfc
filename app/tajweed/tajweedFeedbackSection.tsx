// TajweedFeedbackSection.tsx
import React, { useState, useEffect } from "react";
import { Play, Pause, Info, Trophy, CheckCircle } from "lucide-react";
import Image from "next/image";

// NEW: Interface for Violation, consistent with Tajweed.tsx
interface Violation {
  rule: string;
  message: string;
  severity: "critical" | "moderate" | "mild" | "stylistic";
  word?: string;
  colorClass?: string;
  bgColorClass?: string;
  borderColorClass?: string;
  comparisonData?: {
    userValue: string;
    correctValue: string;
  };
}

// NEW: Expanded props interface to handle both lesson and assessment data
interface TajweedFeedbackSectionProps {
  // Lesson-specific props
  lessonTitle?: string;
  content?: string;
  examples?: {
    letter: string;
    transliteration: string;
    example: string;
  }[];
  practiceNotes?: string;
  audioUrl?: string;
  imageUrl?: string;
  currentSlide?: number;
  totalSlides?: number;
  onNextSlide?: () => void;
  onPrevSlide?: () => void;

  // Assessment-specific props (all optional)
  isLoading?: boolean;
  hasBeenAssessed?: boolean;
  isSuccessful?: boolean;
  score?: number;
  violations?: Violation[];
  feedbackHTML?: string;
}

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "critical":
      return {
        dot: "bg-red-500",
        text: "text-red-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    case "moderate":
      return {
        dot: "bg-amber-500",
        text: "text-amber-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    case "mild":
      return {
        dot: "bg-yellow-500",
        text: "text-yellow-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    case "stylistic":
      return {
        dot: "bg-purple-400",
        text: "text-purple-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
    default:
      return {
        dot: "bg-gray-400",
        text: "text-gray-600",
        bg: "bg-white",
        border: "border-gray-200",
      };
  }
};

// Helper function to parse markdown text with better handling
const parseMarkdownText = (text: string) => {
  // Regular expression to find all instances of **text** including Arabic text
  const boldRegex = /\*\*(.+?)\*\*/g;

  // Find all bold sections
  const boldMatches: Array<{ start: number; end: number; text: string }> = [];
  let match;
  while ((match = boldRegex.exec(text)) !== null) {
    boldMatches.push({
      start: match.index,
      end: match.index + match[0].length,
      text: match[1],
    });
  }

  // If no bold matches, return the text as is
  if (boldMatches.length === 0) {
    return <span>{text}</span>;
  }

  // Build the components array with bold and regular text
  const components: React.ReactNode[] = [];
  let currentIndex = 0;

  boldMatches.forEach((boldMatch, index) => {
    // Add text before the bold section
    if (boldMatch.start > currentIndex) {
      components.push(
        <span key={`text-${index}`}>
          {text.slice(currentIndex, boldMatch.start)}
        </span>
      );
    }

    // Add the bold text
    components.push(
      <strong key={`bold-${index}`} className="font-semibold text-black">
        {boldMatch.text}
      </strong>
    );

    currentIndex = boldMatch.end;
  });

  // Add any remaining text after the last bold section
  if (currentIndex < text.length) {
    components.push(<span key="text-end">{text.slice(currentIndex)}</span>);
  }

  return <>{components}</>;
};

// Modified content preprocessing - clean up duplicate citation headers first
const preprocessContent = (text: string): string => {
  // Split the text into paragraphs
  const paragraphs = text.split("\n\n");

  // Filter out any duplicate "Citation Notes:" lines or any non-numeric citation headers
  const filteredParagraphs = paragraphs.filter((paragraph) => {
    const trimmedParagraph = paragraph.trim();

    // Keep numeric citations (if they are to be rendered as regular text or part of other lists)
    if (/^\d+\.\s/.test(trimmedParagraph)) {
      return true;
    }

    // Keep all other paragraphs
    return true;
  });

  // Join the filtered paragraphs back into a string
  return filteredParagraphs.join("\n\n");
};

// Enhanced content renderer that groups related content together
const renderLessonContentStructure = (text: string) => {
  // Preprocess the content to clean up citation issues
  const cleanedText = preprocessContent(text);

  // Split the text by paragraphs
  const paragraphs = cleanedText.split("\n\n");

  // Group content into logical sections
  const contentSections: React.ReactNode[] = [];
  let currentSectionContent: string[] = [];
  let sectionType: string | null = null;

  // Process paragraphs and group them by logical sections
  for (let i = 0; i < paragraphs.length; i++) {
    const paragraph = paragraphs[i];
    const trimmedParagraph = paragraph.trim();

    // Determine section type
    if (trimmedParagraph === "Look out for:") {
      // If we have collected content for a previous section, render it
      if (currentSectionContent.length > 0) {
        contentSections.push(
          renderContentSection(
            currentSectionContent,
            sectionType,
            contentSections.length
          )
        );
        currentSectionContent = [];
      }

      // Start a new "Look out for" section
      sectionType = "lookout";
      currentSectionContent.push(trimmedParagraph);
    }
    // Handle bullet points - they should be grouped with their header
    else if (trimmedParagraph.includes("•")) {
      // If this is the first bullet point and not already in a lookout section
      if (sectionType !== "lookout" && sectionType !== "bullets") {
        // If we have collected content for a previous section, render it
        if (currentSectionContent.length > 0) {
          contentSections.push(
            renderContentSection(
              currentSectionContent,
              sectionType,
              contentSections.length
            )
          );
          currentSectionContent = [];
        }
        sectionType = "bullets";
      }

      // Add this bullet list to the current section
      currentSectionContent.push(trimmedParagraph);
    }
    // Regular paragraph - if it's clearly a new topic, start a new section
    else {
      // Check if this paragraph should start a new section or continue the current one
      if (
        sectionType === null ||
        (sectionType !== "regular" &&
          sectionType !== "lookout" &&
          sectionType !== "bullets")
      ) {
        // If we have collected content for a previous section, render it
        if (currentSectionContent.length > 0) {
          contentSections.push(
            renderContentSection(
              currentSectionContent,
              sectionType,
              contentSections.length
            )
          );
          currentSectionContent = [];
        }
        sectionType = "regular";
      }

      // Add this paragraph to the current section
      currentSectionContent.push(trimmedParagraph);
    }
  }

  // Don't forget to add the last section
  if (currentSectionContent.length > 0) {
    contentSections.push(
      renderContentSection(
        currentSectionContent,
        sectionType,
        contentSections.length
      )
    );
  }

  return contentSections;
};

// Helper function to render a specific type of content section
const renderContentSection = (
  content: string[],
  sectionType: string | null,
  sectionIndex: number
) => {
  switch (sectionType) {
    case "lookout":
      return renderLookoutSection(content, sectionIndex);
    case "bullets":
      return renderBulletListSection(content, sectionIndex);
    case "regular":
    default:
      return renderRegularSection(content, sectionIndex);
  }
};

// Render a lookout section with its bullet points in a single card
const renderLookoutSection = (content: string[], sectionIndex: number) => {
  // Find the header and the bullet points
  const header = content[0]; // "Look out for:"
  const bulletContent = content.slice(1).join("\n\n");

  return (
    <div
      key={`lookout-${sectionIndex}`}
      className="bg-white rounded-2xl p-5 shadow-sm mb-6 transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
    >
      <h3 className="text-lg font-semibold text-gray-900 tracking-tight mb-4">
        {header}
      </h3>

      {bulletContent && (
        <div className="space-y-3">
          {bulletContent
            .split("\n")
            .filter((item) => item.trim().startsWith("•"))
            .map((item, i) => {
              const bulletText = item.substring(item.indexOf("•") + 1).trim();
              return (
                <div key={i} className="flex items-start">
                  <span className="w-2 h-2 rounded-full bg-black mr-3 mt-2.5 flex-shrink-0" />
                  <span className="text-gray-700 text-[16px] leading-relaxed">
                    {parseMarkdownText(bulletText)}
                  </span>
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};

// Render a bullet list section in a single card
const renderBulletListSection = (content: string[], sectionIndex: number) => {
  return (
    <div
      key={`bullets-${sectionIndex}`}
      className="bg-white rounded-2xl p-5 shadow-sm mb-6 transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
    >
      <ul className="list-none space-y-3">
        {content
          .join("\n\n")
          .split("\n")
          .filter((item) => item.trim().startsWith("•"))
          .map((item, i) => {
            const bulletText = item.substring(item.indexOf("•") + 1).trim();
            return (
              <li key={i} className="flex items-start">
                <span className="w-2 h-2 rounded-full bg-black mr-3 mt-2.5 flex-shrink-0" />
                <span className="text-gray-700 text-[16px] leading-relaxed">
                  {parseMarkdownText(bulletText)}
                </span>
              </li>
            );
          })}
      </ul>
    </div>
  );
};

// Render a regular text section
const renderRegularSection = (content: string[], sectionIndex: number) => {
  return (
    <div
      key={`regular-${sectionIndex}`}
      className="bg-white rounded-2xl p-5 shadow-sm mb-6 transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
    >
      {content.map((paragraph, pIndex) => (
        <p
          key={pIndex}
          className={`text-gray-700 text-[16px] leading-relaxed ${
            pIndex > 0 ? "mt-4" : ""
          }`}
        >
          {parseMarkdownText(paragraph)}
        </p>
      ))}
    </div>
  );
};

const Confetti = () => {
  return (
    <div className="confetti-container">
      {Array.from({ length: 150 }).map((_, i) => (
        <div
          key={i}
          className={`confetti confetti-${i % 5}`}
          style={{
            left: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 5}s`,
            animationDuration: `${3 + Math.random() * 4}s`,
          }}
        />
      ))}
    </div>
  );
};

// Success celebration component used for both lesson and assessment success
const SuccessCelebration = ({ forLesson = false }: { forLesson?: boolean }) => {
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    if (!forLesson) {
      const timer = setInterval(() => {
        setCountdown((prev) => Math.max(0, prev - 1));
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [forLesson]);

  useEffect(() => {
    try {
      const successSound = new Audio("/sounds/success.mp3");
      successSound.volume = 0.5;
      successSound.play();
    } catch (error) {
      console.log("Success sound could not be played:", error);
    }
  }, []);

  return (
    <div className="success-celebration">
      <Confetti />

      <div className="mb-8 relative">
        <div className="success-badge bg-green-50 rounded-full p-6 mx-auto w-32 h-32 flex items-center justify-center">
          <div className="success-pulse"></div>
          <Trophy size={64} className="text-green-600 success-icon" />
        </div>
      </div>

      <h2 className="text-3xl font-bold text-center text-green-700 mb-4 success-text-animation">
        Excellent {forLesson ? "Lesson Complete!" : "Job!"}
      </h2>

      <p className="text-xl text-center text-gray-700 mb-6 success-text-animation animation-delay-100">
        {forLesson
          ? "You're ready for the examples!"
          : "Your pronunciation was perfect!"}
      </p>

      {!forLesson && (
        <>
          <div className="flex flex-col items-center">
            <div className="text-sm text-gray-500 mb-2 success-text-animation animation-delay-200">
              Moving to next example in
            </div>

            <div className="countdown-container success-text-animation animation-delay-300">
              <div className="bg-gray-100 rounded-full h-10 w-10 flex items-center justify-center font-semibold text-xl">
                {countdown}
              </div>
            </div>
          </div>

          <div className="mt-8 max-w-md mx-auto p-4 bg-yellow-50 border border-yellow-200 rounded-lg success-text-animation animation-delay-400">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Continue practicing to master this tajweed rule. You&apos;ll
                  automatically advance to the next example.
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

const TajweedFeedbackSection: React.FC<TajweedFeedbackSectionProps> = ({
  // Lesson props
  lessonTitle,
  content,
  examples,
  practiceNotes,
  audioUrl,
  imageUrl,
  currentSlide,
  totalSlides,
  onNextSlide,
  onPrevSlide,
  // Assessment props
  isLoading = false,
  hasBeenAssessed = false,
  isSuccessful = false,
  score = 0,
  violations = [],
  feedbackHTML = "",
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [audioInstance, setAudioInstance] = useState<HTMLAudioElement | null>(
    null
  );

  const handlePlayPause = () => {
    if (!audioUrl) return;

    if (!audioInstance) {
      const audio = new Audio(audioUrl);
      setAudioInstance(audio);

      audio.addEventListener("timeupdate", () => {
        const percentage = (audio.currentTime / audio.duration) * 100;
        setProgress(percentage);
      });

      audio.addEventListener("ended", () => {
        setIsPlaying(false);
        setProgress(0);
      });

      audio.play();
      setIsPlaying(true);
    } else {
      if (isPlaying) {
        audioInstance.pause();
      } else {
        audioInstance.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const formatTime = (seconds: number) => {
    if (isNaN(seconds)) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };

  const renderAssessmentContent = () => {
    return (
      <div className="flex-1">
        <div className="h-full flex flex-col space-y-4">
          {isSuccessful ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center success-badge p-10 bg-green-50 rounded-xl shadow-sm">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4 success-animation" />
                <h3 className="text-xl font-semibold text-green-700 mb-2">
                  Excellent Pronunciation!
                </h3>
                <p className="text-green-600 text-lg">
                  Success! Moving to next example soon...
                </p>
              </div>
            </div>
          ) : (
            <>
              <div className="mb-6 p-5">
                <div className="space-y-4">
                  <div className="flex items-center justify-center my-4">
                    <div className="text-center">
                      <div className="text-5xl font-bold text-gray-900">
                        {Math.round(score)}%
                      </div>
                      <p className="mt-3 text-gray-700 max-w-md mx-auto text-center">
                        {score > 90
                          ? "Excellent pronunciation! Your tajweed is well-practiced and very accurate. Keep up the great work!"
                          : score > 75
                          ? "Good pronunciation with room for improvement. Review the feedback below to refine your tajweed skills."
                          : "Continue practicing to improve your pronunciation. Focus on the areas highlighted in the feedback below."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {feedbackHTML ? (
                <div
                  className="feedback-container"
                  dangerouslySetInnerHTML={{ __html: feedbackHTML }}
                />
              ) : violations && violations.length > 0 ? (
                <div className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2.5 h-2.5 bg-amber-500 rounded-full" />
                      <h3 className="text-lg font-semibold text-gray-900 tracking-tight">
                        Tajweed Violations:
                      </h3>
                    </div>
                    <div className="space-y-5">
                      {(() => {
                        const violationsByRule: { [key: string]: Violation[] } =
                          {};
                        violations.forEach((v) => {
                          if (!violationsByRule[v.rule])
                            violationsByRule[v.rule] = [];
                          violationsByRule[v.rule].push(v);
                        });
                        return Object.entries(violationsByRule).map(
                          ([rule, ruleViolations]) => (
                            <div
                              key={rule}
                              className="bg-gray-50 rounded-xl p-4"
                            >
                              <div className="flex items-center space-x-2 mb-3">
                                <h4 className="text-base font-medium text-gray-800">
                                  {rule.replace(/-/g, " ").toUpperCase()} (
                                  {ruleViolations.length})
                                </h4>
                              </div>
                              <div className="space-y-4">
                                {ruleViolations.map((v, i) => {
                                  const colors = getSeverityColor(v.severity);
                                  return (
                                    <div
                                      key={i}
                                      className={`${
                                        v.bgColorClass || colors.bg
                                      } p-4 rounded-lg border ${
                                        v.borderColorClass || colors.border
                                      } shadow-sm`}
                                    >
                                      <div className="flex items-baseline space-x-2">
                                        <span className="text-gray-700">
                                          {v.message}
                                        </span>
                                      </div>
                                      {v.comparisonData && (
                                        <div className="flex items-center justify-center space-x-6 my-4">
                                          <div className="flex flex-col items-center">
                                            <span className="text-xs font-medium text-gray-500 mb-1.5">
                                              You said
                                            </span>
                                            <span className="bg-red-50 text-red-600 text-lg px-3 py-1.5 font-normal rounded-lg">
                                              {v.comparisonData.userValue}
                                            </span>
                                          </div>
                                          <div className="text-gray-300">→</div>
                                          <div className="flex flex-col items-center">
                                            <span className="text-xs font-medium text-gray-500 mb-1.5">
                                              Correct
                                            </span>
                                            <span className="bg-green-50 text-green-600 text-lg px-3 py-1.5 font-normal rounded-lg">
                                              {v.comparisonData.correctValue}
                                            </span>
                                          </div>
                                        </div>
                                      )}
                                      <div className="mt-2 text-xs text-gray-500 flex items-center">
                                        <span
                                          className={`${colors.text} font-medium mr-1`}
                                        >
                                          {v.severity.charAt(0).toUpperCase() +
                                            v.severity.slice(1)}
                                        </span>
                                        {v.word && ` | Word: ${v.word}`}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )
                        );
                      })()}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2.5 h-2.5 bg-green-500 rounded-full" />
                      <h3 className="text-lg font-semibold text-gray-900 tracking-tight">
                        Perfect Tajwīd
                      </h3>
                    </div>
                    <div className="bg-green-50 rounded-xl p-4 flex items-center">
                      <CheckCircle className="h-8 w-8 text-green-500 mr-3 flex-shrink-0" />
                      <p className="text-base text-gray-800">
                        No specific tajweed violations detected. Continue
                        practicing to maintain your excellent pronunciation!
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  };

  const renderLessonContent = () => {
    // This function now specifically handles rendering when NOT in assessment mode.
    if (!content) return null;
    return (
      <>
        <div className="prose prose-lg max-w-none">
          {renderLessonContentStructure(content)}
        </div>

        {examples && examples.length > 0 && (
          <div className="mt-8 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md mb-6">
            <h3 className="text-lg font-semibold text-gray-900 tracking-tight mb-6">
              The 4 Letters
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {examples.map((ex, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl p-5 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:translate-y-[-2px]"
                >
                  <div className="flex items-center">
                    <div className="bg-black rounded-xl w-14 h-14 flex items-center justify-center shadow-sm">
                      <span
                        className="text-white text-3xl font-medium"
                        style={{ fontFamily: "Noto Sans Arabic, sans-serif" }}
                      >
                        {ex.letter}
                      </span>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {ex.transliteration}
                      </div>
                      <div
                        className="text-sm text-gray-600 mt-1"
                        style={{ fontFamily: "Noto Sans Arabic, sans-serif" }}
                      >
                        {ex.example}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {imageUrl && (
          <div className="mt-8 mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md flex justify-center">
            <Image
              src={imageUrl}
              alt={`Illustration for ${lessonTitle || "Tajweed Lesson"}`}
              width={800}
              height={450}
              className="rounded-xl max-w-full h-auto object-contain max-h-72"
            />
          </div>
        )}

        {audioUrl && (
          <div className="mt-8 mb-6 bg-white rounded-2xl p-6 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 tracking-tight mb-4">
              Listen to pronunciation
            </h3>
            <div className="flex items-center space-x-4">
              <button
                className={`w-12 h-12 ${
                  isPlaying ? "bg-rose-600" : "bg-black"
                } rounded-full flex items-center justify-center hover:${
                  isPlaying ? "bg-rose-700" : "bg-gray-800"
                } transition-colors shadow-sm`}
                onClick={handlePlayPause}
              >
                {isPlaying ? (
                  <Pause size={20} className="text-white" />
                ) : (
                  <Play size={20} className="text-white ml-0.5" />
                )}
              </button>
              <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-black rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-500 min-w-[40px]">
                {audioInstance ? formatTime(audioInstance.currentTime) : "0:00"}
              </span>
            </div>
          </div>
        )}

        {practiceNotes && (
          <div className="mt-8 mb-6 bg-white rounded-2xl p-6 border-l-4 border-l-gray-500 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
            <div className="flex">
              <Info size={20} className="text-gray-600 flex-shrink-0 mt-0.5" />
              <div className="ml-4">
                <h3 className="text-base font-medium text-gray-900 mb-2">
                  Practice Tip
                </h3>
                <p className="text-[15px] text-amber-800 leading-relaxed">
                  {practiceNotes}
                </p>
              </div>
            </div>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      <div className="flex-1 px-7 py-7 overflow-y-auto">
        {isLoading ? (
          <div className="flex-1 flex items-center justify-center flex-col h-full">
            <div className="loader"></div>
            <p className="text-gray-600 mt-6">Analyzing your recitation...</p>
          </div>
        ) : hasBeenAssessed ? (
          renderAssessmentContent()
        ) : (
          renderLessonContent()
        )}
      </div>

      <style jsx global>{`
        /* ... Your existing styles for confetti, success, etc. ... */
        @keyframes confetti-fall {
          0% {
            transform: translateY(-100vh) rotate(0deg);
          }
          100% {
            transform: translateY(100vh) rotate(720deg);
          }
        }
        .confetti-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
          pointer-events: none;
          z-index: 10;
        }
        .confetti {
          position: absolute;
          width: 10px;
          height: 10px;
          opacity: 0.7;
          animation: confetti-fall linear forwards;
        }
        .confetti-0 {
          background-color: #f44336;
          clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }
        .confetti-1 {
          background-color: #2196f3;
          clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        }
        .confetti-2 {
          background-color: #ffeb3b;
          clip-path: circle(50% at 50% 50%);
        }
        .confetti-3 {
          background-color: #4caf50;
          clip-path: polygon(50% 0%, 100% 100%, 0% 100%);
        }
        .confetti-4 {
          background-color: #9c27b0;
          width: 8px;
          height: 8px;
        }
        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 0.5;
          }
          50% {
            transform: scale(1.5);
            opacity: 0;
          }
          100% {
            transform: scale(1);
            opacity: 0.5;
          }
        }
        .success-badge {
          position: relative;
          animation: success-bounce 1s ease-in-out;
        }
        @keyframes success-bounce {
          0% {
            transform: scale(0.5);
            opacity: 0;
          }
          50% {
            transform: scale(1.1);
          }
          70% {
            transform: scale(0.95);
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }
        .success-pulse {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 50%;
          background-color: rgba(74, 222, 128, 0.4);
          animation: pulse 2s infinite;
        }
        .success-icon {
          animation: success-icon-animation 1s ease-in-out;
          position: relative;
          z-index: 1;
        }
        @keyframes success-icon-animation {
          0% {
            transform: scale(0.5);
            opacity: 0;
          }
          50% {
            transform: scale(1.2);
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }
        .success-text-animation {
          opacity: 0;
          transform: translateY(20px);
          animation: fade-in 0.5s ease-out forwards;
        }
        @keyframes fade-in {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animation-delay-100 {
          animation-delay: 0.1s;
        }
        .animation-delay-200 {
          animation-delay: 0.2s;
        }
        .animation-delay-300 {
          animation-delay: 0.3s;
        }
        .animation-delay-400 {
          animation-delay: 0.4s;
        }
        .countdown-container {
          position: relative;
        }
        .countdown-container::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 50%;
          border: 2px solid #10b981;
          animation: countdown-circle 3s linear forwards;
          clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%, 50% 0%);
          transform: rotate(270deg);
          transform-origin: center;
        }
        @keyframes countdown-circle {
          0% {
            clip-path: polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%, 50% 0%);
          }
          100% {
            clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%, 50% 0%);
          }
        }
        .success-celebration {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 300px;
          padding: 2rem;
          position: relative;
        }
      `}</style>
    </div>
  );
};

export default TajweedFeedbackSection;
