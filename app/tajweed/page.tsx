// app/tajweed/page.tsx

export const dynamic = "force-dynamic"; // Force re-fetch on route change

import React from "react";
import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import Tajweed from "./tajweed";
import { getUserProgress, getUserSubscription } from "@/db/queries";
import { tajweedExamples } from "./tajweedExamples";

// Define TajweedExample and TajweedRule interfaces to match tajweedExamples.ts
interface TajweedExample {
  id?: number; // Added ID for ordering
  surah: number;
  verse: number;
  exampleText: string;
  explanation: string;
  keyWords: string[];
}

interface TajweedRule {
  description: string;
  arabicName: string;
  examples: TajweedExample[];
  practiceNotes?: string;
}

// Define the RuleContext interface to match what's expected in tajweed.tsx
interface RuleContext {
  ruleId: string;
  ruleName: string;
  ruleArabicName: string;
  keyWords: string[];
  practiceNotes: string;
  reason: string; // Added missing property
  difficultyLevel: string; // Added missing property
  examples?: TajweedExample[]; // Add examples array to preserve order
}

// Type for verse data used internally
type Verse = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

// Helper: Remove Basmala from verse 1 text if present.
function removeBasmala(verseText: string): string {
  const basmalaExact = "بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ";
  const basmalaWithSpace = basmalaExact + " ";

  if (verseText.startsWith(basmalaWithSpace)) {
    return verseText.slice(basmalaWithSpace.length).trimStart();
  } else if (verseText.startsWith(basmalaExact)) {
    return verseText.slice(basmalaExact.length).trimStart();
  }
  return verseText;
}

type TajweedPageProps = {
  searchParams: {
    surahNumber?: string;
    versesRange?: string;
    mode?: "startFresh" | "continue" | "example";
    surahName?: string;
    // #TODO: Add tajweed-specific search params
    ruleCategory?: string;
    ruleId?: string;
    ruleName?: string;
    ruleArabicName?: string;
  };
};

const DYNAMIC_FETCH_THRESHOLD = 50;

const TajweedPage = async ({ searchParams }: TajweedPageProps) => {
  // --- Authentication and Param Parsing ---
  const { userId } = await auth();
  console.log("User ID from auth:", userId);
  if (!userId) redirect("/");

  // Parse and log all search parameters
  const surahNumberStr = searchParams.surahNumber;
  const initialVersesRange = searchParams.versesRange || "";
  const mode = searchParams.mode || "startFresh";
  const surahNameFallback = searchParams.surahName || "";
  // Parse tajweed-specific params
  const ruleCategory = searchParams.ruleCategory || "noon";
  const ruleId = searchParams.ruleId || "";
  const ruleName = searchParams.ruleName || "";
  const ruleArabicName = searchParams.ruleArabicName || "";

  console.log("Query Params:", {
    surahNumberStr,
    initialVersesRange,
    mode,
    surahNameFallback,
    ruleCategory,
    ruleId,
    ruleName,
    ruleArabicName,
  });

  // Always use Surah 1 if no surahNumber provided or if it's invalid
  const surahNumber = surahNumberStr ? parseInt(surahNumberStr, 10) : 1;
  console.log(
    "Parsed surahNumber:",
    surahNumber,
    surahNumberStr ? "(from URL)" : "(default)"
  );
  if (isNaN(surahNumber)) {
    console.log("Invalid surahNumber, defaulting to 1");
    // Instead of redirecting, we'll just use surahNumber = 1
  }

  // --- Parse Verses Range ---
  let startVerse = 1;
  let endVerse = Number.MAX_SAFE_INTEGER; // We'll cap this with totalNumberOfAyahs later

  if (initialVersesRange) {
    console.log(`Parsing verses range: ${initialVersesRange}`);
    const rangeParts = initialVersesRange.split("-");
    if (rangeParts.length === 2) {
      startVerse = parseInt(rangeParts[0], 10) || 1;
      endVerse = parseInt(rangeParts[1], 10) || Number.MAX_SAFE_INTEGER;
    } else if (rangeParts.length === 1 && rangeParts[0]) {
      startVerse = parseInt(rangeParts[0], 10) || 1;
      endVerse = startVerse;
    }
    console.log(`Parsed verse range: ${startVerse} to ${endVerse}`);
  }

  // --- Check if we have curated examples for this rule ---
  const hasOptimizedExamples = ruleId && ruleId in tajweedExamples;
  if (hasOptimizedExamples) {
    console.log(`Found optimized examples for rule: ${ruleId}`);
  }

  // --- Fetch Metadata and Verses Data ---
  let surahMetadata: any = null;
  let totalNumberOfAyahs = 0;
  let versesData: Verse[] = [];

  // ENHANCED: Clear conditional handling for example mode
  if (mode === "example") {
    console.log("EXAMPLE MODE DETECTED - Bypassing API fetches");

    // Check if we have examples for this rule
    if (ruleId && tajweedExamples[ruleId as keyof typeof tajweedExamples]) {
      const ruleData = tajweedExamples[ruleId as keyof typeof tajweedExamples];

      // Set totalNumberOfAyahs to actual example count
      totalNumberOfAyahs = ruleData.examples.length;

      // Create appropriate surahMetadata that indicates examples are being shown
      surahMetadata = {
        name: `Examples: ${ruleName || ruleId}`,
        englishName: `Examples: ${ruleName || ruleId}`,
        englishNameTranslation: ruleData.description || "Tajweed Examples",
        revelationType: "Example",
        numberOfAyahs: totalNumberOfAyahs,
      };

      // CRITICAL CHANGE: Sort examples by ID and map to versesData
      versesData = [...ruleData.examples]
        .sort((a, b) => {
          // If the examples have IDs, use those for sorting
          if (a.id !== undefined && b.id !== undefined) {
            return a.id - b.id;
          }
          // Fallback to their original array order (using index)
          return 0;
        })
        .map((ex, index) => ({
          surahNumber: ex.surah || 0,
          // Use ID as verseNumber if available, otherwise use index+1
          verseNumber: ex.id !== undefined ? ex.id : index + 1,
          text: ex.exampleText || "",
        }));

      console.log(
        "Examples mapped to versesData in ID order:",
        versesData.map((v) => `ID ${v.verseNumber}: "${v.text}"`)
      );

      startVerse = 1;
      endVerse = totalNumberOfAyahs;
      console.log(`Using ${totalNumberOfAyahs} examples for rule: ${ruleId}`);
    } else {
      console.warn(
        `Example mode requested but no examples found for rule: ${ruleId}`
      );
      // Fallback to minimal data if no examples found
      totalNumberOfAyahs = 0;
      surahMetadata = {
        name: "No Examples Available",
        englishName: "No Examples Available",
        englishNameTranslation: "Please select another rule",
        revelationType: "None",
        numberOfAyahs: 0,
      };
      versesData = [];
    }
  } else {
    // Regular mode - fetch from API as before
    console.log("REGULAR MODE - Fetching from API");

    // --- Fetch Metadata from API ---
    try {
      const metaResponse = await fetch(
        `https://api.alquran.cloud/v1/surah/${surahNumber}`
      );
      console.log(
        `AlQuranCloud Metadata response status: ${metaResponse.status}`
      );
      if (!metaResponse.ok)
        throw new Error(`Meta fetch failed: ${metaResponse.status}`);
      const metaJson = await metaResponse.json();
      if (!metaJson?.data?.numberOfAyahs) throw new Error("No numberOfAyahs");
      surahMetadata = metaJson.data;
      totalNumberOfAyahs = surahMetadata.numberOfAyahs;
      console.log(
        `Fetched Metadata for ${surahNumber}: ${totalNumberOfAyahs} Ayahs`
      );

      // Cap endVerse to total available verses
      endVerse = Math.min(endVerse, totalNumberOfAyahs);
      console.log(`Final verse range: ${startVerse} to ${endVerse}`);
    } catch (error) {
      console.error("Meta fetch error:", error);
      redirect("/learn");
    }

    // --- Conditional Verse Text Fetching based on size and range ---
    const verseCount = endVerse - startVerse + 1;
    const isSmallFetch =
      totalNumberOfAyahs <= DYNAMIC_FETCH_THRESHOLD ||
      verseCount <= DYNAMIC_FETCH_THRESHOLD;

    if (isSmallFetch) {
      console.log(
        `Small Fetch (${verseCount} verses from ${totalNumberOfAyahs} total). Fetching text immediately.`
      );
      try {
        const textResponse = await fetch(
          `https://api.alquran.cloud/v1/surah/${surahNumber}/quran-uthmani`
        );
        console.log(
          `AlQuranCloud Text response status: ${textResponse.status}`
        );
        if (!textResponse.ok)
          throw new Error(`Text fetch failed: ${textResponse.status}`);
        const textJson = await textResponse.json();
        const allAyahsText = textJson?.data?.ayahs || [];

        if (allAyahsText.length > 0) {
          // Filter to only include verses in our range
          versesData = allAyahsText
            .filter(
              (a: any) =>
                typeof a.numberInSurah === "number" &&
                a.numberInSurah >= startVerse &&
                a.numberInSurah <= endVerse
            )
            .map((a: any) => ({
              surahNumber: surahNumber,
              verseNumber: a.numberInSurah,
              // *** Apply updated removeBasmala only for verse 1 and not Surah 1 ***
              text:
                a.numberInSurah === 1 && surahNumber !== 1
                  ? removeBasmala(a.text)
                  : a.text,
            }));
          console.log(
            `Processed ${versesData.length} verses in range ${startVerse}-${endVerse}`
          );
        } else {
          console.warn("Fetched text but no ayahs.");
        }
      } catch (error) {
        console.error("Surah text fetch/process error:", error);
        redirect("/learn");
      }
    } else {
      console.log(
        `Large Fetch (${verseCount} verses). Initial fetch skipped. Client will fetch on demand.`
      );
    }
  }

  // --- Start/Retrieve Tajweed Session ---
  // #TODO: Create a tajweed session data structure
  const session = {
    sessionId: Date.now(), // Temporary, replace with actual DB session
    // Add tajweed-specific session data
  };

  // --- Fetch User Progress & Subscription ---
  let userProgress = null;
  let userSubscription = null;
  try {
    const [p, s] = await Promise.all([
      getUserProgress(),
      getUserSubscription(),
    ]);
    userProgress = p;
    userSubscription = s;
    console.log("User progress:", userProgress);
    console.log("User subscription:", userSubscription);
  } catch (error) {
    console.error("User data fetch error:", error);
    redirect("/learn");
  }
  if (!userProgress) redirect("/learn");

  // --- Fetch User Tajweed Progress ---
  // #TODO: Implement tajweed progress fetching
  let initialPercentage = 0;
  if (totalNumberOfAyahs > 0) {
    // Placeholder - replace with actual calculation based on tajweed progress
    initialPercentage = Math.min(
      (Math.floor(Math.random() * 30) / totalNumberOfAyahs) * 100,
      100
    );
  }
  console.log("Initial %:", initialPercentage);

  // --- Construct Surah Data ---
  const surahData = {
    id: surahNumber,
    number: surahNumber,
    name: surahMetadata?.name || surahNameFallback || `Surah ${surahNumber}`,
    englishName:
      surahMetadata?.englishName || surahNameFallback || `Surah ${surahNumber}`,
    englishNameTranslation: surahMetadata?.englishNameTranslation || "Unknown",
    revelationPlace: surahMetadata?.revelationType || "Unknown",
    numberOfAyahs: totalNumberOfAyahs,
  };
  console.log("Final surahData:", surahData);

  // --- Get Tajweed Rule Context if available ---
  let ruleContext: RuleContext | null = null;
  if (ruleId && tajweedExamples[ruleId as keyof typeof tajweedExamples]) {
    const ruleData = tajweedExamples[
      ruleId as keyof typeof tajweedExamples
    ] as TajweedRule;

    // CRITICAL CHANGE: Sort examples by ID before creating the ruleContext
    const sortedExamples = [...ruleData.examples].sort((a, b) => {
      // If IDs are present, use them for sorting
      if (a.id !== undefined && b.id !== undefined) {
        return a.id - b.id;
      }
      // Fallback to preserving original order
      return 0;
    });

    // Create ruleContext with the sorted examples
    ruleContext = {
      ruleId,
      ruleName: ruleName || sortedExamples[0]?.keyWords?.join(", ") || "",
      ruleArabicName: ruleArabicName || "",
      keyWords: sortedExamples[0]?.keyWords || [],
      practiceNotes: ruleData?.practiceNotes || "",
      reason: sortedExamples[0]?.explanation || "",
      difficultyLevel: "intermediate",
      examples: sortedExamples, // Include the sorted examples array
    };

    console.log(
      `Added tajweed rule context with ${sortedExamples.length} examples sorted by ID`
    );
  }

  // --- Render Client Component ---
  return (
    <div data-testid="tajweed-page">
      <Tajweed
        key={`${surahNumber}-${startVerse}-${endVerse}-${ruleId}-${mode}`} // Add mode to key for proper re-rendering
        sessionId={session.sessionId}
        userId={userId}
        surahNumber={surahNumber}
        verses={versesData}
        initialPercentage={initialPercentage}
        versesRange={initialVersesRange}
        surahData={surahData}
        userSubscription={userSubscription}
        initialHearts={userProgress.hearts}
        // Add tajweed-specific props
        ruleId={ruleId}
        ruleName={ruleName}
        ruleArabicName={ruleArabicName}
        ruleContext={ruleContext}
        verseStartIndex={startVerse}
        verseEndIndex={endVerse}
        mode={mode} // CRITICAL: Explicitly pass mode to client component
      />
    </div>
  );
};

export default TajweedPage;
