// ---------------------------------------------
//  Tajweed rule data  – streamlined version
// ---------------------------------------------

/** A concrete Qur’ān example used to illustrate a rule. */
export interface TajweedExample {
  id: number;
  surah?: number | null;
  verse?: number | string | null; // Verse number of the first verse in a pair for Waqf Taam
  exampleText: string;
  explanation: string;
  keyWords: string[];
  type?: string; // Optional field for categorizing examples
  letter?: string; // Optional field for specifying which letter is being demonstrated
  wordPositions?: number[] | null;
  start_ms?: number;
  end_ms?: number;
  audio_url?: string; // URL for the audio of the 'verse' (first verse in a Waqf Taam pair)
  nextVerseToPlay?: {
    // Optional field specifically for Waqf <PERSON>am or similar multi-verse examples
    surah: number;
    verse: number;
  } | null; // Make it optional and allow null if not applicable
}

/** Top-level rule definition. */
export interface TajweedRule {
  /** Short English description of the rule. */
  description: string;
  /** Arabic name (for UI badges, etc.). */
  arabicName: string;
  /** Examples that **do** illustrate the rule. */
  examples: TajweedExample[];
  /** (Optional) examples that **do NOT** illustrate the rule – handy for “spot-the-error” drills. */
  nonExamples?: TajweedExample[];
  /** Teaching notes / drill instructions. */
  practiceNotes?: string;
}

export const tajweedExamples: Record<string, TajweedRule> = {
  // -------------------------------------------------------------------
  //  Noon Sākin & Tanwīn
  // -------------------------------------------------------------------
  "idgham-ghunnah": {
    description:
      "Merging with nasalization when noon sākinah / tanwīn meets ي ن م و",
    arabicName: "إدغام بغنة",
    examples: [
      {
        id: 1,
        surah: 99,
        verse: 7,
        exampleText: "فَمَن يَعْمَلْ",
        explanation:
          'Noon Sakinah (نْ) in "فَمَن" is followed by ياء (ي), triggering Idgham with Ghunnah.',
        keyWords: ["فَمَن", "يَعْمَلْ"],
        wordPositions: [1, 2],

        start_ms: 30,

        end_ms: 2090,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/099007.mp3",
      },
      {
        id: 2,
        surah: 99,
        verse: 7,
        exampleText: "خَيْرً‌ا يَرَهُ",
        explanation:
          'Tanween Fatha (ـً) on "خَيْرًا" is followed by ياء (ي), triggering Idgham with Ghunnah.',
        keyWords: ["خَيْرًا", "يَرَهُ"],
        wordPositions: [5, 6],

        start_ms: 4200,

        end_ms: 6380,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/099007.mp3",
      },
      {
        id: 3,
        surah: 92,
        verse: 19,
        exampleText: "مِن نِّعْمَةٍ",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by نون (ن), triggering Idgham with Ghunnah.',
        keyWords: ["مِن", "نِّعْمَةٍ"],
        wordPositions: [4, 5],

        start_ms: 3300,

        end_ms: 6340,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/092019.mp3",
      },
      {
        id: 4,
        surah: 88,
        verse: 8,
        exampleText: "يَوْمَئِذٍ نَّاعِمَةٌ",
        explanation:
          'Tanween Kasra (ـٍ) on "يَوْمَئِذٍ" is followed by نون (ن), triggering Idgham with Ghunnah.',
        keyWords: ["يَوْمَئِذٍ", "نَّاعِمَةٌ"],
        wordPositions: [2, 3],

        start_ms: 1950,

        end_ms: 4820,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/088008.mp3",
      },
      {
        id: 5,
        surah: 86,
        verse: 6,
        exampleText: "مِن مَّاءٍ",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by ميم (م), triggering Idgham with Ghunnah.',
        keyWords: ["مِن", "مَّاءٍ"],
        wordPositions: [2, 3],

        start_ms: 670,

        end_ms: 4770,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/086006.mp3",
      },
      {
        id: 6,
        surah: 36,
        verse: 44,
        exampleText: "رَحْمَةً مِّنَّا",
        explanation:
          'Tanween Fatha (ـً) on "رَحْمَةً" is followed by ميم (م), triggering Idgham with Ghunnah.',
        keyWords: ["رَحْمَةً", "مِّنَّا"],
        wordPositions: [2, 3],

        start_ms: 800,

        end_ms: 4190,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/036044.mp3",
      },
      {
        id: 7,
        surah: 85,
        verse: 20,
        exampleText: "مِن وَرَائِهِم",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by واو (و), triggering Idgham with Ghunnah.',
        keyWords: ["مِن", "وَرَائِهِم"],
        wordPositions: [2, 3],

        start_ms: 1040,

        end_ms: 5890,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/085020.mp3",
      },
      {
        id: 8,
        surah: 6,
        verse: 32,
        exampleText: "لَعِبٌ وَلَهْوٌ",
        explanation:
          'Tanween Damma (ـٌ) on "لَعِبٌ" is followed by واو (و), triggering Idgham with Ghunnah.',
        keyWords: ["لَعِبٌ", "وَلَهْوٌ"],
        wordPositions: [5, 6],

        start_ms: 5350,

        end_ms: 8420,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/006032.mp3",
      },
      {
        id: 9,
        surah: 88,
        verse: 2,
        exampleText: "وُجُوهٌ يَوْمَئِذٍ",
        explanation:
          "Tanween Damma (ـٌ) on \"وُجُوهٌ\" is followed by ياء (ي) in 'يَوْمَئِذٍ خَـٰشِعَةٌ', triggering Idgham with Ghunnah.",
        keyWords: ["وُجُوهٌ", "يَوْمَئِذٍ"],
        wordPositions: [1, 2],

        start_ms: 30,

        end_ms: 3140,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/088002.mp3",
      },
      {
        id: 10,
        surah: 2,
        verse: 19,
        exampleText: "وَرَعْدٌ وَبَرْقٌ",
        explanation:
          'Tanween Damma (ـٌ) on "رَعْدٌ" is followed by واو (و), triggering Idgham with Ghunnah.',
        keyWords: ["رَعْدٌ", "وَبَرْقٌ"],
        wordPositions: [7, 8],

        start_ms: 6400,

        end_ms: 9460,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002019.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Idgham Without Ghunnah
  // -------------------------------------------------------------------

  "idgham-without-ghunnah": {
    description:
      "Merging without nasalization when noon sākinah / tanwīn meets ل (Laam) or ر (Raa)",
    arabicName: "إدغام بغير غنة",
    examples: [
      {
        id: 1,
        surah: 35,
        verse: 2,
        exampleText: "مِن رَّحْمَةٍ",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by Raa (ر) in "رَّحْمَةٍ", triggering Idgham without Ghunnah.',
        keyWords: ["مِن", "رَّحْمَةٍ"],
        wordPositions: [5, 6],

        start_ms: 3460,

        end_ms: 5390,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/035002.mp3",
      },
      {
        id: 2,
        surah: 2,
        verse: 173,
        exampleText: "غَفُورٌ رَّحِيمٌ",
        explanation:
          'Tanween Damma (ـٌ) on "غَفُورٌ" is followed by Raa (ر) in "رَّحِيمٌ", triggering Idgham without Ghunnah.',
        keyWords: ["غَفُورٌ", "رَّحِيمٌ"],
        wordPositions: [24, 25],

        start_ms: 23950,

        end_ms: 26850,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002173.mp3",
      },
      {
        id: 3,
        surah: 18,
        verse: 65,
        exampleText: "مِن لَّدُنَّا",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by Laam (ل) in "لَّدُنَّا" (from \'وَعَلَّمْنَـٰهُ مِن لَّدُنَّا عِلْمًا\'), triggering Idgham without Ghunnah.',
        keyWords: ["مِن", "لَّدُنَّا"],
        wordPositions: [10, 11],

        start_ms: 12750,

        end_ms: 14820,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/018065.mp3",
      },
      {
        id: 4,
        surah: 104,
        verse: 1,
        exampleText: "وَيْلٌ لِّكُلِّ",
        explanation:
          'Tanween Damma (ـٌ) on "وَيْلٌ" is followed by Laam (ل) in "لِّكُلِّ", triggering Idgham without Ghunnah.',
        keyWords: ["وَيْلٌ", "لِّكُلِّ"],
        wordPositions: [1, 2],

        start_ms: 30,

        end_ms: 1820,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/104001.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 5,
        exampleText: "مِّن رَّبِّهِمْ",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by Raa (ر) in "رَّبِّهِمْ", triggering Idgham without Ghunnah.',
        keyWords: ["مِن", "رَّبِّهِمْ"],
        wordPositions: [4, 5],

        start_ms: 3490,

        end_ms: 5490,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002005.mp3",
      },
      {
        id: 6,
        surah: 2,
        verse: 2,
        exampleText: "هُدًى لِّلْمُتَّقِينَ",
        explanation:
          'Tanween Fatha (ـً) on "هُدًى" is followed by Laam (ل) in "لِّلْمُتَّقِينَ", triggering Idgham without Ghunnah.',
        keyWords: ["هُدًى", "لِّلْمُتَّقِينَ"],
        wordPositions: [6, 7],

        start_ms: 4990,

        end_ms: 8610,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002002.mp3",
      },
      {
        id: 7,
        surah: 96,
        verse: 7,
        exampleText: "أَن رَّءَاهُ",
        explanation:
          'Noon Sakinah (نْ) in "أَن" is followed by Raa (ر) in "رَّءَاهُ" (from \'أَن رَّءَاهُ ٱسْتَغْنَىٰٓ\'), triggering Idgham without Ghunnah.',
        keyWords: ["أَن", "رَّءَاهُ"],
        wordPositions: [1, 2],

        start_ms: 100,

        end_ms: 1240,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/096007.mp3",
      },
      {
        id: 8,
        surah: 101,
        verse: 7,
        exampleText: "فِى عِيشَةٍ رَّاضِيَةٍ",
        explanation:
          'Tanween Kasra (ـٍ) on "عِيشَةٍ" is followed by Raa (ر) in "رَّاضِيَةٍ", triggering Idgham without Ghunnah.',
        keyWords: ["عِيشَةٍ", "رَّاضِيَةٍ"],
        wordPositions: [3, 4],

        start_ms: 1060,

        end_ms: 3290,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/101007.mp3",
      },
      {
        id: 9,
        surah: 11,
        verse: 1,
        exampleText: "مِّن لَّدُنْ حَكِيمٍ",
        explanation:
          'Noon Sakinah (نْ) in "مِن" is followed by Laam (ل) in "لَّدُنْ" (from \'مِن لَّدُنْ حَكِيمٍ خَبِيرٍ\'), triggering Idgham without Ghunnah.',
        keyWords: ["مِن", "لَّدُنْ"],
        wordPositions: [8, 9],

        start_ms: 11950,

        end_ms: 13820,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/011001.mp3",
      },
      {
        id: 10,
        surah: 4,
        verse: 171,
        exampleText: "خَيْرًا لَّكُمْ",
        explanation:
          'Tanween Fatha (ـً) on "خَيْرًا" is followed by Laam (ل) in "لَّكُمْ" (from \'ٱنتَهُوا۟ خَيْرًا لَّكُمْ\'), triggering Idgham without Ghunnah.',
        keyWords: ["خَيْرًا", "لَّكُمْ"],
        wordPositions: [33, 34],

        start_ms: 33510,

        end_ms: 35420,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/004171.mp3",
      },
    ],
  },

  iqlab: {
    description:
      "Conversion of نْ or Tanwīn into a م sound with Ghunnah when followed by ب",
    arabicName: "إقلاب",
    examples: [
      {
        id: 1,
        surah: 4,
        verse: 58,
        exampleText: "سَمِيعًا بَصِيرًا",
        explanation:
          'Tanwīn Fatḥah on "سَمِيعًا" is followed by Baa (ب) in "بَصِيرًا", so the Noon of the Tanween is converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["سَمِيعًا", "بَصِيرًا"],
        wordPositions: [24, 25],

        start_ms: 24640,

        end_ms: 27610,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/004058.mp3",
      },
      {
        id: 2, // New, distinct example
        surah: 98,
        verse: 4,
        exampleText: "مِنۢ بَعْدِ",
        explanation:
          'Noon Sākinah (نْ) in "مِنۢ" is followed by Baa (ب) in "بَعْدِ" (from \'إِلَّا مِنۢ بَعْدِ مَا جَآءَتْهُمُ ٱلْبَيِّنَةُ\'), so the Noon Sākinah is converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["مِنۢ", "بَعْدِ"],
        wordPositions: [7, 8],
        start_ms: 5160,

        end_ms: 6860,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/098004.mp3",
        // start_ms and end_ms will be populated by your script
      },
      {
        id: 3,
        surah: 22,
        verse: 75,
        exampleText: "سَمِيعٌ بَصِيرٌ",
        explanation:
          'Tanwīn Ḍammah on "سَمِيعٌ" is followed by Baa (ب) in "بَصِيرٌ", so the Noon of the Tanween is converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["سَمِيعٌ", "بَصِيرٌ"],
        wordPositions: [10, 11],

        start_ms: 13470,

        end_ms: 17860,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/022075.mp3",
      },
      {
        id: 4,
        surah: 10,
        verse: 36,
        exampleText: "عَلِيمٌ بِمَا",
        explanation:
          'Tanwīn Ḍammah on "عَلِيمٌ" is followed by Baa (ب) in "بِمَا" (from \'إِنَّ ٱللَّهَ عَلِيمٌۢ بِمَا يَفْعَلُونَ\'), converting the Noon of the Tanween to a Meem (م) sound with Ghunnah.',
        keyWords: ["عَلِيمٌ", "بِمَا"],
        wordPositions: [15, 16],
        start_ms: 15770,

        end_ms: 18180,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/010036.mp3",
      },
      {
        id: 5,
        surah: 3,
        verse: 119,
        exampleText: "عَلِيمٌ بِذَاتِ",
        explanation:
          'Tanwīn Ḍammah on "عَلِيمٌ" is followed by Baa (ب) in "بِذَاتِ" (from \'إِنَّ ٱللَّهَ عَلِيمٌۢ بِذَاتِ ٱلصُّدُورِ\'), so Iqlāb occurs, converting the Noon of the Tanween to a Meem (م) sound with Ghunnah.',
        keyWords: ["عَلِيمٌ", "بِذَاتِ"],
        wordPositions: [29, 30],
        start_ms: 33610,

        end_ms: 35990,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/003119.mp3",
      },
      {
        id: 6,
        surah: 24,
        verse: 39,
        exampleText: "كَسَرَابٍۭ بِقِيعَةٍۢ",
        explanation:
          'Tanwīn Kasrah on "سَرَابٍ" is followed by Baa (ب) in "بِقِيعَةٍ" (from \'كَسَرَابٍۭ بِقِيعَةٍ\'), causing the Noon of the Tanween to be converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["سَرَابٍ", "بِقِيعَةٍ"],
        wordPositions: [4, 5],

        start_ms: 5110,

        end_ms: 9210,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/024039.mp3",
      },
      {
        id: 7,
        surah: 50,
        verse: 7,
        exampleText: "زَوْجٍ بَهِيجٍ",
        explanation:
          'Tanwīn Kasrah on "زَوْجٍ" is followed by Baa (ب) in "بَهِيجٍ" (from \'مِن كُلِّ زَوْجٍۭ بَهِيجٍ\'), triggering Iqlāb where the Noon of the Tanween is converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["زَوْجٍ", "بَهِيجٍ"],
        wordPositions: [10, 11],

        start_ms: 11490,

        end_ms: 15600,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/050007.mp3",
      },
      {
        id: 8,
        surah: 23,
        verse: 31,
        exampleText: "مِن بَعْدِهِمْ",
        explanation:
          'Noon Sākinah (نْ) in "مِن" is directly followed by Baa (ب) in "بَعْدِهِمْ", so the Noon Sākinah (نْ) is pronounced as a Meem (م) sound with Ghunnah.',
        keyWords: ["مِن", "بَعْدِهِمْ"],
        wordPositions: [3, 4],

        start_ms: 3180,

        end_ms: 5350,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/023031.mp3",
      },
      {
        id: 9,
        surah: 96,
        verse: 15,
        exampleText: "لَنَسْفَعًا بِالنَّاصِيَةِ",
        explanation:
          'Tanwīn Fatḥah on "لَنَسْفَعًا" (represented by a Noon Sakinah on the Alif) is followed by Baa (ب) in "بِالنَّاصِيَةِ", so the Noon of the Tanween is converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["لَنَسْفَعًا", "بِالنَّاصِيَةِ"],
        wordPositions: [5, 6],

        start_ms: 3890,

        end_ms: 8070,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/096015.mp3",
      },
      {
        id: 10,
        surah: 40,
        verse: 44,
        exampleText: "بَصِيرٌ بِالْعِبَادِ",
        explanation:
          'Tanwīn Ḍammah on "بَصِيرٌ" is followed by Baa (ب) in "بِالْعِبَادِ" (from \'إِنَّ ٱللَّهَ بَصِيرٌۢ بِٱلْعِبَادِ\'), resulting in Iqlāb where the Noon of the Tanween is converted to a Meem (م) sound with Ghunnah.',
        keyWords: ["بَصِيرٌ", "بِالْعِبَادِ"],
        wordPositions: [11, 12],

        start_ms: 14440,

        end_ms: 19310,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/040044.mp3",
      },
    ],
  },
  izhar: {
    description:
      "Clear pronunciation of نْ or Tanwīn when followed by a throat letter (ء ه ع ح غ خ)",
    arabicName: "إظهار",
    examples: [
      {
        id: 1,
        surah: 2,
        verse: 62,
        exampleText: "مَنْ ءَامَنَ",
        explanation:
          'Noon Sākinah (نْ) in "مَنْ" is followed by Hamza (ء) in "ءَامَنَ", so the ن is pronounced clearly. (Original 2:231 for "مِنْ أَمْرِهِمْ" was incorrect.)',
        keyWords: ["مَنْ", "ءَامَنَ"],
        wordPositions: [8, 9],

        start_ms: 8750,

        end_ms: 9860,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002062.mp3",
      },
      {
        id: 2,
        surah: 13,
        verse: 7,
        exampleText: "قَوْمٍ هَادٍ",
        explanation:
          'Tanwīn Kasra on "قَوْمٍ" is followed by Ha (ه) in "هَادٍ" (from \'وَلِكُلِّ قَوْمٍ هَادٍ\'), so the Noon sound of the Tanwīn is pronounced clearly. (Original 2:269 for "حِكْمَةٍ هُدًى" was incorrect.)',
        keyWords: ["قَوْمٍ", "هَادٍ"],
        wordPositions: [14, 15],
        start_ms: 19420,

        end_ms: 22650,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/013007.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 255,
        exampleText: "مِنْ عِلْمِهِ",
        explanation:
          'Noon Sākinah (نْ) in "مِنْ" is followed by ʿAyn (ع) in "عِلْمِهِ" (from \'وَلَا يُحِيطُونَ بِشَىْءٍۢ مِّنْ عِلْمِهِۦٓ إِلَّا بِمَا شَآءَ\'), so the ن is articulated distinctly. (Original 2:129 for "مِنْ عِلْمِهِمْ" was incorrect.)',
        keyWords: ["مِنْ", "عِلْمِهِ"],
        wordPositions: [36, 37],

        start_ms: 33200,

        end_ms: 36320,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002255.mp3",
      },
      {
        id: 4,
        surah: 95,
        verse: 6,
        exampleText: "أَجْرٌ غَيْرُ",
        explanation:
          'Tanwīn Ḍammah on "أَجْرٌ" is followed by Ghayn (غ) in "غَيْرُ" (from \'فَلَهُمْ أَجْرٌ غَيْرُ مَمْنُونٍۢ\'), so the Noon sound of the Tanwīn is pronounced clearly. (Original 6:19 for "حَقٍّ غَيْرِ" was incorrect.)',
        keyWords: ["أَجْرٌ", "غَيْرُ"],
        wordPositions: [7, 8],
        start_ms: 5640,

        end_ms: 6950,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/095006.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 105,
        exampleText: "مِنْ خَيْرٍ",
        explanation:
          'Noon Sākinah (نْ) in "مِنْ" is followed by Kha (خ) in "خَيْرٍ" (from \'أَن يُنَزَّلَ عَلَيْكُم مِّنْ خَيْرٍۢ مِّن رَّبِّكُمْ\'), so the ن remains distinct and is pronounced clearly. (Original 10:58 was incorrect for this phrase.)',
        keyWords: ["مِنْ", "خَيْرٍ"],
        wordPositions: [13, 14],

        start_ms: 9820,

        end_ms: 11600,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002105.mp3",
      },
      {
        id: 6,
        surah: 5,
        verse: 17,
        exampleText: "إِنْ أَرَادَ",
        explanation:
          'Noon Sākinah (نْ) in "إِنْ" is followed by Hamza (أ) in "أَرَادَ" (from \'فَمَن يَمْلِكُ مِنَ ٱللَّهِ شَيْـًٔا إِنْ أَرَادَ أَن يُهْلِكَ\'), so the ن is pronounced clearly. (Original 16:101 was incorrect.)',
        keyWords: ["إِنْ", "أَرَادَ"],
        wordPositions: [17, 18],

        start_ms: 14070,

        end_ms: 15370,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/005017.mp3",
      },
      {
        id: 7,
        surah: 97,
        verse: 5,
        exampleText: "سَلَامٌ هِيَ",
        explanation:
          'Tanwīn Ḍammah on "سَلَامٌ" is followed by Ha (ه) in "هِيَ", so the Noon sound of the Tanwīn is pronounced clearly. (Original 19:4 for "قُولُوا۟ هَآ" was incorrect as the text was not a Tanween example and the verse was wrong.)',
        keyWords: ["سَلَامٌ", "هِيَ"],
        wordPositions: [1, 2],

        start_ms: 200,

        end_ms: 1530,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/097005.mp3",
      },
      {
        id: 8,
        surah: 41,
        verse: 42,
        exampleText: "مِنْ حَكِيمٍ",
        explanation:
          'Noon Sākinah (نْ) in "مِنْ" is followed by Haa (ح) in "حَكِيمٍ" (from \'تَنزِيلٌ مِّنْ حَكِيمٍ حَمِيدٍ\'), so the ن is pronounced clearly. (Original 33:24 for "مِنْ ءَايَتِهِ" was incorrect, and this example ensures letter \'ح\' is covered.)',
        keyWords: ["مِنْ", "حَكِيمٍ"],
        wordPositions: [11, 12],

        start_ms: 11130,

        end_ms: 12490,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/041042.mp3",
      },
      {
        id: 9,
        surah: 11,
        verse: 58,
        exampleText: "عَذَابٌ غَلِيظٌ",
        explanation:
          'Tanwīn Ḍammah on "عَذَابٌ" is followed by Ghayn (غ) in "غَلِيظٌ" (from \'وَنَجَّيْنَـٰهُم مِّنْ عَذَابٍ غَلِيظٍۢ\'), so the Noon sound of the Tanwīn is pronounced clearly. (Original 35:30 for "حَقٍّ غَافِرٍ" was incorrect.)',
        keyWords: ["عَذَابٌ", "غَلِيظٌ"],
        wordPositions: [13, 14],
        start_ms: 20550,

        end_ms: 24860,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/011058.mp3",
      },
      {
        id: 10,
        surah: 106,
        verse: 4,
        exampleText: "مِنْ خَوْفٍ",
        explanation:
          'Noon Sākinah (نْ) in "مِنْ" is followed by Kha (خ) in "خَوْفٍ" (from \'وَءَامَنَهُم مِّنْ خَوْفٍۭ\'), so the ن remains distinct and is pronounced clearly. (Original 41:30 was incorrect.)',
        keyWords: ["مِنْ", "خَوْفٍ"],
        wordPositions: [6, 7],
        start_ms: 9490,

        end_ms: 10700,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/106004.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Mīm Sākin
  // -------------------------------------------------------------------
  "idgham-shafawi": {
    description:
      "Merging of Meem Sākinah (مْ) into the following Meem (م) with Ghunnah for 2 counts",
    arabicName: "إدغام شفوي",
    examples: [
      {
        id: 1,
        surah: 9,
        verse: 58,
        exampleText: "وَمِنْهُم مَّن",
        explanation:
          'Meem Sākinah in "وَمِنْهُم" merges into the Meem of "مَّن" with two counts of Ghunnah.',
        keyWords: ["وَمِنْهُم", "مَّن"],
        wordPositions: [1, 2],

        start_ms: 100,

        end_ms: 3340,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/009058.mp3",
      },
      {
        id: 2,
        surah: 2,
        verse: 134,
        exampleText: "وَلَكُم مَّا",
        explanation:
          'Meem Sākinah in "وَلَكُم" merges into the Meem of "مَّا" (from \'وَلَكُم مَّا كَسَبْتُمْ\') with Ghunnah for two counts.',
        keyWords: ["وَلَكُم", "مَّا"],
        // start_ms and end_ms removed; script will populate
        wordPositions: [8, 9],

        start_ms: 6570,

        end_ms: 8460,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002134.mp3",
      },
      {
        id: 3,
        surah: 71,
        verse: 11,
        exampleText: "عَلَيْكُم مِّدْرَارًا",
        explanation:
          'Meem Sākinah in "عَلَيْكُم" merges into the Meem of "مِّدْرَارًا" with two counts of Ghunnah.',
        keyWords: ["عَلَيْكُم", "مِّدْرَارًا"],
        wordPositions: [3, 4],
        start_ms: 3040,
        end_ms: 6330,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/071011.mp3",
      },
      {
        id: 4,
        surah: 106,
        verse: 4,
        exampleText: "أَطْعَمَهُم مِّن",
        explanation:
          'Meem Sākinah in "أَطْعَمَهُم" merges into the Meem of "مِّن" (from \'ٱلَّذِىٓ أَطْعَمَهُم مِّن جُوعٍ\') with a nasal hold for two counts.',
        keyWords: ["أَطْعَمَهُم", "مِّن"],
        wordPositions: [2, 3],
        start_ms: 2290,
        end_ms: 5400,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/106004.mp3",
      },
      {
        id: 5,
        surah: 104,
        verse: 8,
        exampleText: "عَلَيْهِم مُّؤْصَدَةٌ",
        explanation:
          'Meem Sākinah in "عَلَيْهِم" merges into the Meem of "مُّؤْصَدَةٌ" with Ghunnah for two counts.',
        keyWords: ["عَلَيْهِم", "مُّؤْصَدَةٌ"],
        wordPositions: [2, 3],
        start_ms: 1800,
        end_ms: 4960,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/104008.mp3",
      },
      {
        id: 6,
        surah: 85,
        verse: 20,
        exampleText: "وَرَائِهِم مُّحِيطٌ",
        explanation:
          'Meem Sākinah in "وَرَائِهِم" merges into the Meem of "مُّحِيطٌ" (from \'وَٱللَّهُ مِن وَرَآئِهِم مُّحِيطٌۢ\') with two counts of Ghunnah.',
        keyWords: ["وَرَائِهِم", "مُّحِيطٌ"],
        // start_ms and end_ms removed; script will populate
        wordPositions: [3, 4],

        start_ms: 2040,

        end_ms: 8330,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/085020.mp3",
      },
      {
        id: 7,
        surah: 83,
        verse: 4,
        exampleText: "أَنَّهُم مَّبْعُوثُونَ",
        explanation:
          'Meem Sākinah in "أَنَّهُم" merges into the Meem of "مَّبْعُوثُونَ" with Ghunnah for two counts.',
        keyWords: ["أَنَّهُم", "مَّبْعُوثُونَ"],
        wordPositions: [4, 5],
        start_ms: 4560,
        end_ms: 10000,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/083004.mp3",
      },
      {
        id: 8,
        surah: 77,
        verse: 20,
        exampleText: "نَخْلُقكُّم مِّن",
        explanation:
          'Meem Sākinah in "نَخْلُقكُّم" merges into the Meem of "مِّن" (from \'أَلَمْ نَخْلُقكُّم مِّن مَّآءٍۢ مَّهِينٍۢ\') with two counts of Ghunnah.',
        keyWords: ["نَخْلُقكُّكُم", "مِّن"], // Standardized first keyword to match common Uthmani script
        wordPositions: [2, 3],
        start_ms: 510,
        end_ms: 2450,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/077020.mp3",
      },
      {
        id: 9,
        surah: 68,
        verse: 46,
        exampleText: "فَهُم مِّن",
        explanation:
          'Meem Sākinah in "فَهُم" merges into the Meem of "مِّن" (from \'أَمْ تَسْـَٔلُهُمْ أَجْرًا فَهُم مِّن مَّغْرَمٍۢ مُّثْقَلُونَ\') with Ghunnah for two counts.',
        keyWords: ["فَهُم", "مِّن"],
        wordPositions: [4, 5],
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/068046.mp3",
        start_ms: 2860,
        end_ms: 5230,
      },
      {
        id: 10,
        surah: 39,
        verse: 34,
        exampleText: "لَهُم مَّا يَشَاءُونَ",
        explanation:
          'Meem Sākinah in "لَهُم" merges into the Meem of "مَّا" (in "مَّا يَشَاءُونَ") with two counts of Ghunnah.',
        keyWords: ["لَهُمْ", "مَّا"], // Note: "لَهُمْ" used as keyword, verse has "لَهُم"
        wordPositions: [1, 3],
        start_ms: 120,
        end_ms: 1950,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/039034.mp3",
      },
    ],
    practiceNotes:
      "Recite each pair aloud, blending the Meem Sākinah into the next Meem with Ghunnah for two counts.",
  },
  "ikhfa-shafawi": {
    description:
      "Concealing the Meem Sākinah (مْ) when followed by Baa (ب) with Ghunnah for 2 counts",
    arabicName: "إخفاء شفوي",
    examples: [
      {
        id: 1,
        surah: 18,
        verse: 13,
        exampleText: "نَبَأَهُم بِالْحَقِّ",
        explanation:
          'Meem Sākinah in "نَبَأَهُم" is concealed when meeting the Baa (ب) of "بِالْحَقِّ" with Ghunnah (nasal sound) for two counts.',
        keyWords: ["نَبَأَهُم", "بِالْحَقِّ"],
        wordPositions: [4, 5],
        start_ms: 2380,
        end_ms: 5470,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/018013.mp3",
      },
      {
        id: 2,
        surah: 105,
        verse: 4,
        exampleText: "تَرْمِيهِم بِحِجَارَةٍ",
        explanation:
          'Meem Sākinah in "تَرْمِيهِم" is concealed when followed by Baa (ب) in "بِحِجَارَةٍ" with Ghunnah for two counts.',
        keyWords: ["تَرْمِيهِم", "بِحِجَارَةٍ"],
        wordPositions: [1, 2],
        start_ms: 30,
        end_ms: 4620,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/105004.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 33,
        exampleText: "أَنبِئْهُم بِأَسْمَائِهِمْ",
        explanation:
          'Meem Sākinah in "أَنبِئْهُم" (أَنۢبِئْهُم) is concealed before the Baa (ب) of "بِأَسْمَائِهِمْ" with Ghunnah for two counts.',
        keyWords: ["أَنۢبِئْهُم", "بِأَسْمَائِهِمْ"], // Corrected first keyword to reflect common Uthmani script with "Meem Qulabah" // Corrected word positions,
        // start_ms and end_ms removed; script will populate
        wordPositions: [3, 4],

        start_ms: 3260,

        end_ms: 8850,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002033.mp3",
      },
      {
        id: 4,
        surah: 81,
        verse: 22,
        exampleText: "صَاحِبُكُم بِمَجْنُونٍ",
        explanation:
          'Meem Sākinah in "صَاحِبُكُم" meets Baa (ب) in "بِمَجْنُونٍ" (from \'وَمَا صَاحِبُكُم بِمَجْنُونٍۢ\') , requiring concealment with Ghunnah for two counts.',
        keyWords: ["صَاحِبُكُم", "بِمَجْنُونٍ"],
        wordPositions: [2, 3],
        start_ms: 560,
        end_ms: 4760,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/081022.mp3",
      },
      {
        id: 5,
        surah: 100,
        verse: 11,
        exampleText: "رَبَّهُم بِهِمْ",
        explanation:
          'Meem Sākinah in "رَبَّهُم" is concealed when followed by Baa (ب) in "بِهِمْ" with Ghunnah for two counts.',
        keyWords: ["رَبَّهُم", "بِهِمْ"],
        wordPositions: [2, 3],
        start_ms: 1370,
        end_ms: 3880,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/100011.mp3",
      },
      {
        id: 6,
        surah: 88,
        verse: 22,
        exampleText: "عَلَيْهِم بِمُصَيْطِرٍ",
        explanation:
          'Meem Sākinah in "عَلَيْهِم" (from "لَّسْتَ عَلَيْهِم بِمُصَيْطِرٍ") is concealed when meeting the Baa (ب) of "بِمُصَيْطِرٍ" with Ghunnah for two counts.',
        keyWords: ["عَلَيْهِم", "بِمُصَيْطِرٍ"],
        wordPositions: [2, 3],

        start_ms: 600,

        end_ms: 3420,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/088022.mp3",
      },
      {
        id: 7,
        surah: 84,
        verse: 24,
        exampleText: "فَبَشِّرْهُم بِعَذَابٍ",
        explanation:
          'Meem Sākinah in "فَبَشِّرْهُم" is concealed when followed by Baa (ب) in "بِعَذَابٍ" with Ghunnah for two counts.',
        keyWords: ["فَبَشِّرْهُم", "بِعَذَابٍ"],
        wordPositions: [1, 2],
        start_ms: 90,
        end_ms: 3190,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/084024.mp3",
      },
      {
        id: 8,
        surah: 2,
        verse: 8,
        exampleText: "هُم بِمُؤْمِنِينَ",
        explanation:
          'Meem Sākinah in "هُم" (from "وَمَا هُم بِمُؤْمِنِينَ") is concealed when followed by Baa (ب) in "بِمُؤْمِنِينَ" with Ghunnah for two counts.',
        keyWords: ["هُم", "بِمُؤْمِنِينَ"], // Corrected word positions,
        // start_ms and end_ms removed; script will populate
        wordPositions: [10, 11],

        start_ms: 8570,

        end_ms: 11690,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002008.mp3",
      },
      {
        id: 9,
        surah: 3,
        verse: 101,
        exampleText: "يَعْتَصِم بِاللَّهِ",
        explanation:
          'Meem Sākinah in "يَعْتَصِم" is concealed when followed by Baa (ب) in "بِاللَّهِ" with Ghunnah for two counts.',
        keyWords: ["يَعْتَصِم", "بِاللَّهِ"],
        wordPositions: [11, 12], // Corrected word positions
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/003101.mp3",
        start_ms: 12020,

        end_ms: 14390,
      },
      {
        id: 10,
        surah: 2,
        verse: 126,
        exampleText: "مِنْهُم بِاللَّهِ",
        explanation:
          'Meem Sākinah in "مِنْهُم" (from the phrase "مَنْ آمَنَ مِنْهُم بِاللَّهِ") is concealed when meeting the Baa (ب) of "بِاللَّهِ" with Ghunnah for two counts.',
        keyWords: ["مِنْهُم", "بِاللَّهِ"], // Corrected word positions,
        // start_ms and end_ms removed; script will populate
        wordPositions: [15, 16],

        start_ms: 11740,

        end_ms: 14040,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002126.mp3",
      },
    ],
  },
  "izhar-shafawi": {
    description:
      "Clear pronunciation of Meem Sākinah (مْ) when followed by any letter other than Meem (م) or Baa (ب)",
    arabicName: "إظهار شفوي",
    examples: [
      {
        id: 1,
        surah: 2,
        verse: 39,
        exampleText: "هُمْ فِيهَا",
        explanation:
          'Meem Sākinah in "هُمْ" is pronounced clearly when followed by Fa (ف) in "فِيهَا" without Ghunnah or merging.',
        keyWords: ["هُمْ", "فِيهَا"],
        wordPositions: [8, 9],

        start_ms: 12780,

        end_ms: 14070,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002039.mp3",
      },
      {
        id: 2,
        surah: 6,
        verse: 33,
        exampleText: "فَإِنَّهُمْ لَا",
        explanation:
          'Meem Sākinah in "فَإِنَّهُمْ" is pronounced distinctly when followed by Lam (ل) in "لَا" (from \'فَإِنَّهُمْ لَا يُكَذِّبُونَكَ\') with no concealment or merging.',
        keyWords: ["فَإِنَّهُمْ", "لَا"],
        wordPositions: [7, 8],

        start_ms: 8270,

        end_ms: 10410,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/006033.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 3,
        exampleText: "رَزَقْنَاهُمْ يُنفِقُونَ",
        explanation:
          'Meem Sākinah in "رَزَقْنَاهُمْ" is articulated clearly when followed by Ya (ي) in "يُنفِقُونَ".',
        keyWords: ["رَزَقْنَاهُمْ", "يُنفِقُونَ"],
        wordPositions: [7, 8],

        start_ms: 6580,

        end_ms: 11060,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002003.mp3",
      },
      {
        id: 4,
        surah: 5,
        verse: 105,
        exampleText: "كُنتُمْ تَعْمَلُونَ",
        explanation:
          'Meem Sākinah in "كُنتُمْ" (from "فَيُنَبِّئُكُم بِمَا كُنتُمْ تَعْمَلُونَ") is pronounced clearly when followed by Ta (ت) in "تَعْمَلُونَ", without any merging or nasal sound.',
        keyWords: ["كُنتُمْ", "تَعْمَلُونَ"],
        wordPositions: [18, 19],
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/005105.mp3",
        start_ms: 21060,

        end_ms: 25270,
      },
      {
        id: 5,
        surah: 2,
        verse: 25,
        exampleText: "وَلَهُمْ فِيهَا",
        explanation:
          'Meem Sākinah in "وَلَهُمْ" is pronounced clearly when followed by Fa (ف) in "فِيهَا", with no Ghunnah or concealment.',
        keyWords: ["وَلَهُمْ", "فِيهَا"],
        // start_ms and end_ms removed; script will populate
        wordPositions: [28, 29],

        start_ms: 27020,

        end_ms: 29490,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002025.mp3",
      },
      {
        id: 6,
        surah: 16,
        verse: 30,
        exampleText: "رَبُّكُمْ قَالُوا۟", // Changed example text to a valid one from the verse
        explanation:
          'Meem Sākinah in "رَبُّكُمْ" is clearly pronounced when followed by Qaf (ق) in "قَالُوا۟" (from \'...مَاذَآ أَنزَلَ رَبُّكُمْ ۚ قَالُوا۟ خَيْرًا...\')',
        keyWords: ["رَبُّكُمْ", "قَالُوا۟"], // Changed keywords
        wordPositions: [6, 7], // Corrected for the new example
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/016030.mp3",
        start_ms: 7390,

        end_ms: 9590,
      },
      {
        id: 7,
        surah: 109,
        verse: 6,
        exampleText: "لَكُمْ دِينُكُمْ",
        explanation:
          'Meem Sākinah in "لَكُمْ" is pronounced distinctly when followed by Dal (د) in "دِينُكُمْ".',
        keyWords: ["لَكُمْ", "دِينُكُمْ"],
        wordPositions: [1, 2],
        start_ms: 50,
        end_ms: 1910,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/109006.mp3",
      },
      {
        id: 8,
        surah: 18,
        verse: 107,
        exampleText: "لَهُمْ جَنَّاتُ",
        explanation:
          'Meem Sākinah in "لَهُمْ" is pronounced clearly when followed by Jeem (ج) in "جَنَّاتُ".',
        keyWords: ["لَهُمْ", "جَنَّاتُ"],
        wordPositions: [7, 8],
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/018107.mp3",
        start_ms: 6680,

        end_ms: 8990,
      },
      {
        id: 9,
        surah: 2,
        verse: 4,
        exampleText: "هُمْ يُوقِنُونَ",
        explanation:
          'Meem Sākinah in "هُمْ" is articulated clearly when followed by Ya (ي) in "يُوقِنُونَ" (from \'وَبِٱلْـَٔاخِرَةِ هُمْ يُوقِنُونَ\'), without concealment or merging.',
        keyWords: ["هُمْ", "يُوقِنُونَ"],
        // start_ms and end_ms removed; script will populate
        wordPositions: [11, 12],

        start_ms: 12120,

        end_ms: 15480,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002004.mp3",
      },
      {
        id: 10,
        surah: 53,
        verse: 61,
        exampleText: "وَأَنتُمْ سَامِدُونَ", // Made example text more precise
        explanation:
          'Meem Sākinah in "وَأَنتُمْ" is pronounced distinctly when followed by Seen (س) in "سَامِدُونَ".',
        keyWords: ["وَأَنتُمْ", "سَامِدُونَ"], // Made keyword more precise
        wordPositions: [1, 2],
        start_ms: 30,
        end_ms: 5650,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/053061.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Qalqalah  (merged Sughra + Kubra)
  // -------------------------------------------------------------------
  qalqalah: {
    description:
      "A bouncing sound that occurs when any of the 5 Qalqalah letters (ق ط ب ج د) has sukoon, either in the middle or at the end of a word when stopping",
    arabicName: "قلقلة",
    examples: [
      {
        id: 1,
        surah: 113,
        verse: 1,
        exampleText: "قُلْ",
        explanation:
          'The letter Qaf (ق) in "قُلْ" has sukoon, requiring Qalqalah with a slight bounce.',
        keyWords: ["قُلْ"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 340,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/113001.mp3",
      },
      {
        id: 2,
        surah: 96,
        verse: 2,
        exampleText: "خَلَقَ",
        explanation:
          'When stopping on the word "خَلَقَ", the letter Qaf (ق) at the end receives an implied sukoon, requiring Qalqalah.',
        keyWords: ["خَلَقَ"],
        wordPositions: [1],
        start_ms: 90,
        end_ms: 710,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/096002.mp3",
      },
      {
        id: 3,
        surah: 112,
        verse: 1,
        exampleText: "أَحَدٌ", // Changed to Uthmani script
        explanation:
          "The letter Dal (د) at the end of the word \"أَحَدٌ\" requires Qalqalah when stopping (pronounced 'أَحَدْ'), creating a bouncing articulation.",
        keyWords: ["أَحَدٌ"], // Changed to Uthmani script
        wordPositions: [4],
        start_ms: 1650,
        end_ms: 2300,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/112001.mp3",
      },
      {
        id: 4,
        surah: 2,
        verse: 20,
        exampleText: "أَبْصَٰرَهُمْ", // Adjusted to match Uthmani script for the intended instance
        explanation:
          "The letter Ba (ب) has Sukoon in the middle of the word \"أَبْصَٰرَهُمْ\" (from 'يَخْطَفُ أَبْصَٰرَهُمْ'), requiring Qalqalah with a distinct bouncing sound.",
        keyWords: ["أَبْصَٰرَهُمْ"], // Adjusted // Corrected word position for this instance,
        // start_ms and end_ms removed; script will populate
        wordPositions: [4],

        start_ms: 2410,

        end_ms: 4090,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002020.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 32,
        exampleText: "سُبْحَٰنَكَ", // Adjusted to match common Uthmani script
        explanation:
          'The letter Ba (ب) has Sukoon in the middle of the word "سُبْحَٰنَكَ", requiring a bouncing articulation (Qalqalah).',
        keyWords: ["سُبْحَٰنَكَ"], // Adjusted,
        wordPositions: [2],

        start_ms: 790,

        end_ms: 2010,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002032.mp3",
      },
      {
        id: 6,
        surah: 4,
        verse: 29,
        exampleText: "تَقْتُلُوٓا۟", // Adjusted to match Uthmani script
        explanation:
          "The letter Qaf (ق) has sukoon in the middle of the word \"تَقْتُلُوٓا۟\" (from 'وَلَا تَقْتُلُوٓا۟ أَنفُسَكُمْ'), requiring a slight bouncing sound during pronunciation.",
        keyWords: ["تَقْتُلُوٓا۟"], // Adjusted
        wordPositions: [17], // Corrected
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/004029.mp3",
        start_ms: 21590,

        end_ms: 23630,
      },
      {
        id: 7,
        surah: 85,
        verse: 3,
        exampleText: "مَشْهُودٍ",
        explanation:
          'The letter Dal (د) at the end of the word "مَشْهُودٍ" requires Qalqalah when stopping, with a distinct echoing sound.',
        keyWords: ["مَشْهُودٍ"],
        // start_ms and end_ms will be populated by your script
        wordPositions: [2],

        start_ms: 2400,

        end_ms: 5980,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/085003.mp3",
      },
      {
        id: 8,
        surah: 2,
        verse: 19,
        exampleText: "مُحِيطٌۢ", // Adjusted to match Uthmani script
        explanation:
          "The letter Taw (ط) at the end of \"مُحِيطٌۢ\" (from 'وَٱللَّهُ مُحِيطٌۢ بِٱلْكَـٰفِرِينَ') requires Qalqalah when stopping, producing an echoing sound.",
        keyWords: ["مُحِيطٌۢ"], // Adjusted // Corrected,
        // start_ms and end_ms will be populated by your script
        wordPositions: [18],

        start_ms: 20770,

        end_ms: 22510,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002019.mp3",
      },
      {
        id: 9,
        surah: 48,
        verse: 29,
        exampleText: "وَأَجْرًا",
        explanation:
          "The letter Jeem (ج) has Sukoon in the middle of the word \"أَجْرًا\" (from 'وَمَغْفِرَةً وَأَجْرًا عَظِيمًا'), requiring a bouncing sound during articulation.",
        keyWords: ["أَجْرًا"],
        wordPositions: [53], // Corrected
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/048029.mp3",
        start_ms: 71840,

        end_ms: 73920,
      },
      {
        id: 10,
        surah: 23,
        verse: 1,
        exampleText: "قَدْ",
        explanation:
          "The letter Dal (د) has Sukoon in \"قَدْ\" (from 'قَدْ أَفْلَحَ ٱلْمُؤْمِنُونَ'), requiring a clear Qalqalah sound.",
        keyWords: ["قَدْ"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 270,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/023001.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Ghunnah – now focused on mushaddad noon / mīm only
  // -------------------------------------------------------------------
  ghunnah: {
    description:
      "A nasal sound produced with the letters Noon (ن) and Meem (م) when they have shaddah (doubled), held for 2 counts",
    arabicName: "غُنَّة",
    examples: [
      {
        id: 1,
        surah: 114,
        verse: 1,
        exampleText: "النَّاسِ",
        explanation:
          'The letter Noon (ن) with shaddah in "النَّاسِ" requires Ghunnah, producing a resonating nasal sound for two counts.',
        keyWords: ["النَّاسِ"],
        wordPositions: [4],
        start_ms: 2060,
        end_ms: 5830,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/114001.mp3",
      },
      {
        id: 2,
        surah: 17,
        verse: 1,
        exampleText: "إِنَّهُ",
        explanation:
          'The letter Noon (ن) with shaddah in "إِنَّهُ" produces a clear nasal sound (Ghunnah) held for two counts.',
        keyWords: ["إِنَّهُ"],
        wordPositions: [18],
        start_ms: 20520,
        end_ms: 21590,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/017001.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 29,
        exampleText: "ثُمَّ",
        explanation:
          'The letter Meem (م) with shaddah in "ثُمَّ" (from "ثُمَّ ٱسْتَوَىٰٓ إِلَى ٱلسَّمَآءِ") requires Ghunnah, with the sound resonating in the nasal cavity for two counts.',
        keyWords: ["ثُمَّ"], // Corrected,
        // start_ms and end_ms removed; script will populate
        wordPositions: [9],

        start_ms: 5080,

        end_ms: 7020,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002029.mp3",
      },
      {
        id: 4,
        surah: 93,
        verse: 11,
        exampleText: "وَأَمَّا",
        explanation:
          'The letter Meem (م) with shaddah in "وَأَمَّا" requires a pronounced nasal sound (Ghunnah) for two counts.',
        keyWords: ["وَأَمَّا"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 1710,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/093011.mp3",
      },
      {
        id: 5,
        surah: 114,
        verse: 6,
        exampleText: "الْجِنَّةِ", // Corrected to match Uthmani script more precisely for clarity
        explanation:
          'The letter Noon (ن) with shaddah in "الْجِنَّةِ" (from "مِنَ الْجِنَّةِ وَالنَّاسِ") requires Ghunnah for two counts.',
        keyWords: ["الْجِنَّةِ"], // Corrected
        wordPositions: [2],
        start_ms: 390,
        end_ms: 2580,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/114006.mp3",
      },
      {
        id: 6,
        surah: 78,
        verse: 1,
        exampleText: "عَمَّ",
        explanation:
          'The letter Meem (م) with shaddah in "عَمَّ" (from "عَمَّ يَتَسَاءَلُونَ") requires Ghunnah for two counts.',
        keyWords: ["عَمَّ"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 1240,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/078001.mp3",
      },
      {
        id: 7,
        surah: 20,
        verse: 14,
        exampleText: "إِنَّنِي",
        explanation:
          'The letter Noon (ن) with shaddah in "إِنَّنِي" requires a clear Ghunnah sound for two full counts.',
        keyWords: ["إِنَّنِي"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 3280,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/020014.mp3",
      },
      {
        id: 8,
        surah: 49,
        verse: 12,
        exampleText: "ٱلظَّنِّ",
        explanation:
          'The letter Noon (ن) with shaddah in "الظَّنِّ" (from "إِنَّ بَعْضَ الظَّنِّ إِثْمٌ") requires Ghunnah for two counts.',
        keyWords: ["الظَّنِّ"],
        wordPositions: [7],
        start_ms: 22650,
        end_ms: 23670,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/049012.mp3",
      },
      {
        id: 9,
        surah: 4,
        verse: 93,
        exampleText: "جَهَنَّمُ",
        explanation:
          'The letter Noon (ن) with shaddah in "جَهَنَّمُ" (from "فَجَزَآؤُهُۥ جَهَنَّمُ") requires Ghunnah for two counts.',
        keyWords: ["جَهَنَّمُ"],
        wordPositions: [6], // Corrected
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/004093.mp3",
        start_ms: 9540,
        end_ms: 11220,
      },
      {
        id: 10,
        surah: 2,
        verse: 33,
        exampleText: "فَلَمَّآ",
        explanation:
          'The letter Meem (م) with shaddah in "فَلَمَّا" (from "فَلَمَّآ أَنۢبَأَهُم بِأَسْمَآئِهِمْ") requires Ghunnah for two counts.',
        keyWords: ["فَلَمَّا"], // Corrected based on QUL-like segmentation,
        // start_ms and end_ms removed; script will populate
        wordPositions: [5],

        start_ms: 8860,

        end_ms: 10400,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002033.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Lām in the name of Allah  (merged tafkhīm + tarqīq)
  // -------------------------------------------------------------------
  "laam-allah": {
    description:
      "Special rule for pronouncing the Laam in the word 'Allah' (الله), which is pronounced either thick (Tafkheem) or thin (Tarqeeq) depending on the preceding vowel",
    arabicName: "لام الجلالة",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 1,
        exampleText: "بِسْمِ اللَّهِ",
        explanation:
          'The Laam in "اللَّهِ" is pronounced lightly (Tarqeeq) because it is preceded by a letter with Kasra (the "مِ" in "بِسْمِ").',
        keyWords: ["بِسْمِ", "ٱللَّهِ"], // Matched Uthmani for Allah
        wordPositions: [1, 2],
        start_ms: 60,
        end_ms: 1310,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001001.mp3",
      },
      {
        id: 2,
        surah: 2,
        verse: 7,
        exampleText: "خَتَمَ اللَّهُ",
        explanation:
          'The Laam in "اللَّهُ" is pronounced heavily (Tafkheem) because it is preceded by a letter with Fatha (the "مَ" in "خَتَمَ").',
        keyWords: ["خَتَمَ", "ٱللَّهُ"], // Matched Uthmani for Allah,
        wordPositions: [1, 2],

        start_ms: 140,

        end_ms: 1390,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002007.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 23,
        exampleText: "دُونِ ٱللَّهِ",
        explanation:
          'The Laam in "اللَّهِ" is pronounced lightly (Tarqeeq) because it is preceded by a letter with Kasra (the "نِ" in "دُونِ" from \'مِّن دُونِ ٱللَّهِ\').',
        keyWords: ["دُونِ", "ٱللَّهِ"], // Matched Uthmani for Allah // Corrected: "مِن" is word 8,
        // start_ms and end_ms removed; script will populate
        wordPositions: [16, 17],

        start_ms: 18000,

        end_ms: 19280,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002023.mp3",
      },
      {
        id: 4,
        surah: 2,
        verse: 27,
        exampleText: "عَهْدَ ٱللَّهِ",
        explanation:
          'The Laam in "اللَّهِ" is pronounced heavily (Tafkheem) because it is preceded by a letter with Fatha (the "دَ" in "عَهْدَ").',
        keyWords: ["عَهْدَ", "ٱللَّهِ"], // Matched Uthmani for Allah,
        wordPositions: [3, 4],

        start_ms: 2850,

        end_ms: 4210,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002027.mp3",
      },
      {
        id: 5,
        surah: 3,
        verse: 18,
        exampleText: "شَهِدَ ٱللَّهُ",
        explanation:
          'The Laam in "اللَّهُ" is pronounced heavily (Tafkheem) because it is preceded by a letter with Fatha (the "دَ" in "شَهِدَ").',
        keyWords: ["شَهِدَ", "ٱللَّهُ"], // Matched Uthmani for Allah
        wordPositions: [1, 2],
        start_ms: 150,
        end_ms: 1650,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/003018.mp3",
      },
      {
        id: 6,
        surah: 3,
        verse: 45,
        exampleText: "إِنَّ ٱللَّهَ",
        explanation:
          'The Laam in "اللَّهَ" is pronounced heavily (Tafkheem) because it is preceded by a letter with Fatha (the "نَّ" in "إِنَّ").',
        keyWords: ["إِنَّ", "ٱللَّهَ"], // Matched Uthmani for Allah
        wordPositions: [5, 6],
        start_ms: 5420,
        end_ms: 7220,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/003045.mp3",
      },
      {
        id: 7,
        surah: 22,
        verse: 78,
        exampleText: "فِى ٱللَّهِ",
        explanation:
          'The Laam in "اللَّهِ" is pronounced lightly (Tarqeeq) because it is preceded by the Kasra of "فِي".',
        keyWords: ["فِي", "ٱللَّهِ"], // Matched Uthmani for Allah
        wordPositions: [2, 3],
        start_ms: 1460,
        end_ms: 2650,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/022078.mp3",
      },
      {
        id: 8,
        surah: 5,
        verse: 4,
        exampleText: "ٱسْمَ ٱللَّهِ",
        explanation:
          'The Laam in "اللَّهِ" is pronounced heavily (Tafkheem) because it is preceded by a letter with Fatha (the "مَ" in "اسْمَ" from \'وَٱذْكُرُوا۟ ٱسْمَ ٱللَّهِ عَلَيْهِ\').',
        keyWords: ["ٱسْمَ", "ٱللَّهِ"], // Matched Uthmani for both
        wordPositions: [23, 24], // Assuming "وَٱذْكُرُوا۟" is word 14
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/005004.mp3",
        start_ms: 29260,
        end_ms: 30630,
      },
      {
        id: 9,
        surah: 2,
        verse: 115,
        exampleText: "وَلِلَّهِ", // Corrected to be a single word as per Uthmani script
        explanation:
          "The Laam in 'لِلَّهِ' (from 'وَلِلَّهِ ٱلْمَشْرِقُ وَٱلْمَغْرِبُ') is pronounced lightly (Tarqeeq) because it is preceded by the implicit Kasra on the first Laam of the merged word.",
        keyWords: ["وَلِلَّهِ"], // Corrected to be a single word,
        wordPositions: [1],

        start_ms: 150,

        end_ms: 1290,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002115.mp3",
      },
      {
        id: 10,
        surah: 112,
        verse: 1,
        exampleText: "هُوَ ٱللَّهُ",
        explanation:
          'The Laam in "اللَّهُ" is pronounced heavily (Tafkheem) because it is preceded by a letter with Fatha (the "وَ" in "هُوَ").',
        keyWords: ["هُوَ", "ٱللَّهُ"], // Matched Uthmani for Allah
        wordPositions: [2, 3],
        start_ms: 400,
        end_ms: 1640,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/112001.mp3",
      },
    ],
  },

  // Qamariyyah / Shamsiyyah left untouched
  "laam-qamariyyah": {
    description:
      "When the definite article 'Al' (ال) is followed by one of the 14 Moon Letters, the Laam is pronounced clearly without assimilation",
    arabicName: "لام قمرية",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 2,
        exampleText: "ٱلْحَمْدُ",
        explanation:
          "The Laam in the definite article 'ال' is pronounced clearly because it is followed by Haa (ح), one of the Moon Letters.",
        keyWords: ["ٱلْحَمْدُ"],
        wordPositions: [1],
        start_ms: 80,
        end_ms: 960,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001002.mp3",
      },
      {
        id: 2,
        surah: 2,
        verse: 177,
        exampleText: "ٱلْبِرَّ",
        explanation:
          "The Laam in the definite article 'ال' in 'الْبِرَّ' is pronounced clearly because it is followed by Baa (ب), one of the Moon Letters.",
        keyWords: ["ٱلْبِرَّ"],
        wordPositions: [2],

        start_ms: 620,

        end_ms: 1330,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002177.mp3",
      },
      {
        id: 3,
        surah: 96,
        verse: 2,
        exampleText: "ٱلْإِنسَـٰنَ",
        explanation:
          "The Laam in the definite article 'ال' in 'ٱلْإِنسَٰنَ' (from 'خَلَقَ ٱلْإِنسَٰنَ') is pronounced clearly because it is followed by Hamza (إ), one of the Moon Letters.",
        keyWords: ["ٱلْإِنسَٰنَ"],
        wordPositions: [2],
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/096002.mp3",
        start_ms: 720,
        end_ms: 2710,
      },
      {
        id: 4,
        surah: 5,
        verse: 3,
        exampleText: "ٱلْيَوْمَ",
        explanation:
          "The Laam in the definite article 'ال' in 'ٱلْيَوْمَ' is pronounced clearly because it is followed by Yaa (ي), one of the Moon Letters.",
        keyWords: ["ٱلْيَوْمَ"],
        wordPositions: [31],
        start_ms: 3500,
        end_ms: 4330,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/005003.mp3",
      },
      {
        id: 5,
        surah: 54,
        verse: 1,
        exampleText: "ٱلْقَمَرُ",
        explanation:
          "The Laam in the definite article 'ال' in 'ٱلْقَمَرُ' is pronounced clearly because it is followed by Qaf (ق), one of the Moon Letters.",
        keyWords: ["ٱلْقَمَرُ"],
        wordPositions: [4],
        start_ms: 2520,
        end_ms: 4380,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/054001.mp3",
      },
      {
        id: 6,
        surah: 103,
        verse: 1,
        exampleText: "وَٱلْعَصْرِ",
        explanation:
          "The Laam in the definite article 'ال' (in 'وَٱلْعَصْرِ') is pronounced clearly because it is followed by 'Ayn (ع), one of the Moon Letters.",
        keyWords: ["وَٱلْعَصْرِ"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 1230,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/103001.mp3",
      },
      {
        id: 7,
        surah: 2,
        verse: 3,
        exampleText: "بِٱلْغَيْبِ",
        explanation:
          "The Laam in the definite article 'ال' (in 'بِٱلْغَيْبِ') is pronounced clearly because it is followed by Ghayn (غ), one of the Moon Letters.",
        keyWords: ["بِٱلْغَيْبِ"],
        wordPositions: [3],

        start_ms: 2130,

        end_ms: 3070,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002003.mp3",
      },
      {
        id: 8,
        surah: 87,
        verse: 1,
        exampleText: "ٱلْأَعْلَى",
        explanation:
          "The Laam in the definite article 'ال' in 'ٱلْأَعْلَىٰ' is pronounced clearly because it is followed by Hamza (أ), one of the Moon Letters.",
        keyWords: ["ٱلْأَعْلَىٰ"],
        wordPositions: [4],
        start_ms: 2090,
        end_ms: 3440,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/087001.mp3",
      },
      {
        id: 9,
        surah: 2,
        verse: 120,
        exampleText: "ٱلنَّصَـٰرَىٰ",
        explanation:
          "The Laam in the definite article 'ال' in 'ٱلْهُدَىٰ' (from 'إِنَّ هُدَى ٱللَّهِ هُوَ ٱلْهُدَىٰ') is pronounced clearly because it is followed by Haa (ه), one of the Moon Letters.",
        keyWords: ["ٱلْهُدَىٰ"],
        wordPositions: [6],

        start_ms: 5460,

        end_ms: 6450,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002120.mp3",
      },
      {
        id: 10,
        surah: 2,
        verse: 126,
        exampleText: "الْآخِرِ",
        explanation:
          "The Laam in the definite article 'ال' is pronounced clearly because it is followed by Alif/Hamza (آ) (from 'وَٱلْيَوْmِ ٱلْـَٔاخِرِ'), one of the Moon Letters.",
        keyWords: ["ٱلْـَٔاخِرِ"],
        wordPositions: [18],

        start_ms: 15040,

        end_ms: 16360,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002126.mp3",
      },
    ],
  },
  "laam-shamsiyyah": {
    description:
      "When the definite article 'Al' (ال) is followed by one of the 14 Sun Letters, the Laam is not pronounced and instead the following letter is doubled with shaddah",
    arabicName: "لام شمسية",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 1,
        exampleText: "ٱلرَّحْمَـٰنِ ",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Raa (ر) which is doubled with shaddah, as Raa is one of the Sun Letters.",
        keyWords: ["ٱلرَّحْمَٰنِ"],
        wordPositions: [3],
        start_ms: 1320,
        end_ms: 2450,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001001.mp3",
      },
      {
        id: 2,
        surah: 1,
        verse: 7,
        exampleText: "ٱلضَّآلِّينَ",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Daad (ض) which is doubled with shaddah, as Daad is one of the Sun Letters.",
        keyWords: ["ٱلضَّآلِّينَ"],
        wordPositions: [9],
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001007.mp3",
        start_ms: 6640,
        end_ms: 12320,
      },
      {
        id: 3,
        surah: 93,
        verse: 1,
        exampleText: "وَٱلضُّحَىٰ",
        explanation:
          "The Laam in the definite article 'ال' (in وَالضُّحَىٰ) is assimilated into the Daad (ض) which is doubled with shaddah, as Daad is one of the Sun Letters.",
        keyWords: ["وَٱلضُّحَىٰ"],
        wordPositions: [1],
        start_ms: 60,
        end_ms: 1610,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/093001.mp3",
      },
      {
        id: 4,
        surah: 91,
        verse: 1,
        exampleText: "وَٱلشَّمْسِ",
        explanation:
          "The Laam in the definite article 'ال' (in وَالشَّمْسِ) is assimilated into the Sheen (ش) which is doubled with shaddah, as Sheen is one of the Sun Letters.",
        keyWords: ["وَٱلشَّمْسِ"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 1040,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/091001.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 185,
        exampleText: "ٱلشَّهْرَ",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Sheen (ش) which is doubled with shaddah, as Sheen is one of the Sun Letters.",
        keyWords: ["ٱلشَّهْرَ"],
        wordPositions: [16],

        start_ms: 18660,

        end_ms: 19390,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002185.mp3",
      },
      {
        id: 6,
        surah: 92,
        verse: 1,
        exampleText: "وَٱلَّيْلِ",
        explanation:
          "The Laam in the definite article 'ال' (in وَاللَّيْلِ) is assimilated into the Laam (ل) which is doubled with shaddah, as Laam is one of the Sun Letters.",
        keyWords: ["وَٱللَّيْلِ"],
        wordPositions: [1],

        start_ms: 30,

        end_ms: 1020,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/092001.mp3",
      },
      {
        id: 7,
        surah: 114,
        verse: 1,
        exampleText: "ٱلنَّاسِ",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Noon (ن) which is doubled with shaddah, as Noon is one of the Sun Letters.",
        keyWords: ["ٱلنَّاسِ"],
        wordPositions: [4],
        start_ms: 2060,
        end_ms: 5830,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/114001.mp3",
      },
      {
        id: 8,
        surah: 79,
        verse: 34,
        exampleText: "ٱلطَّآمَّةُ",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Tah (ط) which is doubled with shaddah, as Tah is one of the Sun Letters.",
        keyWords: ["ٱلطَّآمَّةُ"],
        wordPositions: [3],
        start_ms: 3330,
        end_ms: 7550,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/079034.mp3",
      },
      {
        id: 9,
        surah: 103,
        verse: 3,
        exampleText: "ٱلصَّـٰلِحَـٰتِ",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Saad (ص) which is doubled with shaddah, as Saad is one of the Sun Letters.",
        keyWords: ["ٱلصَّٰلِحَٰتِ"],
        wordPositions: [5],
        start_ms: 3750,
        end_ms: 5330,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/103003.mp3",
      },
      {
        id: 10,
        surah: 2,
        verse: 275,
        exampleText: "ٱلرِّبَوٰا۟",
        explanation:
          "The Laam in the definite article 'ال' is assimilated into the Raa (ر) which is doubled with shaddah, as Raa is one of the Sun Letters.",
        keyWords: ["ٱلرِّبَوٰا۟"],
        wordPositions: [3],

        start_ms: 2290,

        end_ms: 3030,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002275.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Madd  → compressed into Natural, Hamza-based, Sukūn-based
  // -------------------------------------------------------------------
  "madd-natural": {
    description:
      "Natural elongation of vowels when Fatha is followed by Alif (ا), Damma is followed by Waw Sakinah (و), or Kasra is followed by Ya Sakinah (ي), held for 2 counts, provided no Hamza or Sukoon follows the Madd letter.",
    arabicName: "مد طبيعي",
    examples: [
      {
        id: 1,
        surah: 2,
        verse: 30,
        exampleText: "قَالَ",
        explanation:
          "The Fatha on Qaf (ق) is followed by Alif (ا), creating a natural elongation of the vowel sound 'aa' for 2 counts.",
        keyWords: ["قَالَ"],
        wordPositions: [2],

        start_ms: 590,

        end_ms: 1140,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002030.mp3",
      },
      {
        id: 2,
        surah: 1,
        verse: 4,
        exampleText: "مَـٰلِكِ",
        explanation:
          "The Fatha on Meem (م) is followed by Alif (ا) in the reading 'مَٰلِكِ', creating a natural elongation of the vowel sound 'aa' for 2 counts.",
        keyWords: ["مَٰلِكِ"],
        wordPositions: [1],
        start_ms: 60,
        end_ms: 840,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001004.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 2,
        exampleText: "ذَٰلِكَ",
        explanation:
          "The Fatha on Dhal (ذ) is followed by a small Alif (dagger Alif), creating a natural elongation 'aa' for 2 counts.",
        keyWords: ["ذَٰلِكَ"],
        wordPositions: [1],

        start_ms: 150,

        end_ms: 860,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002002.mp3",
      },
      {
        id: 4,
        surah: 2,
        verse: 26,
        exampleText: "فَيَقُولُونَ",
        explanation:
          "The Damma on Laam (لُ) in 'فَيَقُولُونَ' is followed by Waw Sakinah (و), creating a natural elongation of the vowel sound 'oo' for 2 counts.",
        keyWords: ["فَيَقُولُونَ"],
        wordPositions: [23],

        start_ms: 23430,

        end_ms: 24780,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002026.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 8,
        exampleText: "يَقُولُ",
        explanation:
          "The Damma on Qaf (ق) in 'يَقُولُ' is followed by Waw Sakinah (و), creating a natural elongation of the vowel sound 'oo' for 2 counts.",
        keyWords: ["يَقُولُ"],
        wordPositions: [4],

        start_ms: 2890,

        end_ms: 3470,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002008.mp3",
      },
      {
        id: 6,
        surah: 71,
        verse: 1,
        exampleText: "نُوحًا",
        explanation:
          "The Damma on Noon (ن) in 'نُوحًا' is followed by Waw Sakinah (و), creating a natural elongation of the vowel sound 'oo' for 2 counts.",
        keyWords: ["نُوحًا"],
        wordPositions: [3],
        start_ms: 3860,
        end_ms: 4560,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/071001.mp3",
      },
      {
        id: 7,
        surah: 1,
        verse: 5,
        exampleText: "إِيَّاكَ",
        explanation:
          "The Kasra under Alif/Hamza (إ) in 'إِيَّاكَ' is followed by Ya Sakinah (ي), creating a natural elongation of the vowel sound 'ee' for 2 counts.",
        keyWords: ["إِيَّاكَ"],
        wordPositions: [1],
        start_ms: 30,
        end_ms: 970,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001005.mp3",
      },
      {
        id: 8,
        surah: 1,
        verse: 3,
        exampleText: "ٱلرَّحِيمِ",
        explanation:
          "The Kasra under Ha (ح) in 'ٱلرَّحِيمِ' is followed by Ya Sakinah (ي), creating a natural elongation of the vowel sound 'ee' for 2 counts.",
        keyWords: ["ٱلرَّحِيمِ"],
        wordPositions: [2],
        start_ms: 1240,
        end_ms: 4160,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001003.mp3",
      },
      {
        id: 9,
        surah: 1,
        verse: 4,
        exampleText: "ٱلدِّينِ",
        explanation:
          "The Kasra under Dal (د) in 'ٱلدِّينِ' is followed by Ya Sakinah (ي), creating a natural elongation of the vowel sound 'ee' for 2 counts.",
        keyWords: ["ٱلدِّينِ"],
        wordPositions: [3],
        start_ms: 1410,
        end_ms: 4280,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001004.mp3",
      },
      {
        id: 10,
        surah: 2,
        verse: 2,
        exampleText: "فِيهِ ۛ",
        explanation:
          "The Kasra under Fa (ف) in 'فِيهِ' is followed by Ya Sakinah (ي), creating a natural elongation of the vowel sound 'ee' for 2 counts.",
        keyWords: ["فِيهِ"],
        wordPositions: [5],

        start_ms: 2690,

        end_ms: 4980,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002002.mp3",
      },
    ],
  },
  "madd-hamza": {
    description:
      "Elongation of a madd letter when followed by a hamza (ء), either in the same word (Madd Muttasil) or in the next word (Madd Munfasil), held for 4-5 counts",
    arabicName: "مد متصل و مد منفصل",
    examples: [
      {
        id: 1,
        surah: 110,
        verse: 1,
        exampleText: "جَآءَ", // Matched common Uthmani
        explanation:
          "Madd Muttasil: The Alif madd letter is followed by hamza in the same word, requiring elongation for 4-5 counts.",
        keyWords: ["جَآءَ"], // Matched common Uthmani
        wordPositions: [2],
        start_ms: 690,
        end_ms: 3590,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/110001.mp3",
      },
      {
        id: 2,
        surah: 12,
        verse: 24,
        exampleText: "ٱلسُّوٓءَ", // Matched common Uthmani
        explanation:
          "Madd Muttasil: The Waw madd letter is followed by hamza in the same word, requiring elongation for 4-5 counts.",
        keyWords: ["ٱلسُّوٓءَ"], // Matched common Uthmani
        wordPositions: [14],
        start_ms: 14330,
        end_ms: 16700,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/012024.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 19,
        exampleText: "ٱلسَّمَآءِ", // Matched common Uthmani
        explanation:
          "Madd Muttasil: The Alif madd letter is followed by hamza in the same word, requiring elongation for 4-5 counts.",
        keyWords: ["ٱلسَّمَآءِ"], // Matched common Uthmani,
        wordPositions: [4],

        start_ms: 2520,

        end_ms: 4600,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002019.mp3",
      },
      {
        id: 4,
        surah: 2, // Surah Al-Baqarah
        verse: 142, // Verse 142
        exampleText: "يَشَآءُ",
        explanation:
          "Madd Muttasil: The Alif madd letter in 'يَشَآءُ' is followed by hamza in the same word, requiring elongation for 4-5 counts.",
        keyWords: ["يَشَآءُ"], // In 2:142, "يَشَآءُ" is the 18th word in "... يَهْدِى مَن يَشَآءُ إِلَىٰ صِرَٰطٍ مُّسْتَقِيمٍ",
        // start_ms and end_ms will be populated by your script
        wordPositions: [18],

        start_ms: 19690,

        end_ms: 21680,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002142.mp3",
      },
      {
        id: 5,
        surah: 12,
        verse: 25,
        exampleText: "سُوٓءًا", // Matched common Uthmani
        explanation:
          "Madd Muttasil: The Waw madd letter is followed by hamza in the same word, requiring elongation for 4-5 counts.",
        keyWords: ["سُوٓءًا"], // Matched common Uthmani
        wordPositions: [17],
        start_ms: 17120,
        end_ms: 18000,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/012025.mp3",
      },
      {
        id: 6,
        surah: 2,
        verse: 4,
        exampleText: "بِمَآ أُنزِلَ",
        explanation:
          'Madd Munfasil: The Alif madd letter at the end of "بِمَا" is followed by hamza at the beginning of "أُنزِلَ", requiring elongation for 4-5 counts.',
        keyWords: ["بِمَا", "أُنزِلَ"],
        wordPositions: [3, 4],

        start_ms: 2200,

        end_ms: 5250,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002004.mp3",
      },
      {
        id: 7,
        surah: 2,
        verse: 21,
        exampleText: "يَا أَيُّهَا",
        explanation:
          'Madd Munfasil: The Alif madd letter at the end of "يَا" is followed by hamza at the beginning of "أَيُّهَا", requiring elongation for 4-5 counts.',
        keyWords: ["يَٰٓ", "أَيُّهَا"], // Matched common Uthmani for "Ya",
        wordPositions: [1],

        start_ms: 200,

        end_ms: 2790,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002021.mp3",
      },
      {
        id: 8,
        surah: 108,
        verse: 1,
        exampleText: "إِنَّآ أَعْطَيْنَـٰكَ",
        explanation:
          'Madd Munfasil: The Alif madd letter at the end of "إِنَّا" is followed by hamza at the beginning of "أَعْطَيْنَاكَ", requiring elongation for 4-5 counts.',
        keyWords: ["إِنَّآ", "أَعْطَيْنَٰكَ"], // Matched common Uthmani
        wordPositions: [1, 2],
        start_ms: 150,
        end_ms: 4970,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/108001.mp3",
      },
      {
        id: 9,
        surah: 48,
        verse: 28,
        exampleText: " ٱلَّذِىٓ أَرْسَلَ",
        explanation:
          'Madd Munfasil: The Ya madd letter at the end of "الَّذِي" is followed by hamza at the beginning of "أَرْسَلَ", requiring elongation for 4-5 counts.',
        keyWords: ["ٱلَّذِيٓ", "أَرْسَلَ"], // Matched common Uthmani
        wordPositions: [2, 3],
        start_ms: 440,
        end_ms: 3840,
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/048028.mp3",
      },
      {
        id: 10,
        surah: 2,
        verse: 14, // Corrected verse
        exampleText: "قَالُوٓا۟ ءَامَنَّا",
        explanation:
          'Madd Munfasil: The Waw madd letter at the end of "قَالُوا" is followed by hamza at the beginning of "آمَنَّا", requiring elongation for 4-5 counts.',
        keyWords: ["قَالُوٓا۟", "ءَامَنَّا"], // Corrected positions for 2:14,
        // start_ms and end_ms will be populated by your script
        wordPositions: [5, 6],

        start_ms: 3020,

        end_ms: 6490,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002014.mp3",
      },
    ],
    practiceNotes:
      "Madd Hamza comes in two forms: Muttasil (connected) when the madd letter and hamza are in the same word, and Munfasil (separated) when the madd letter ends one word and hamza begins the next. Both are elongated for 4-5 counts depending on the recitation style.",
  },
  "madd-sukoon": {
    description:
      "Elongation that occurs when a madd letter is followed by a letter with sukoon, either permanent (Madd Lazim, 6 counts) or temporary when stopping (Madd Arid, 2-6 counts)",
    arabicName: "مد السكون (مد لازم و مد عارض للسكون)",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 7,
        exampleText: "ٱلضَّآلِّينَ",
        explanation:
          "Madd Lazim: The Alif madd letter is followed by Lam with shaddah (which contains an implicit sukoon), requiring 6 counts of elongation.",
        keyWords: ["الضَّالِّينَ"],
        wordPositions: [9],
        start_ms: 6640,
        end_ms: 12320,
      },
      {
        id: 2,
        surah: 79,
        verse: 34,
        exampleText: "ٱلطَّآمَّةُ",
        explanation:
          "Madd Lazim: The Alif madd letter is followed by Meem with shaddah (which contains an implicit sukoon), requiring 6 counts of elongation.",
        keyWords: ["الطَّامَّةُ"],
        wordPositions: [3],

        start_ms: 3330,

        end_ms: 7550,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/079034.mp3",
      },
      {
        id: 3,
        surah: 10,
        verse: 91,
        exampleText: "ءَآلْـَٔـٰنَ",
        explanation:
          "Madd Lazim: The Alif with madda is followed by Lam with sukoon, requiring 6 counts of elongation.",
        keyWords: ["آلْئَٰنَ"],
        wordPositions: [1],

        start_ms: 70,

        end_ms: 3990,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/010091.mp3",
      },
      {
        id: 4,
        surah: 69,
        verse: 1,
        exampleText: "ٱلْحَآقَّةُ",
        explanation:
          "Madd Lazim: The Alif madd letter is followed by Qaf with shaddah (which contains an implicit sukoon), requiring 6 counts of elongation.",
        keyWords: ["الْحَاقَّةُ"],
        wordPositions: [1],

        start_ms: 80,

        end_ms: 4370,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/069001.mp3",
      },
      {
        id: 5,
        surah: 6,
        verse: 38,
        exampleText: "دَآبَّةٍۢ",
        explanation:
          "Madd Lazim: The Alif madd letter is followed by Ba with shaddah (which contains an implicit sukoon), requiring 6 counts of elongation.",
        keyWords: ["دَابَّةٍ"],
        wordPositions: [3],

        start_ms: 2380,

        end_ms: 7130,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/006038.mp3",
      },
      {
        id: 6,
        surah: 1,
        verse: 3,
        exampleText: "ٱلرَّحِيمِ",
        explanation:
          "Madd Arid: When stopping on this word, the Ya madd letter is followed by Meem with temporary sukoon, allowing 2, 4, or 6 counts of elongation.",
        keyWords: ["الرَّحِيمِ"],
        wordPositions: [2],

        start_ms: 1240,

        end_ms: 4160,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001003.mp3",
      },
      {
        id: 7,
        surah: 1,
        verse: 5,
        exampleText: "نَسْتَعِينُ",
        explanation:
          "Madd Arid: When stopping on this word, the Ya madd letter is followed by Noon with temporary sukoon, allowing 2, 4, or 6 counts of elongation.",
        keyWords: ["نَسْتَعِينُ"],
        wordPositions: [4],

        start_ms: 2880,

        end_ms: 6290,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001005.mp3",
      },
      {
        id: 8,
        surah: 1,
        verse: 2,
        exampleText: "ٱلْعَـٰلَمِينَ",
        explanation:
          "Madd Arid: When stopping on this word, the Ya madd letter is followed by Noon with temporary sukoon, allowing 2, 4, or 6 counts of elongation.",
        keyWords: ["الْعَالَمِينَ"],
        wordPositions: [4],

        start_ms: 2470,

        end_ms: 5140,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001002.mp3",
      },
      {
        id: 9,
        surah: 2,
        verse: 20,
        exampleText: "قَدِيرٌ",
        explanation:
          "Madd Arid: When stopping on this word, the Ya madd letter is followed by Ra with temporary sukoon, allowing 2, 4, or 6 counts of elongation.",
        keyWords: ["قَدِيرٌ"],
        wordPositions: [25],

        start_ms: 27910,

        end_ms: 30120,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002020.mp3",
      },
      {
        id: 10,
        surah: 2,
        verse: 255,
        exampleText: "ٱلْعَظِيمُ",
        explanation:
          "Madd Arid: When stopping on this word, the Ya madd letter is followed by Meem with temporary sukoon, allowing 2, 4, or 6 counts of elongation.",
        keyWords: ["الْعَظِيمُ"],
        wordPositions: [50],

        start_ms: 49420,

        end_ms: 51590,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002255.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Tafkhīm / Tarqīq
  // -------------------------------------------------------------------
  "tafkheem-letters": {
    description:
      "Heavy or thick pronunciation of the seven emphatic letters (خُصَّ ضَغْطٍ قِظْ), which are always pronounced with tafkheem regardless of vowel",
    arabicName: "تفخيم الحروف",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 7,
        exampleText: "ٱلضَّآلِّينَ",
        explanation:
          "The letter Dhad (ض) is one of the seven emphatic letters that is always pronounced with tafkheem (heaviness/fullness).",
        keyWords: ["الضَّالِّينَ"],
        wordPositions: [9],
        start_ms: 6640,

        end_ms: 12320,
      },
      {
        id: 2,
        surah: 1,
        verse: 6,
        exampleText: "ٱلصِّرَٰطَ",
        explanation:
          "The letter Sad (ص) is one of the seven emphatic letters that is always pronounced with tafkheem, even with kasra.",
        keyWords: ["الصِّرَاطَ"],
        wordPositions: [2],

        start_ms: 680,

        end_ms: 1630,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001006.mp3",
      },
      {
        id: 3,
        surah: 85,
        verse: 20,
        exampleText: "مُّحِيطٌۢ",
        explanation:
          "The letter Tah (ط) is one of the seven emphatic letters that is always pronounced with tafkheem.",
        keyWords: ["مُّحِيطٌ"],
        wordPositions: [4],

        start_ms: 5900,

        end_ms: 8330,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/085020.mp3",
      },
      {
        id: 4,
        surah: 57,
        verse: 3,
        exampleText: "وَٱلظَّـٰهِرُ",
        explanation:
          "The letter Zha (ظ) is one of the seven emphatic letters that is always pronounced with tafkheem. (Original citation 1:3 was incorrect).",
        keyWords: ["الظَّاهِرُ"],
        wordPositions: [4],

        start_ms: 3850,

        end_ms: 5040,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/057003.mp3",
      },
      {
        id: 5,
        surah: 96,
        verse: 1,
        exampleText: "خَلَقَ",
        explanation:
          "The letter Kha (خ) is one of the seven emphatic letters that is always pronounced with tafkheem.",
        keyWords: ["خَلَقَ"],
        wordPositions: [5],

        start_ms: 3000,

        end_ms: 3600,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/096001.mp3",
      },
      {
        id: 6,
        surah: 1,
        verse: 7,
        exampleText: "غَيْرِ",
        explanation:
          "The letter Ghayn (غ) is one of the seven emphatic letters that is always pronounced with tafkheem. (Original citation 1:4 was incorrect).",
        keyWords: ["غَيْرِ"],
        wordPositions: [5],

        start_ms: 4220,

        end_ms: 5290,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001007.mp3",
      },
      {
        id: 7,
        surah: 112,
        verse: 1,
        exampleText: "قُلْ",
        explanation:
          "The letter Qaf (ق) is one of the seven emphatic letters that is always pronounced with tafkheem.",
        keyWords: ["قُلْ"],
        wordPositions: [1],

        start_ms: 30,

        end_ms: 390,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/112001.mp3",
      },
    ],
  },
  "tafkheem-ra": {
    description:
      "Heavy pronunciation of the letter Ra (ر) in specific conditions, giving it a fuller, deeper sound",
    arabicName: "تفخيم الراء",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 1,
        exampleText: "ٱلرَّحِيمِ",
        explanation:
          "The Ra (ر) with fatha (and shaddah) is pronounced with tafkheem (heaviness).",
        keyWords: ["الرَّحْمَٰنِ"],
        wordPositions: [4],

        start_ms: 1320,

        end_ms: 2450,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001001.mp3",
      },
      {
        id: 2,
        surah: 106,
        verse: 3,
        exampleText: "رَبَّ",
        explanation:
          "The Ra (ر) with fatha is pronounced with tafkheem (heaviness).",
        keyWords: ["رَبَّ"],
        wordPositions: [2],

        start_ms: 1410,

        end_ms: 2060,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/106003.mp3",
      },
      {
        id: 3,
        surah: 9,
        verse: 62,
        exampleText: "لِيُرْضُوكُمْ",
        explanation:
          "The Ra (ر) with sukoon preceded by damma in 'لِيُرْضُوكُمْ' is pronounced with tafkheem (heaviness). (Original example 'يُرْضِيكَ' and citation 93:5 were incorrect).",
        keyWords: ["لِيُرْضُوكُمْ"],
        wordPositions: [4],

        start_ms: 3210,

        end_ms: 4770,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/009062.mp3",
      },
      {
        id: 4,
        surah: 87,
        verse: 8,
        exampleText: "لِلْيُسْرَىٰ",
        explanation:
          "The Ra (ر) with fatha (represented by Alif Maqsurah at the end) is pronounced with tafkheem (heaviness).",
        keyWords: ["لِلْيُسْرَىٰ"],
        wordPositions: [2],

        start_ms: 1480,

        end_ms: 2960,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/087008.mp3",
      },
      {
        id: 5,
        surah: 108,
        verse: 2,
        exampleText: "وَٱنْحَرْ",
        explanation:
          "The Ra (ر) with sukoon (when stopping on the word) preceded by fatha is pronounced with tafkheem (heaviness).",
        keyWords: ["وَانْحَرْ"],
        wordPositions: [3],

        start_ms: 1980,

        end_ms: 2790,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/108002.mp3",
      },
    ],
  },
  "tarqeeq-ra": {
    description:
      "Light pronunciation of the letter Ra (ر) in specific conditions, giving it a thinner, softer sound",
    arabicName: "ترقيق الراء",
    examples: [
      {
        id: 1,
        surah: 56,
        verse: 77,
        exampleText: "كَرِيمٌۭ",
        explanation:
          "The Ra (ر) with kasra in 'كَرِيمٌ' is pronounced with tarqeeq (lightness). (Original citation 1:4 was incorrect).",
        keyWords: ["كَرِيمٌ"],
        wordPositions: [3],

        start_ms: 4330,

        end_ms: 7260,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/056077.mp3",
      },
      {
        id: 2,
        surah: 103,
        verse: 2,
        exampleText: "خُسْرٍ",
        explanation:
          "The Ra (ر) with kasra tanween (effectively a kasra) is pronounced with tarqeeq (lightness).",
        keyWords: ["خُسْرٍ"],
        wordPositions: [4],

        start_ms: 4440,

        end_ms: 4900,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/103002.mp3",
      },
      {
        id: 3,
        surah: 80,
        verse: 37,
        exampleText: "ٱمْرِئٍۢ",
        explanation:
          "The Ra (ر) with kasra is pronounced with tarqeeq (lightness).",
        keyWords: ["امْرِئٍ"],
        wordPositions: [2],

        start_ms: 940,

        end_ms: 1330,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/080037.mp3",
      },
      {
        id: 4,
        surah: 18,
        verse: 107,
        exampleText: "ٱلْفِرْدَوْسِ",
        explanation:
          "The Ra (ر) with sukoon in 'الْفِرْدَوْسِ' preceded by an original kasra (on the Fa) is pronounced with tarqeeq (lightness). (Original citation 2:25 was incorrect).",
        keyWords: ["الْفِرْدَوْسِ"],
        wordPositions: [9],

        start_ms: 7330,

        end_ms: 8990,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/018107.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 20,
        exampleText: "قَدِيرٌۭ",
        explanation:
          "When stopping on 'قَدِيرٌ', the Ra (ر) receives a temporary sukoon and is preceded by a Ya Sakinah (ـِيْ), so it is pronounced with tarqeeq (lightness). (Replacing original example 'نَصْرُ' which was Tafkheem).",
        keyWords: ["قَدِيرٌ"],
        wordPositions: [25],

        start_ms: 27910,

        end_ms: 30120,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002020.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Hamzah
  // -------------------------------------------------------------------
  "hamzat-wasl": {
    description:
      "A connecting hamza (ٱ) that is only pronounced at the beginning of speech and dropped when connected to the previous word",
    arabicName: "همزة الوصل",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 1,
        exampleText: "بِسْمِ ٱللَّهِ",
        explanation:
          'The hamza in "اللَّهِ" is a hamzat wasl (connecting hamza) and is not pronounced because it is connected to the previous word.',
        keyWords: ["بِسْمِ", "اللَّهِ"],
        wordPositions: [1, 2],

        start_ms: 60,

        end_ms: 1310,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001001.mp3",
      },
      {
        id: 2,
        surah: 1,
        verse: 2,
        exampleText: "ٱلْحَمْدُ",
        explanation:
          'When starting recitation with "الْحَمْدُ", the hamzat wasl at the beginning is pronounced. When it follows another word, it is dropped.',
        keyWords: ["الْحَمْدُ"],
        wordPositions: [1],

        start_ms: 80,

        end_ms: 960,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001002.mp3",
      },
      {
        id: 3,
        surah: 1,
        verse: 4,
        exampleText: "مَـٰلِكِ يَوْمِ",
        explanation:
          "The hamza in \"اليَوْمِ\" (from 'مَـٰلِكِ يَوْمِ ٱلدِّينِ', where 'يَوْمِ' is followed by 'ٱلدِّينِ' whose Hamzatul Wasl is dropped) is a hamzat wasl and is not pronounced because it is connected to the previous word. Or more accurately, if it were 'مَالِكِ الْيَوْمِ', the Hamzatul Wasl of 'الْيَوْمِ' would be dropped after 'مَالِكِ'.",
        keyWords: ["مَالِكِ", "اليَوْمِ"],
        wordPositions: [1, 2],

        start_ms: 60,

        end_ms: 1400,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001004.mp3",
      },
      {
        id: 4,
        surah: 19,
        verse: 12,
        exampleText: "خُذِ ٱلْكِتَـٰبَ",
        explanation:
          'The hamza in "الْكِتَابَ" is a hamzat wasl and is not pronounced because it is connected to the previous word "خُذِ".',
        keyWords: ["خُذِ", "الْكِتَابَ"],
        wordPositions: [2, 3],

        start_ms: 1430,

        end_ms: 3060,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/019012.mp3",
      },
      {
        id: 5,
        surah: 96,
        verse: 1,
        exampleText: "ٱقْرَأْ",
        explanation:
          'The hamza in "اقْرَأْ" is a hamzat wasl. When starting recitation with this word, the hamza is pronounced with kasra.',
        keyWords: ["اقْرَأْ"],
        wordPositions: [1],

        start_ms: 190,

        end_ms: 720,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/096001.mp3",
      },
      {
        id: 6,
        surah: 1,
        verse: 6,
        exampleText: "ٱهْدِنَا",
        explanation:
          'The hamza in "اهْدِنَا" is a hamzat wasl. When starting recitation with this word, the hamza is pronounced with kasra.',
        keyWords: ["اهْدِنَا"],
        wordPositions: [1],

        start_ms: 30,

        end_ms: 670,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001006.mp3",
      },
      {
        id: 7,
        surah: 7,
        verse: 199,
        exampleText: "خُذِ ٱلْعَفْوَ",
        explanation:
          'The hamza in "الْعَفْوَ" is a hamzat wasl and is not pronounced when connected to the previous word "خُذِ".',
        keyWords: ["خُذِ", "الْعَفْوَ"],
        wordPositions: [1, 2],

        start_ms: 50,

        end_ms: 1360,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/007199.mp3",
      },
    ],
  },
  "hamzat-qat": {
    description:
      "A cutting hamza (أ/إ/ء) that is always pronounced whether at the beginning, middle, or end of speech",
    arabicName: "همزة القطع",
    examples: [
      {
        id: 1,
        surah: 112,
        verse: 1,
        exampleText: "أَحَدٌ",
        explanation:
          'The hamza in "أَحَدٌ" is a hamzat qat (cutting hamza) and is always pronounced, regardless of its position in speech.',
        keyWords: ["أَحَدٌ"],
        wordPositions: [4],

        start_ms: 1650,

        end_ms: 2300,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/112001.mp3",
      },
      {
        id: 2,
        surah: 1,
        verse: 5,
        exampleText: "إِيَّاكَ",
        explanation:
          'The hamza in "إِيَّاكَ" is a hamzat qat with kasra and is always pronounced in all positions of speech.',
        keyWords: ["إِيَّاكَ"],
        wordPositions: [1],

        start_ms: 30,

        end_ms: 970,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001005.mp3",
      },
      {
        id: 3,
        surah: 93,
        verse: 7,
        exampleText: "ضَآلًّۭا",
        explanation:
          'The word "ضَالًّا" does not begin with a hamza, contrasting with words that do have hamzat qat.',
        keyWords: ["ضَالًّا"],
        wordPositions: [2],

        start_ms: 1020,

        end_ms: 5520,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/093007.mp3",
      },
      {
        id: 4,
        surah: 2,
        verse: 2,
        exampleText: "رَيْبَ ۛ",
        explanation:
          'The word "رَيْبَ" does not begin with hamza, showing the distinction between words with and without hamzat qat.',
        keyWords: ["رَيْبَ"],
        wordPositions: [4],

        start_ms: 2140,

        end_ms: 2680,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002002.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 13,
        exampleText: "ٱلسُّفَهَآءُ",
        explanation:
          'The hamza in "السُّفَهَاءُ" is a hamzat qat that appears at the end of the word and is always pronounced. (Original citation 2:144 was incorrect).',
        keyWords: ["السُّفَهَاءُ"],
        wordPositions: [16],

        start_ms: 18560,

        end_ms: 21040,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002013.mp3",
      },
      {
        id: 6,
        surah: 2,
        verse: 186,
        exampleText: "سَأَلَكَ",
        explanation:
          'The hamza in the middle of "سَأَلَكَ" is a hamzat qat and is always pronounced clearly. (Original citation 2:233 was incorrect).',
        keyWords: ["سَأَلَكَ"],
        wordPositions: [2],

        start_ms: 870,

        end_ms: 1660,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002186.mp3",
      },
      {
        id: 7,
        surah: 2,
        verse: 275,
        exampleText: "ٱلرِّبَوٰا۟",
        explanation:
          'The alif at the end of "الرِّبَا" is not a hamza but a regular alif (Alif Maqsurah), demonstrating the difference between an alif and a hamzat qat.',
        keyWords: ["الرِّبَا"],
        wordPositions: [3],

        start_ms: 2290,

        end_ms: 3030,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002275.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Waqf  – collapsed labels kept, detailed types intact
  // -------------------------------------------------------------------
  "waqf-taam": {
    description:
      "A complete stop where the meaning is completely finished and has no grammatical or meaning connection with what follows",
    arabicName: "الوقف التام",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 3, // This is the first verse (where the stop occurs)
        exampleText: "ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ ۝ مَـٰلِكِ يَوْمِ ٱلدِّينِ", // Verse 1:3 and 1:4
        explanation:
          'Stopping after "ٱلرَّحِيمِ" (end of verse 1:3) is waqf taam because it completes the description of Allah, and what follows ("مَـٰلِكِ يَوْمِ ٱلدِّينِ", verse 1:4) is a new attribute.',
        keyWords: ["ٱلرَّحِيمِ"], // Keyword at the end of the first verse
        wordPositions: [2], // Position of "ٱلرَّحِيمِ" in verse 1:3
        // audio_url for 1:3 will be populated by script. Timestamps for "ٱلرَّحِيمِ" also by script.
        // App will play full 1:3 then full 1:4.
        nextVerseToPlay: { surah: 1, verse: 4 },
      },
      {
        id: 2,
        surah: 2,
        verse: 1, // First verse
        exampleText:
          "الٓمٓ ۝ ذَٰلِكَ ٱلْكِتَٰبُ لَا رَيْبَ ۛ فِيهِ ۛ هُدًى لِّلْمُتَّقِينَ", // Verse 2:1 and 2:2
        explanation:
          'Stopping after "الٓمٓ" (verse 2:1) is waqf taam because these disconnected letters form a complete unit, and what follows (verse 2:2) is the beginning of a new statement.',
        keyWords: ["الٓمٓ"],
        wordPositions: [1],
        // audio_url for 2:1, start_ms, end_ms for "الٓمٓ" by script.
        // App will play full 2:1 then full 2:2.
        nextVerseToPlay: { surah: 2, verse: 2 },
      },
      {
        id: 3,
        surah: 112,
        verse: 1, // First verse
        exampleText: "قُلْ هُوَ ٱللَّهُ أَحَدٌ ۝ ٱللَّهُ ٱلصَّمَدُ", // Verse 112:1 and 112:2
        explanation:
          'Stopping after "أَحَدٌ" (end of verse 112:1) is waqf taam because it completes the statement of Allah\'s Oneness, and the next verse (112:2) introduces a new attribute.',
        keyWords: ["أَحَدٌ"],
        wordPositions: [4],
        // audio_url for 112:1, start_ms, end_ms for "أَحَدٌ" by script.
        // App will play full 112:1 then full 112:2.
        nextVerseToPlay: { surah: 112, verse: 2 },
      },
      {
        id: 4,
        surah: 103,
        verse: 2, // First verse
        exampleText:
          "إِنَّ ٱلْإِنسَٰنَ لَفِى خُسْرٍ ۝ إِلَّا ٱلَّذِينَ ءَامَنُوا۟ وَعَمِلُوا۟ ٱلصَّـٰلِحَٰتِ وَتَوَاصَوْا۟ بِٱلْحَقِّ وَتَوَاصَوْا۟ بِٱلصَّبْرِ", // Verse 103:2 and 103:3 (full)
        explanation:
          'Stopping after "خُسْرٍ" (end of verse 103:2) is waqf taam because it completes the statement about mankind\'s loss, and what follows (verse 103:3) is an exception that starts a new grammatical structure.',
        keyWords: ["خُسْرٍ"],
        wordPositions: [4],
        // audio_url for 103:2, start_ms, end_ms for "خُسْرٍ" by script.
        // App will play full 103:2 then full 103:3.
        nextVerseToPlay: { surah: 103, verse: 3 },
      },
      {
        id: 5,
        surah: 1,
        verse: 4, // First verse
        exampleText:
          "مَـٰلِكِ يَوْمِ ٱلدِّينِ ۝ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ", // Verse 1:4 and 1:5
        explanation:
          'Stopping after "ٱلدِّينِ" (end of verse 1:4) is waqf taam because it completes the description of Allah, and what follows (verse 1:5) is a shift to direct address.',
        keyWords: ["ٱلدِّينِ"],
        wordPositions: [3],
        // audio_url for 1:4, start_ms, end_ms for "ٱلدِّينِ" by script.
        // App will play full 1:4 then full 1:5.
        nextVerseToPlay: { surah: 1, verse: 5 },
      },
    ],
  },
  "waqf-kaafi": {
    description:
      "A sufficient stop where the meaning is complete but has some connection with what follows in meaning (not grammar)",
    arabicName: "الوقف الكافي",
    examples: [
      {
        id: 1,
        surah: 2,
        verse: 5,
        exampleText:
          "أُو۟لَـٰٓئِكَ عَلَىٰ هُدًۭى مِّن رَّبِّهِمْ ۖ وَأُو۟لَـٰٓئِكَ هُمُ ٱلْمُفْلِحُونَ",
        explanation:
          'Stopping after "رَّبِّهِمْ" is waqf kaafi because the statement is complete, but what follows is connected in meaning as it describes the same group of people.',
        keyWords: ["رَّبِّهِمْ"],
        wordPositions: [5],

        start_ms: 70,

        end_ms: 10970,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002005.mp3",
      },
      {
        id: 2,
        surah: 2,
        verse: 196,
        exampleText:
          "وَأَتِمُّوا الْحَجَّ وَالْعُمْرَةَ لِلَّهِ ۚ فَإِنْ أُحْصِرْتُمْ فَمَا اسْتَيْسَرَ مِنَ الْهَدْيِ",
        explanation:
          'Stopping after "لِلَّهِ" is waqf kaafi because it completes the command to perform Hajj and Umrah, while what follows is a related but new ruling about being prevented from completing them.',
        keyWords: ["لِلَّهِ"],
        wordPositions: [10],

        start_ms: 110,

        end_ms: 10130,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002196.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 25,
        exampleText:
          "وَبَشِّرِ الَّذِينَ آمَنُوا وَعَمِلُوا الصَّالِحَاتِ أَنَّ لَهُمْ جَنَّاتٍ تَجْرِي مِن تَحْتِهَا الْأَنْهَارُ ۖ كُلَّمَا رُزِقُوا مِنْهَا مِن ثَمَرَةٍ رِّزْقًا",
        explanation:
          'Stopping after "الْأَنْهَارُ" is waqf kaafi because it completes the description of the gardens, while what follows adds more details about the same gardens.',
        keyWords: ["الْأَنْهَارُ"],
        wordPositions: [14],

        start_ms: 90,

        end_ms: 19310,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002025.mp3",
      },
      {
        id: 4,
        surah: 1,
        verse: 5,
        exampleText: "إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ",
        explanation:
          'Stopping after "نَعْبُدُ" is waqf kaafi because the statement of worship is complete, while what follows is related but shifts to seeking help.',
        keyWords: ["نَعْبُدُ"],
        wordPositions: [2],

        start_ms: 30,

        end_ms: 6290,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001005.mp3",
      },
      {
        id: 5,
        surah: 2,
        verse: 185,
        exampleText:
          "شَهْرُ رَمَضَانَ الَّذِي أُنزِلَ فِيهِ الْقُرْآنُ هُدًى لِّلنَّاسِ وَبَيِّنَاتٍ مِّنَ الْهُدَىٰ وَالْفُرْقَانِ ۚ فَمَن شَهِدَ مِنكُمُ الشَّهْرَ فَلْيَصُمْهُ",
        explanation:
          'Stopping after "وَالْفُرْقَانِ" is waqf kaafi because it completes the description of the Quran, while what follows shifts to rules about fasting in the same month.',
        keyWords: ["وَالْفُرْقَانِ"],
        wordPositions: [15],

        start_ms: 100,

        end_ms: 21060,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002185.mp3",
      },
    ],
  },
  "waqf-hasan": {
    description:
      "A good stop where the meaning is grammatically complete but still has a strong connection with what follows in both meaning and grammar",
    arabicName: "الوقف الحسن",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 2,
        exampleText: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
        explanation:
          'Stopping after "لِلَّهِ" is waqf hasan because the basic meaning "All praise is for Allah" is complete, but what follows "رَبِّ الْعَالَمِينَ" is an essential description (attribute) of Allah and grammatically connected.',
        keyWords: ["لِلَّهِ"],
        wordPositions: [1],

        start_ms: 80,

        end_ms: 5140,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001002.mp3",
      },
      {
        id: 2,
        surah: 2,
        verse: 2,
        exampleText:
          "ذَٰلِكَ الْكِتَابُ لَا رَيْبَ ۛ فِيهِ ۛ هُدًى لِّلْمُتَّقِينَ",
        explanation:
          'Stopping after "الْكِتَابُ" is waqf hasan because "That is the Book" is a complete statement, but what follows "لَا رَيْبَ ۛ فِيهِ" is strongly connected as it describes the Book.',
        keyWords: ["الْكِتَابُ"],
        wordPositions: [2],

        start_ms: 150,

        end_ms: 8610,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002002.mp3",
      },
      {
        id: 3, // New example, within a single verse
        surah: 2,
        verse: 177,
        exampleText:
          "وَلَـٰكِنَّ ٱلْبِرَّ مَنْ ءَامَنَ بِٱللَّهِ وَٱلْيَوْمِ ٱلْءَاخِرِ وَٱلْمَلَـٰٓئِكَةِ وَٱلْكِتَـٰبِ وَٱلنَّبِيِّـۧنَ وَءَاتَى ٱلْمَالَ عَلَىٰ حُبِّهِۦ...",
        explanation:
          'Stopping after "ٱلنَّبِيِّـۧنَ" (the Prophets) is Waqf Hasan. The list of items of belief is grammatically complete. What follows ("وَءَاتَى ٱلْمَالَ...") introduces acts of righteousness, which are strongly connected in meaning but start a new clause.',
        keyWords: ["ٱلنَّبِيِّـۧنَ"],
        wordPositions: [9],
        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002177.mp3",
        start_ms: 6920,

        end_ms: 30630,
      },
      {
        id: 4,
        surah: 2,
        verse: 26,
        exampleText:
          "إِنَّ اللَّهَ لَا يَسْتَحْيِي أَن يَضْرِبَ مَثَلًا مَّا بَعُوضَةً فَمَا فَوْقَهَا",
        explanation:
          'Stopping after "يَسْتَحْيِي" is waqf hasan because "Indeed, Allah is not shy" is a grammatically complete statement, but what follows "أَن يَضْرِبَ مَثَلًا" (to set forth an example) is the ma\'fool bihi (object/specification) of "يَسْتَحْيِي" and is thus strongly grammatically connected. Starting with "أَن يَضْرِبَ..." alone would be incomplete.',
        keyWords: ["يَسْتَحْيِي"],
        wordPositions: [4],

        start_ms: 200,

        end_ms: 12280,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002026.mp3",
      },
      {
        id: 5,
        surah: 107,
        verse: 1,
        exampleText: "أَرَأَيْتَ الَّذِي يُكَذِّبُ بِالدِّينِ",
        explanation:
          'Stopping after "أَرَأَيْتَ" (Have you seen?) is waqf hasan. The question is formed and grammatically complete, but what follows "الَّذِي يُكَذِّبُ بِالدِّينِ" (the one who denies the Recompense) is the maf\'ool bihi (object) of "أَرَأَيْتَ", making it grammatically essential to the full intended question.',
        keyWords: ["أَرَأَيْتَ"],
        wordPositions: [1],

        start_ms: 150,

        end_ms: 5710,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/107001.mp3",
      },
    ],
  },
  "waqf-qabih": {
    description:
      "An inappropriate stop that disrupts the meaning and should be avoided except in necessity",
    arabicName: "الوقف القبيح",
    examples: [
      {
        id: 1,
        surah: 1,
        verse: 2,
        exampleText: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
        explanation:
          'Stopping after "الْحَمْدُ" is waqf qabih because it separates the subject "الْحَمْدُ" from its essential predicate "لِلَّهِ", disrupting the meaning.',
        keyWords: ["الْحَمْدُ"],
        wordPositions: [1],

        start_ms: 60,

        end_ms: 5970,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001002.mp3",
      },
      {
        id: 2,
        surah: 1,
        verse: 7,
        exampleText:
          "صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ",
        explanation:
          'Stopping after "غَيْرِ" is waqf qabih because it separates the modifying word "غَيْرِ" from what it modifies "الْمَغْضُوبِ", creating an incomplete meaning.',
        keyWords: ["غَيْرِ"],
        wordPositions: [6],

        start_ms: 30,

        end_ms: 6320,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/001007.mp3",
      },
      {
        id: 3,
        surah: 2,
        verse: 22,
        exampleText: "فَلَا تَجْعَلُوا لِلَّهِ أَندَادًا وَأَنتُمْ تَعْلَمُونَ",
        explanation:
          'Stopping after "لِلَّهِ" is waqf qabih because it separates the verb phrase "تَجْعَلُوا لِلَّهِ" from its object "أَندَادًا", leaving the meaning incomplete.',
        keyWords: ["لِلَّهِ"],
        wordPositions: [18],

        start_ms: 22710,

        end_ms: 30710,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/002022.mp3",
      },
      {
        id: 4,
        surah: 4,
        verse: 171,
        exampleText: "وَلَا تَقُولُوا ثَلَاثَةٌ ۚ انتَهُوا خَيْرًا لَّكُمْ",
        explanation:
          'Stopping after "تَقُولُوا" is waqf qabih because it separates the verb "تَقُولُوا" from its object "ثَلَاثَةٌ", making the prohibition unclear.',
        keyWords: ["تَقُولُوا"],
        wordPositions: [29],

        start_ms: 28820,

        end_ms: 35420,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/004171.mp3",
      },
      {
        id: 5,
        surah: 16,
        verse: 98,
        exampleText:
          "فَإِذَا قَرَأْتَ الْقُرْآنَ فَاسْتَعِذْ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ",
        explanation:
          'Stopping after "فَاسْتَعِذْ" is waqf qabih because it separates the verb "فَاسْتَعِذْ" from its essential complement "بِاللَّهِ", leaving the command hanging.',
        keyWords: ["فَاسْتَعِذْ"],
        wordPositions: [5],

        start_ms: 30,

        end_ms: 9510,

        audio_url: "https://audio.qurancdn.com/Alafasy/mp3/016098.mp3",
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Makharij  (fixed spelling of shafatain)
  // -------------------------------------------------------------------
  "makharij-jawf": {
    description:
      "Letters articulated from the hollow space of the mouth and throat (the oral and pharyngeal cavities), producing the long vowel sounds",
    arabicName: "مخارج الجوف",
    examples: [
      {
        id: 1,
        exampleText: "ـَا",
        explanation:
          "The long Alif (ا) after fatḥa is articulated from the empty space in the middle of the mouth without the tongue touching any part of the mouth.",
        keyWords: ["ـَا"],
        audio_url: "/aa.wav", // Assuming a.wav is the 'aa' sound of Alif Madd
      },
      {
        id: 2,
        exampleText: "ـُو",
        explanation:
          "The long Wāw (و) after ḍamma emerges from the jawf (empty space) without any specific point of articulation.",
        keyWords: ["ـُو"],
        audio_url: "/uu.wav",
      },
      {
        id: 3,
        exampleText: "ـِي",
        explanation:
          "The long Yā (ي) after kasra is articulated from the empty space in the mouth without the tongue touching any specific area.",
        keyWords: ["ـِي"],
        audio_url: "/ii.wav",
      },
    ],
  },

  "makharij-halq": {
    description:
      "Letters articulated from the throat, divided into three parts: deepest part, middle part, and nearest part to the mouth",
    arabicName: "مخارج الحلق",
    examples: [
      {
        id: 1,
        exampleText: "ء",
        explanation:
          "The Hamza (ء) is articulated from the deepest part of the throat (beginning of the throat near the chest) by completely closing the air passage.",
        keyWords: ["ء"],
        audio_url: "/ha.wav",
      },
      {
        id: 2,
        exampleText: "هـ",
        explanation:
          "The letter Hā (هـ) is articulated from the deepest part of the throat with a whispered flow of air.",
        keyWords: ["هـ"],
        audio_url: "/ha.wav",
      },
      {
        id: 3,
        exampleText: "ع",
        explanation:
          "The letter 'Ayn (ع) is articulated from the middle part of the throat with a constriction of the airflow.",
        keyWords: ["ع"],
        audio_url: "/ayn.wav",
      },
      {
        id: 4,
        exampleText: "ح",
        explanation:
          "The letter Ḥā (ح) is articulated from the middle part of the throat with a friction of air but no vibration of vocal cords.",
        keyWords: ["ح"],
        audio_url: "/hha.wav",
      },
      {
        id: 5,
        exampleText: "غ",
        explanation:
          "The letter Ghayn (غ) is articulated from the nearest part of the throat to the mouth with a gargling sound and vibration of vocal cords.",
        keyWords: ["غ"],
        audio_url: "/ghayn.wav",
      },
      {
        id: 6,
        exampleText: "خ",
        explanation:
          "The letter Khā (خ) is articulated from the nearest part of the throat to the mouth with friction and no vibration of vocal cords.",
        keyWords: ["خ"],
        audio_url: "/kha.wav",
      },
    ],
  },

  "makharij-lisan": {
    description:
      "Letters articulated from various parts of the tongue, forming the largest group of Arabic letters",
    arabicName: "مخارج اللسان",
    examples: [
      {
        id: 1,
        exampleText: "ق",
        explanation:
          "The letter Qāf (ق) is articulated from the back of the tongue against the soft palate (back roof of the mouth).",
        keyWords: ["ق"],
        audio_url: "/qaf.wav",
      },
      {
        id: 2,
        exampleText: "ك",
        explanation:
          "The letter Kāf (ك) is articulated from a point slightly forward from the Qāf, where the back of the tongue meets the soft palate.",
        keyWords: ["ك"],
        audio_url: "/kaf.wav",
      },
      {
        id: 3,
        exampleText: "ج",
        explanation:
          "The letter Jīm (ج) is articulated from the middle of the tongue against the hard palate (middle roof of the mouth).",
        keyWords: ["ج"],
        audio_url: "/jiim.wav",
      },
      {
        id: 4,
        exampleText: "ش",
        explanation:
          "The letter Shīn (ش) is articulated from the middle of the tongue against the hard palate, with a spreading of air across the tongue.",
        keyWords: ["ش"],
        audio_url: "/shiin.wav",
      },
      {
        id: 5,
        exampleText: "ي", // Consonantal Ya
        explanation:
          "The letter Yā (ي) as a consonant is articulated from the middle of the tongue against the hard palate, but with less friction than Shīn.",
        keyWords: ["ي"],
        audio_url: "/ya.wav",
      },
      {
        id: 6,
        exampleText: "ل",
        explanation:
          "The letter Lām (ل) is articulated from the sides of the front part of the tongue touching the upper molars and the tip touching the gums of the front teeth.",
        keyWords: ["ل"],
        audio_url: "/lam.wav",
      },
      {
        id: 7,
        exampleText: "ر",
        explanation:
          "The letter Rā (ر) is articulated from the tip of the tongue near the gums above the front teeth, with a slight rolling.",
        keyWords: ["ر"],
        audio_url: "/ra.wav",
      },
      {
        id: 8,
        exampleText: "ن",
        explanation:
          "The letter Nūn (ن) is articulated from the tip of the tongue against the gums of the front teeth, with air flowing through the nose.",
        keyWords: ["ن"],
        audio_url: "/nuun.wav",
      },
      {
        id: 9,
        exampleText: "ط",
        explanation:
          "The letter Ṭā (ط) is articulated from the tip of the tongue against the roots of the upper front teeth, with elevation and fullness.",
        keyWords: ["ط"],
        audio_url: "/taa_e.wav", // Placeholder: CHANGE TO YOUR ACTUAL FILENAME FOR ط (e.g., ta_emphatic.wav or taa.wav if distinct from ت)
      },
      {
        id: 10,
        exampleText: "د",
        explanation:
          "The letter Dāl (د) is articulated from the same position as Ṭā, but without the elevation (tafkhīm).",
        keyWords: ["د"],
        audio_url: "/daal.wav",
      },
      {
        id: 11,
        exampleText: "ت",
        explanation:
          "The letter Tā (ت) is articulated by pressing the tip of the tongue against the roots of the upper front teeth.",
        keyWords: ["ت"],
        audio_url: "/ta_l.wav", // Placeholder: CHANGE TO YOUR ACTUAL FILENAME FOR ت (e.g., ta_light.wav or ta.wav if distinct from ط)
      },
      {
        id: 12,
        exampleText: "ص",
        explanation:
          "The letter Ṣād (ص) is articulated from the tip of the tongue near the lower front teeth, with fullness and elevation.",
        keyWords: ["ص"],
        audio_url: "/saad.wav",
      },
      {
        id: 13,
        exampleText: "س",
        explanation:
          "The letter Sīn (س) is articulated from the same position as Ṣād, but without the elevation.",
        keyWords: ["س"],
        audio_url: "/siin.wav",
      },
      {
        id: 14,
        exampleText: "ز",
        explanation:
          "The letter Zāy (ز) is articulated from the same position as Sīn, but with vibration of the vocal cords.",
        keyWords: ["ز"],
        audio_url: "/za.wav",
      },
      {
        id: 15,
        exampleText: "ظ",
        explanation:
          "The letter Ẓā (ظ) is articulated by placing the tip of the tongue between the front teeth, with elevation and fullness.",
        keyWords: ["ظ"],
        audio_url: "/dhaa_e.wav", // Placeholder: CHANGE TO YOUR ACTUAL FILENAME FOR ظ (e.g., dhaa_emphatic.wav or dhaa.wav if distinct from ذ/ث)
      },
      {
        id: 16,
        exampleText: "ذ",
        explanation:
          "The letter Dhāl (ذ) is articulated by placing the tip of the tongue between the front teeth, without elevation.",
        keyWords: ["ذ"],
        audio_url: "/thaal.wav", // Your filename for ذ
      },
      {
        id: 17,
        exampleText: "ث",
        explanation:
          "The letter Thā (ث) is articulated by placing the tip of the tongue between the front teeth, without vocal cord vibration.",
        keyWords: ["ث"],
        audio_url: "/thaa.wav", // Your filename for ث
      },
      {
        id: 18,
        exampleText: "ض",
        explanation:
          "The letter Ḍād (ض) is articulated from the side of the tongue against the upper molars, with fullness and elevation.",
        keyWords: ["ض"],
        audio_url: "/daad.wav",
      },
    ],
  },

  "makharij-shafatain": {
    description:
      "Letters articulated using one or both lips, consisting of four letters: Fā, Wāw, Bā, and Mīm",
    arabicName: "مخارج الشفتين",
    examples: [
      {
        id: 1,
        exampleText: "ف",
        explanation:
          "The letter Fā (ف) is articulated by placing the edge of the upper front teeth on the inner part of the lower lip.",
        keyWords: ["ف"],
        audio_url: "/fa.wav",
      },
      {
        id: 2,
        exampleText: "و", // Consonantal Waw
        explanation:
          "The letter Wāw (و) as a consonant is articulated by rounding both lips without complete closure, creating a narrow passage for the air.",
        keyWords: ["و"],
        audio_url: "/waw.wav",
      },
      {
        id: 3,
        exampleText: "ب",
        explanation:
          "The letter Bā (ب) is articulated by pressing both lips together completely and then releasing them with a slight explosion of air.",
        keyWords: ["ب"],
        audio_url: "/ba.wav",
      },
      {
        id: 4,
        exampleText: "م",
        explanation:
          'The letter Mīm (م) is articulated by pressing both lips together while allowing air to flow through the nose, creating the nasal "m" sound.',
        keyWords: ["م"],
        audio_url: "/miim.wav",
      },
    ],
  },

  "makharij-khayshum": {
    description:
      "Sounds produced from the nasal passage, specifically the ghunnah (nasalization) that occurs with certain pronunciations of Nūn and Mīm",
    arabicName: "مخرج الخيشوم",
    examples: [
      {
        id: 1,
        exampleText: "نْ",
        explanation:
          "The letter Nūn (ن) with sukūn produces ghunnah from the nasal passage when followed by certain letters.",
        keyWords: ["نْ"],
      },
      {
        id: 2,
        exampleText: "مْ",
        explanation:
          "The letter Mīm (م) with sukūn produces a nasal sound from the khayshūm when followed by another Mīm or Bā.",
        keyWords: ["مْ"],
      },
      {
        id: 3,
        exampleText: "نّ",
        explanation:
          "The letter Nūn (ن) with shaddah has an emphasized ghunnah (nasalization) that flows through the nasal passage due to the doubling.",
        keyWords: ["نّ"],
      },
      {
        id: 4,
        exampleText: "مّ",
        explanation:
          "The letter Mīm (م) with shaddah produces an emphasized ghunnah sound through the nasal passage due to the doubling.",
        keyWords: ["مّ"],
      },
      {
        id: 5,
        exampleText: "ـًـ",
        explanation:
          "The tanwīn fatḥa (ً) produces a nasal sound similar to a light Nūn through the khayshūm.",
        keyWords: ["ـًـ"],
      },
      {
        id: 6,
        exampleText: "ـٍـ",
        explanation:
          "The tanwīn kasra (ٍ) produces a nasal sound through the khayshūm.",
        keyWords: ["ـٍـ"],
      },
      {
        id: 7,
        exampleText: "ـٌـ",
        explanation:
          "The tanwīn ḍamma (ٌ) produces a nasal sound through the khayshūm.",
        keyWords: ["ـٌـ"],
      },
    ],
  },

  // -------------------------------------------------------------------
  //  Ṣifāt (basic set – left for advanced drills)
  // -------------------------------------------------------------------
  "sifaat-jahr": {
    description:
      "Letters pronounced with full vocal cord vibration (voiced letters)",
    arabicName: "الْجَهْر",
    examples: [
      {
        id: 1,
        exampleText: "بَ",
        explanation:
          "Ba with fatḥa (بَ) is voiced—vocal cords vibrate during articulation.",
        keyWords: ["بَ"],
        audio_url: "/ba.wav", // From your list: بَ ba ba.wav
      },
      {
        id: 2,
        exampleText: "جُ",
        explanation:
          "Jeem with ḍamma (جُ) is voiced—feel the clear resonance in the throat.",
        keyWords: ["جُ"],
        audio_url: "/ju.wav", // From your list: جُ ju ju.wav
      },
      {
        id: 3,
        exampleText: "زَ",
        explanation:
          "Zay with fatḥa (زَ) is voiced—complete vocal cord engagement during the fricative sound.",
        keyWords: ["زَ"],
        audio_url: "/za.wav", // From your list: زَ za za.wav
      },
      {
        id: 4,
        exampleText: "مِ",
        explanation:
          "Meem with kasra (مِ) is voiced—nasal vibration present despite the lip closure.",
        keyWords: ["مِ"],
        audio_url: "/mi.wav", // From your list: مِ mi mi.wav
      },
      {
        id: 5,
        exampleText: "رُ",
        explanation:
          "Ra with ḍamma (رُ) is voiced—strong rolling vibration of the tongue.",
        keyWords: ["رُ"],
        audio_url: "/ru.wav", // From your list: رُ ru ru.wav
      },
    ],
  },

  "sifaat-hams": {
    description:
      "Letters pronounced with a breathy, whisper-like quality (unvoiced letters)",
    arabicName: "الْهِمْس",
    examples: [
      {
        id: 1,
        exampleText: "هَ",
        explanation:
          "Ha with fatḥa (هَ) is whispered—no vocal cord vibration, only breath.",
        keyWords: ["هَ"],
        audio_url: "/ha.wav", // Assuming your 'ha.wav' for هَ is distinct from حَ. Let's call it ha_fatha_vowel.wav (you had ha.wav for هَ)
      },
      {
        id: 2,
        exampleText: "خُ",
        explanation:
          "Kha with ḍamma (خُ) is hams—breathy friction from the throat, unvoiced.",
        keyWords: ["خُ"],
        audio_url: "/khu.wav", // From your list: خُ khu khu.wav
      },
      {
        id: 3,
        exampleText: "ثَ",
        explanation:
          "Tha with fatḥa (ثَ) is hams—soft, breathy sound between the teeth.",
        keyWords: ["ثَ"],
        audio_url: "/tha.wav", // Assuming your 'tha.wav' for ثَ is distinct. Let's call it tha_fatha_vowel.wav (you had tha.wav for ثَ)
      },
      {
        id: 4,
        exampleText: "شِ",
        explanation:
          "Sheen with kasra (شِ) is hams—whisper-like sibilant without vocal cord engagement.",
        keyWords: ["شِ"],
        audio_url: "/shi.wav", // From your list: شِ shi shi.wav
      },
    ],
  },

  "sifaat-shiddah": {
    description:
      "The attribute of strength/forcefulness, clearly manifested when a letter has Shaddah (ّ), indicating a doubling of the letter with a slight emphasis or hold.",
    arabicName: "الشِّدَّة",
    examples: [
      {
        id: 1,
        exampleText: "أَبَّ", // Meem with Shaddah implicitly via Ba Shaddah
        explanation:
          "The Baa (ب) with Shaddah in 'أَبَّ' demonstrates Shiddah. The sound is held briefly on the first Baa (from the Shaddah) then released with the vowel.",
        keyWords: ["أَبَّ"],
        audio_url: "/abba.wav",
      },
      {
        id: 2,
        exampleText: "أَتَّ", // Dal with Shaddah implicitly via Ta Shaddah
        explanation:
          "The Taa (ت) with Shaddah in 'أَتَّ' demonstrates Shiddah. There's an emphasis from the doubling of the Taa.",
        keyWords: ["أَتَّ"],
        audio_url: "/atta.wav",
      },
      {
        id: 3,
        exampleText: "أَجُّ", // Ra with Shaddah - Let's use Jeem Shaddah from your list
        explanation:
          "The Jeem (ج) with Shaddah in 'أَجُّ' demonstrates Shiddah. The sound of the Jeem is emphasized due to the doubling.",
        keyWords: ["أَجُّ"],
        audio_url: "/ajju.wav",
      },
      {
        id: 4,
        exampleText: "إِثِّ", // Qaf with Shaddah - Let's use Thaa Shaddah from your list
        explanation:
          "The Thaa (ث) with Shaddah in 'إِثِّ' demonstrates Shiddah. The Thaa sound is doubled and pronounced with emphasis.",
        keyWords: ["إِثِّ"],
        audio_url: "/iththi.wav",
      },
    ],
  },

  "sifaat-rakhawah": {
    description:
      "Letters pronounced with a continuous flow of sound (opposite of shiddah for letters not in Tawassut).",
    arabicName: "الرَّخَاوَة",
    examples: [
      {
        id: 1,
        exampleText: "لَ",
        explanation:
          "Lam with fatḥa (لَ)—gentle, continuous flow of sound with soft articulation. (Note: Lam is a letter of Tawassut/Bayniyyah - intermediate flow).",
        keyWords: ["لَ"],
        audio_url: "/la.wav", // From your list: لَ la la.wav
      },
      {
        id: 2,
        exampleText: "سِ",
        explanation:
          "Seen with kasra (سِ)—light, clear sibilant allowing continuous airflow.",
        keyWords: ["سِ"],
        audio_url: "/si.wav", // From your list: سِ si si.wav
      },
      {
        id: 3,
        exampleText: "فِ",
        explanation:
          "Fa with kasra (فِ)—soft fricative allowing air to flow between teeth and lip.",
        keyWords: ["فِ"],
        audio_url: "/fi.wav", // From your list: فِ fi fi.wav
      },
      {
        id: 4,
        exampleText: "عَ",
        explanation:
          "Ayn with fatḥa (عَ)—guttural with an intermediate flow of sound (Tawassut), not full Rakhawah or full Shiddah.",
        keyWords: ["عَ"],
        audio_url: "/Aa.wav", // From your list: عَ Aa Aa.wav
      },
    ],
  },
};
