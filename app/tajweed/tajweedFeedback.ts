// app/tajweed/tajweedFeedback.ts

//---------------------------------------------------------------
//  A  L  L     T A J W E E D    B U S I N E S S    L O G I C
//---------------------------------------------------------------
/* eslint‑disable @typescript-eslint/no‑shadow */

/* ------------------------------------------------------------------ */
/* 0.  Small helpers                                                  */
/* ------------------------------------------------------------------ */
export type Severity = "critical" | "moderate" | "mild" | "stylistic";

// Enhanced Violation interface with UI styling fields
export interface Violation {
  rule: string;
  message: string;
  severity: Severity;
  position: number;
  ref_phoneme: string | null;
  pred_phoneme: string | null;
  arabic_char: string;
  word_idx: number;
  word: string;
  letter_position: string;
  // UI-specific fields
  colorClass?: string; // CSS color class for the violation
  bgColorClass?: string; // Background color class
  borderColorClass?: string; // Border color class
  iconType?: string; // Icon type to display
  displayGroup?: string; // For grouping in the UI
  comparisonData?: {
    // For showing comparisons
    userValue: string;
    correctValue: string;
  };
}

// UI Color mapping for severity levels
export const severityColorMap: Record<
  Severity,
  {
    dot: string;
    bg: string;
    border: string;
    text: string;
    badge: string;
  }
> = {
  critical: {
    dot: "bg-red-500",
    bg: "bg-white",
    border: "border-red-200",
    text: "text-red-600",
    badge: "bg-red-600",
  },
  moderate: {
    dot: "bg-amber-500",
    bg: "bg-white",
    border: "border-amber-200",
    text: "text-amber-600",
    badge: "bg-amber-600",
  },
  mild: {
    dot: "bg-yellow-500",
    bg: "bg-white",
    border: "border-yellow-200",
    text: "text-yellow-600",
    badge: "bg-yellow-600",
  },
  stylistic: {
    dot: "bg-purple-400",
    bg: "bg-white",
    border: "border-purple-200",
    text: "text-purple-600",
    badge: "bg-purple-600",
  },
};

// Rule color mapping
export const ruleColorMap: Record<string, string> = {
  "qalqalah-letters": "bg-orange-500",
  "noon-idgham-ghunnah": "bg-blue-500",
  "noon-idgham-without-ghunnah": "bg-blue-600",
  "noon-ikhfa": "bg-indigo-500",
  "noon-iqlab": "bg-green-500",
  "noon-idhar": "bg-purple-500",
  "noon-deletion": "bg-red-600",
  "meem-idgham-shafawi": "bg-cyan-500",
  "meem-ikhfa-shafawi": "bg-teal-500",
  "meem-idhar-shafawi": "bg-emerald-500",
  "meem-deletion": "bg-red-600",
  "madd-substitution": "bg-rose-500",
  "madd-deletion": "bg-rose-600",
  "laam-tafkheem": "bg-violet-500",
  "laam-tarqeeq": "bg-violet-400",
  "tafkheem-ra": "bg-fuchsia-500",
  "tarqeeq-ra": "bg-fuchsia-400",
  "tafkheem-letters": "bg-pink-500",
  "ghunnah-ikhfa": "bg-sky-500",
  "ghunnah-idgham": "bg-sky-600",
  "ghunnah-mushaddadah": "bg-sky-400",
  "makharij-jawf": "bg-neutral-500",
  "makharij-halq": "bg-stone-500",
  "makharij-lisan": "bg-gray-500",
  "makharij-shafatan": "bg-zinc-500",
  "makharij-khayshum": "bg-slate-500",
};

// Enhanced feedback interface
export interface Feedback {
  score: number; // 0‑100
  violations: Violation[];
  report: string; // ready for UI printout
  recitationContext: RecitationContext;
  detailedScores?: {
    // Added detailed scoring breakdown
    byRule: Record<string, number>;
    byCategory: Record<string, number>;
  };
  summaryHTML?: string; // Optional HTML formatted summary
}

// Enhanced recitation context with more display info
export interface RecitationContext {
  speed: "fast" | "medium" | "slow";
  style: string;
  displayName?: string; // Human-readable display name
  description?: string; // Description of the recitation style
  recommendedFocus?: string; // Recommendation for focus area
}

/* ------------------------------------------------------------------ */
/* 1.  Phoneme converter  (port of convert_to_phonemes)               */
/* ------------------------------------------------------------------ */
type PhonemeMaps = ReturnType<typeof buildMaps>;

function buildMaps() {
  // NOTE: "aa" etc must be *before* single‑letter keys so we check longer keys first
  const phonemeMap: Record<string, string> = {
    ب: "b",
    ت: "t",
    ث: "th",
    ج: "j",
    ح: "ħ",
    خ: "kh",
    د: "d",
    ذ: "dh",
    ر: "r",
    ز: "z",
    س: "s",
    ش: "sh",
    ص: "ṣ",
    ض: "ḍ",
    ط: "ṭ",
    ظ: "ẓ",
    ع: "ʕ",
    غ: "gh",
    ف: "f",
    ق: "q",
    ك: "k",
    ل: "l",
    م: "m",
    ن: "n",
    ه: "h",
    و: "w",
    ي: "y",
    ء: "ʔ",
    أ: "ʔ",
    إ: "ʔ",
    ؤ: "ʔ",
    ئ: "ʔ",
    ا: "aa",
    ى: "aa",
    وْ: "uu",
    يْ: "ii",
    "َ": "a",
    "ُ": "u",
    "ِ": "i",
    "ً": "an",
    "ٌ": "un",
    "ٍ": "in",
    "ْ": "",
    "ّ": "*",
    "ٰ": "aa",
    "ٓ": ":",
    " ": " ",
    ٱ: "", // Alif wasla - no phoneme but tracked
  };

  // Regular diacritics plus special marks for comprehensive handling
  const diacritics = /[\u064b-\u065f\u06e4-\u06ed]/; // basic + "special" marks
  return { phonemeMap, diacritics };
}

export function convertToPhonemes(
  text: string,
  maps: PhonemeMaps = buildMaps()
) {
  const { phonemeMap, diacritics } = maps;
  // Strip tatweel (elongation character)
  const clean = text.replace(/\u0640/g, "");
  const words = clean.trim().split(/\s+/);

  const phonemes: string[] = [];
  const refArabicMap: string[] = [];
  const wordMap: number[] = [];
  const letterPositions: string[] = [];
  const wordOriginals: string[] = [];

  let wordIdx = 0;
  for (const w of words) {
    if (!w) continue;
    wordOriginals.push(w);

    // collect clusters letter+diacritics
    const clusters: string[] = [];
    let i = 0;
    while (i < w.length) {
      let cluster = w[i];
      let j = i + 1;
      while (j < w.length && diacritics.test(w[j])) {
        cluster += w[j++];
      }
      clusters.push(cluster);
      i = j;
    }

    clusters.forEach((cluster, cIdx) => {
      const base = cluster[0] ?? "";
      const ph = phonemeMap[cluster] ?? phonemeMap[base] ?? "";
      if (!ph && base !== "ٱ") return; // e.g. sukūn or Wasla

      // Only add phoneme if there is one (skip alif wasla that has no phoneme)
      if (ph) {
        phonemes.push(ph);
        refArabicMap.push(cluster);
        wordMap.push(wordIdx);

        let pos = "in the middle of the word";
        if (clusters.length === 1) pos = "only letter in the word";
        else if (cIdx === 0) pos = "at the beginning of the word";
        else if (cIdx === clusters.length - 1) pos = "at the end of the word";
        else
          pos = `in the middle of the word (letter ${cIdx + 1} of ${
            clusters.length
          })`;
        letterPositions.push(pos);
      }
    });

    wordIdx++;
  }

  return { phonemes, refArabicMap, wordMap, letterPositions, wordOriginals };
}

/* ------------------------------------------------------------------ */
/* 2.  Alignment (simplified DTW)                                     */
/* ------------------------------------------------------------------ */
export function simpleAlign(ref: string[], pred: string[]) {
  const m = ref.length,
    n = pred.length;
  const dp: number[][] = Array.from({ length: m + 1 }, () =>
    Array(n + 1).fill(0)
  );
  for (let i = 1; i <= m; i++) dp[i][0] = Infinity;
  for (let j = 1; j <= n; j++) dp[0][j] = Infinity;

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      const cost = ref[i - 1] === pred[j - 1] ? 0 : 1;
      dp[i][j] =
        cost + Math.min(dp[i - 1][j - 1], dp[i - 1][j] + 1, dp[i][j - 1] + 1);
    }
  }

  // traceback
  const alignment: Array<[number, number]> = [];
  let i = m,
    j = n;
  while (i > 0 || j > 0) {
    if (
      i > 0 &&
      j > 0 &&
      dp[i][j] === dp[i - 1][j - 1] + (ref[i - 1] === pred[j - 1] ? 0 : 1)
    ) {
      alignment.push([i - 1, j - 1]);
      i--;
      j--;
    } else if (i > 0 && dp[i][j] === dp[i - 1][j] + 1) {
      alignment.push([i - 1, -1]);
      i--;
    } else {
      alignment.push([-1, j - 1]);
      j--;
    }
  }
  return alignment.reverse();
}

/* ------------------------------------------------------------------ */
/* 3.  Diff helpers   (substitutions / insertions / deletions)        */
/* ------------------------------------------------------------------ */
export function diffFromAlignment(
  alignment: Array<[number, number]>,
  refPhs: string[],
  predPhs: string[],
  refArabic: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[]
) {
  const subs: any[] = [];
  const ins: any[] = [];
  const dels: any[] = [];

  alignment.forEach(([ri, pi]) => {
    if (ri >= 0 && pi >= 0) {
      if (refPhs[ri] !== predPhs[pi])
        subs.push([
          ri,
          refPhs[ri],
          predPhs[pi],
          refArabic[ri],
          wordMap[ri],
          wordOriginals[wordMap[ri]],
          letterPos[ri],
        ]);
    } else if (ri >= 0) {
      dels.push([
        ri,
        refPhs[ri],
        refArabic[ri],
        wordMap[ri],
        wordOriginals[wordMap[ri]],
        letterPos[ri],
      ]);
    } else if (pi >= 0) {
      ins.push([pi, predPhs[pi]]);
    }
  });

  return { subs, ins, dels };
}

/* ------------------------------------------------------------------ */
/* 4.  Enhanced letter classifications for rules                      */
/* ------------------------------------------------------------------ */
export const classifications = {
  // Letter Classifications for Points of Articulation (Makharij)
  makharij_jawf: new Set(["aa", "uu", "ii"]), // Throat/Air cavity
  makharij_halq: new Set(["ʔ", "h", "ʕ", "ħ", "gh", "kh"]), // Throat
  makharij_lisan: new Set([
    "q",
    "k",
    "j",
    "sh",
    "y",
    "ḍ",
    "l",
    "n",
    "r",
    "ṭ",
    "d",
    "t",
    "ẓ",
    "dh",
    "th",
    "ṣ",
    "s",
    "z",
  ]), // Tongue
  makharij_shafatan: new Set(["f", "m", "b", "w"]), // Lips
  makharij_khayshum: new Set(["m", "n"]), // Nasal cavity (when with ghunnah)

  // Characteristics (Sifaat)
  sifaat_jahr: new Set([
    "ʔ",
    "b",
    "j",
    "d",
    "dh",
    "r",
    "z",
    "ḍ",
    "ẓ",
    "ʕ",
    "gh",
    "l",
    "m",
    "n",
    "w",
    "y",
  ]), // Voiced
  sifaat_hams: new Set([
    "t",
    "th",
    "ħ",
    "kh",
    "s",
    "sh",
    "ṣ",
    "f",
    "q",
    "k",
    "h",
  ]), // Unvoiced/whispered
  sifaat_shiddah: new Set(["ʔ", "b", "t", "j", "d", "ṭ", "q", "k"]), // Stop consonants
  sifaat_rakhawah: new Set([
    "th",
    "ħ",
    "kh",
    "dh",
    "z",
    "s",
    "sh",
    "ṣ",
    "ḍ",
    "ẓ",
    "ʕ",
    "gh",
    "f",
    "h",
  ]), // Fricatives

  // Various rule-specific sets
  qalqalahLetters: new Set(["q", "ṭ", "b", "j", "d"]), // Qalqalah letters
  ikhfa_letters: new Set([
    "t",
    "th",
    "j",
    "d",
    "dh",
    "z",
    "s",
    "sh",
    "ṣ",
    "ḍ",
    "ṭ",
    "ẓ",
    "f",
    "q",
    "k",
  ]), // Ikhfa
  idgham_letters: new Set(["y", "r", "m", "l", "w", "n"]), // Letters for idgham
  idgham_with_ghunnah: new Set(["y", "n", "m", "w"]), // Idgham with ghunnah
  idgham_without_ghunnah: new Set(["l", "r"]), // Idgham without ghunnah
  iqlab_letters: new Set(["b"]), // Iqlab letter
  idhar_letters: new Set(["ʔ", "h", "ʕ", "ħ", "gh", "kh"]), // Idhar letters
  laam_tafkheem_triggers: new Set(["ṣ", "ḍ", "ṭ", "ẓ"]), // Letters that cause tafkheem of lam
  tafkheem_letters: new Set(["ṣ", "ḍ", "ṭ", "ẓ", "kh", "gh", "q"]), // Tafkheem letters
  ra_tafkheem_vowels: new Set(["a", "u", "aa", "uu"]), // Vowels that cause tafkheem of ra
  ra_tarqeeq_vowels: new Set(["i", "ii"]), // Vowels that cause tarqeeq of ra
  longVowels: new Set(["aa", "uu", "ii"]), // Long vowels
  short_vowels: new Set(["a", "u", "i"]), // Short vowels
  tanween: new Set(["an", "un", "in"]), // Tanween
};

/* ------------------------------------------------------------------ */
/* 4.5  NEW: Expected phoneme patterns for merged noon + idgham letters */
/* ------------------------------------------------------------------ */
// Map of what the merged phoneme sequence should look like for each idgham letter
export const idghamExpectedPatterns: {
  // Added the explicit type declaration here
  "n+y": string[];
  "n+m": string[];
  "n+w": string[];
  "n+n": string[];
  "n+l": string[];
  "n+r": string[];
} = {
  // Idgham with Ghunnah
  "n+y": ["y", "yy", "ii", "iiy"], // Noon + Ya variations
  "n+m": ["m", "mm"], // Noon + Meem variations
  "n+w": ["w", "ww", "uu", "uuw"], // Noon + Waw variations
  "n+n": ["n", "nn"], // Noon + Noon variations

  // Idgham without Ghunnah
  "n+l": ["l", "ll"], // Noon + Lam variations
  "n+r": ["r", "rr"], // Noon + Ra variations
};

// Define a type for the valid keys of idghamExpectedPatterns
type IdghamPatternKey = keyof typeof idghamExpectedPatterns;

// Helper function to check if a phoneme matches expected merged pattern
export function matchesIdghamPattern(
  refPhoneme: string,
  nextPhoneme: string,
  predPhoneme: string
): boolean {
  // Skip if not noon or not followed by idgham letter
  if (refPhoneme !== "n") return false;

  // Determine which pattern to check based on the next phoneme
  let patternKey: IdghamPatternKey | null = null;

  if (nextPhoneme === "y") patternKey = "n+y";
  else if (nextPhoneme === "m") patternKey = "n+m";
  else if (nextPhoneme === "w") patternKey = "n+w";
  else if (nextPhoneme === "n") patternKey = "n+n";
  else if (nextPhoneme === "l") patternKey = "n+l";
  else if (nextPhoneme === "r") patternKey = "n+r";

  // If no pattern matches, return false
  if (!patternKey) return false;

  // Check if predicted phoneme matches any expected pattern
  const expectedPatterns = idghamExpectedPatterns[patternKey];
  return expectedPatterns.includes(predPhoneme);
}

/* ------------------------------------------------------------------ */
/* 5.  Helper function to classify violation severity                 */
/* ------------------------------------------------------------------ */
export function classifyViolationSeverity(
  rule_type: string,
  position: number,
  ref_phoneme: string | null,
  pred_phoneme: string | null,
  context?: RecitationContext | null
): Severity {
  // Define rules that are always critical regardless of context
  const critical_rules = new Set([
    "noon-deletion",
    "meem-deletion",
    "hamzat-qat", // Core consonant deletions
  ]);

  // Define rules that may have stylistic variations
  const flexible_rules = new Set([
    "madd-deletion",
    "madd-substitution", // Elongation variations
  ]);

  // Known positions where experts may vary (based on recitation patterns)
  const known_variations: Record<string, Severity> = {
    // Format: `${position},${ref_phoneme},${pred_phoneme}`: "severity"
    "0,aa,": "stylistic", // Initial aa in alhamdu
    "17,aa,": "stylistic", // The specific aa in al-'aalamiin
  };

  // Check for known specific variations first
  const variation_key = `${position},${ref_phoneme},${pred_phoneme || ""}`;
  if (known_variations[variation_key]) {
    return known_variations[variation_key];
  }

  // Then check rule categories
  if (critical_rules.has(rule_type)) {
    return "critical";
  } else if (flexible_rules.has(rule_type)) {
    // For flexible rules, consider position and context
    if (rule_type === "madd-deletion") {
      // Specific position-based exceptions for madd
      if (position === 17 || position === 21) {
        // Positions where shortening is common
        return "stylistic";
      }
      // Context-based analysis for madd
      if (context && context.speed === "fast") {
        return "mild";
      }
    }
    return "moderate";
  }

  // Default case - most violations are moderate concerns
  return "moderate";
}

/* ------------------------------------------------------------------ */
/* 6.  Helper function to detect recitation style                     */
/* ------------------------------------------------------------------ */
export function detectRecitationStyle(
  pred_phs: string[],
  audioDuration?: number | null
): RecitationContext {
  const context: RecitationContext = {
    speed: "medium", // Default assumption
    style: "standard",
    displayName: "Standard Recitation",
    description: "Standard recitation style with moderate speed",
  };

  // If we have duration information, we can estimate recitation speed
  if (audioDuration) {
    // Calculate phonemes per second
    const phonemesPerSecond = pred_phs.length / audioDuration;

    // Classify based on typical ranges
    if (phonemesPerSecond > 4.0) {
      context.speed = "fast";
      context.displayName = "Hadr (Fast)";
      context.description = "Fast-paced recitation style";
      context.recommendedFocus =
        "Focus on proper letter articulation and preventing letter omissions";
    } else if (phonemesPerSecond < 2.5) {
      context.speed = "slow";
      context.displayName = "Tartil (Slow)";
      context.description = "Slow, deliberate recitation style";
      context.recommendedFocus =
        "Focus on proper madd elongation and ghunnah nasalization";
    } else {
      context.speed = "medium";
      context.displayName = "Tadwir (Medium)";
      context.description = "Balanced recitation with moderate speed";
      context.recommendedFocus =
        "Focus on balancing speed with proper articulation";
    }
  }

  return context;
}

/* ------------------------------------------------------------------ */
/* 7.  Enhanced rule checkers                                         */
/* ------------------------------------------------------------------ */

// QALQALAH RULES - Enhanced with context awareness
export function checkQalqalah(
  ref: string[],
  subs: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const v: Violation[] = [];
  subs.forEach(
    ([idx, r, p, arChar, wIdx, word, position]: any /* prettier fix */) => {
      if (classifications.qalqalahLetters.has(r)) {
        const severity = classifyViolationSeverity(
          "qalqalah-letters",
          idx,
          r,
          p,
          context
        );

        // Create enhanced violation with UI fields
        v.push({
          rule: "qalqalah-letters",
          message: `Qalqalah letter '${arChar}' not pronounced with sufficient bounce (pronounced as '${p}')`,
          severity: severity,
          position: idx,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arChar,
          word_idx: wIdx,
          word,
          letter_position: position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Qalqalah",
          comparisonData: {
            userValue: p,
            correctValue: r,
          },
        });
      }
    }
  );
  return v;
}

// NOON SAAKIN & TANWEEN RULES - UPDATED with proper Idgham handling
export function checkNoonTanweenRules(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  dels: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  // Helper to check if a letter follows in the reference
  const hasNextLetter = (idx: number, letterSet: Set<string>): boolean => {
    return idx + 1 < ref_phs.length && letterSet.has(ref_phs[idx + 1]);
  };

  // Helper to get the next letter in reference phonemes
  const getNextLetter = (idx: number): string | null => {
    return idx + 1 < ref_phs.length ? ref_phs[idx + 1] : null;
  };

  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    // Check Noon or Tanween
    if (r === "n" || classifications.tanween.has(r)) {
      const nextLetter = getNextLetter(i);

      // IDGHAM: Noon/tanween before idgham letters
      if (hasNextLetter(i, classifications.idgham_letters)) {
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";

        // Determine idgham type based on next letter
        const isIdghamWithGhunnah =
          nextLetter && classifications.idgham_with_ghunnah.has(nextLetter);
        const rule_name = isIdghamWithGhunnah
          ? "noon-idgham-ghunnah"
          : "noon-idgham-without-ghunnah";

        // NEW: Special handling for Idgham with Ghunnah
        if (isIdghamWithGhunnah && nextLetter) {
          // Check if predicted phoneme matches expected merged pattern
          const matchesExpectedPattern = matchesIdghamPattern(r, nextLetter, p);

          // If the predicted phoneme matches an expected pattern, this is correct application
          // of Idgham with Ghunnah - so no violation should be reported
          if (matchesExpectedPattern) {
            // No violation - this is correct application of Idgham with Ghunnah
            continue;
          }

          // ENHANCEMENT: If predicted phoneme is the same as original 'n', it means
          // they pronounced noon distinctly instead of merging - which is wrong for Idgham
          const wrongNoonPronunciation = p === "n";

          const severity = classifyViolationSeverity(
            rule_name,
            i,
            r,
            p,
            context
          );

          // Customize message based on the specific error
          let message = "";
          if (wrongNoonPronunciation) {
            message = `Idgham rule not applied: '${arabic_char}' should merge with '${nextArabic}' instead of pronouncing the noon distinctly`;
          } else {
            message = `Idgham with Ghunnah issue with '${arabic_char}' before '${nextArabic}' (missing proper nasalization when merging)`;
          }

          violations.push({
            rule: rule_name,
            message: message,
            severity: severity,
            position: i,
            ref_phoneme: r,
            pred_phoneme: p,
            arabic_char: arabic_char,
            word_idx: word_idx,
            word: word,
            letter_position: letter_position,
            // Add UI specific fields
            colorClass: severityColorMap[severity].text,
            bgColorClass: severityColorMap[severity].bg,
            borderColorClass: severityColorMap[severity].border,
            displayGroup: "Noon Rules - Idgham",
            comparisonData: {
              userValue: p,
              correctValue: isIdghamWithGhunnah
                ? "merged with ghunnah"
                : "merged",
            },
          });
        }
        // Standard handling for Idgham without Ghunnah and cases where expected pattern check failed
        else {
          const severity = classifyViolationSeverity(
            rule_name,
            i,
            r,
            p,
            context
          );
          violations.push({
            rule: rule_name,
            message: `Idgham issue with '${arabic_char}' before '${nextArabic}' (pronounced as '${p}' instead of properly merging)`,
            severity: severity,
            position: i,
            ref_phoneme: r,
            pred_phoneme: p,
            arabic_char: arabic_char,
            word_idx: word_idx,
            word: word,
            letter_position: letter_position,
            // Add UI specific fields
            colorClass: severityColorMap[severity].text,
            bgColorClass: severityColorMap[severity].bg,
            borderColorClass: severityColorMap[severity].border,
            displayGroup: "Noon Rules - Idgham",
            comparisonData: {
              userValue: p,
              correctValue: "merged",
            },
          });
        }
      }

      // IQLAB: Noon/tanween before Ba
      else if (hasNextLetter(i, classifications.iqlab_letters)) {
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";

        // ENHANCEMENT: Check if the user pronounced noon as meem (which is correct for Iqlab)
        const correctIqlabPronunciation = p === "m" || p === "mm";

        if (correctIqlabPronunciation) {
          // No violation - this is correct application of Iqlab
          continue;
        }

        const severity = classifyViolationSeverity(
          "noon-iqlab",
          i,
          r,
          p,
          context
        );

        violations.push({
          rule: "noon-iqlab",
          message: `Iqlab rule not applied: '${arabic_char}' before '${nextArabic}' should be pronounced as 'm' with nasalization`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Noon Rules - Iqlab",
          comparisonData: {
            userValue: p,
            correctValue: "m",
          },
        });
      }

      // IKHFA: Noon/tanween before ikhfa letters
      else if (hasNextLetter(i, classifications.ikhfa_letters)) {
        const nextLetter = ref_phs[i + 1];
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";
        const severity = classifyViolationSeverity(
          "noon-ikhfa",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "noon-ikhfa",
          message: `Ikhfa issue with '${arabic_char}' before '${nextArabic}' (should have partial nasalization)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Noon Rules - Ikhfa",
          comparisonData: {
            userValue: p,
            correctValue: "nasal " + nextLetter,
          },
        });
      }

      // IDHAR: Noon/tanween before idhar letters
      else if (hasNextLetter(i, classifications.idhar_letters)) {
        const nextLetter = ref_phs[i + 1];
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";
        const severity = classifyViolationSeverity(
          "noon-idhar",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "noon-idhar",
          message: `Idhar issue with '${arabic_char}' before '${nextArabic}' (should be pronounced clearly)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Noon Rules - Idhar",
          comparisonData: {
            userValue: p,
            correctValue: r,
          },
        });
      }
    }
  }

  // Check for deletions of noon/tanween (serious error)
  for (const [i, r, arabic_char, word_idx, word, letter_position] of dels) {
    if (r === "n" || classifications.tanween.has(r)) {
      // NEW: Check if this is an Idgham situation where noon should be merged
      const nextLetter = getNextLetter(i);
      if (nextLetter && classifications.idgham_letters.has(nextLetter)) {
        // This is likely a deletion as part of Idgham - check the next phoneme in actual recitation
        // to see if it shows evidence of proper merging

        // Find the proper "merged" phoneme index in the prediction sequence
        // This is complex logic and may need refinement based on alignment

        // For now, we'll simply skip reporting this as a deletion violation
        // since it might be correct application of Idgham
        continue;
      }

      const severity = classifyViolationSeverity(
        "noon-deletion",
        i,
        r,
        null,
        context
      );
      violations.push({
        rule: "noon-deletion",
        message: `Missing Noon/Tanween: '${arabic_char}' was omitted`,
        severity: severity,
        position: i,
        ref_phoneme: r,
        pred_phoneme: null,
        arabic_char: arabic_char,
        word_idx: word_idx,
        word: word,
        letter_position: letter_position,
        // Add UI specific fields
        colorClass: severityColorMap[severity].text,
        bgColorClass: severityColorMap[severity].bg,
        borderColorClass: severityColorMap[severity].border,
        displayGroup: "Critical Omissions",
        comparisonData: {
          userValue: "omitted",
          correctValue: r,
        },
      });
    }
  }

  return violations;
}

// MEEM SAAKIN RULES
export function checkMeemRules(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  dels: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  // Helper to check if a letter follows in the reference
  const hasNextLetter = (idx: number, letter: string): boolean => {
    return idx + 1 < ref_phs.length && ref_phs[idx + 1] === letter;
  };

  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    if (r === "m") {
      // IDGHAM SHAFAWI: Meem before Meem
      if (hasNextLetter(i, "m")) {
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";

        // ENHANCEMENT: Check if the user pronounced double meem (which is correct for Idgham Shafawi)
        const correctIdghamShafawiPronunciation = p === "mm" || p === "m";

        if (correctIdghamShafawiPronunciation) {
          // No violation - this is correct application of Idgham Shafawi
          continue;
        }

        const severity = classifyViolationSeverity(
          "meem-idgham-shafawi",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "meem-idgham-shafawi",
          message: `Meem Idgham rule not applied: '${arabic_char}' before '${nextArabic}' should be merged with nasalization`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Meem Rules - Idgham",
          comparisonData: {
            userValue: p,
            correctValue: "mm",
          },
        });
      }

      // IKHFA SHAFAWI: Meem before Ba
      else if (hasNextLetter(i, "b")) {
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";
        const severity = classifyViolationSeverity(
          "meem-ikhfa-shafawi",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "meem-ikhfa-shafawi",
          message: `Meem Ikhfa issue with '${arabic_char}' before '${nextArabic}' (should have partial hiding with nasalization)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Meem Rules - Ikhfa",
          comparisonData: {
            userValue: p,
            correctValue: "nasal m",
          },
        });
      }

      // IDHAR SHAFAWI: Meem before other letters
      else {
        const severity = classifyViolationSeverity(
          "meem-idhar-shafawi",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "meem-idhar-shafawi",
          message: `Meem Idhar issue with '${arabic_char}' (should be pronounced clearly)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Meem Rules - Idhar",
          comparisonData: {
            userValue: p,
            correctValue: r,
          },
        });
      }
    }
  }

  // Check for deletions of meem (serious error)
  for (const [i, r, arabic_char, word_idx, word, letter_position] of dels) {
    if (r === "m") {
      const severity = classifyViolationSeverity(
        "meem-deletion",
        i,
        r,
        null,
        context
      );
      violations.push({
        rule: "meem-deletion",
        message: `Missing Meem: '${arabic_char}' was omitted`,
        severity: severity,
        position: i,
        ref_phoneme: r,
        pred_phoneme: null,
        arabic_char: arabic_char,
        word_idx: word_idx,
        word: word,
        letter_position: letter_position,
        // Add UI specific fields
        colorClass: severityColorMap[severity].text,
        bgColorClass: severityColorMap[severity].bg,
        borderColorClass: severityColorMap[severity].border,
        displayGroup: "Critical Omissions",
        comparisonData: {
          userValue: "omitted",
          correctValue: r,
        },
      });
    }
  }

  return violations;
}

// MADD RULES (ELONGATION)
export function checkMaddRules(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  dels: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  // Check for substitutions of long vowels
  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    if (
      classifications.longVowels.has(r) &&
      !classifications.longVowels.has(p)
    ) {
      // Get severity classification
      const severity = classifyViolationSeverity(
        "madd-substitution",
        i,
        r,
        p,
        context
      );

      // Generate message based on severity
      let message = "";
      if (severity === "stylistic") {
        message = `Stylistic variation with long vowel '${arabic_char}' shortened to '${p}' (acceptable in this context)`;
      } else if (severity === "mild") {
        message = `Minor madd adjustment with long vowel '${arabic_char}' shortened to '${p}'`;
      } else if (severity === "moderate") {
        message = `Madd shortening with long vowel '${arabic_char}' shortened to '${p}'`;
      } else {
        // critical
        message = `Critical madd error with long vowel '${arabic_char}' shortened to '${p}'`;
      }

      violations.push({
        rule: "madd-substitution",
        message: message,
        severity: severity,
        position: i,
        ref_phoneme: r,
        pred_phoneme: p,
        arabic_char: arabic_char,
        word_idx: word_idx,
        word: word,
        letter_position: letter_position,
        // Add UI specific fields
        colorClass: severityColorMap[severity].text,
        bgColorClass: severityColorMap[severity].bg,
        borderColorClass: severityColorMap[severity].border,
        displayGroup: "Madd (Elongation)",
        comparisonData: {
          userValue: p,
          correctValue: r,
        },
      });
    }
  }

  // Check for deletions of long vowels
  for (const [i, r, arabic_char, word_idx, word, letter_position] of dels) {
    if (classifications.longVowels.has(r)) {
      // Get severity classification
      const severity = classifyViolationSeverity(
        "madd-deletion",
        i,
        r,
        null,
        context
      );

      // Generate appropriate message based on severity
      let message = "";
      if (severity === "stylistic") {
        message = `Stylistic variation with long vowel '${arabic_char}' shortened (acceptable in this context)`;
      } else if (severity === "mild") {
        message = `Minor madd adjustment with long vowel '${arabic_char}' slightly shortened`;
      } else if (severity === "moderate") {
        message = `Madd shortening with long vowel '${arabic_char}' was reduced`;
      } else {
        // critical
        message = `Critical madd error with long vowel '${arabic_char}' was omitted`;
      }

      violations.push({
        rule: "madd-deletion",
        message: message,
        severity: severity,
        position: i,
        ref_phoneme: r,
        pred_phoneme: null,
        arabic_char: arabic_char,
        word_idx: word_idx,
        word: word,
        letter_position: letter_position,
        // Add UI specific fields
        colorClass: severityColorMap[severity].text,
        bgColorClass: severityColorMap[severity].bg,
        borderColorClass: severityColorMap[severity].border,
        displayGroup: "Madd (Elongation)",
        comparisonData: {
          userValue: "omitted",
          correctValue: r,
        },
      });
    }
  }

  return violations;
}

// LAAM RULES
export function checkLaamRules(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  // Helper to check if a letter follows in the reference
  const hasNextLetter = (idx: number, letterSet: Set<string>): boolean => {
    return idx + 1 < ref_phs.length && letterSet.has(ref_phs[idx + 1]);
  };

  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    if (r === "l") {
      // Tafkheem/Tarqeeq issues for Laam
      if (hasNextLetter(i, classifications.laam_tafkheem_triggers)) {
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";
        const severity = classifyViolationSeverity(
          "laam-tafkheem",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "laam-tafkheem",
          message: `Laam Tafkheem issue with '${arabic_char}' before emphatic letter '${nextArabic}' (should be pronounced with emphasis)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Laam Rules",
          comparisonData: {
            userValue: p,
            correctValue: "heavy l",
          },
        });
      } else {
        const severity = classifyViolationSeverity(
          "laam-tarqeeq",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "laam-tarqeeq",
          message: `Laam Tarqeeq issue with '${arabic_char}' (should be pronounced lightly)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Laam Rules",
          comparisonData: {
            userValue: p,
            correctValue: "light l",
          },
        });
      }
    }
  }

  return violations;
}

// TAFKHEEM & TARQEEQ
export function checkTafkheemTarqeeq(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  // Helper to check vowel after a letter in the reference
  const getNextVowel = (idx: number): [string | null, string | null] => {
    for (let j = idx + 1; j < ref_phs.length; j++) {
      if (
        classifications.short_vowels.has(ref_phs[j]) ||
        classifications.longVowels.has(ref_phs[j])
      ) {
        return [ref_phs[j], j < arabicMap.length ? arabicMap[j] : "?"];
      }
    }
    return [null, null];
  };

  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    // RA tafkheem/tarqeeq issues
    if (r === "r") {
      const [nextVowel, nextArabic] = getNextVowel(i);
      if (nextVowel && classifications.ra_tafkheem_vowels.has(nextVowel)) {
        const severity = classifyViolationSeverity(
          "tafkheem-ra",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "tafkheem-ra",
          message: `Ra Tafkheem issue with '${arabic_char}' before '${nextArabic}' (should be pronounced with emphasis)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Tafkheem/Tarqeeq",
          comparisonData: {
            userValue: p,
            correctValue: "heavy r",
          },
        });
      } else if (
        nextVowel &&
        classifications.ra_tarqeeq_vowels.has(nextVowel)
      ) {
        const severity = classifyViolationSeverity(
          "tarqeeq-ra",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "tarqeeq-ra",
          message: `Ra Tarqeeq issue with '${arabic_char}' before '${nextArabic}' (should be pronounced lightly)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Tafkheem/Tarqeeq",
          comparisonData: {
            userValue: p,
            correctValue: "light r",
          },
        });
      }
    }

    // Emphatic letter issues
    else if (classifications.tafkheem_letters.has(r)) {
      const severity = classifyViolationSeverity(
        "tafkheem-letters",
        i,
        r,
        p,
        context
      );
      violations.push({
        rule: "tafkheem-letters",
        message: `Tafkheem issue with emphatic '${arabic_char}' (should be pronounced with emphasis)`,
        severity: severity,
        position: i,
        ref_phoneme: r,
        pred_phoneme: p,
        arabic_char: arabic_char,
        word_idx: word_idx,
        word: word,
        letter_position: letter_position,
        // Add UI specific fields
        colorClass: severityColorMap[severity].text,
        bgColorClass: severityColorMap[severity].bg,
        borderColorClass: severityColorMap[severity].border,
        displayGroup: "Tafkheem/Tarqeeq",
        comparisonData: {
          userValue: p,
          correctValue: "emphatic " + r,
        },
      });
    }
  }

  return violations;
}

// GHUNNAH RULES
export function checkGhunnahRules(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  // Helper to check if a letter follows in the reference
  const hasNextLetter = (idx: number, letterSet: Set<string>): boolean => {
    return idx + 1 < ref_phs.length && letterSet.has(ref_phs[idx + 1]);
  };

  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    // Ghunnah with noon/meem
    if (classifications.makharij_khayshum.has(r)) {
      if (hasNextLetter(i, classifications.ikhfa_letters)) {
        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";
        const severity = classifyViolationSeverity(
          "ghunnah-ikhfa",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "ghunnah-ikhfa",
          message: `Ghunnah Ikhfa issue with '${arabic_char}' before ikhfa letter '${nextArabic}' (should have nasal quality)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Ghunnah",
          comparisonData: {
            userValue: p,
            correctValue: "nasal " + r,
          },
        });
      } else if (hasNextLetter(i, classifications.idgham_with_ghunnah)) {
        // ENHANCEMENT: Check if this is an Idgham with Ghunnah situation where
        // the noon has been properly merged with the following letter
        const nextLetter = i + 1 < ref_phs.length ? ref_phs[i + 1] : null;
        if (nextLetter && r === "n" && matchesIdghamPattern(r, nextLetter, p)) {
          // This is correct application - no violation
          continue;
        }

        const nextArabic = i + 1 < arabicMap.length ? arabicMap[i + 1] : "?";
        const severity = classifyViolationSeverity(
          "ghunnah-idgham",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "ghunnah-idgham",
          message: `Ghunnah Idgham issue with '${arabic_char}' before idgham letter '${nextArabic}' (missing proper nasalization when merging)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Ghunnah",
          comparisonData: {
            userValue: p,
            correctValue: "nasal merged",
          },
        });
      } else {
        const severity = classifyViolationSeverity(
          "ghunnah-mushaddadah",
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: "ghunnah-mushaddadah",
          message: `Ghunnah issue with nasal '${arabic_char}' (should have nasalization)`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Ghunnah",
          comparisonData: {
            userValue: p,
            correctValue: "nasal " + r,
          },
        });
      }
    }
  }

  return violations;
}

// MAKHARIJ RULES (ARTICULATION POINTS)
export function checkMakharijRules(
  ref_phs: string[],
  pred_phs: string[],
  subs: any[],
  arabicMap: string[],
  wordMap: number[],
  letterPos: string[],
  wordOriginals: string[],
  context?: RecitationContext | null
): Violation[] {
  const violations: Violation[] = [];

  for (const [i, r, p, arabic_char, word_idx, word, letter_position] of subs) {
    // ENHANCEMENT: Special handling for Idgham with Ghunnah to avoid false positives
    const nextLetter = i + 1 < ref_phs.length ? ref_phs[i + 1] : null;
    if (
      r === "n" &&
      nextLetter &&
      classifications.idgham_with_ghunnah.has(nextLetter)
    ) {
      // Check if this is a correct application of Idgham with Ghunnah
      if (matchesIdghamPattern(r, nextLetter, p)) {
        // Skip checking articulation point for correctly merged noon
        continue;
      }
    }

    // Identify the category of the reference phoneme
    const categoryMappings: [string, Set<string>][] = [
      ["makharij-jawf", classifications.makharij_jawf],
      ["makharij-halq", classifications.makharij_halq],
      ["makharij-lisan", classifications.makharij_lisan],
      ["makharij-shafatan", classifications.makharij_shafatan],
    ];

    for (const [categoryName, phonemeSet] of categoryMappings) {
      if (phonemeSet.has(r)) {
        const severity = classifyViolationSeverity(
          categoryName,
          i,
          r,
          p,
          context
        );
        violations.push({
          rule: categoryName,
          message: `Articulation issue (${categoryName}) with '${arabic_char}' (pronounced as '${p}' instead of '${r}')`,
          severity: severity,
          position: i,
          ref_phoneme: r,
          pred_phoneme: p,
          arabic_char: arabic_char,
          word_idx: word_idx,
          word: word,
          letter_position: letter_position,
          // Add UI specific fields
          colorClass: severityColorMap[severity].text,
          bgColorClass: severityColorMap[severity].bg,
          borderColorClass: severityColorMap[severity].border,
          displayGroup: "Articulation Points",
          comparisonData: {
            userValue: p,
            correctValue: r,
          },
        });
        break;
      }
    }
  }

  return violations;
}

/* ------------------------------------------------------------------ */
/* 8.  Enhanced report generation                                     */
/* ------------------------------------------------------------------ */
export function generateLetterPositionReport(
  all_violations: Violation[],
  wordOriginals: string[]
): string {
  if (!all_violations || all_violations.length === 0) {
    return "✅ Perfect Tajwīd for this recitation";
  }

  // Group violations by word index
  const violations_by_word: Record<number, Violation[]> = {};
  for (const v of all_violations) {
    const word_idx = v.word_idx;
    if (!violations_by_word[word_idx]) {
      violations_by_word[word_idx] = [];
    }
    violations_by_word[word_idx].push(v);
  }

  // Prepare output
  const output: string[] = [];
  const total_violations = all_violations.length;

  // Count severity types
  const severity_counts: Record<string, number> = {};
  for (const v of all_violations) {
    const sev = v.severity;
    severity_counts[sev] = (severity_counts[sev] || 0) + 1;
  }

  const severity_summary = Object.entries(severity_counts)
    .map(([sev, count]) => {
      // Add colored spans for each severity level
      let colorClass = "";
      switch (sev) {
        case "critical":
          colorClass = "text-red-600";
          break;
        case "moderate":
          colorClass = "text-amber-600";
          break;
        case "mild":
          colorClass = "text-yellow-600";
          break;
        case "stylistic":
          colorClass = "text-purple-600";
          break;
      }
      return `<span class="${colorClass} font-medium">${count} ${sev}</span>`;
    })
    .join(", ");

  output.push(
    `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
      <div class="flex items-center space-x-3">
        <div class="w-2.5 h-2.5 bg-gray-400 rounded-full"></div>
        <h3 class="text-lg font-semibold text-gray-900 tracking-tight">Assessment Report</h3>
      </div>
      <p class="mt-4 text-gray-700">Found ${total_violations} potential Tajweed issues: ${severity_summary}</p>
    </div>`
  );

  // Output grouped by word and then by letter with position
  for (const word_idx of Object.keys(violations_by_word).map(Number).sort()) {
    let word_display = "Unknown word";
    if (word_idx >= 0 && word_idx < wordOriginals.length) {
      word_display = wordOriginals[word_idx];
    }

    const word_violations = violations_by_word[word_idx];
    output.push(
      `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
        <div class="flex items-center space-x-3">
          <div class="w-2.5 h-2.5 bg-blue-500 rounded-full"></div>
          <h3 class="text-lg font-semibold text-gray-900 tracking-tight">Word: '${word_display}' (${word_violations.length} issues)</h3>
        </div>
        <div class="mt-4 space-y-4">`
    );

    // Group by letter within each word (with position)
    const violations_by_letter_position: Record<string, Violation[]> = {};
    for (const v of word_violations) {
      const letter = v.arabic_char;
      const position = v.letter_position;
      const letter_key = `${letter} (${position})`;

      if (!violations_by_letter_position[letter_key]) {
        violations_by_letter_position[letter_key] = [];
      }
      violations_by_letter_position[letter_key].push(v);
    }

    // Print violations by letter for this word
    for (const letter_key of Object.keys(violations_by_letter_position)) {
      const letter_violations = violations_by_letter_position[letter_key];
      output.push(
        `<div class="bg-gray-50 rounded-xl p-4">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
            <h4 class="text-base font-medium text-gray-800">Letter: '${letter_key}' (${letter_violations.length} issues)</h4>
          </div>
          <div class="mt-3 space-y-3">`
      );

      // Group violations by rule within each letter
      const rules_for_letter: Record<string, Violation[]> = {};
      for (const v of letter_violations) {
        const rule = v.rule;
        if (!rules_for_letter[rule]) {
          rules_for_letter[rule] = [];
        }
        rules_for_letter[rule].push(v);
      }

      // Print violations by rule for this letter
      for (const [rule, violations] of Object.entries(rules_for_letter)) {
        const ruleColor = ruleColorMap[rule] || "bg-gray-500";

        output.push(
          `<div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 ${ruleColor} rounded-full"></div>
              <h5 class="text-sm font-medium text-gray-800">${rule.toUpperCase()} (${
            violations.length
          } issues)</h5>
            </div>
            <ul class="mt-2 space-y-2">`
        );

        for (const v of violations) {
          const severityColor = severityColorMap[v.severity].dot;
          output.push(
            `<li class="flex items-baseline space-x-2 text-sm">
              <div class="w-1.5 h-1.5 ${severityColor} rounded-full mt-1.5"></div>
              <span class="text-gray-700">${v.message}</span>
            </li>`
          );
        }

        output.push(
          `</ul>
          </div>`
        );
      }

      output.push(
        `</div>
        </div>`
      );
    }

    output.push(
      `</div>
      </div>`
    );
  }

  return output.join("\n");
}

export function generateSeverityBasedReport(
  all_violations: Violation[],
  min_severity: Severity = "stylistic",
  group_by_severity: boolean = true
): string {
  // Define severity order for sorting
  const severity_order: Record<Severity, number> = {
    critical: 0,
    moderate: 1,
    mild: 2,
    stylistic: 3,
  };

  // Filter violations by minimum severity
  const severity_levels: Severity[] = Object.keys(severity_order) as Severity[];
  const min_idx = severity_levels.indexOf(min_severity);
  const filtered_violations = all_violations.filter(
    (v) => severity_levels.indexOf(v.severity) <= min_idx
  );

  if (filtered_violations.length === 0) {
    return `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
      <div class="flex items-center space-x-3">
        <div class="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
        <h3 class="text-lg font-semibold text-gray-900 tracking-tight">Perfect Tajwīd</h3>
      </div>
      <p class="mt-4 text-gray-700">✅ Perfect Tajwīd for this recitation (within selected severity threshold)</p>
    </div>`;
  }

  // Prepare output
  const output: string[] = [];

  // Add summary card
  output.push(
    `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
      <div class="flex items-center space-x-3">
        <div class="w-2.5 h-2.5 bg-amber-500 rounded-full"></div>
        <h3 class="text-lg font-semibold text-gray-900 tracking-tight">Tajweed Assessment</h3>
      </div>
      <p class="mt-4 text-gray-700">Found ${filtered_violations.length} potential Tajweed issues:</p>
    </div>`
  );

  if (group_by_severity) {
    // Group by severity first, then by rule
    const by_severity: { [S in Severity]?: Record<string, Violation[]> } = {};

    for (const v of filtered_violations) {
      const sev = v.severity;
      if (!by_severity[sev]) {
        by_severity[sev] = {};
      }

      const rule = v.rule;
      if (!by_severity[sev]![rule]) {
        by_severity[sev]![rule] = [];
      }

      by_severity[sev]![rule].push(v);
    }

    // Output grouped by severity
    for (const severity of severity_levels.filter((s) => by_severity[s])) {
      const severityColors = severityColorMap[severity];

      output.push(
        `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
          <div class="flex items-center space-x-3">
            <div class="w-2.5 h-2.5 ${severityColors.dot} rounded-full"></div>
            <h3 class="text-lg font-semibold text-gray-900 tracking-tight">${severity.toUpperCase()} ISSUES</h3>
          </div>
          <div class="mt-4 space-y-4">`
      );

      const recordForSeverity = by_severity[severity];

      if (recordForSeverity) {
        for (const [rule, violations] of Object.entries(recordForSeverity)) {
          const ruleColor = ruleColorMap[rule] || "bg-gray-500";

          output.push(
            `<div class="bg-${severityColors.bg} p-4 rounded-xl border ${
              severityColors.border
            }">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 ${ruleColor} rounded-full"></div>
                <h4 class="text-base font-medium ${
                  severityColors.text
                }">${rule.toUpperCase()} (${violations.length} issues)</h4>
              </div>
              <ul class="mt-3 space-y-2">`
          );

          for (const v of violations) {
            // Add comparison data if available
            let comparisonHtml = "";
            if (v.comparisonData) {
              comparisonHtml = `
                <div class="flex items-center justify-center space-x-6 my-3">
                  <div class="flex flex-col items-center">
                    <span class="text-xs font-medium text-gray-500 mb-1.5">You said</span>
                    <span class="bg-red-50 text-red-600 text-lg px-3 py-1.5 font-normal rounded-lg">
                      ${v.comparisonData.userValue || "?"}
                    </span>
                  </div>
                  <div class="text-gray-300">→</div>
                  <div class="flex flex-col items-center">
                    <span class="text-xs font-medium text-gray-500 mb-1.5">Correct</span>
                    <span class="bg-green-50 text-green-600 text-lg px-3 py-1.5 font-normal rounded-lg">
                      ${v.comparisonData.correctValue || "?"}
                    </span>
                  </div>
                </div>`;
            }

            output.push(
              `<li class="bg-white p-3 rounded-lg shadow-sm">
                <div class="flex items-baseline space-x-2">
                  <div class="w-1.5 h-1.5 ${severityColors.dot} rounded-full mt-1.5"></div>
                  <span class="text-gray-700">${v.message}</span>
                </div>
                ${comparisonHtml}
                <div class="mt-2 text-xs text-gray-500">
                  Letter: <span class="font-medium">'${v.arabic_char}'</span> in word <span class="font-medium">'${v.word}'</span> ${v.letter_position}
                </div>
              </li>`
            );
          }

          output.push(
            `</ul>
            </div>`
          );
        }
      }

      output.push(
        `</div>
        </div>`
      );
    }
  } else {
    // Group by rule only
    const rule_categories: Record<string, Violation[]> = {};
    for (const v of filtered_violations) {
      const rule = v.rule;
      if (!rule_categories[rule]) {
        rule_categories[rule] = [];
      }
      rule_categories[rule].push(v);
    }

    for (const [category, violations] of Object.entries(rule_categories)) {
      const ruleColor = ruleColorMap[category] || "bg-gray-500";

      output.push(
        `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
          <div class="flex items-center space-x-3">
            <div class="w-2.5 h-2.5 ${ruleColor} rounded-full"></div>
            <h3 class="text-lg font-semibold text-gray-900 tracking-tight">${category.toUpperCase()} (${
          violations.length
        } issues)</h3>
          </div>
          <ul class="mt-4 space-y-3">`
      );

      for (const v of violations) {
        const severityColors = severityColorMap[v.severity];

        // Add comparison data if available
        let comparisonHtml = "";
        if (v.comparisonData) {
          comparisonHtml = `
            <div class="flex items-center justify-center space-x-6 my-3">
              <div class="flex flex-col items-center">
                <span class="text-xs font-medium text-gray-500 mb-1.5">You said</span>
                <span class="bg-red-50 text-red-600 text-lg px-3 py-1.5 font-normal rounded-lg">
                  ${v.comparisonData.userValue || "?"}
                </span>
              </div>
              <div class="text-gray-300">→</div>
              <div class="flex flex-col items-center">
                <span class="text-xs font-medium text-gray-500 mb-1.5">Correct</span>
                <span class="bg-green-50 text-green-600 text-lg px-3 py-1.5 font-normal rounded-lg">
                  ${v.comparisonData.correctValue || "?"}
                </span>
              </div>
            </div>`;
        }

        output.push(
          `<li class="bg-${severityColors.bg} p-4 rounded-xl border ${severityColors.border}">
            <div class="flex items-baseline space-x-2">
              <div class="w-1.5 h-1.5 ${severityColors.dot} rounded-full mt-1.5"></div>
              <span class="${severityColors.text} font-medium">(${v.severity})</span>
              <span class="text-gray-700">${v.message}</span>
            </div>
            ${comparisonHtml}
            <div class="mt-2 text-xs text-gray-500">
              Letter: <span class="font-medium">'${v.arabic_char}'</span> in word <span class="font-medium">'${v.word}'</span> ${v.letter_position}
            </div>
          </li>`
        );
      }

      output.push(
        `</ul>
        </div>`
      );
    }
  }

  return output.join("\n");
}

/* ------------------------------------------------------------------ */
/* 9.  Enhanced score calculation                                     */
/* ------------------------------------------------------------------ */
export function computeScore(violations: Violation[]): number {
  let penalty = 0;
  let criticalCount = 0;
  let moderateCount = 0;
  let mildCount = 0;

  // Category penalties to track detailed scores
  const rulePenalties: Record<string, number> = {};
  const categoryPenalties: Record<string, number> = {
    "Noon Rules": 0,
    "Meem Rules": 0,
    Elongation: 0,
    Articulation: 0,
    Emphasis: 0,
    Other: 0,
  };

  for (const v of violations) {
    let violationPenalty = 0;

    // Calculate penalty based on severity
    if (v.severity === "critical") {
      violationPenalty = 15;
      criticalCount++;
    } else if (v.severity === "moderate") {
      violationPenalty = 5;
      moderateCount++;
    } else if (v.severity === "mild") {
      violationPenalty = 2;
      mildCount++;
    }
    // No penalty for stylistic variations

    // Track penalty by rule
    const rule = v.rule;
    rulePenalties[rule] = (rulePenalties[rule] || 0) + violationPenalty;

    // Track penalty by category
    if (rule.includes("noon")) {
      categoryPenalties["Noon Rules"] += violationPenalty;
    } else if (rule.includes("meem")) {
      categoryPenalties["Meem Rules"] += violationPenalty;
    } else if (rule.includes("madd")) {
      categoryPenalties["Elongation"] += violationPenalty;
    } else if (rule.includes("makharij")) {
      categoryPenalties["Articulation"] += violationPenalty;
    } else if (rule.includes("tafkheem") || rule.includes("tarqeeq")) {
      categoryPenalties["Emphasis"] += violationPenalty;
    } else {
      categoryPenalties["Other"] += violationPenalty;
    }

    penalty += violationPenalty;
  }

  // Calculate final score
  const finalScore = Math.max(0, 100 - penalty);

  // Attach detailed scoring to the violation objects if needed for UI
  (violations as any).detailedScore = {
    total: finalScore,
    byRule: rulePenalties,
    byCategory: categoryPenalties,
    criticalCount,
    moderateCount,
    mildCount,
  };

  return finalScore;
}

/* ------------------------------------------------------------------ */
/* 10.  Public API: getFeedback(audioBlob, referenceText)              */
/* ------------------------------------------------------------------ */
export async function getFeedback(
  audio: Blob,
  referenceText: string
): Promise<Feedback> {
  //------------------------------------------------------------------
  // 10‑A  call HuggingFace endpoint → phoneme tokens
  //------------------------------------------------------------------
  const b64 = await audio
    .arrayBuffer()
    .then((buf) => Buffer.from(buf).toString("base64"));

  const { tokens }: { tokens: string[] } = await fetch(
    process.env.NEXT_PUBLIC_HF_EP as string,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HF_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ inputs: b64 }),
    }
  ).then((r) => r.json());

  const predPhs = tokens.filter((t) => t !== "<unk>");

  //------------------------------------------------------------------
  // 10‑B  reference phonemes
  //------------------------------------------------------------------
  const {
    phonemes: refPhs,
    refArabicMap,
    wordMap,
    letterPositions,
    wordOriginals,
  } = convertToPhonemes(referenceText);

  //------------------------------------------------------------------
  // 10‑C  alignment + diff
  //------------------------------------------------------------------
  const alignment = simpleAlign(refPhs, predPhs);
  const { subs, ins, dels } = diffFromAlignment(
    alignment,
    refPhs,
    predPhs,
    refArabicMap,
    wordMap,
    letterPositions,
    wordOriginals
  );

  //------------------------------------------------------------------
  // 10‑D  detect recitation context
  //------------------------------------------------------------------
  // Estimate duration from audio size (rough calculation)
  const durationSec = (audio.size / 16000) * (1 / 2); // ≈16 kB per sec for 16 kHz mono pcm
  const recitationContext = detectRecitationStyle(predPhs, durationSec);

  //------------------------------------------------------------------
  // 10‑E  apply all rule checks with context
  //------------------------------------------------------------------
  let violations: Violation[] = [];

  // Apply all rule checks with context
  violations = violations.concat(
    checkQalqalah(
      refPhs,
      subs,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkNoonTanweenRules(
      refPhs,
      predPhs,
      subs,
      dels,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkMeemRules(
      refPhs,
      predPhs,
      subs,
      dels,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkMaddRules(
      refPhs,
      predPhs,
      subs,
      dels,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkLaamRules(
      refPhs,
      predPhs,
      subs,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkTafkheemTarqeeq(
      refPhs,
      predPhs,
      subs,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkGhunnahRules(
      refPhs,
      predPhs,
      subs,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  violations = violations.concat(
    checkMakharijRules(
      refPhs,
      predPhs,
      subs,
      refArabicMap,
      wordMap,
      letterPositions,
      wordOriginals,
      recitationContext
    )
  );

  //------------------------------------------------------------------
  // 10‑F  compute score & build human report
  //------------------------------------------------------------------
  const score = computeScore(violations);
  const reportLines: string[] = [];

  // Generate standard text report
  if (!violations.length) reportLines.push("✅ Perfect Tajwīd – well done!");
  else {
    reportLines.push(
      `Found ${violations.length} issues. Overall score: ${score}%.`
    );
    violations.forEach((v) =>
      reportLines.push(`• (${v.severity}) ${v.message}`)
    );
  }

  // Generate enhanced HTML report
  const htmlReport = generateSeverityBasedReport(violations, "stylistic", true);

  return {
    score,
    violations,
    report: reportLines.join("\n"),
    recitationContext,
    summaryHTML: htmlReport,
  };
}

/* ------------------------------------------------------------------ */
/* 11.  Utility for the UI  (very small wrapper)                       */
/* ------------------------------------------------------------------ */
export function formatReportForHtml(report: string) {
  if (report.startsWith("<div")) {
    // Already HTML formatted, return as is
    return report;
  }

  // Otherwise, convert to HTML with proper styling
  const lines = report.split("\n");
  const formattedLines = lines.map((line) => {
    if (line.startsWith("✅")) {
      return `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
                <div class="flex items-center space-x-3">
                  <div class="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
                  <h3 class="text-lg font-semibold text-gray-900 tracking-tight">Perfect Tajwīd</h3>
                </div>
                <p class="mt-4 text-gray-700">${line}</p>
              </div>`;
    } else if (line.startsWith("Found")) {
      return `<div class="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md">
                <div class="flex items-center space-x-3">
                  <div class="w-2.5 h-2.5 bg-amber-500 rounded-full"></div>
                  <h3 class="text-lg font-semibold text-gray-900 tracking-tight">Tajweed Assessment</h3>
                </div>
                <p class="mt-4 text-gray-700">${line}</p>
              </div>`;
    } else if (line.startsWith("•")) {
      // Determine severity from the line
      let severityClass = "bg-amber-500";
      if (line.includes("(critical)")) severityClass = "bg-red-500";
      else if (line.includes("(moderate)")) severityClass = "bg-amber-500";
      else if (line.includes("(mild)")) severityClass = "bg-yellow-500";
      else if (line.includes("(stylistic)")) severityClass = "bg-purple-400";

      return `<p class="flex items-start space-x-2 mb-2">
                <span class="w-1.5 h-1.5 ${severityClass} rounded-full mt-1.5"></span>
                <span class="text-gray-700">${line.substring(2)}</span>
              </p>`;
    }
    return `<p class="mb-2">${line}</p>`;
  });

  return formattedLines.join("");
}
