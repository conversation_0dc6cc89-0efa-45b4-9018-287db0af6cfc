// /tajweed/tajweedPatterns.ts (Enhanced Combined File)

/**
 * Base color palette for semantic grouping by rule function.
 * These are used to assign consistent colors to related Tajweed rules.
 */
const baseHighlightColors: Record<string, string> = {
  ghunnah: "#1e40af", // nasal sounds (blue)
  idgham: "#0ea5e9", // merging sounds (light blue)
  ikhfa: "#6366f1", // hiding/concealing sounds (indigo)
  iqlab: "#ec4899", // converting sounds (pink)
  izhar: "#16a34a", // clear pronunciation (green)
  qalqalah: "#d97706", // bouncing/echo (amber)
  madd: "#0891b2", // elongation (cyan)
  tafkheem: "#b91c1c", // heavy pronunciation (red)
  tarqeeq: "#f97316", // light pronunciation (orange)
  laam: "#9333ea", // laam rules (purple)
  makharij: "#84cc16", // articulation points (lime)
  sifaat: "#14b8a6", // letter characteristics (teal)
  hamzah: "#3b82f6", // hamza rules (blue)
  waqf: "#8b5cf6", // stopping rules (violet)
  defaultGrey: "#6b7280", // fallback neutral (gray)
};

/**
 * Default color used when no specific color is defined for a rule.
 */
export const fallbackTajweedColor = baseHighlightColors.defaultGrey;

/**
 * Interface defining the structure for each Tajweed rule's data,
 * including its visual patterns and highlight color.
 */
export interface TajweedRuleInfo {
  patterns: string[];
  color: string;
  description?: string; // Optional short description
  approximate?: boolean; // Flag for rules that depend on context beyond adjacent characters
  priority?: number; // Higher numbers take precedence when multiple rules match the same text
}

/**
 * Main data export. Maps Tajweed rule IDs to their associated visual patterns
 * for highlighting and the color to be used for that highlight.
 *
 * IMPORTANT NOTES:
 * 1. Patterns are meant for simple string matching and have limitations.
 * 2. Rules or patterns marked [APPROXIMATE] depend on context beyond
 *    adjacent characters and may not always be detected reliably by simple matching.
 * 3. Some rule patterns are simplified to reduce false positives.
 * 4. Unicode normalization variations are included for better matching.
 */
export const tajweedRuleData: Record<string, TajweedRuleInfo> = {
  // == Makharij al-Huruf (Points of Articulation) ==
  "makharij-jawf": {
    patterns: [
      "ا",
      "و",
      "ي", // Basic long vowels
      "آ",
      "ٱ",
      "أ",
      "إ", // Alif variations
      "ۤ",
      "ٰ",
      "ۡ", // Madd symbols and dagger alif
      "َا",
      "ِي",
      "ُو", // Vowel + long vowel combinations
    ],
    color: baseHighlightColors.makharij,
    description: "Oral cavity - long vowels formed without obstruction",
    priority: 1,
  },
  "makharij-halq": {
    patterns: [
      "ء",
      "ه",
      "ع",
      "ح",
      "غ",
      "خ", // Basic throat letters
      "أ",
      "إ",
      "ؤ",
      "ئ", // Hamza in different positions
      "ة",
      "ۃ", // Ta marbuta variations
    ],
    color: baseHighlightColors.makharij,
    description: "Throat letters articulated at various depths",
    priority: 2,
  },
  "makharij-lisan": {
    patterns: [
      // Tongue letters - comprehensive list
      "ق",
      "ك",
      "ج",
      "ش",
      "ي",
      "ض",
      "ل",
      "ن",
      "ر",
      "ت",
      "د",
      "ط",
      "ظ",
      "س",
      "ز",
      "ص",
      "ث",
      "ذ",
      // With diacritics for common combinations
      "قْ",
      "كْ",
      "جْ",
      "شْ",
      "يْ",
      "ضْ",
      "لْ",
      "نْ",
      "رْ",
      "تْ",
      "دْ",
      "طْ",
      "ظْ",
      "سْ",
      "زْ",
      "صْ",
      "ثْ",
      "ذْ",
    ],
    color: baseHighlightColors.makharij,
    description: "Letters articulated with various parts of the tongue",
    priority: 1,
  },
  "makharij-shafatain": {
    patterns: [
      "ف",
      "و",
      "ب",
      "م", // Basic lip letters
      "فْ",
      "وْ",
      "بْ",
      "مْ", // With sukoon
      "فّ",
      "وّ",
      "بّ",
      "مّ", // With shaddah
    ],
    color: baseHighlightColors.makharij,
    description: "Letters formed by lip contact or positioning",
    priority: 1,
  },
  "makharij-khayshum": {
    patterns: [
      "نّ",
      "مّ", // Primary patterns
      "نَّ",
      "نِّ",
      "نُّ",
      "مَّ",
      "مِّ",
      "مُّ", // With vowels
      "ۭ",
      "ۢ", // Small meem and noon symbols
    ],
    color: baseHighlightColors.ghunnah, // Using ghunnah color for nasal cavity
    description: "Nasal passage - produces ghunnah resonance",
    priority: 3,
  },

  // == Sifaat al-Huruf (Characteristics) ==
  "sifaat-jahr": {
    patterns: [
      "ب",
      "ج",
      "د",
      "ذ",
      "ر",
      "ز",
      "ض",
      "ظ",
      "ع",
      "غ",
      "ل",
      "م",
      "ن",
      "و",
      "ي",
      "ا",
      "ء",
      // With common diacritics
      "بَ",
      "بِ",
      "بُ",
      "بْ",
      "جَ",
      "جِ",
      "جُ",
      "جْ",
      "دَ",
      "دِ",
      "دُ",
      "دْ",
      "ذَ",
      "ذِ",
      "ذُ",
      "ذْ",
    ],
    color: baseHighlightColors.sifaat,
    description: "Voiced letters - pronounced with vocal cord vibration",
    priority: 1,
  },
  "sifaat-hams": {
    patterns: [
      "ت",
      "ث",
      "ح",
      "خ",
      "س",
      "ش",
      "ص",
      "ط",
      "ف",
      "ق",
      "ك",
      "ه",
      // With common diacritics
      "تَ",
      "تِ",
      "تُ",
      "تْ",
      "ثَ",
      "ثِ",
      "ثُ",
      "ثْ",
      "حَ",
      "حِ",
      "حُ",
      "حْ",
      "خَ",
      "خِ",
      "خُ",
      "خْ",
    ],
    color: baseHighlightColors.sifaat,
    description:
      "Whispered letters - pronounced with breath, no vocal vibration",
    priority: 1,
  },
  "sifaat-shiddah": {
    patterns: [
      "ء",
      "ت",
      "ج",
      "د",
      "ط",
      "ب",
      "ق",
      "ك",
      // With shaddah for emphasis
      "تّ",
      "جّ",
      "دّ",
      "طّ",
      "بّ",
      "قّ",
      "كّ",
    ],
    color: baseHighlightColors.sifaat,
    description: "Strong letters - complete airflow stoppage",
    priority: 1,
  },
  "sifaat-rakhawah": {
    patterns: [
      "ث",
      "ح",
      "خ",
      "ذ",
      "ز",
      "س",
      "ش",
      "ص",
      "ض",
      "ظ",
      "غ",
      "ف",
      "ه",
      "و",
      "ي",
      "ا",
      // With common diacritics
      "ثَ",
      "ثِ",
      "ثُ",
      "ثْ",
      "حَ",
      "حِ",
      "حُ",
      "حْ",
    ],
    color: baseHighlightColors.sifaat,
    description: "Soft letters - partial airflow allowed",
    priority: 1,
  },

  // == Noon Saakin & Tanween Rules ==
  "idgham-ghunnah": {
    patterns: [
      // Noon Saakin patterns
      "نْ ي",
      "نْي",
      "نْ و",
      "نْو",
      "نْ م",
      "نْم",
      "نْ ن",
      "نْن",
      // Tanween Fatha variations
      "ًا ي",
      "ًاي",
      "اً ي",
      "اًي",
      "ً ي",
      "ًي",
      "ًا و",
      "ًاو",
      "اً و",
      "اًو",
      "ً و",
      "ًو",
      "ًا م",
      "ًام",
      "اً م",
      "اًم",
      "ً م",
      "ًم",
      "ًا ن",
      "ًان",
      "اً ن",
      "اًن",
      "ً ن",
      "ًن",
      // Tanween Kasra
      "ٍ ي",
      "ٍي",
      "ٍ و",
      "ٍو",
      "ٍ م",
      "ٍم",
      "ٍ ن",
      "ٍن",
      // Tanween Damma
      "ٌ ي",
      "ٌي",
      "ٌ و",
      "ٌو",
      "ٌ م",
      "ٌم",
      "ٌ ن",
      "ٌن",
      // With vowel diacritics on following letters
      "نْ يَ",
      "نْ يِ",
      "نْ يُ",
      "نْ وَ",
      "نْ وِ",
      "نْ وُ",
      "نْ مَ",
      "نْ مِ",
      "نْ مُ",
      "نْ نَ",
      "نْ نِ",
      "نْ نُ",
      // Merged forms with shaddah
      "يّ",
      "وّ",
      "مّ",
      "نّ", // When appearing after potential noon/tanween
    ],
    color: baseHighlightColors.ghunnah,
    description: "Merging with nasalization - ي و م ن letters",
    priority: 4,
  },
  "idgham-without-ghunnah": {
    patterns: [
      // Noon Saakin patterns
      "نْ ل",
      "نْل",
      "نْ ر",
      "نْر",
      // Tanween Fatha variations
      "ًا ل",
      "ًال",
      "اً ل",
      "اًل",
      "ً ل",
      "ًل",
      "ًا ر",
      "ًار",
      "اً ر",
      "اًر",
      "ً ر",
      "ًر",
      // Tanween Kasra
      "ٍ ل",
      "ٍل",
      "ٍ ر",
      "ٍر",
      // Tanween Damma
      "ٌ ل",
      "ٌل",
      "ٌ ر",
      "ٌر",
      // With vowel diacritics
      "نْ لَ",
      "نْ لِ",
      "نْ لُ",
      "نْ رَ",
      "نْ رِ",
      "نْ رُ",
      // Merged forms with shaddah
      "لّ",
      "رّ", // When appearing after potential noon/tanween
    ],
    color: baseHighlightColors.idgham,
    description: "Merging without nasalization - ل ر letters",
    priority: 4,
  },
  ikhfa: {
    patterns: [
      // Noon Saakin + 15 Ikhfa letters (complete list)
      "نْ ت",
      "نْت",
      "نْ ث",
      "نْث",
      "نْ ج",
      "نْج",
      "نْ د",
      "نْد",
      "نْ ذ",
      "نْذ",
      "نْ ز",
      "نْز",
      "نْ س",
      "نْس",
      "نْ ش",
      "نْش",
      "نْ ص",
      "نْص",
      "نْ ض",
      "نْض",
      "نْ ط",
      "نْط",
      "نْ ظ",
      "نْظ",
      "نْ ف",
      "نْف",
      "نْ q",
      "نْق",
      "نْ ك",
      "نْك",

      // Tanween Fatha + 15 letters
      "ًا ت",
      "ًات",
      "اً ت",
      "اًت",
      "ً ت",
      "ًت",
      "ًا ث",
      "ًاث",
      "اً ث",
      "اًث",
      "ً ث",
      "ًث",
      "ًا ج",
      "ًاج",
      "اً ج",
      "اًج",
      "ً ج",
      "ًج",
      "ًا د",
      "ًاد",
      "اً د",
      "اًد",
      "ً d",
      "ًد",
      "ًا ذ",
      "ًاذ",
      "اً ذ",
      "اًذ",
      "ً ذ",
      "ًذ",
      "ًا ز",
      "ًاز",
      "اً ز",
      "اًز",
      "ً ز",
      "ًز",
      "ًا س",
      "ًاس",
      "اً س",
      "اًس",
      "ً س",
      "ًس",
      "ًا ش",
      "ًاش",
      "اً ش",
      "اًش",
      "ً ش",
      "ًش",
      "ًا ص",
      "ًاص",
      "اً ص",
      "اًص",
      "ً ص",
      "ًص",
      "ًا ض",
      "ًاض",
      "اً ض",
      "اًض",
      "ً ض",
      "ًض",
      "ًا ط",
      "ًاط",
      "اً ط",
      "اًط",
      "ً ط",
      "ًط",
      "ًا ظ",
      "ًاظ",
      "اً ظ",
      "اًظ",
      "ً ظ",
      "ًظ",
      "ًا ف",
      "ًاف",
      "اً ف",
      "اًف",
      "ً ف",
      "ًف",
      "ًا ق",
      "ًاق",
      "اً ق",
      "اًق",
      "ً ق",
      "ًق",
      "ًا ك",
      "ًاك",
      "اً ك",
      "اًك",
      "ً ك",
      "ًك",

      // Tanween Kasra + 15 letters
      "ٍ ت",
      "ٍت",
      "ٍ ث",
      "ٍث",
      "ٍ ج",
      "ٍج",
      "ٍ د",
      "ٍد",
      "ٍ ذ",
      "ٍذ",
      "ٍ ز",
      "ٍز",
      "ٍ س",
      "ٍس",
      "ٍ ش",
      "ٍش",
      "ٍ ص",
      "ٍص",
      "ٍ ض",
      "ٍض",
      "ٍ ط",
      "ٍط",
      "ٍ ظ",
      "ٍظ",
      "ٍ ف",
      "ٍف",
      "ٍ ق",
      "ٍق",
      "ٍ ك",
      "ٍك",

      // Tanween Damma + 15 letters
      "ٌ ت",
      "ٌت",
      "ٌ ث",
      "ٌث",
      "ٌ ج",
      "ٌج",
      "ٌ د",
      "ٌد",
      "ٌ ذ",
      "ٌذ",
      "ٌ ز",
      "ٌز",
      "ٌ س",
      "ٌس",
      "ٌ ش",
      "ٌش",
      "ٌ ص",
      "ٌص",
      "ٌ ض",
      "ٌض",
      "ٌ ط",
      "ٌط",
      "ٌ ظ",
      "ٌظ",
      "ٌ ف",
      "ٌف",
      "ٌ ق",
      "ٌق",
      "ٌ ك",
      "ٌك",

      // With vowel diacritics on following letters
      "نْ تَ",
      "نْ تِ",
      "نْ تُ",
      "نْ ثَ",
      "نْ ثِ",
      "نْ ثُ",
      "نْ جَ",
      "نْ جِ",
      "نْ جُ",
      "نْ دَ",
      "نْ دِ",
      "نْ دُ",
    ],
    color: baseHighlightColors.ikhfa,
    description: "Concealment with partial nasalization - 15 letters",
    priority: 4,
  },
  iqlab: {
    patterns: [
      // Noon Saakin + Baa
      "نْ ب",
      "نْب",
      // Tanween + Baa
      "ًا ب",
      "ًاب",
      "اً ب",
      "اًب",
      "ً ب",
      "ًب",
      "ٍ ب",
      "ٍب",
      "ٌ ب",
      "ٌب",
      // With vowel diacritics
      "نْ بَ",
      "نْ بِ",
      "نْ بُ",
      // Special symbols
      "ۢ",
      "ۭ", // Small meem symbol often used to mark iqlab
      // Converted form (conceptual)
      "مْ ب",
      "مْب", // What it becomes after conversion
    ],
    color: baseHighlightColors.iqlab,
    description: "Conversion to meem before baa",
    priority: 5,
  },
  izhar: {
    patterns: [
      // Noon Saakin + 6 throat letters
      "نْ ء",
      "نْء",
      "نْ ه",
      "نْه",
      "نْ ع",
      "نْع",
      "نْ ح",
      "نْح",
      "نْ غ",
      "نْغ",
      "نْ خ",
      "نْخ",

      // Tanween Fatha + 6 throat letters
      "ًا ء",
      "ًاء",
      "اً ء",
      "اًء",
      "ً ء",
      "ًء",
      "ًا ه",
      "ًاه",
      "اً ه",
      "اًه",
      "ً ه",
      "ًه",
      "ًا ع",
      "ًاع",
      "اً ع",
      "اًع",
      "ً ع",
      "ًع",
      "ًا ح",
      "ًاح",
      "اً ح",
      "اًح",
      "ً ح",
      "ًح",
      "ًا غ",
      "ًاغ",
      "اً غ",
      "اًغ",
      "ً غ",
      "ًغ",
      "ًا خ",
      "ًاخ",
      "اً خ",
      "اًخ",
      "ً خ",
      "ًخ",

      // Tanween Kasra + 6 throat letters
      "ٍ ء",
      "ٍء",
      "ٍ ه",
      "ٍه",
      "ٍ ع",
      "ٍع",
      "ٍ ح",
      "ٍح",
      "ٍ غ",
      "ٍغ",
      "ٍ خ",
      "ٍخ",

      // Tanween Damma + 6 throat letters
      "ٌ ء",
      "ٌء",
      "ٌ ه",
      "ٌه",
      "ٌ ع",
      "ٌع",
      "ٌ ح",
      "ٌح",
      "ٌ غ",
      "ٌغ",
      "ٌ خ",
      "ٌخ",

      // With vowel diacritics
      "نْ أَ",
      "نْ أِ",
      "نْ أُ",
      "نْ هَ",
      "نْ هِ",
      "نْ هُ",
      "نْ عَ",
      "نْ عِ",
      "نْ عُ",
      "نْ حَ",
      "نْ حِ",
      "نْ حُ",
    ],
    color: baseHighlightColors.izhar,
    description: "Clear pronunciation before throat letters",
    priority: 4,
  },

  // == Meem Saakin Rules ==
  "idgham-shafawi": {
    patterns: [
      "مْ م",
      "مْم", // Basic patterns
      "مْ مَ",
      "مْ مِ",
      "مْ مُ", // With vowels
      "مّ", // Resulting shaddah
    ],
    color: baseHighlightColors.ghunnah, // Using ghunnah color due to nasalization
    description: "Lip merging - meem saakin before meem",
    priority: 5,
  },
  "ikhfa-shafawi": {
    patterns: [
      "مْ ب",
      "مْب", // Basic patterns
      "مْ بَ",
      "مْ بِ",
      "مْ بُ", // With vowels
      "ۭ", // Small meem symbol sometimes used
    ],
    color: baseHighlightColors.ikhfa,
    description: "Lip concealment - meem saakin before baa",
    priority: 5,
  },
  "izhar-shafawi": {
    patterns: [
      // Meem Saakin + all letters except م and ب (comprehensive list)
      "مْ ء",
      "مْء",
      "مْ ت",
      "مْت",
      "مْ ث",
      "مْث",
      "مْ ج",
      "مْج",
      "مْ ح",
      "مْح",
      "مْ خ",
      "مْخ",
      "مْ د",
      "مْد",
      "مْ ذ",
      "مْذ",
      "مْ ر",
      "مْر",
      "مْ ز",
      "مْز",
      "مْ س",
      "مْس",
      "مْ ش",
      "مْش",
      "مْ ص",
      "مْص",
      "مْ ض",
      "مْض",
      "مْ ط",
      "مْط",
      "مْ ظ",
      "مْظ",
      "مْ ع",
      "مْع",
      "مْ غ",
      "مْغ",
      "مْ ف",
      "مْف",
      "مْ ق",
      "مْق",
      "مْ ك",
      "مْك",
      "مْ ل",
      "مْل",
      "مْ ن",
      "مْن",
      "مْ ه",
      "مْه",
      "مْ و",
      "مْو",
      "مْ ي",
      "مْي",
      // With vowel diacritics
      "مْ تَ",
      "مْ تِ",
      "مْ تُ",
      "مْ ثَ",
      "مْ ثِ",
      "مْ ثُ",
      "مْ جَ",
      "مْ جِ",
      "مْ جُ",
      "مْ حَ",
      "مْ حِ",
      "مْ حُ",
    ],
    color: baseHighlightColors.izhar,
    description: "Clear pronunciation before non-labial letters",
    priority: 4,
  },

  // == Qalqalah ==
  qalqalah: {
    patterns: [
      // Basic letters with sukoon
      "قْ",
      "طْ",
      "بْ",
      "جْ",
      "دْ",
      // At word boundaries (qalqalah kubra)
      "قْ ",
      "طْ ",
      "بْ ",
      "جْ ",
      "دْ ",
      // In pausal forms
      "ق",
      "ط",
      "ب",
      "ج",
      "د", // When stopping on these letters
      // With shadda (for mutaharrik qalqalah scenarios)
      "قّ",
      "طّ",
      "بّ",
      "جّ",
      "دّ",
    ],
    color: baseHighlightColors.qalqalah,
    description: "Echoing pronunciation of ق ط ب ج د letters",
    approximate: true,
    priority: 3,
  },

  // == Ghunnah ==
  ghunnah: {
    patterns: [
      "نّ",
      "مّ", // Primary patterns
      "نَّ",
      "نِّ",
      "نُّ",
      "مَّ",
      "مِّ",
      "مُّ", // With vowels
      "نّْ",
      "مّْ", // With sukoon after shaddah
      "ۭ",
      "ۢ", // Ghunnah symbols
    ],
    color: baseHighlightColors.ghunnah,
    description: "Nasalization on noon and meem with shaddah",
    priority: 4,
  },

  // == Laam Rules ==
  "laam-allah": {
    patterns: [
      // Various forms of Allah
      "ٱللَّه",
      "ٱللّٰه",
      "اللَّه",
      "اللّٰه",
      "ٱللَّٰه",
      "لِلَّه",
      "بِٱللَّه",
      "بِاللَّه",
      "وَٱللَّه",
      "وَاللَّه",
      "فَٱللَّه",
      "فَاللَّه",
      "كَٱللَّه",
      "كَاللَّه",
      // With different connecting particles
      "ٱللَّهُ",
      "ٱللَّهِ",
      "ٱللَّهَ",
      "اللَّهُ",
      "اللَّهِ",
      "اللَّهَ",
    ],
    color: baseHighlightColors.laam,
    description: "Laam pronunciation in Allah varies by preceding vowel",
    approximate: true,
    priority: 5,
  },
  "laam-qamariyyah": {
    patterns: [
      // Definite article + 14 moon letters
      "الْء",
      "ٱلْء",
      "ال ء",
      "ٱل ء",
      "الأ",
      "ٱلأ",
      "الإ",
      "ٱلإ",
      "الْب",
      "ٱلْب",
      "ال ب",
      "ٱل ب",
      "البَ",
      "البِ",
      "البُ",
      "الْج",
      "ٱلْج",
      "ال ج",
      "ٱل ج",
      "الجَ",
      "الجِ",
      "الجُ",
      "الْح",
      "ٱلْح",
      "ال ح",
      "ٱل ح",
      "الحَ",
      "الحِ",
      "الحُ",
      "الْخ",
      "ٱلْخ",
      "ال خ",
      "ٱل خ",
      "الخَ",
      "الخِ",
      "الخُ",
      "الْع",
      "ٱلْع",
      "ال ع",
      "ٱل ع",
      "العَ",
      "العِ",
      "العُ",
      "الْغ",
      "ٱلْغ",
      "ال غ",
      "ٱل غ",
      "الغَ",
      "الغِ",
      "الغُ",
      "الْف",
      "ٱلْف",
      "ال ف",
      "ٱل ف",
      "الفَ",
      "الفِ",
      "الفُ",
      "الْق",
      "ٱلْق",
      "ال ق",
      "ٱل ق",
      "القَ",
      "القِ",
      "القُ",
      "الْك",
      "ٱلْك",
      "ال ك",
      "ٱل ك",
      "الكَ",
      "الكِ",
      "الكُ",
      "الْم",
      "ٱلْم",
      "ال م",
      "ٱل م",
      "المَ",
      "المِ",
      "المُ",
      "الْه",
      "ٱلْه",
      "ال ه",
      "ٱل ه",
      "الهَ",
      "الهِ",
      "الهُ",
      "الْو",
      "ٱلْو",
      "ال و",
      "ٱل و",
      "الوَ",
      "الوِ",
      "الوُ",
      "الْي",
      "ٱلْي",
      "ال ي",
      "ٱل ي",
      "اليَ",
      "اليِ",
      "اليُ",
    ],
    color: baseHighlightColors.izhar,
    description: "Clear laam before moon letters",
    priority: 4,
  },
  "laam-shamsiyyah": {
    patterns: [
      // Definite article + 14 sun letters with shaddah
      "التّ",
      "ٱلتّ",
      "ال تّ",
      "ٱل تّ",
      "التَّ",
      "التِّ",
      "التُّ",
      "الثّ",
      "ٱلثّ",
      "ال ثّ",
      "ٱل ثّ",
      "الثَّ",
      "الثِّ",
      "الثُّ",
      "الدّ",
      "ٱلدّ",
      "ال دّ",
      "ٱل دّ",
      "الدَّ",
      "الدِّ",
      "الدُّ",
      "الذّ",
      "ٱلذّ",
      "ال ذّ",
      "ٱل ذّ",
      "الذَّ",
      "الذِّ",
      "الذُّ",
      "الرّ",
      "ٱلرّ",
      "ال رّ",
      "ٱل رّ",
      "الرَّ",
      "الرِّ",
      "الرُّ",
      "الزّ",
      "ٱلزّ",
      "ال زّ",
      "ٱل زّ",
      "الزَّ",
      "الزِّ",
      "الزُّ",
      "السّ",
      "ٱلسّ",
      "ال سّ",
      "ٱل سّ",
      "السَّ",
      "السِّ",
      "السُّ",
      "الشّ",
      "ٱلشّ",
      "ال شّ",
      "ٱل شّ",
      "الشَّ",
      "الشِّ",
      "الشُّ",
      "الصّ",
      "ٱلصّ",
      "ال صّ",
      "ٱل صّ",
      "الصَّ",
      "الصِّ",
      "الصُّ",
      "الضّ",
      "ٱلضّ",
      "ال ضّ",
      "ٱل ضّ",
      "الضَّ",
      "الضِّ",
      "الضُّ",
      "الطّ",
      "ٱلطّ",
      "ال طّ",
      "ٱل طّ",
      "الطَّ",
      "الطِّ",
      "الطُّ",
      "الظّ",
      "ٱلظّ",
      "ال ظّ",
      "ٱل ظّ",
      "الظَّ",
      "الظِّ",
      "الظُّ",
      "اللّ",
      "ٱللّ",
      "ال لّ",
      "ٱل لّ",
      "اللَّ",
      "اللِّ",
      "اللُّ",
      "النّ",
      "ٱلنّ",
      "ال نّ",
      "ٱل نّ",
      "النَّ",
      "النِّ",
      "النُّ",
    ],
    color: baseHighlightColors.idgham,
    description: "Silent laam merged into sun letters",
    priority: 4,
  },

  // == Madd Rules ==
  "madd-natural": {
    patterns: [
      // Basic natural madd patterns
      "َا",
      "ِي",
      "ُو", // Fatha+Alif, Kasra+Ya, Damma+Waw
      "ـٰ",
      "ـٖ",
      "ـٗ",
      "ۤ", // Dagger alif and other madd symbols
      "آ", // Madd alif
      // In context
      "بَا",
      "تَا",
      "ثَا",
      "جَا",
      "حَا",
      "خَا",
      "دَا", // Examples with alif
      "بِي",
      "تِي",
      "ثِي",
      "جِي",
      "حِي",
      "خِي",
      "دِي", // Examples with ya
      "بُو",
      "تُو",
      "ثُو",
      "جُو",
      "حُو",
      "خُو",
      "دُو", // Examples with waw
    ],
    color: baseHighlightColors.madd,
    description: "Natural elongation - 2 counts",
    priority: 3,
  },
  "madd-hamza": {
    patterns: [
      // Madd Muttasil (connected)
      "َاء",
      "ِيء",
      "ُوء",
      "آء",
      // Madd Munfasil (separated)
      "َا ء",
      "ِي ء",
      "ُو ء",
      "آ ء",
      "َا أ",
      "ِي أ",
      "ُو أ",
      "آ أ",
      "َا إ",
      "ِي إ",
      "ُو إ",
      "آ إ",
      // Madd Badal
      "أَا",
      "إِي",
      "أُو",
      "آ",
      // Real-world examples
      "جَاء",
      "شَيء",
      "سُوء",
      "مَاء",
      // With hamza in different positions
      "رَءَا",
      "قُرَءَا",
      "يَشَاء",
      "يُشَاء",
    ],
    color: baseHighlightColors.madd,
    description: "Elongation connected to hamza - 4-5 counts",
    priority: 4,
  },
  "madd-sukoon": {
    patterns: [
      // Madd Lazim Kalimi (word-level)
      "َاْ",
      "ِيْ",
      "ُوْ",
      "آْ",
      "َاّ",
      "ِيّ",
      "ُوّ",
      "آّ", // With shaddah
      // Madd Lazim Harfi (letter-level in Muqatta'at)
      "ٓ",
      "ۤ",
      "ۭ", // Madd symbols
      // Madd Leen
      "َوْ",
      "َيْ", // Fatha + Waw/Ya sakin
      "ْوَ",
      "ْيَ", // After sukoon
      // Real examples
      "الضَّالِّين",
      "الطَّامَّة",
      "يَوْم",
      "خَوْف",
    ],
    color: baseHighlightColors.madd,
    description: "Elongation before sukoon - 6 counts or variable",
    approximate: true,
    priority: 4,
  },

  // == Tafkheem & Tarqeeq ==
  "tafkheem-letters": {
    patterns: [
      // Isti'la letters (always heavy)
      "ص",
      "ض",
      "ط",
      "ظ",
      "خ",
      "غ",
      "ق",
      // With all possible diacritics
      "صَ",
      "صِ",
      "صُ",
      "صْ",
      "صّ",
      "ضَ",
      "ضِ",
      "ضُ",
      "ضْ",
      "ضّ",
      "طَ",
      "طِ",
      "طُ",
      "طْ",
      "طّ",
      "ظَ",
      "ظِ",
      "ظُ",
      "ظْ",
      "ظّ",
      "خَ",
      "خِ",
      "خُ",
      "خْ",
      "خّ",
      "غَ",
      "غِ",
      "غُ",
      "غْ",
      "غّ",
      "قَ",
      "قِ",
      "قُ",
      "قْ",
      "قّ",
    ],
    color: baseHighlightColors.tafkheem,
    description: "Always pronounced with heaviness",
    priority: 3,
  },
  "tafkheem-ra": {
    patterns: [
      // Ra with fatha/damma
      "رَ",
      "رُ",
      "رً",
      "رٌ",
      // Ra sakin after fatha/damma
      "َرْ",
      "ُرْ",
      "ًرْ",
      "ٌرْ",
      // Ra at word end after heavy letters
      "صَر",
      "ضَر",
      "طَر",
      "ظَر",
      "قَر",
      "غَر",
      "خَر",
      // In common words
      "رَبّ",
      "رُوح",
      "قُرْآن",
      "بَرَكَة",
    ],
    color: baseHighlightColors.tafkheem,
    description: "Heavy ra - depends on vowels and context",
    approximate: true,
    priority: 3,
  },
  "tarqeeq-ra": {
    patterns: [
      // Ra with kasra
      "رِ",
      "رٍ",
      // Ra sakin after kasra
      "ِرْ",
      "ٍرْ",
      // In common words
      "رِجَال",
      "رِزْق",
      "خَيْر",
      "بِر",
    ],
    color: baseHighlightColors.tarqeeq,
    description: "Light ra - after kasra or in specific contexts",
    approximate: true,
    priority: 3,
  },

  // == Hamzah Rules ==
  "hamzat-wasl": {
    patterns: [
      "ٱ", // Alif wasl symbol
      "ٱلْ",
      "ٱل", // Common forms
      "ٱبْن",
      "ٱمْرُؤ",
      "ٱسْم",
      "ٱثْنَان", // Standard wasl words
      "ٱقْرَأ",
      "ٱنْطَلِق",
      "ٱسْتَقِيم", // Command forms
    ],
    color: baseHighlightColors.hamzah,
    description:
      "Connecting hamza - pronounced when starting, dropped when continuing",
    approximate: true,
    priority: 3,
  },
  "hamzat-qat": {
    patterns: [
      "ء",
      "أ",
      "إ",
      "ؤ",
      "ئ", // Different hamza forms
      "أَ",
      "أِ",
      "أُ",
      "إِ",
      "إَ", // With vowels
      "ؤُ",
      "ؤْ",
      "ئِ",
      "ئْ",
      "ئَ",
      "ئُ", // On waw/ya carriers
      // In common words
      "أَنْت",
      "إِلَى",
      "شَيْء",
      "سُؤَال",
      "مَسْئُول",
    ],
    color: baseHighlightColors.hamzah,
    description: "Cutting hamza - always pronounced",
    priority: 3,
  },

  // == Waqf Rules ==
  "waqf-taam": {
    patterns: [
      "۝",
      "ۘ", // Perfect stop symbols
      "ط",
      "م", // When used as waqf symbols
    ],
    color: baseHighlightColors.waqf,
    description: "Complete stop - must pause",
    priority: 2,
  },
  "waqf-kaafi": {
    patterns: [
      "ج",
      "صلى", // Permissible stop symbols
      "ۖ",
      "ۚ", // Unicode waqf marks
    ],
    color: baseHighlightColors.waqf,
    description: "Sufficient stop - may pause",
    priority: 2,
  },
  "waqf-hasan": {
    patterns: [
      "ۗ",
      "∴", // Good stop symbols
      "ز", // When used as waqf symbol
    ],
    color: baseHighlightColors.waqf,
    description: "Good stop - recommended pause",
    priority: 2,
  },
  "waqf-qabih": {
    patterns: [
      "لا",
      "ص", // Ugly stop symbols
      "ۛ", // Not recommended stop
    ],
    color: baseHighlightColors.waqf,
    description: "Ugly stop - avoid pausing",
    priority: 2,
  },
  "waqf-signs": {
    patterns: [
      "۝",
      "ۘ",
      "ۖ",
      "ۚ",
      "ۛ",
      "ۗ",
      "∴",
      ".", // Standard waqf marks
      "ج",
      "صلى",
      "قلى",
      "ط",
      "ز",
      "ص",
      "م",
      "لا", // Text-based waqf signs
    ],
    color: baseHighlightColors.waqf,
    description: "Stopping signs and symbols",
    priority: 2,
  },
};

// ================== HELPER FUNCTIONS ==================

/**
 * Defines the structure for a rule that has been matched against a text.
 */
export interface MatchedRule {
  ruleId: string;
  matchedPatterns: string[];
}

/**
 * Retrieves the highlight color for a given Tajweed rule ID.
 * Returns a fallback color if the rule ID is not found.
 * @param ruleId The ID of the Tajweed rule.
 * @returns The hex color string for the rule.
 */
export const getRuleColor = (ruleId: string): string => {
  return tajweedRuleData[ruleId]?.color || fallbackTajweedColor;
};

/**
 * Retrieves the array of string patterns for a given Tajweed rule ID.
 * Returns an empty array if the rule ID is not found or has no patterns.
 * @param ruleId The ID of the Tajweed rule.
 * @returns An array of string patterns.
 */
export const getRulePatterns = (ruleId: string): string[] => {
  return tajweedRuleData[ruleId]?.patterns || [];
};

/**
 * Retrieves the description for a given Tajweed rule ID.
 * @param ruleId The ID of the Tajweed rule.
 * @returns The description string or undefined if not found.
 */
export const getRuleDescription = (ruleId: string): string | undefined => {
  return tajweedRuleData[ruleId]?.description;
};

/**
 * Checks if a rule is marked as approximate (context-dependent).
 * @param ruleId The ID of the Tajweed rule.
 * @returns True if the rule is approximate, false otherwise.
 */
export const isRuleApproximate = (ruleId: string): boolean => {
  return tajweedRuleData[ruleId]?.approximate || false;
};

/**
 * Gets the priority level for a rule (higher numbers take precedence).
 * @param ruleId The ID of the Tajweed rule.
 * @returns The priority number, or 1 as default.
 */
export const getRulePriority = (ruleId: string): number => {
  return tajweedRuleData[ruleId]?.priority || 1;
};

/**
 * Finds all rules that have patterns matching the given text.
 * Useful for debugging and validation.
 * @param text The Arabic text to search within.
 * @returns Array of objects with ruleId and matched patterns.
 */
export const findMatchingRules = (text: string): MatchedRule[] => {
  const matches: MatchedRule[] = [];

  for (const [ruleId, ruleInfo] of Object.entries(tajweedRuleData)) {
    const matchedPatterns = ruleInfo.patterns.filter((pattern) =>
      text.includes(pattern)
    );
    if (matchedPatterns.length > 0) {
      matches.push({ ruleId, matchedPatterns });
    }
  }

  // Sort by priority (higher first)
  return matches.sort(
    (a, b) => getRulePriority(b.ruleId) - getRulePriority(a.ruleId)
  );
};

/**
 * Validates that a rule ID exists in the pattern data.
 * @param ruleId The ID to validate.
 * @returns True if the rule exists, false otherwise.
 */
export const isValidRuleId = (ruleId: string): boolean => {
  return ruleId in tajweedRuleData;
};

/**
 * Gets all available rule IDs.
 * @returns Array of all rule IDs in the system.
 */
export const getAllRuleIds = (): string[] => {
  return Object.keys(tajweedRuleData);
};
