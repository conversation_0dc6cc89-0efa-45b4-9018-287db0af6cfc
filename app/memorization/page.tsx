// app/memorization/page.tsx

export const dynamic = "force-dynamic"; // This is correct and stays as is.

import React from "react";
import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
// --- FIX: Rename the imported 'dynamic' function to avoid conflict ---
import nextDynamic from "next/dynamic";
import {
  startMemorizationSession,
  getUserMemorizationProgress,
} from "@/actions/memorization-progress";
import {
  getMemorizedVersesBySurah,
  getUserProgress,
  getUserSubscription,
} from "@/db/queries";

// --- Teacher's Improvement: Dynamic Import of Memorization Component ---
// This will defer loading the Memorization component and its dependencies
// until the user navigates to this page, improving initial load times for the app.
console.log(
  "[MemorizationPage] Initializing dynamic import of <Memorization>…"
);
// --- FIX: Use the new, non-conflicting name 'nextDynamic' ---
const Memorization = nextDynamic(
  () =>
    import("./memorization").then((mod) => {
      console.log("[MemorizationPage] <Memorization> module loaded");
      return mod.default; // Assuming Memorization is the default export
    }),
  {
    loading: () => {
      console.log("[MemorizationPage] Rendering fallback loading state");
      // A simple, non-jarring loading placeholder.
      // This can be replaced with a more sophisticated skeleton component.
      return (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "80vh",
          }}
        >
          <p>Loading Memorization Session...</p>
        </div>
      );
    },
    ssr: false, // Disable Server-Side Rendering for this component
  }
);

// Type for verse data used internally
type Verse = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

// Helper: Remove Basmala from verse 1 text if present.
function removeBasmala(verseText: string): string {
  // *** CORRECTED BASMALA STRING (Ensure this matches the one in route.ts) ***
  const basmalaExact = "بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ";
  const basmalaWithSpace = basmalaExact + " ";

  // Uncomment the log below if needed for debugging:
  // console.log(`Page.tsx: Checking verse 1 text [${verseText.substring(0, 50)}...]`);

  if (verseText.startsWith(basmalaWithSpace)) {
    // console.log(`Page.tsx: Basmala (with space) found. Removing...`);
    return verseText.slice(basmalaWithSpace.length).trimStart();
  } else if (verseText.startsWith(basmalaExact)) {
    // console.log(`Page.tsx: Basmala (exact) found. Removing...`);
    return verseText.slice(basmalaExact.length).trimStart();
  } else {
    // console.log(`Page.tsx: Basmala string constant did not match start of verse text.`);
  }
  return verseText;
}

type MemorizationPageProps = {
  searchParams: {
    surahNumber?: string;
    versesRange?: string;
    mode?: "startFresh" | "continue";
    surahName?: string;
  };
};

const DYNAMIC_FETCH_THRESHOLD = 50;

const MemorizationPage = async ({ searchParams }: MemorizationPageProps) => {
  // --- Authentication and Param Parsing ---
  const { userId } = await auth();
  console.log("User ID from auth:", userId);
  if (!userId) redirect("/");
  const surahNumberStr = searchParams.surahNumber;
  const initialVersesRange = searchParams.versesRange || "";
  const mode = searchParams.mode || "startFresh";
  const surahNameFallback = searchParams.surahName || "";
  console.log("Query Params:", {
    surahNumberStr,
    initialVersesRange,
    mode,
    surahNameFallback,
  });
  if (!surahNumberStr) redirect("/learn");
  const surahNumber = parseInt(surahNumberStr, 10);
  console.log("Parsed surahNumber:", surahNumber);
  if (isNaN(surahNumber)) redirect("/learn");

  // --- Fetch Metadata ---
  let surahMetadata: any = null;
  let totalNumberOfAyahs = 0;
  try {
    const metaResponse = await fetch(
      `https://api.alquran.cloud/v1/surah/${surahNumber}`
    );
    console.log(
      `AlQuranCloud Metadata response status: ${metaResponse.status}`
    );
    if (!metaResponse.ok)
      throw new Error(`Meta fetch failed: ${metaResponse.status}`);
    const metaJson = await metaResponse.json();
    if (!metaJson?.data?.numberOfAyahs) throw new Error("No numberOfAyahs");
    surahMetadata = metaJson.data;
    totalNumberOfAyahs = surahMetadata.numberOfAyahs;
    console.log(
      `Fetched Metadata for ${surahNumber}: ${totalNumberOfAyahs} Ayahs`
    );
  } catch (error) {
    console.error("Meta fetch error:", error);
    redirect("/learn");
  }

  // --- Initialize Variables ---
  let versesData: Verse[] = [];
  let initialPercentage = 0;

  // --- Conditional Verse Text Fetching for SMALL Surahs ---
  if (totalNumberOfAyahs <= DYNAMIC_FETCH_THRESHOLD) {
    console.log(
      `Small Surah (${totalNumberOfAyahs}). Fetching text immediately.`
    );
    try {
      const textResponse = await fetch(
        `https://api.alquran.cloud/v1/surah/${surahNumber}/quran-uthmani`
      );
      console.log(`AlQuranCloud Text response status: ${textResponse.status}`);
      if (!textResponse.ok)
        throw new Error(`Text fetch failed: ${textResponse.status}`);
      const textJson = await textResponse.json();
      const allAyahsText = textJson?.data?.ayahs || [];

      if (allAyahsText.length > 0) {
        versesData = allAyahsText
          .filter(
            (a: any) =>
              typeof a.numberInSurah === "number" &&
              a.numberInSurah >= 1 &&
              a.numberInSurah <= totalNumberOfAyahs
          )
          .map((a: any) => ({
            surahNumber: surahNumber,
            verseNumber: a.numberInSurah,
            // *** Apply updated removeBasmala only for verse 1 and not Surah 1 ***
            text:
              a.numberInSurah === 1 && surahNumber !== 1
                ? removeBasmala(a.text)
                : a.text,
          }));
        console.log("Processed verses for small surah:", versesData.length);
      } else {
        console.warn("Fetched text but no ayahs.");
      }
    } catch (error) {
      console.error("Small Surah text fetch/process error:", error);
      redirect("/learn");
    }
  } else {
    console.log(`Large Surah (${totalNumberOfAyahs}). Initial fetch skipped.`);
  }

  // --- Start/Retrieve Memorization Session ---
  let session;
  try {
    session = await startMemorizationSession({
      surahNumber,
      versesRange: initialVersesRange,
      mode,
    });
    console.log("Memorization session:", session);
  } catch (error) {
    console.error("Session error:", error);
    redirect("/learn");
  }

  // --- Fetch User Progress & Subscription ---
  let userProgress = null;
  let userSubscription = null;
  try {
    const [p, s] = await Promise.all([
      getUserProgress(),
      getUserSubscription(),
    ]);
    userProgress = p;
    userSubscription = s;
    console.log("User progress:", userProgress);
    console.log("User subscription:", userSubscription);
  } catch (error) {
    console.error("User data fetch error:", error);
    redirect("/learn");
  }
  if (!userProgress) redirect("/learn");

  // --- Fetch User Memorization Progress ---
  let userMemorizationProgress = null;
  try {
    userMemorizationProgress = await getUserMemorizationProgress({
      surahNumber,
    });
    console.log("User memorization progress:", userMemorizationProgress);
  } catch (error) {
    console.error("Mem progress fetch error:", error);
    redirect("/learn");
  }
  if (totalNumberOfAyahs > 0 && userMemorizationProgress) {
    const completed = userMemorizationProgress.completedVerses || 0;
    initialPercentage = Math.min((completed / totalNumberOfAyahs) * 100, 100);
  } else if (totalNumberOfAyahs === 0) {
    console.warn(
      "Total number of Ayahs is 0, setting initial percentage to 0."
    );
    initialPercentage = 0;
  } else {
    console.warn("Could not calculate initial percentage, defaulting to 0.");
    initialPercentage = 0;
  }
  console.log("Initial %:", initialPercentage);

  // --- Fetch Memorized Verses ---
  let memorizedVerses: any[] = [];
  try {
    memorizedVerses = await getMemorizedVersesBySurah(userId, surahNumber);
    console.log("Number of memorized verses:", memorizedVerses.length);
  } catch (error) {
    console.error("Mem verses fetch error:", error);
  }
  const memorizedVerseNumbers = memorizedVerses.map((v) => v.verseNumber);
  console.log("Memorized verse numbers:", memorizedVerseNumbers);

  // --- Construct Surah Data ---
  const surahData = {
    id: surahNumber,
    number: surahNumber,
    name: surahMetadata?.name || surahNameFallback || `Surah ${surahNumber}`,
    englishName:
      surahMetadata?.englishName || surahNameFallback || `Surah ${surahNumber}`,
    englishNameTranslation: surahMetadata?.englishNameTranslation || "Unknown",
    revelationPlace: surahMetadata?.revelationType || "Unknown",
    numberOfAyahs: totalNumberOfAyahs,
  };
  console.log("Final surahData:", surahData);

  // --- Render Client Component ---
  return (
    <div data-testid="memorization-page">
      <Memorization
        key={surahNumber}
        sessionId={session.sessionId}
        userId={userId}
        surahNumber={surahNumber}
        verses={versesData}
        initialPercentage={initialPercentage}
        versesRange={initialVersesRange}
        memorizedVerseNumbers={memorizedVerseNumbers}
        surahData={surahData}
        userSubscription={userSubscription}
        initialHearts={userProgress.hearts}
      />
    </div>
  );
};

export default MemorizationPage;
