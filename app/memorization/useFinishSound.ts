// useFinishSound.ts
import { useEffect, useRef, useState, useCallback } from "react";

interface UseFinishSoundProps {
  score: number;
  initialScore: number;
  isRecording?: boolean;
  onSoundPlayed?: () => void;
}

const useFinishSound = ({
  score,
  initialScore,
  isRecording = false,
  onSoundPlayed,
}: UseFinishSoundProps) => {
  // Preserve existing refs
  const prevScoreRef = useRef<number>(initialScore);
  const hasPlayedFinishSound = useRef<boolean>(false);
  const wasScoreLowerRef = useRef<boolean>(false);

  // Add new refs for enhanced functionality
  const isFirstMount = useRef<boolean>(true);
  const lowestActiveScore = useRef<number>(100);
  const [hasStartedExercise, setHasStartedExercise] = useState<boolean>(false);

  // Handle exercise start detection
  useEffect(() => {
    if (isRecording && !hasStartedExercise) {
      setHasStartedExercise(true);
      // Set initial lowest score when exercise starts
      lowestActiveScore.current = Math.round(score);
      // Ensure we don't count initial load as "lower score"
      wasScoreLowerRef.current = false;
    }
  }, [isRecording, hasStartedExercise, score]);

  // Main sound effect logic
  useEffect(() => {
    // Skip effect on first mount
    if (isFirstMount.current) {
      isFirstMount.current = false;
      return;
    }

    const roundedScore = Math.round(score);
    const roundedPrevScore = Math.round(prevScoreRef.current);

    // Update lowest score tracking during active use
    if (hasStartedExercise && roundedScore < lowestActiveScore.current) {
      lowestActiveScore.current = roundedScore;
    }

    // Mark if score was ever below 100% during active use
    if (hasStartedExercise && roundedScore < 100) {
      wasScoreLowerRef.current = true;
    }

    // Enhanced conditions for playing finish sound:
    // 1. Exercise has started (user has recorded)
    // 2. Current score is 100%
    // 3. Previous score wasn't 100%
    // 4. We've seen a lower score during active use
    // 5. Haven't played the sound yet this session
    if (
      hasStartedExercise &&
      roundedScore === 100 &&
      roundedPrevScore !== 100 &&
      wasScoreLowerRef.current &&
      !hasPlayedFinishSound.current &&
      lowestActiveScore.current < 100
    ) {
      const finishSound = new Audio("/finish.mp3");
      // Add error handling for sound playback
      finishSound.play().catch((error) => {
        console.error("Error playing finish sound:", error);
      });

      hasPlayedFinishSound.current = true;
      onSoundPlayed?.();
    }

    prevScoreRef.current = score;
  }, [score, hasStartedExercise, onSoundPlayed]);

  // Provide reset functionality - memoized with useCallback to maintain referential equality
  const reset = useCallback(() => {
    hasPlayedFinishSound.current = false;
    wasScoreLowerRef.current = false;
    lowestActiveScore.current = 100;
    prevScoreRef.current = initialScore;
    setHasStartedExercise(false);
    // Don't reset isFirstMount here to avoid unnecessary reinitialization of verse states
    // Only reset it when actually needed (new exercise session)
    isFirstMount.current = false; // Changed from true to false
  }, [initialScore]); // Add initialScore as dependency

  // Return reset function and current state
  return {
    reset,
    hasPlayed: hasPlayedFinishSound.current,
    hasStarted: hasStartedExercise,
    lowestScore: lowestActiveScore.current,
  };
};

export default useFinishSound;
