// phonetics.tsx

import { Letter } from "./FeedbackManager/alignmentUtils";
export const getVowelExplanation = (letter: Letter): string => {
  if (
    !letter ||
    !Array.isArray(letter.vowelMarks) ||
    letter.vowelMarks.length === 0
  ) {
    return "";
  }

  // Create explanation for each vowel mark
  const explanations = letter.vowelMarks.map((vowelMark) => {
    switch (vowelMark) {
      case "\u064B": // Fathatan
        return "pronounced as 'an' at the end of a word";
      case "\u064C": // Dammatan
        return "pronounced as 'un' at the end of a word";
      case "\u064D": // Kasratan
        return "pronounced as 'in' at the end of a word";
      case "\u064E": // Fatha
        return "pronounced as a short 'a' sound";
      case "\u064F": // Dam<PERSON>
        return "pronounced as a short 'u' sound";
      case "\u0650": // Ka<PERSON>ra
        return "pronounced as a short 'i' sound";
      case "\u0651": // Shadda
        return "doubles the consonant sound";
      case "\u0652": // Sukun
        return "indicates no vowel sound follows";
      case "\u0653": // Mad<PERSON>
        return "extends the vowel sound";
      case "\u0654": // Hamza Above
        return "adds a glottal stop sound";
      case "\u0655": // Hamza Below
        return "adds a glottal stop sound";
      default:
        return "";
    }
  });

  // Filter out empty strings and join explanations
  return explanations.filter((exp) => exp).join(", ");
};

// A map combining letters and vowel forms into corresponding phonetics
export const phoneticMap: { [key: string]: string } = {
  ا: "a",
  "ا\u064B": "an", // Fathatan
  "ا\u064C": "un", // Dammatan
  "ا\u064D": "in", // Kasratan
  "ا\u064E": "a", // Fatha
  "ا\u064F": "u", // Damma
  "ا\u0650": "i", // Kasra
  "ا\u0652": "a", // Sukun (no vowel)

  ب: "b",
  "ب\u064E": "ba",
  "ب\u064F": "bu",
  "ب\u0650": "bi",
  "ب\u0652": "b", // Sukun (sharp stop)
  "ب\u0651": "bb", // Shadda (double 'b')

  ت: "t",
  "ت\u064E": "ta",
  "ت\u064F": "tu",
  "ت\u0650": "ti",
  "ت\u0652": "t",
  "ت\u0651": "tt", // Shadda (double 't')

  ث: "th",
  "ث\u064E": "tha",
  "ث\u064F": "thu",
  "ث\u0650": "thi",
  "ث\u0652": "th",
  "ث\u0651": "thth", // Shadda (double 'th')

  ج: "j",
  "ج\u064E": "ja",
  "ج\u064F": "ju",
  "ج\u0650": "ji",
  "ج\u0652": "j",
  "ج\u0651": "jj", // Shadda (double 'j')

  ح: "h",
  "ح\u064E": "ha",
  "ح\u064F": "hu",
  "ح\u0650": "hi",
  "ح\u0652": "h",
  "ح\u0651": "hh", // Shadda (double 'h')

  خ: "kh",
  "خ\u064E": "kha",
  "خ\u064F": "khu",
  "خ\u0650": "khi",
  "خ\u0652": "kh",
  "خ\u0651": "khkh", // Shadda (double 'kh')

  د: "d",
  "د\u064E": "da",
  "د\u064F": "du",
  "د\u0650": "di",
  "د\u0652": "d",
  "د\u0651": "dd", // Shadda (double 'd')

  ذ: "dh",
  "ذ\u064E": "dha",
  "ذ\u064F": "dhu",
  "ذ\u0650": "dhi",
  "ذ\u0652": "dh",
  "ذ\u0651": "dhdh", // Shadda (double 'dh')

  ر: "r",
  "ر\u064E": "ra",
  "ر\u064F": "ru",
  "ر\u0650": "ri",
  "ر\u0652": "r",
  "ر\u0651": "rr", // Shadda (double 'r')

  ز: "z",
  "ز\u064E": "za",
  "ز\u064F": "zu",
  "ز\u0650": "zi",
  "ز\u0652": "z",
  "ز\u0651": "zz", // Shadda (double 'z')

  س: "s",
  "س\u064E": "sa",
  "س\u064F": "su",
  "س\u0650": "si",
  "س\u0652": "s",
  "س\u0651": "ss", // Shadda (double 's')

  ش: "sh",
  "ش\u064E": "sha",
  "ش\u064F": "shu",
  "ش\u0650": "shi",
  "ش\u0652": "sh",
  "ش\u0651": "shsh", // Shadda (double 'sh')

  ص: "s",
  "ص\u064E": "sa",
  "ص\u064F": "su",
  "ص\u0650": "si",
  "ص\u0652": "s",
  "ص\u0651": "ss", // Shadda (double 's' for ص)

  ض: "d",
  "ض\u064E": "da",
  "ض\u064F": "du",
  "ض\u0650": "di",
  "ض\u0652": "d",
  "ض\u0651": "dd", // Shadda (double 'd' for ض)

  ط: "t",
  "ط\u064E": "ta",
  "ط\u064F": "tu",
  "ط\u0650": "ti",
  "ط\u0652": "t",
  "ط\u0651": "tt", // Shadda (double 't' for ط)

  ظ: "th",
  "ظ\u064E": "tha",
  "ظ\u064F": "thu",
  "ظ\u0650": "thi",
  "ظ\u0652": "th",
  "ظ\u0651": "thth", // Shadda (double 'th' for ظ)

  ع: "‘",
  "ع\u064E": "‘a",
  "ع\u064F": "‘u",
  "ع\u0650": "‘i",
  "ع\u0652": "‘",
  "ع\u0651": "‘‘", // Shadda (double 'ع')

  غ: "gh",
  "غ\u064E": "gha",
  "غ\u064F": "ghu",
  "غ\u0650": "ghi",
  "غ\u0652": "gh",
  "غ\u0651": "ghgh", // Shadda (double 'gh')

  ف: "f",
  "ف\u064E": "fa",
  "ف\u064F": "fu",
  "ف\u0650": "fi",
  "ف\u0652": "f",
  "ف\u0651": "ff", // Shadda (double 'f')

  ق: "q",
  "ق\u064E": "qa",
  "ق\u064F": "qu",
  "ق\u0650": "qi",
  "ق\u0652": "q",
  "ق\u0651": "qq", // Shadda (double 'q')

  ك: "k",
  "ك\u064E": "ka",
  "ك\u064F": "ku",
  "ك\u0650": "ki",
  "ك\u0652": "k",
  "ك\u0651": "kk", // Shadda (double 'k')

  ل: "l",
  "ل\u064E": "la",
  "ل\u064F": "lu",
  "ل\u0650": "li",
  "ل\u0652": "l",
  "ل\u0651": "ll", // Shadda (double 'l')

  م: "m",
  "م\u064E": "ma",
  "م\u064F": "mu",
  "م\u0650": "mi",
  "م\u0652": "m",
  "م\u0651": "mm", // Shadda (double 'm')

  ن: "n",
  "ن\u064E": "na",
  "ن\u064F": "nu",
  "ن\u0650": "ni",
  "ن\u0652": "n",
  "ن\u0651": "nn", // Shadda (double 'n')

  ه: "h",
  "ه\u064E": "ha",
  "ه\u064F": "hu",
  "ه\u0650": "hi",
  "ه\u0652": "h",
  "ه\u0651": "hh", // Shadda (double 'h')

  و: "w",
  "و\u064E": "wa",
  "و\u064F": "wu",
  "و\u0650": "wi",
  "و\u0652": "w",
  "و\u0651": "ww", // Shadda (double 'w')

  ي: "y",
  "ي\u064E": "ya",
  "ي\u064F": "yu",
  "ي\u0650": "yi",
  "ي\u0652": "y",
  "ي\u0651": "yy", // Shadda (double 'y')
};

// Function to return the phonetic sound for each Arabic letter
export const getPhoneticSound = (letter: string): JSX.Element | string => {
  const phoneticDescriptions: { [key: string]: JSX.Element } = {
    ا: (
      <>
        Stop the sound in your throat, like when you say{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;uh-oh&rdquo;
        </span>
        .
      </>
    ),
    ب: (
      <>
        Close your lips and then make a sound like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;b&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;bat&rdquo;
        </span>
        .
      </>
    ),
    ت: (
      <>
        Put your tongue behind your teeth and make a{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;t&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;tap&rdquo;
        </span>
        .
      </>
    ),
    ث: (
      <>
        Put your tongue on your teeth and blow, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;th&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;think&rdquo;
        </span>
        .
      </>
    ),
    ج: (
      <>
        Make a{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;j&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;judge&rdquo;
        </span>
        .
      </>
    ),
    ح: (
      <>
        Breathe out from your throat, like a quiet{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;sigh&rdquo;
        </span>
        .
      </>
    ),
    خ: (
      <>
        Make a scratchy sound from the back of your throat, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;loch&rdquo;
        </span>
        .
      </>
    ),
    د: (
      <>
        Put your tongue behind your teeth and make a{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;d&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;dog&rdquo;
        </span>
        .
      </>
    ),
    ذ: (
      <>
        Put your tongue on your teeth and make a soft{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;th&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;this&rdquo;
        </span>
        .
      </>
    ),
    ر: (
      <>
        Make your tongue wiggle to make a rolling{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;r&rdquo;
        </span>{" "}
        sound.
      </>
    ),
    ز: (
      <>
        Make a buzzing sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;z&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;zebra&rdquo;
        </span>
        .
      </>
    ),
    س: (
      <>
        Make a hissing sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;s&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;snake&rdquo;
        </span>
        .
      </>
    ),
    ش: (
      <>
        Make a soft{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;sh&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;shoe&rdquo;
        </span>
        .
      </>
    ),
    ص: (
      <>
        Make a strong{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;s&rdquo;
        </span>{" "}
        sound from deep inside.
      </>
    ),
    ض: (
      <>
        Make a strong{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;d&rdquo;
        </span>{" "}
        sound from deep inside.
      </>
    ),
    ط: (
      <>
        Make a strong{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;t&rdquo;
        </span>{" "}
        sound from deep inside.
      </>
    ),
    ظ: (
      <>
        Make a strong{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;th&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;this&rdquo;
        </span>
        , but deeper.
      </>
    ),
    ع: <>Make a deep sound from your throat.</>,
    غ: (
      <>
        Make a growly sound from the back of your throat, like the French{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;r&rdquo;
        </span>
        .
      </>
    ),
    ف: (
      <>
        Put your bottom lip on your teeth and blow, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;f&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;fun&rdquo;
        </span>
        .
      </>
    ),
    ق: (
      <>
        Make a strong{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;k&rdquo;
        </span>{" "}
        sound from the back of your{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;throat&rdquo;
        </span>
        .
      </>
    ),
    ك: (
      <>
        Make a{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;k&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;cat&rdquo;
        </span>
        .
      </>
    ),
    ل: (
      <>
        Touch your tongue to the roof of your mouth and say{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;l&rdquo;
        </span>
        , like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;lamp&rdquo;
        </span>
        .
      </>
    ),
    م: (
      <>
        Close your lips and hum, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;m&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;moon&rdquo;
        </span>
        .
      </>
    ),
    ن: (
      <>
        Put your tongue behind your teeth and hum, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;n&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;no&rdquo;
        </span>
        .
      </>
    ),
    ه: (
      <>
        Breathe out softly, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;h&rdquo;
        </span>{" "}
        in{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;hat&rdquo;
        </span>
        .
      </>
    ),
    و: (
      <>
        Make a{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;w&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;water&rdquo;
        </span>
        .
      </>
    ),
    ي: (
      <>
        Make a{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;y&rdquo;
        </span>{" "}
        sound, like{" "}
        <span className="text-xl font-medium font-style: italic">
          &ldquo;yes&rdquo;
        </span>
        .
      </>
    ),
  };

  const description = phoneticDescriptions[letter] || "unknown letter";
  return description;
};
