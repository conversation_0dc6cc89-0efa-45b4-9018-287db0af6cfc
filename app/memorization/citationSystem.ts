// citationSystem.ts

// Interface defining the structure for a word object used by the citation system.
export interface Word {
  text: string; // The actual Arabic text of the word.
  verseNumber: number; // The verse number this word belongs to.
  wordIndex: number; // The index of this word within its verse.
  almostMatched?: boolean; // Flag indicating if the word was close but not exact.
  matched?: boolean; // Flag indicating if the word was correctly matched.
  letterCountMismatch?: boolean; // Flag indicating if the letter count differed from the user's input.
  given?: boolean; // Flag indicating if the word was 'given' (e.g., in first two verses or already memorized).
}

// Type definition for the citation map, mapping unique keys to citation numbers.
export interface CitationMap {
  [key: string]: number; // A unique string key identifying the word -> the citation number.
}

/**
 * Manages the generation and application of citation numbers for feedback.
 * Citations are typically applied to words that require correction or attention.
 */
export class CitationSystem {
  /**
   * Generates a unique and consistent key for a given word object.
   * This key is used to map words to their citation numbers.
   * Made public for potential external use or debugging.
   * @param word - The word object.
   * @returns A unique string key.
   */
  public static generateUniqueKey(word: Word): string {
    // Normalize the word text to handle variations before creating the key.
    const normalizedWord = this.normalizeWord(word.text);
    // Combine normalized text, verse number, and word index for uniqueness.
    return `${normalizedWord}_v${word.verseNumber}_w${word.wordIndex}`;
  }

  /**
   * Normalizes Arabic word text for consistent key generation.
   * Removes vowel markings (Tashkeel), normalizes Alef variants, and removes Tatweel (kashida).
   * @param word - The Arabic word string.
   * @returns The normalized word string.
   */
  private static normalizeWord(word: string): string {
    // Ensure input is a string before processing.
    if (typeof word !== "string") return "";
    return word
      .normalize("NFKD") // Decompose combined characters.
      .replace(/[\u064B-\u065F]/g, "") // Remove Arabic diacritics/Tashkeel (vowels, Shadda, Sukun etc.).
      .replace(/\u0640/g, "") // Remove Tatweel (kashida - line extension character).
      .replace(/[\u0622\u0623\u0625]/g, "\u0627") // Normalize different forms of Alef (Madda, Hamza above/below) to standard Alef.
      .normalize("NFKC"); // Recompose characters after normalization.
  }

  /**
   * Generates a citation map for a list of words based on their current feedback state.
   * Citations are assigned sequentially to words meeting the criteria for needing feedback.
   * @param words - An array of Word objects (typically flattened from the displayed state).
   * @returns A CitationMap object.
   */
  static generateCitations(words: Word[]): CitationMap {
    const citationMap: CitationMap = {};
    let citationCounter = 1; // Start citation numbering from 1.

    words.forEach((word) => {
      // Debug log: Show the state of the word being processed.
      // console.log("Processing word for citation:", {
      //   text: word.text,
      //   verseNumber: word.verseNumber,
      //   almostMatched: word.almostMatched,
      //   letterCountMismatch: word.letterCountMismatch,
      //   matched: word.matched,
      //   given: word.given,
      // });

      // --- Eligibility Criteria for Citation ---
      // 1. Must be beyond the first two verses.
      // 2. Must not be a 'given' word (pre-filled or already memorized).
      // 3. Must require feedback (almost matched, letter count mismatch, or completely unmatched).
      // 4. Must have actual text content.
      const isEligible =
        word.verseNumber > 2 &&
        !word.given &&
        (word.almostMatched || word.letterCountMismatch || !word.matched) &&
        word.text;

      if (isEligible) {
        const uniqueKey = this.generateUniqueKey(word);
        // Assign the next available citation number to this word's unique key.
        citationMap[uniqueKey] = citationCounter++;

        // Debug log: Confirm citation generation.
        // console.log("Generated citation:", {
        //   uniqueKey,
        //   citationNumber: citationMap[uniqueKey],
        // });
      }
    });

    // Debug log: Show the final map generated in this pass.
    // console.log("Final citation map generated:", citationMap);

    return citationMap;
  }

  /**
   * Applies citation numbers from a generated map to the displayedWords state object.
   * Crucially, this function now also CLEARS feedback numbers for words that no longer
   * qualify for a citation in the current feedback round, fixing the "lingering citation" issue.
   *
   * @param displayedWords - The nested state object representing words displayed on screen.
   * @param citationMap - The CitationMap generated based on the *current* feedback results.
   * @returns The modified displayedWords object (note: it modifies the object in place).
   */
  static applyCitations(
    displayedWords: { [verseNumber: number]: any[] },
    citationMap: CitationMap
  ): { [verseNumber: number]: any[] } {
    // console.log("APPLYING CITATIONS based on current map:", JSON.stringify(citationMap));
    // Optional: For debugging, track which keys from the map were successfully applied.
    // const appliedKeys = new Set<string>();

    // Iterate through each verse in the displayedWords object.
    Object.entries(displayedWords).forEach(([verseNumberStr, words]) => {
      const verseNumber = parseInt(verseNumberStr);

      // Iterate through each word within the verse.
      words.forEach((word, wordIndex) => {
        // --- Determine if this specific word should have a citation IN THIS ROUND ---
        let shouldHaveCitationThisRound = false;
        let uniqueKey: string | null = null;

        // 1. Check basic eligibility based on current word state.
        const isPotentiallyEligible =
          verseNumber > 2 &&
          !word.given &&
          (word.almostMatched || word.letterCountMismatch || !word.matched) &&
          word.text; // Must have text

        if (isPotentiallyEligible) {
          // 2. Generate the key for this word.
          uniqueKey = this.generateUniqueKey({
            text: word.text,
            verseNumber: verseNumber,
            wordIndex,
            // Pass current state flags, even if generateUniqueKey doesn't use them directly, for clarity/future use.
            almostMatched: word.almostMatched,
            letterCountMismatch: word.letterCountMismatch,
            matched: word.matched,
            given: word.given,
          });

          // 3. Check if a citation was *actually generated* for this key in the current map.
          //    Using hasOwnProperty is slightly safer than just checking truthiness (in case a value could be 0).
          if (citationMap.hasOwnProperty(uniqueKey)) {
            shouldHaveCitationThisRound = true;
          }
        }

        // --- Apply or Clear the feedback number ---
        if (shouldHaveCitationThisRound && uniqueKey) {
          // Assign the citation number from the current map.
          word.feedbackNumber = citationMap[uniqueKey];
          // appliedKeys.add(uniqueKey); // Optional debug tracking
          // console.log(`Applied citation #${word.feedbackNumber} to word "${word.text}" (${uniqueKey})`);
        } else {
          // **CRITICAL FIX:** If the word should NOT have a citation this round
          // (because it's now matched, or given, or simply wasn't in the current citationMap),
          // explicitly set its feedbackNumber to undefined to remove any old citation.
          word.feedbackNumber = undefined;
        }
      });
    });

    // Optional: Log keys from the map that weren't applied (can help diagnose key mismatches).
    // Object.keys(citationMap).forEach(key => {
    //   if (!appliedKeys.has(key)) {
    //     console.warn(`CitationMap key ${key} (Value: ${citationMap[key]}) was generated but not applied to any word.`);
    //   }
    // });

    // console.log("FINISHED APPLYING CITATIONS.");
    // Return the modified object (it's modified in place, but returning is good practice).
    return displayedWords;
  }

  /**
   * Flattens the nested displayedWords state object into a single array of Word objects.
   * This is useful for passing the word data to `generateCitations`.
   * @param displayedWords - The nested state object.
   * @returns A flat array of Word objects.
   */
  static flattenDisplayedWords(displayedWords: {
    [verseNumber: number]: any[];
  }): Word[] {
    const flattened: Word[] = [];

    // Iterate through verses and words, creating a flat list.
    Object.entries(displayedWords).forEach(([verseNumberStr, words]) => {
      // Ensure 'words' is actually an array before iterating
      if (Array.isArray(words)) {
        words.forEach((word, wordIndex) => {
          // Basic check to ensure 'word' is an object with expected properties
          if (word && typeof word === "object" && "text" in word) {
            flattened.push({
              text: word.text,
              verseNumber: parseInt(verseNumberStr), // Ensure verse number is integer
              wordIndex,
              // Safely access optional properties
              almostMatched: !!word.almostMatched, // Convert to boolean
              matched: !!word.matched, // Convert to boolean
              letterCountMismatch: !!word.letterCountMismatch, // Convert to boolean
              given: !!word.given, // Convert to boolean
            });
          } else {
            // Log a warning if the word structure is unexpected
            // console.warn(`Unexpected word structure encountered in verse ${verseNumberStr}, index ${wordIndex}:`, word);
          }
        });
      } else {
        // Log a warning if a verse doesn't contain an array
        // console.warn(`Expected an array for verse ${verseNumberStr}, but got:`, words);
      }
    });

    return flattened;
  }
}

// Example of how this system might be integrated into a larger feedback generation process.
// This function itself is not part of the CitationSystem class but demonstrates its usage.
export const generateFeedbackWithCitations = (
  wordAlignment: any[], // Results from the alignment process.
  displayedWords: { [verseNumber: number]: any[] } // The current display state.
  // ... potentially other parameters like feedbackPairs, verses, etc.
): {
  updatedDisplayedWords: { [verseNumber: number]: any[] }; // State updated with new/cleared citations.
  citationMap: CitationMap; // The map of citations generated in this round.
} => {
  // 1. Flatten the current word state for processing.
  const flattenedWords = CitationSystem.flattenDisplayedWords(displayedWords);

  // 2. Generate citations based *only* on the current state of correctness/errors.
  //    Words that are now 'matched' will not be included in this map.
  const citationMap = CitationSystem.generateCitations(flattenedWords);

  // 3. Apply these newly generated citations to the displayedWords object.
  //    This step will also clear any feedbackNumbers on words that are no longer in the citationMap.
  const updatedDisplayedWords = CitationSystem.applyCitations(
    displayedWords, // Pass the original object (it will be modified)
    citationMap
  );

  // 4. Return the updated state and the map generated in this cycle.
  return {
    updatedDisplayedWords,
    citationMap,
    // ... potentially return combinedFeedback JSX elements generated using the updatedDisplayedWords and citationMap
  };
};
