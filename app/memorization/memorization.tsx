// memorization.tsx

"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  FC,
  RefObject,
} from "react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { ArrowLeft } from "lucide-react";
import {
  prepareDataStructures,
  initializeMatchedWords,
  performWordComparisonAligned,
  finalizeAlignment,
  removeVowelMarkings,
} from "./FeedbackManager/alignmentUtils";
import { generateFeedback } from "./FeedbackManager/feedbackutils";
import { addMemorizedVerse } from "@/actions/memorization-progress";
import { produce } from "immer";
import { cloneDeep } from "lodash";
import type { Surah as BaseSurah } from "../(main)/surahGrid/page";

import useFinishSound from "./useFinishSound";
import { CitationSystem } from "./citationSystem";
import {
  synthesizeSpeech,
  batchPrefetchTTS,
  isTextCached,
} from "./Audio/ttsUtils";
import { generateCitationFocusedSummary } from "./Audio/summaryFeedbackUtils";
import { useVoiceStore } from "@/store/useVoiceStore";

import { reduceHeartsForMemorization } from "@/actions/user-progress";
import { toast } from "sonner";
import LoadingPage from "./loading";
import { SurahDisplaySkeleton } from "./SurahDisplay";
import { MediaPlayerSkeleton } from "./MediaPlayer";

// --- Helper component for panel loading placeholders ---
const PanelSkeleton = ({ text }: { text: string }) => (
  <div className="flex-1 flex items-center justify-center text-gray-500">
    <p>{text}</p>
  </div>
);

// --- Teacher's Improvement: Dynamically load heavy UI chunks ---

const SurahDisplay = dynamic(
  () =>
    import("./SurahDisplay").then((mod) => {
      console.log("[Memorization] Loaded <SurahDisplay>…");
      return mod.default;
    }),
  {
    loading: () => <SurahDisplaySkeleton />,
    ssr: false,
  }
);

const MediaPlayer = dynamic(
  () =>
    import("./MediaPlayer").then((mod) => {
      console.log("[Memorization] <MediaPlayer> module loaded");
      return mod.default;
    }),
  {
    loading: () => <MediaPlayerSkeleton />,
    ssr: false,
  }
);

// --- Teacher's Improvement: Dynamically load each right-hand panel on demand ---

const FeedbackSection = dynamic(
  () => {
    console.log("[Memorization] Importing <FeedbackSection>…");
    return import("./FeedbackSection").then((mod) => {
      console.log("[Memorization] <FeedbackSection> loaded");
      return mod.default;
    });
  },
  {
    loading: () => <PanelSkeleton text="Loading Feedback..." />,
    ssr: false,
  }
);

const AssistantSection = dynamic(
  () => {
    console.log("[Memorization] Importing <AssistantSection>…");
    return import("./AssistantSection").then((mod) => {
      console.log("[Memorization] <AssistantSection> loaded");
      return mod.default;
    });
  },
  {
    loading: () => <PanelSkeleton text="Loading Assistant..." />,
    ssr: false,
  }
);

const TranscriptionSection = dynamic(
  () => {
    console.log("[Memorization] Importing <TranscriptionSection>…");
    return import("./TranscriptionSection").then((mod) => {
      console.log("[Memorization] <TranscriptionSection> loaded");
      return mod.default;
    });
  },
  {
    loading: () => <PanelSkeleton text="Loading Transcription..." />,
    ssr: false,
  }
);

/* ------------------------------------------------------------------ */
/*  🆕   NEW TYPE FOR WHISPER TOKENS (start / end time stamps)         */
/* ------------------------------------------------------------------ */
interface WordStamp {
  word: string;
  start: number; // seconds
  end: number; // seconds
}

/* ------------------------------------------------------------------ */
/*  🆕   NEW INTERFACE FOR API WORD FEEDBACK WITH CORRECT WORD INFO    */
/*       (This interface already correctly includes audioUrl)         */
/* ------------------------------------------------------------------ */
interface ApiWordFeedback extends WordStamp {
  correctWordInfo?: {
    text: string;
    surah: number;
    ayah: number;
    wordInAyahIndex: number;
    audioUrl: string; // This is the crucial part that passes the audio URL
  };
  // ... any other fields returned by your API for each word
}

/* ------------------------------------------------------------------ */
/*  🆕   ENHANCED VALIDATION INTERFACES AND TYPES                      */
/* ------------------------------------------------------------------ */
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface ApiResponseValidation extends ValidationResult {
  hasAudioMetadata: boolean;
  audioMetadataCount: number;
  expectedWordCount: number;
}

// ====================== TYPES ======================

type Surah = BaseSurah & {
  id?: number;
};

export interface Letter {
  baseLetter: string;
  vowelMarks: string[];
  originalChar: string;
}

type Difficulty = "easy" | "hard";
type FeedbackMode = "overview" | "assistant" | "pronunciation";

type Verse = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

type Props = {
  sessionId: number;
  userId: string;
  surahNumber: number;
  verses: Verse[];
  initialPercentage: number;
  versesRange: string;
  memorizedVerseNumbers: number[];
  surahData: Surah;
  userSubscription: {
    isActive: boolean;
  } | null;
  initialHearts: number;
};

type AddMemorizedVerseParams = {
  surahNumber: number;
  verseNumber: number;
};

// ============== NEW RANGE-BASED CONSTANTS & TYPES =============

const DYNAMIC_FETCH_THRESHOLD = 50; // If Surah is longer, we show a range picker
const VERSES_PER_RANGE = 50; // The chunk size for each selectable range

type VerseRange = {
  start: number;
  end: number;
};

// ====================== VALIDATION FUNCTIONS ======================

/**
 * Validates the structure of API word feedback response
 */
const validateApiWordFeedback = (data: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data) {
    errors.push("API response data is null or undefined");
    return { isValid: false, errors, warnings };
  }

  if (!Array.isArray(data)) {
    errors.push("API response data is not an array");
    return { isValid: false, errors, warnings };
  }

  data.forEach((item: any, index: number) => {
    if (!item || typeof item !== "object") {
      errors.push(`Item at index ${index} is not an object`);
      return;
    }

    // Validate required WordStamp fields
    if (typeof item.word !== "string") {
      errors.push(`Item at index ${index} missing or invalid 'word' field`);
    }
    if (typeof item.start !== "number" || isNaN(item.start)) {
      errors.push(`Item at index ${index} missing or invalid 'start' field`);
    }
    if (typeof item.end !== "number" || isNaN(item.end)) {
      errors.push(`Item at index ${index} missing or invalid 'end' field`);
    }

    // Validate timing consistency
    if (typeof item.start === "number" && typeof item.end === "number") {
      if (item.start < 0) {
        warnings.push(`Item at index ${index} has negative start time`);
      }
      if (item.end < item.start) {
        warnings.push(`Item at index ${index} has end time before start time`);
      }
      if (item.end - item.start > 10) {
        warnings.push(
          `Item at index ${index} has unusually long duration (${(
            item.end - item.start
          ).toFixed(2)}s)`
        );
      }
    }

    // Validate optional correctWordInfo
    if (item.correctWordInfo) {
      const cwi = item.correctWordInfo;
      if (typeof cwi.text !== "string") {
        warnings.push(
          `Item at index ${index} has invalid correctWordInfo.text`
        );
      }
      if (typeof cwi.surah !== "number") {
        warnings.push(
          `Item at index ${index} has invalid correctWordInfo.surah`
        );
      }
      if (typeof cwi.ayah !== "number") {
        warnings.push(
          `Item at index ${index} has invalid correctWordInfo.ayah`
        );
      }
      if (typeof cwi.wordInAyahIndex !== "number") {
        warnings.push(
          `Item at index ${index} has invalid correctWordInfo.wordInAyahIndex`
        );
      }
      if (typeof cwi.audioUrl !== "string") {
        warnings.push(
          `Item at index ${index} has invalid correctWordInfo.audioUrl`
        );
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Validates API response structure and content
 */
const validateApiResponse = (
  data: any,
  expectedWordCount: number
): ApiResponseValidation => {
  const baseValidation = validateApiWordFeedback(data.words);
  const audioMetadataCount = Array.isArray(data.words)
    ? data.words.filter((w: any) => w.correctWordInfo).length
    : 0;

  return {
    ...baseValidation,
    hasAudioMetadata: audioMetadataCount > 0,
    audioMetadataCount,
    expectedWordCount,
  };
};

/**
 * Validates displayedWords structure integrity
 */
const validateDisplayedWordsStructure = (
  displayedWords: any
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!displayedWords || typeof displayedWords !== "object") {
    errors.push("displayedWords is not a valid object");
    return { isValid: false, errors, warnings };
  }

  Object.entries(displayedWords).forEach(
    ([verseNumber, words]: [string, any]) => {
      if (!Array.isArray(words)) {
        errors.push(`Verse ${verseNumber} words is not an array`);
        return;
      }

      words.forEach((word: any, index: number) => {
        if (!word || typeof word !== "object") {
          errors.push(
            `Word at verse ${verseNumber}, index ${index} is not an object`
          );
          return;
        }

        // Validate required fields
        const requiredFields = [
          "text",
          "displayText",
          "matched",
          "almostMatched",
          "highlighted",
          "given",
        ];
        requiredFields.forEach((field) => {
          if (!(field in word)) {
            errors.push(
              `Word at verse ${verseNumber}, index ${index} missing field: ${field}`
            );
          }
        });

        // Validate types
        if (typeof word.text !== "string") {
          errors.push(
            `Word at verse ${verseNumber}, index ${index} has invalid text type`
          );
        }
        if (typeof word.displayText !== "string") {
          errors.push(
            `Word at verse ${verseNumber}, index ${index} has invalid displayText type`
          );
        }
        if (typeof word.matched !== "boolean") {
          errors.push(
            `Word at verse ${verseNumber}, index ${index} has invalid matched type`
          );
        }
        // 🆕 Check correctWordInfo structure if it exists
        if (word.correctWordInfo) {
          const cwi = word.correctWordInfo;
          if (
            typeof cwi.text !== "string" ||
            typeof cwi.surah !== "number" ||
            typeof cwi.ayah !== "number" ||
            typeof cwi.wordInAyahIndex !== "number" ||
            (typeof cwi.audioUrl !== "string" && cwi.audioUrl !== null)
          ) {
            warnings.push(
              `Word at verse ${verseNumber}, index ${index} has invalid correctWordInfo structure.`
            );
          }
        }
      });
    }
  );

  return { isValid: errors.length === 0, errors, warnings };
};

// ====================== HELPER FUNCTIONS ======================

function extractTextFromJSX(node: React.ReactNode): string {
  if (typeof node === "string" || typeof node === "number") {
    return node.toString();
  }
  if (Array.isArray(node)) {
    return node.map(extractTextFromJSX).join(" ");
  }
  if (React.isValidElement(node)) {
    return extractTextFromJSX(node.props.children);
  }
  return "";
}

function shortenTTSText(text: string): string {
  if (!text) return "";
  let shortened = text
    .replace(/\*\*/g, "")
    .replace(/\*/g, "")
    .replace(/`/g, "")
    .replace(/\[(\d+)\]/g, "citation $1");

  if (shortened.length > 500) {
    const sentences = shortened
      .split(/[.!?]+/)
      .filter((s) => s.trim().length > 0);

    if (sentences.length > 5) {
      shortened =
        sentences.slice(0, 2).join(". ") +
        ". ... " +
        sentences.slice(-2).join(". ") +
        ".";
    }
  }

  return shortened;
}

const extractAllCitationNumbers = (displayedWords: {
  [verseNumber: number]: any[];
}): number[] => {
  const citationNumbers: number[] = [];
  Object.values(displayedWords).forEach((verseWords) => {
    verseWords.forEach((word) => {
      if (
        word.feedbackNumber !== undefined &&
        !isNaN(parseInt(String(word.feedbackNumber)))
      ) {
        citationNumbers.push(parseInt(String(word.feedbackNumber)));
      }
    });
  });
  return [...new Set(citationNumbers)].sort((a, b) => a - b);
};

const prefetchCitationSummaries = async (
  feedbackElements: JSX.Element[],
  citationNumbers: number[]
): Promise<void> => {
  if (citationNumbers.length === 0 || feedbackElements.length === 0) return;

  console.log(`Pre-generating TTS for ${citationNumbers.length} citations`);

  const summaries: {
    citationNumber: number;
    summary: string;
    priority: number;
  }[] = [];

  for (const citationNumber of citationNumbers) {
    const summary = generateCitationFocusedSummary(
      feedbackElements,
      citationNumber
    );
    if (summary && summary.trim().length > 0) {
      const basePriority =
        citationNumber <= 3 ? 1 : citationNumber <= 6 ? 2 : 3;
      const lengthFactor = Math.floor(summary.length / 100);
      const priority = basePriority + lengthFactor;

      summaries.push({
        citationNumber,
        summary: shortenTTSText(summary),
        priority,
      });
    }
  }

  const uncachedSummaries = summaries.filter((s) => !isTextCached(s.summary));
  if (uncachedSummaries.length === 0) {
    console.log("All citation summaries already cached!");
    return;
  }

  uncachedSummaries.sort((a, b) => a.priority - b.priority);
  console.log(
    `Prefetching ${uncachedSummaries.length} uncached citation summaries`
  );

  const summaryTexts = uncachedSummaries.map((s) => s.summary);
  await batchPrefetchTTS(summaryTexts, 3);

  console.log(
    `Completed pre-generating TTS for ${uncachedSummaries.length} citations`
  );
};

/* ------------------------------------------------------------------ */
/*  🆕   ENHANCED TIMESTAMP ATTACHMENT HELPER WITH VALIDATION          */
/*       (This function already correctly attaches correctWordInfo)   */
/* ------------------------------------------------------------------ */
/**
 * Attaches start/end timestamps from Whisper to displayedWords structure
 * Now includes comprehensive validation and error handling
 */
const attachTimestampsToDisplayedWords = (
  words: ApiWordFeedback[],
  currentDisplayedWords: { [verseNumber: number]: any[] }
): {
  updatedWords: { [verseNumber: number]: any[] };
  validation: ValidationResult;
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Early validation
  if (!Array.isArray(words)) {
    errors.push("API words data is not an array");
    return {
      updatedWords: currentDisplayedWords,
      validation: { isValid: false, errors, warnings },
    };
  }

  if (!words.length) {
    warnings.push("No timestamp data received from API");
    return {
      updatedWords: currentDisplayedWords,
      validation: { isValid: true, errors, warnings },
    };
  }

  // Validate API word structure
  const apiValidation = validateApiWordFeedback(words);
  if (!apiValidation.isValid) {
    errors.push(...apiValidation.errors);
    return {
      updatedWords: currentDisplayedWords,
      validation: {
        isValid: false,
        errors: [...errors, ...apiValidation.errors],
        warnings,
      },
    };
  }
  warnings.push(...apiValidation.warnings);

  // Validate displayedWords structure
  const displayedWordsValidation = validateDisplayedWordsStructure(
    currentDisplayedWords
  );
  if (!displayedWordsValidation.isValid) {
    errors.push(
      "DisplayedWords structure is invalid",
      ...displayedWordsValidation.errors
    );
    return {
      updatedWords: currentDisplayedWords,
      validation: { isValid: false, errors, warnings },
    };
  }

  // Create a flat array of all non-given words in order
  const flatWords: string[] = [];
  const wordToLocation: Map<
    number,
    { verseNumber: number; wordIndex: number }
  > = new Map();

  let flatIndex = 0;
  const sortedVerseEntries = Object.entries(currentDisplayedWords).sort(
    ([a], [b]) => Number(a) - Number(b)
  );

  sortedVerseEntries.forEach(([verseNumberStr, verseWords]) => {
    const verseNumber = Number(verseNumberStr);
    if (!Array.isArray(verseWords)) {
      warnings.push(`Verse ${verseNumber} words is not an array, skipping`);
      return;
    }

    verseWords.forEach((wordObj, wordIndex) => {
      if (!wordObj || typeof wordObj !== "object") {
        warnings.push(
          `Invalid word object at verse ${verseNumber}, index ${wordIndex}`
        );
        return;
      }

      if (!wordObj.given) {
        // Only non-given words can have user recordings
        flatWords.push(wordObj.text);
        wordToLocation.set(flatIndex, { verseNumber, wordIndex });
        flatIndex++;
      }
    });
  });

  console.log(
    `[attachTimestampsToDisplayedWords] Processing: ${words.length} API tokens to ${flatWords.length} target words`
  );

  // Check for length mismatch
  if (words.length !== flatWords.length) {
    warnings.push(
      `Length mismatch: API returned ${words.length} tokens but expected ${flatWords.length} words. ` +
        `Proceeding with available data.`
    );
  }

  // Match transcribed words to displayedWords and attach timestamps + correctWordInfo
  const updatedWords = produce(currentDisplayedWords, (draft) => {
    let attachedCount = 0;
    let correctWordInfoCount = 0;
    let timestampErrors = 0;

    words.forEach((token, tokenIndex) => {
      try {
        const location = wordToLocation.get(tokenIndex);
        if (!location) {
          if (tokenIndex < flatWords.length) {
            warnings.push(
              `No location mapping found for token ${tokenIndex}: "${token.word}"`
            );
          }
          return;
        }

        const { verseNumber, wordIndex } = location;

        // Validate the target location exists
        if (!draft[verseNumber] || !draft[verseNumber][wordIndex]) {
          warnings.push(
            `Target location does not exist: verse ${verseNumber}, word ${wordIndex}`
          );
          return;
        }

        const targetWord = draft[verseNumber][wordIndex];

        // Validate timestamp data
        if (typeof token.start !== "number" || typeof token.end !== "number") {
          timestampErrors++;
          warnings.push(
            `Invalid timestamp data for token ${tokenIndex}: start=${token.start}, end=${token.end}`
          );
          return;
        }

        if (token.start < 0 || token.end < token.start) {
          timestampErrors++;
          warnings.push(
            `Invalid timestamp range for token ${tokenIndex}: ${token.start}-${token.end}`
          );
          return;
        }

        // Attach timestamps for user's audio segment
        targetWord.clipStart = token.start;
        targetWord.clipEnd = token.end;
        attachedCount++;

        // Attach correctWordInfo if available
        // This is the critical part for passing the audioUrl
        if (token.correctWordInfo) {
          try {
            // Validate correctWordInfo structure (already done by validateApiWordFeedback, but good defensive check)
            const cwi = token.correctWordInfo;
            if (
              typeof cwi.text === "string" &&
              typeof cwi.surah === "number" &&
              typeof cwi.ayah === "number" &&
              typeof cwi.wordInAyahIndex === "number" &&
              typeof cwi.audioUrl === "string" // Ensures audioUrl is a string
            ) {
              // Ensure we're setting the full correctWordInfo object
              targetWord.correctWordInfo = {
                text: cwi.text,
                surah: cwi.surah,
                ayah: cwi.ayah,
                wordInAyahIndex: cwi.wordInAyahIndex,
                audioUrl: cwi.audioUrl, // Assign the audioUrl
              };
              correctWordInfoCount++;

              console.log(
                `[attachTimestampsToDisplayedWords] Successfully attached correctWordInfo to "${token.word}": ` +
                  `audioUrl: ${cwi.audioUrl}`
              );
            } else {
              warnings.push(
                `Invalid correctWordInfo structure for token ${tokenIndex}`
              );
            }
          } catch (cwiError) {
            warnings.push(
              `Error processing correctWordInfo for token ${tokenIndex}: ${cwiError}`
            );
          }
        }
      } catch (error) {
        timestampErrors++;
        errors.push(`Error processing token ${tokenIndex}: ${error}`);
      }
    });

    console.log(
      `[attachTimestampsToDisplayedWords] Attachment summary: ` +
        `${attachedCount} timestamps, ${correctWordInfoCount} correctWordInfo, ${timestampErrors} errors`
    );

    if (attachedCount === 0 && words.length > 0) {
      warnings.push(
        "No timestamps were successfully attached despite having API data"
      );
    }

    if (timestampErrors > 0) {
      warnings.push(`${timestampErrors} timestamp attachment errors occurred`);
    }
  });

  return {
    updatedWords,
    validation: {
      isValid: errors.length === 0,
      errors,
      warnings,
    },
  };
};

// ====================== HOOK & UTILITY ======================

/**
 * Calculate the completion rate (0-100%) based on words, not verses.
 * - Verses #1 and #2 are "given" and excluded from total/matched.
 * - If a verse is in `memorizedVersesState`, all its words are matched.
 * - Otherwise, only count the words actually marked `.matched`.
 */
export function calculateCompletionRate(
  displayedWords: Record<number, { matched: boolean }[]>,
  memorizedVersesState: number[]
): number {
  let totalWords = 0;
  let matchedWords = 0;

  // Sort verse numbers so we process them in ascending order
  const sortedVerseNumbers = Object.keys(displayedWords)
    .map(Number)
    .sort((a, b) => a - b);

  for (const verseNumber of sortedVerseNumbers) {
    // 1) Skip the first 2 verses entirely
    if (verseNumber < 3) {
      continue;
    }

    const verseWordArray = displayedWords[verseNumber];
    if (!verseWordArray || verseWordArray.length === 0) {
      continue;
    }

    // 2) If the verse is already memorized, count all its words as matched
    if (memorizedVersesState.includes(verseNumber)) {
      totalWords += verseWordArray.length;
      matchedWords += verseWordArray.length;
    } else {
      // 3) Otherwise, only count the words actually marked `.matched`
      totalWords += verseWordArray.length;
      matchedWords += verseWordArray.filter((w) => w.matched).length;
    }
  }

  if (totalWords === 0) {
    return 0; // no words to memorize => 0% or 100%, your choice
  }

  // Return a percent, e.g. 44.444... => 44
  return (matchedWords / totalWords) * 100;
}

const useScoreCalculation = (
  initialPercentage: number,
  displayedWords: { [verseNumber: number]: any[] },
  unmatchedWords: string[],
  memorizedVersesState: number[]
): [number, React.Dispatch<React.SetStateAction<number>>] => {
  const [score, setScore] = useState<number>(initialPercentage);

  const calculatedScore = useMemo(() => {
    let totalWords = 0;
    let matchedWordsCount = 0;
    let almostMatchedCount = 0;

    // Get all verse numbers in order
    const sortedVerseNumbers = Object.keys(displayedWords)
      .map(Number)
      .sort((a, b) => a - b);

    // Only consider verses >= 3 (skip verses 1 and 2)
    const versesToScore = sortedVerseNumbers.filter((num) => num >= 3);
    if (versesToScore.length === 0) {
      return 0; // No words to calculate score for
    }

    versesToScore.forEach((verseNumber) => {
      const verseWords = displayedWords[verseNumber];
      if (!verseWords) return;

      // If this verse is already memorized, count all its words as matched.
      if (memorizedVersesState.includes(verseNumber)) {
        totalWords += verseWords.length;
        matchedWordsCount += verseWords.length;
      } else {
        // For non-memorized verses, only count words that are not "given"
        const nonGivenWords = verseWords.filter((word: any) => !word.given);
        totalWords += nonGivenWords.length;
        matchedWordsCount += nonGivenWords.filter(
          (word: any) => word.matched
        ).length;
        almostMatchedCount += nonGivenWords.filter(
          (word: any) => word.almostMatched
        ).length;
      }
    });

    return totalWords === 0
      ? 0
      : ((matchedWordsCount + almostMatchedCount * 0.5) / totalWords) * 100;
  }, [displayedWords, memorizedVersesState]); // ✅ FIX 1: Removed unnecessary 'unmatchedWords' dependency

  useEffect(() => {
    if (Math.abs(calculatedScore - score) > 0.01) {
      setScore(calculatedScore);
    }
  }, [calculatedScore, score, setScore]); // ✅ FIX 3: Added missing 'setScore' dependency

  return [score, setScore];
};

export const updateMemorizedVerse = (
  displayedWords: { [verseNumber: number]: any[] },
  verseNumber: number,
  memorizedVerseNumbers: number[]
): { [verseNumber: number]: any[] } => {
  return produce(displayedWords, (draft) => {
    if (draft[verseNumber]) {
      const shouldHighlight =
        verseNumber > 2 && !memorizedVerseNumbers.includes(verseNumber);

      draft[verseNumber] = draft[verseNumber].map((word) => ({
        ...word,
        matched: true,
        displayText: word.text,
        textColor: shouldHighlight ? "text-emerald-500" : undefined,
      }));
    }
  });
};

// ====================== MAIN COMPONENT ======================

const Memorization: FC<Props> = ({
  sessionId,
  surahNumber,
  verses,
  initialPercentage,
  memorizedVerseNumbers,
  surahData,
  userSubscription,
  initialHearts,
}) => {
  const router = useRouter();

  // >>> NEWLY ADDED LINES FOR NEXT/PREV SURAH LOGIC <<<
  // Determine the next and previous surah numbers
  // (Feel free to adjust bounds if your data includes more or fewer surahs)
  const nextSurahNumber = surahNumber < 114 ? surahNumber + 1 : null;
  const previousSurahNumber = surahNumber > 1 ? surahNumber - 1 : null;

  // A simple navigation handler that moves the user to a new surah
  // (You can extend this to also pass in "versesRange" if desired)
  const handleNavigateSurah = useCallback(
    (targetSurah: number) => {
      router.push(`/memorization?surahNumber=${targetSurah}`);
    },
    [router]
  );
  // >>> END NEW LINES <<<

  /* ------------------------------------------------------------------ */
  /*  🆕   STATE: Enhanced API word-level tokens with correctWordInfo     */
  /* ------------------------------------------------------------------ */
  const [transcribedTokens, setTranscribedTokens] = useState<ApiWordFeedback[]>(
    []
  );

  // Get the connection status from the voice store to pass down
  const connectionStatus = useVoiceStore((state) => state.connectionStatus);

  // ★ NEW – live mic amplitude arrives here
  const [inputAmplitude, setInputAmplitude] = useState<number>(0);

  // PAGE-LEVEL LOADING: only used to show <LoadingPage /> initially
  const [loading, setLoading] = useState(true);

  // EPHEMERAL TASK STATE: used for TTS/transcription, etc.
  const [subLoading, setSubLoading] = useState(false);

  // 🆕 Enhanced error state management
  const [lastApiError, setLastApiError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [maxRetries] = useState(3);

  const [audioURL, setAudioURL] = useState<string | null>(null);
  const [responseText, setResponseText] = useState<string>("");
  const [feedbackText, setFeedbackText] = useState<React.ReactNode[]>([]);
  const [combinedFeedback, setCombinedFeedback] = useState<React.ReactNode[]>(
    []
  );
  const [feedbackData, setFeedbackData] = useState<any>(null);
  const [citationMap, setCitationMap] = useState<Record<string, number>>({});
  const [highlightedWord, setHighlightedWord] = useState<string | null>(null);
  const prevSurahNumberRef = useRef(surahNumber);

  const [displayedWords, setDisplayedWords] = useState<{
    [verseNumber: number]: any[];
  }>({});
  const [unmatchedWords, setUnmatchedWords] = useState<string[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [correctWordsList, setCorrectWordsList] = useState<string[]>([]);
  const [selectedOption, setSelectedOption] = useState<string>("Feedback");
  const [feedbackRefs, setFeedbackRefs] = useState<{
    [key: string]: RefObject<HTMLDivElement>;
  }>({});
  const [difficulty, setDifficulty] = useState<"easy" | "hard">("easy");
  const [memorizedVersesState, setMemorizedVersesState] = useState<number[]>(
    memorizedVerseNumbers
  );

  const [exerciseCompleted, setExerciseCompleted] = useState(false);
  const [hasExerciseStarted, setHasExerciseStarted] = useState(false);
  const [ttsLoading, setTtsLoading] = useState(false);
  const [ttsError, setTtsError] = useState<string | null>(null);
  const [selectedCitation, setSelectedCitation] = useState<number | null>(null);
  const [activeFeedbackSection, setActiveFeedbackSection] =
    useState<FeedbackMode>("overview");
  const [isTTSPlaying, setIsTTSPlaying] = useState(false);
  const [ttsAmplitude, setTTSAmplitude] = useState<number | undefined>(
    undefined
  );
  const [frequencyBands, setFrequencyBands] = useState<number[] | undefined>(
    undefined
  );

  const audioAnalysisRef = useRef<{
    audioContext?: AudioContext;
    analyser?: AnalyserNode;
    dataArray?: Uint8Array;
    animationFrameId?: number;
  }>({});
  const previousDisplayedWordsRef = useRef<{
    [verseNumber: number]: any[];
  }>({});

  const [hearts, setHearts] = useState<number>(initialHearts);

  const [score, setScore] = useScoreCalculation(
    initialPercentage,
    displayedWords,
    unmatchedWords,
    memorizedVersesState
  );

  const { reset: resetFinishSound } = useFinishSound({
    score,
    initialScore: initialPercentage,
    isRecording,
    onSoundPlayed: () => {
      console.log("Finish sound played!");
    },
  });

  const memorizedVerseNumbersSet = useMemo(
    () => new Set(memorizedVersesState),
    [memorizedVersesState]
  );

  const ttsAudioRef = useRef<HTMLAudioElement | null>(null);

  const [feedbackCount, setFeedbackCount] = useState<number>(0);

  // ========== NEW RANGE-BASED STATES & EFFECTS ==========
  const [totalAyahs, setTotalAyahs] = useState(0);
  const [isLoadingRange, setIsLoadingRange] = useState(false);
  const [selectedRange, setSelectedRange] = useState<VerseRange | null>(null);

  /* ------------------------------------------------------------------ */
  /*  🆕   ENHANCED AUDIO SEGMENT PLAYBACK HOOK WITH VALIDATION          */
  /* ------------------------------------------------------------------ */
  // Enhanced playSegment function with better blob URL handling and audio management
  const playSegment = useCallback(
    (start: number, end: number) => {
      console.log(
        `[PLAY_SEGMENT] Starting playback request: ${start.toFixed(
          3
        )}s - ${end.toFixed(3)}s`
      );

      // Enhanced validation checks
      if (!audioURL) {
        console.error(
          "[PLAY_SEGMENT] No audioURL available. Cannot play segment. This is expected if the user hasn't recorded yet."
        );
        toast.error("No audio recording available for playback");
        return;
      }

      if (
        typeof start !== "number" ||
        typeof end !== "number" ||
        isNaN(start) ||
        isNaN(end)
      ) {
        console.error(
          `[PLAY_SEGMENT] Invalid start/end times. Start: ${start}, End: ${end}`
        );
        toast.error("Invalid audio timing data");
        return;
      }

      // Enhanced edge case handling
      if (start === end) {
        console.log(
          `[PLAY_SEGMENT] Start and end times identical (${start.toFixed(
            3
          )}s). Adding minimal duration.`
        );
        end = start + 0.1; // Add 100ms minimum duration
      } else if (end < start) {
        console.warn(
          `[PLAY_SEGMENT] End time (${end.toFixed(
            3
          )}) < start time (${start.toFixed(3)}). Correcting.`
        );
        end = start + 0.1;
      }

      // Validate reasonable duration
      if (end - start > 30) {
        console.warn(
          `[PLAY_SEGMENT] Unusually long segment duration: ${(
            end - start
          ).toFixed(2)}s`
        );
        toast.warning("Audio segment is unusually long");
      }

      // Validate blob URL format
      if (audioURL.startsWith("blob:")) {
        console.log(
          `[PLAY_SEGMENT] Detected blob URL, using enhanced blob handling`
        );
      }

      console.log(
        `[PLAY_SEGMENT] Attempting to play audio segment ${start.toFixed(
          3
        )}s - ${end.toFixed(3)}s from source: ${
          audioURL.length > 50 ? audioURL.slice(0, 50) + "..." : audioURL
        }`
      );

      const audio = new Audio();
      let isCleanedUp = false;
      let metadataTimeout: NodeJS.Timeout;

      // Enhanced cleanup function
      const cleanup = () => {
        if (isCleanedUp) return;
        isCleanedUp = true;

        console.log("[PLAY_SEGMENT] Cleaning up audio resources");

        // Clear timeout
        if (metadataTimeout) {
          clearTimeout(metadataTimeout);
        }

        // Remove all event listeners
        audio.removeEventListener("loadedmetadata", metadataHandler);
        audio.removeEventListener("loadeddata", dataHandler);
        audio.removeEventListener("canplay", canPlayHandler);
        audio.removeEventListener("timeupdate", timeUpdateHandler);
        audio.removeEventListener("ended", endedHandler);
        audio.removeEventListener("pause", pauseHandler);
        audio.removeEventListener("error", errorHandler);

        // Pause and reset
        try {
          audio.pause();
          audio.currentTime = 0;
          audio.src = "";
        } catch (e) {
          console.warn("[PLAY_SEGMENT] Error during cleanup:", e);
        }
      };

      // Enhanced error handler
      const errorHandler = (e: Event) => {
        console.error("[PLAY_SEGMENT] Audio error occurred:", e);

        if (audio.error) {
          const errorCode = audio.error.code;
          const errorMessage = audio.error.message || "Unknown error";

          console.error(
            `[PLAY_SEGMENT] Audio Error - Code: ${errorCode}, Message: ${errorMessage}`
          );

          // Specific error handling
          switch (errorCode) {
            case MediaError.MEDIA_ERR_ABORTED:
              console.error("[PLAY_SEGMENT] Playback aborted by user");
              toast.error("Audio playback was stopped");
              break;
            case MediaError.MEDIA_ERR_NETWORK:
              console.error("[PLAY_SEGMENT] Network error while loading audio");
              toast.error("Network error during audio playback");
              break;
            case MediaError.MEDIA_ERR_DECODE:
              console.error("[PLAY_SEGMENT] Audio decoding error");
              toast.error("Audio format error");
              break;
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              console.error("[PLAY_SEGMENT] Audio format not supported");
              toast.error("Audio format not supported");
              break;
            default:
              console.error("[PLAY_SEGMENT] Unknown audio error");
              toast.error("Audio playback error");
          }
        }

        cleanup();
      };

      // Declare handlers in scope
      let timeUpdateHandler: () => void;
      let endedHandler: () => void;
      let pauseHandler: () => void;

      // Enhanced metadata handler with blob URL support
      const metadataHandler = () => {
        console.log(
          `[PLAY_SEGMENT] Metadata loaded. Duration: ${audio.duration}s, Ready State: ${audio.readyState}`
        );

        // Handle infinite or invalid duration (common with blob URLs)
        const isValidDuration = isFinite(audio.duration) && audio.duration > 0;

        if (!isValidDuration) {
          console.warn(
            "[PLAY_SEGMENT] Invalid duration detected, but proceeding with blob URL playback"
          );
          // For blob URLs, we often can't get accurate duration until playback starts
          // So we'll proceed anyway and rely on the timeupdate handler
        }

        // Validate start time against duration (only if we have valid duration)
        if (isValidDuration && start >= audio.duration) {
          console.error(
            `[PLAY_SEGMENT] Start time (${start.toFixed(
              3
            )}) >= audio duration (${audio.duration.toFixed(3)}). Cannot play.`
          );
          toast.error("Audio segment is beyond recording duration");
          cleanup();
          return;
        }

        // Calculate effective end time
        const effectiveEnd = isValidDuration
          ? Math.min(end, audio.duration)
          : end;
        const finalEffectiveEnd = Math.max(start, effectiveEnd);

        console.log(
          `[PLAY_SEGMENT] Effective playback range: ${start.toFixed(
            3
          )}s - ${finalEffectiveEnd.toFixed(3)}s}`
        );

        // Set up playback handlers
        timeUpdateHandler = () => {
          const currentTime = audio.currentTime;

          // Stop playback when we reach the end time
          if (currentTime >= finalEffectiveEnd) {
            if (!audio.paused) {
              console.log(
                `[PLAY_SEGMENT] Reached end time ${finalEffectiveEnd.toFixed(
                  3
                )}s. Current: ${currentTime.toFixed(3)}s. Stopping.`
              );
              audio.pause();
              cleanup();
            }
          }
        };

        endedHandler = () => {
          console.log("[PLAY_SEGMENT] Audio playback ended naturally.");
          cleanup();
        };

        pauseHandler = () => {
          console.log(
            `[PLAY_SEGMENT] Audio paused at ${audio.currentTime.toFixed(3)}s`
          );
          // Only cleanup if we're past our target end time or if playback was interrupted
          if (audio.currentTime >= finalEffectiveEnd || audio.ended) {
            cleanup();
          }
        };

        // Add event listeners
        audio.addEventListener("timeupdate", timeUpdateHandler);
        audio.addEventListener("ended", endedHandler);
        audio.addEventListener("pause", pauseHandler);

        // Start playback
        try {
          audio.currentTime = start;
          console.log(
            `[PLAY_SEGMENT] Set currentTime to ${audio.currentTime.toFixed(3)}s`
          );

          audio
            .play()
            .then(() => {
              console.log(
                `[PLAY_SEGMENT] ✅ Playback started successfully at ${audio.currentTime.toFixed(
                  3
                )}s`
              );
            })
            .catch((playError) => {
              console.error("[PLAY_SEGMENT] Playback failed:", playError);
              toast.error("Failed to start audio playback");
              cleanup();
            });
        } catch (seekError) {
          console.error("[PLAY_SEGMENT] Error setting currentTime:", seekError);
          toast.error("Error seeking to audio position");
          cleanup();
        }
      };

      // Additional handlers for better blob URL support
      const dataHandler = () => {
        console.log(
          "[PLAY_SEGMENT] Audio data loaded, ready state:",
          audio.readyState
        );
      };

      const canPlayHandler = () => {
        console.log("[PLAY_SEGMENT] Audio can start playing");
      };

      // Enhanced loading with timeout
      const loadAudio = () => {
        console.log("[PLAY_SEGMENT] Loading audio source");

        // Set up timeout for metadata loading (important for blob URLs)
        metadataTimeout = setTimeout(() => {
          if (!isCleanedUp && audio.readyState < 1) {
            console.warn(
              "[PLAY_SEGMENT] Metadata loading timeout, attempting to proceed anyway"
            );
            // Try to proceed even without full metadata (common with blob URLs)
            if (audio.readyState >= 0) {
              metadataHandler();
            } else {
              console.error(
                "[PLAY_SEGMENT] Audio failed to load within timeout period"
              );
              toast.error("Audio loading timeout");
              cleanup();
            }
          }
        }, 3000); // 3 second timeout

        // Add all event listeners
        audio.addEventListener("loadedmetadata", metadataHandler, {
          once: true,
        });
        audio.addEventListener("loadeddata", dataHandler, { once: true });
        audio.addEventListener("canplay", canPlayHandler, { once: true });
        audio.addEventListener("error", errorHandler);

        // Enhanced loading for blob URLs
        audio.preload = "metadata";
        audio.src = audioURL;

        // Force load
        audio.load();

        console.log(
          `[PLAY_SEGMENT] Audio loading initiated. Network State: ${audio.networkState}, Ready State: ${audio.readyState}`
        );
      };

      // Start the loading process
      try {
        loadAudio();
      } catch (loadError) {
        console.error("[PLAY_SEGMENT] Error initiating audio load:", loadError);
        toast.error("Error loading audio for playback");
        cleanup();
      }

      // Return cleanup function for external cleanup if needed
      return cleanup;
    },
    [audioURL]
  );

  // ──────────────────────────────────────────────────────────
  //      CALLBACK: store mic RMS from MediaPlayer
  // ──────────────────────────────────────────────────────────
  const handleAmplitudeUpdate = useCallback((amp: number) => {
    setInputAmplitude(amp); // ★ NEW
  }, []);

  // Once we know the total ayahs, we decide whether to show a range picker
  useEffect(() => {
    if (surahData && typeof surahData.numberOfAyahs === "number") {
      setTotalAyahs(surahData.numberOfAyahs);
    } else {
      // fallback if surahData doesn't have it
      setTotalAyahs(verses?.length ?? 0);
    }
  }, [surahData, verses]);

  // We'll store "activeVerses" in a local state, so we can replace them
  // if user picks a range from a big Surah.
  const [activeVerses, setActiveVerses] = useState<Verse[]>([]);

  // We only initialize displayedWords once we have "activeVerses"
  // If Surah is small (<= threshold) => we skip range approach & set them immediately
  useEffect(() => {
    if (!loading && totalAyahs > 0) {
      if (totalAyahs <= DYNAMIC_FETCH_THRESHOLD) {
        setActiveVerses(verses);
        // Immediately pick a "range" that is effectively all verses
        setSelectedRange({ start: 1, end: totalAyahs });
      }
    }
  }, [loading, totalAyahs, verses]);

  // This effect triggers the old logic (initializeDisplayedWords)
  // once we have some activeVerses set.
  useEffect(() => {
    if (activeVerses.length > 0) {
      initializeDisplayedWords(activeVerses);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeVerses]);

  // We'll generate all possible chunk ranges if surah is large
  const availableRanges = useMemo(() => {
    if (totalAyahs <= DYNAMIC_FETCH_THRESHOLD) return [];
    const arr: VerseRange[] = [];
    let start = 1;
    while (start <= totalAyahs) {
      const end = Math.min(start + VERSES_PER_RANGE - 1, totalAyahs);
      arr.push({ start, end });
      start += VERSES_PER_RANGE;
    }
    return arr;
  }, [totalAyahs]);

  // The function that fetches verses for the user-selected range
  const handleRangeSelectionRequest = useCallback(
    async (range: VerseRange) => {
      setIsLoadingRange(true);
      setSelectedRange(range);
      setFeedbackText([]);
      setCombinedFeedback([]);
      setFeedbackData(null);
      setCitationMap({});
      setFeedbackRefs({});
      setSelectedCitation(null);
      setActiveFeedbackSection("overview");
      setResponseText("");
      setDisplayedWords({});
      setUnmatchedWords([]);
      setCorrectWordsList([]);
      setLastApiError(null); // 🆕 Clear previous errors
      setRetryCount(0); // 🆕 Reset retry count

      try {
        console.log(`Attempting to fetch range: ${range.start}-${range.end}`);
        const response = await fetch(
          `/api/verses?surah=${surahNumber}&start=${range.start}&end=${range.end}`
        );
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `API error. Status: ${response.status}`
          );
        }
        const fetchedVerses: Verse[] = await response.json();
        console.log(
          "API response verse numbers:",
          fetchedVerses.map((v) => v.verseNumber).sort((a, b) => a - b)
        );
        if (!fetchedVerses || fetchedVerses.length === 0) {
          toast.error(
            `Could not load verses for range ${range.start}-${range.end}.`
          );
          setActiveVerses([]);
          setSelectedRange(null);
        } else {
          setActiveVerses(fetchedVerses);
          console.log("Set activeVerses with count:", fetchedVerses.length);
        }
      } catch (err: any) {
        const errorMessage = err.message || "Unknown error";
        setLastApiError(errorMessage); // 🆕 Store error for potential retry
        toast.error(`Failed to load range: ${errorMessage}`);
        setActiveVerses([]);
        setSelectedRange(null);
      } finally {
        setIsLoadingRange(false);
        resetFinishSound();
      }
    },
    [surahNumber, resetFinishSound]
  );

  // If user wants to go back to range selection
  const handleClearRangeSelection = useCallback(() => {
    setSelectedRange(null);
    setActiveVerses([]);
    setDisplayedWords({});
    setCorrectWordsList([]);
    setUnmatchedWords([]);
    setFeedbackText([]);
    setCombinedFeedback([]);
    setFeedbackData(null);
    setCitationMap({});
    setFeedbackRefs({});
    setSelectedCitation(null);
    setActiveFeedbackSection("overview");
    setResponseText("");
    setLastApiError(null); // 🆕 Clear errors
    setRetryCount(0); // 🆕 Reset retry count
  }, []);

  // ============ END RANGE-BASED ADDITIONS ============

  // Simulate or handle initial page loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // ====================== FEEDBACK COUNT ======================
  const getFeedbackIssueCount = useCallback(
    (feedbackItems: React.ReactNode[]) => {
      let missingCount = 0;
      let pronunciationCount = 0;
      let extraCount = 0;

      feedbackItems.forEach((item) => {
        if (React.isValidElement(item)) {
          const dataWord = item.props["data-word"] || "";
          let childText = "";
          const childrenArr = React.Children.toArray(item.props.children);
          if (childrenArr.length > 0 && React.isValidElement(childrenArr[0])) {
            childText = extractTextFromJSX(childrenArr[0].props.children || "");
          }

          if (childText.includes("Missing")) {
            missingCount++;
          } else if (childText.includes("Extra")) {
            extraCount++;
          } else {
            if (
              dataWord.includes("congratulations") ||
              dataWord.includes("perfect-recitation") ||
              dataWord.includes("all-memorized")
            ) {
              // It's a summary or success message
            } else {
              pronunciationCount++;
            }
          }
        }
      });

      return missingCount + extraCount + pronunciationCount;
    },
    []
  );

  useEffect(() => {
    if (combinedFeedback && combinedFeedback.length > 0) {
      const count = getFeedbackIssueCount(combinedFeedback);
      setFeedbackCount(count);
    } else {
      setFeedbackCount(0);
    }
  }, [combinedFeedback, getFeedbackIssueCount]);

  // ====================== CITATION TTS ======================
  const handlePlayCitationTTS = useCallback(
    async (citationNumber: number) => {
      console.time("citation-playback-total");

      if (ttsLoading) {
        console.log("TTS already loading, skipping request");
        return;
      }

      setTtsLoading(true);
      setTtsError(null);

      try {
        if (ttsAudioRef.current) {
          console.log("Cleaning up previous audio instance");
          ttsAudioRef.current.pause();
          ttsAudioRef.current = null;
        }

        console.time("summary-generation");
        const summary = generateCitationFocusedSummary(
          feedbackText as JSX.Element[],
          citationNumber
        );
        console.timeEnd("summary-generation");

        if (!summary) {
          throw new Error(`No feedback found for citation #${citationNumber}`);
        }

        console.time("audio-synthesis");
        const audioElement = await synthesizeSpeech(summary);
        console.timeEnd("audio-synthesis");

        if (!audioElement) {
          throw new Error("Failed to synthesize speech");
        }

        ttsAudioRef.current = audioElement;

        const setupEventListeners = () => {
          audioElement.addEventListener("playing", () => {
            console.log("Audio playing event fired");
            setIsTTSPlaying(true);
            setTtsLoading(false);
          });

          audioElement.addEventListener("pause", () => {
            console.log("Audio paused event");
            setIsTTSPlaying(false);
          });

          audioElement.addEventListener("ended", () => {
            console.log("Audio ended event");
            setIsTTSPlaying(false);
            setTtsLoading(false);
            setTimeout(() => {
              setSelectedCitation(null);
            }, 300);
          });

          audioElement.addEventListener("error", (err) => {
            console.error("Audio error event:", err);
            setIsTTSPlaying(false);
            setTtsError("Error playing audio");
            setTtsLoading(false);
          });
        };

        setupEventListeners();
        setIsTTSPlaying(true);

        try {
          await audioElement.play();
        } catch (playError) {
          console.error("Error starting playback:", playError);
          setTtsLoading(false);
          setIsTTSPlaying(false);
          throw playError;
        }
      } catch (error) {
        console.error("Error in citation TTS playback:", error);
        setTtsError(
          typeof error === "string" ? error : "Failed to play citation feedback"
        );
        setIsTTSPlaying(false);
        setTtsLoading(false);
        setTimeout(() => setSelectedCitation(null), 300);
      }
    },
    [feedbackText, ttsLoading]
  );

  // Cleanup audio on unmount
  useEffect(() => {
    // ✅ FIX 2: Capture ref.current in a variable inside the effect
    const analysisRef = audioAnalysisRef.current;
    const audioRef = ttsAudioRef.current;

    return () => {
      if (audioRef) {
        audioRef.pause();
        // It's safer to remove specific listeners if you have them, but for brevity:
        // Here we assume listeners are handled within the audio elements' lifecycles
      }
      if (analysisRef.animationFrameId) {
        cancelAnimationFrame(analysisRef.animationFrameId);
      }
      if (analysisRef.audioContext) {
        try {
          analysisRef.audioContext.close();
        } catch (err) {
          console.error("Error closing audio context:", err);
        }
      }
      setIsTTSPlaying(false);
    };
  }, []);

  const cleanupAudioResources = useCallback(() => {
    if (ttsAudioRef.current) {
      ttsAudioRef.current.pause();
      ttsAudioRef.current = null;
    }
    if (audioAnalysisRef.current.animationFrameId) {
      cancelAnimationFrame(audioAnalysisRef.current.animationFrameId);
    }
    if (audioAnalysisRef.current.audioContext) {
      try {
        audioAnalysisRef.current.audioContext.close();
      } catch (err) {
        console.error("Error closing audio context:", err);
      }
    }
    setIsTTSPlaying(false);
  }, []);

  useEffect(() => {
    return () => {
      cleanupAudioResources();
    };
  }, [cleanupAudioResources]);

  const handleCitationClick = useCallback(
    (citationNumber: number) => {
      setSelectedCitation(citationNumber);
      setActiveFeedbackSection("overview");
      handlePlayCitationTTS(citationNumber);
    },
    [handlePlayCitationTTS]
  );

  const handleFeedbackSectionChange = useCallback(
    (section: FeedbackMode) => {
      if (section !== activeFeedbackSection) {
        console.log(
          `Changing feedback section from ${activeFeedbackSection} to ${section}`
        );
        setActiveFeedbackSection(section);
        if (section === "overview") {
          setSelectedCitation(null);
        }
      }
    },
    [activeFeedbackSection]
  );

  // ====================== DISPLAYED WORDS INIT (MODIFIED) ======================
  const initializeDisplayedWords = useCallback(
    (verseSource: Verse[]) => {
      console.log(
        "initializeDisplayedWords called with verses:",
        verseSource.map((v) => v.verseNumber).sort((a, b) => a - b)
      );
      const correctWords: { [verseNumber: number]: any[] } = {};

      verseSource.forEach((verse) => {
        const verseNumber = verse.verseNumber;
        const isMemorized = memorizedVerseNumbersSet.has(verseNumber);

        let originalWords: any[] = [];
        if (
          !isMemorized &&
          verseNumber > 2 &&
          previousDisplayedWordsRef.current[verseNumber]
        ) {
          originalWords = previousDisplayedWordsRef.current[verseNumber];
        }

        const verseWords = verse.text.split(" ").map((word, wordIndex) => {
          // This audioFilePath is for local/static audio, not the dynamic Quran.com one
          // The correctWordInfo.audioUrl from API is for that.
          const wordNumber = (wordIndex + 1).toString().padStart(3, "0");
          const audioFilePath = `/112_${verseNumber
            .toString()
            .padStart(3, "0")}_${wordNumber}.wav`;

          if (originalWords.length > 0) {
            return {
              ...originalWords[wordIndex],
              verseNumber,
              wordIndex,
              audio: audioFilePath,
            };
          }

          let displayText = "•••";
          let given = false;
          if (verseNumber <= 2 || isMemorized) {
            displayText = word;
            given = true;
          }

          return {
            text: word,
            audio: audioFilePath, // Still keep this for any existing static audio logic
            displayText,
            matched: false,
            almostMatched: false,
            highlighted: false,
            given,
            textColor: undefined,
            correctWord: !given ? word : null,
            verseNumber,
            wordIndex,
            // correctWordInfo: undefined, // Initialize as undefined, will be populated by API response
          };
        });

        correctWords[verseNumber] = verseWords;
      });

      console.log(
        "correctWords keys after processing:",
        Object.keys(correctWords)
          .map(Number)
          .sort((a, b) => a - b)
      );

      setDisplayedWords((_) => {
        const newState = { ...correctWords };

        const newUnmatchedWords: string[] = [];
        const newCorrectWordsList: string[] = [];

        Object.keys(newState)
          .map(Number)
          .sort((a, b) => a - b)
          .forEach((verseNumber) => {
            const verseWords = newState[verseNumber];
            verseWords.forEach((wordObj) => {
              if (!wordObj.given) {
                if (!wordObj.matched && !wordObj.almostMatched) {
                  newUnmatchedWords.push(wordObj.text);
                }
                newCorrectWordsList.push(wordObj.text);
              }
            });
          });

        setUnmatchedWords(newUnmatchedWords);
        setCorrectWordsList(newCorrectWordsList);

        previousDisplayedWordsRef.current = newState;
        console.log(
          "Final displayedWords keys:",
          Object.keys(newState)
            .map(Number)
            .sort((a, b) => a - b)
        );
        return newState;
      });

      setSelectedCitation(null);
      setActiveFeedbackSection("overview");
    },
    [memorizedVerseNumbersSet]
  );

  useEffect(() => {
    // Only reset everything if the surah number has changed
    if (prevSurahNumberRef.current !== surahNumber) {
      console.log(
        `Navigating from surah ${prevSurahNumberRef.current} to ${surahNumber}`
      );

      // Reset memorization state
      setMemorizedVersesState(memorizedVerseNumbers);
      previousDisplayedWordsRef.current = {};
      setDisplayedWords({});
      setUnmatchedWords([]);
      setCorrectWordsList([]);

      // Reset feedback states
      setResponseText("");
      setFeedbackText([]);
      setCombinedFeedback([]);
      setFeedbackData(null);
      setCitationMap({});
      setFeedbackRefs({});
      setSelectedCitation(null);
      setActiveFeedbackSection("overview");

      // 🆕 Reset error states
      setLastApiError(null);
      setRetryCount(0);

      // Reset exercise states
      setHasExerciseStarted(false);
      setExerciseCompleted(false);
      setScore(initialPercentage);
      resetFinishSound();

      // Clear selected range for large surahs
      setSelectedRange(
        totalAyahs <= DYNAMIC_FETCH_THRESHOLD
          ? { start: 1, end: totalAyahs }
          : null
      );

      // Only initialize if verses are available and surah is small enough
      if (totalAyahs <= DYNAMIC_FETCH_THRESHOLD && verses.length > 0) {
        setTimeout(() => {
          initializeDisplayedWords(verses);
        }, 0);
      }

      // Update ref for next comparison
      prevSurahNumberRef.current = surahNumber;
    }
  }, [
    surahNumber,
    memorizedVerseNumbers,
    verses,
    totalAyahs,
    initialPercentage,
    resetFinishSound,
    initializeDisplayedWords,
    setScore,
  ]);

  const resetMatchedStatusInDisplayedWords = useCallback(() => {
    setDisplayedWords((current) =>
      produce(current, (draft) => {
        Object.keys(draft).forEach((verseNumberStr) => {
          draft[Number(verseNumberStr)] = draft[Number(verseNumberStr)].map(
            (wordObj: any) => ({
              ...wordObj,
              matched: false,
              almostMatched: false,
              highlighted: false,
              textColor: undefined,
            })
          );
        });
      })
    );
  }, []);

  const handleUnmatchedWordClick = useCallback(
    (word: string, citationNumber?: number) => {
      if (citationNumber) {
        handleCitationClick(citationNumber);
        return;
      }

      const normalizedWord = removeVowelMarkings(word);
      setHighlightedWord(normalizedWord);
      setSelectedOption("Feedback");

      const feedbackRef = feedbackRefs[normalizedWord];
      if (feedbackRef?.current) {
        feedbackRef.current.classList.add("feedback-highlight");
        setTimeout(() => {
          feedbackRef.current?.classList.remove("feedback-highlight");
        }, 2000);
        feedbackRef.current.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    },
    [feedbackRefs, handleCitationClick]
  );

  const storeMemorizedVerses = useCallback(
    async (newlyMemorized: number[], surahNumberParam: number) => {
      console.log("Attempting to store verses:", {
        newlyMemorized,
        surahNumberParam,
      });

      for (const verseNumber of newlyMemorized) {
        try {
          console.log("Storing verse:", {
            surahNumber: surahNumberParam,
            verseNumber,
          });

          await addMemorizedVerse({
            surahNumber: surahNumberParam,
            verseNumber,
          });

          console.log("Successfully stored verse:", verseNumber);
        } catch (error) {
          console.error(
            `Error storing memorized verse number ${verseNumber}:`,
            error
          );
        }
      }
    },
    []
  );

  const handlePlayFeedbackTTS = async () => {
    if (ttsLoading) return;
    setTtsLoading(true);
    setTtsError(null);

    try {
      const plainText = feedbackText
        .map((node) => extractTextFromJSX(node))
        .join(" ")
        .trim();

      if (!plainText) {
        throw new Error("No feedback text available to play");
      }

      const maxChars = 4000;
      if (plainText.length <= maxChars) {
        const optimizedText = shortenTTSText(plainText);
        const audioElement = await synthesizeSpeech(optimizedText);
        if (audioElement) {
          ttsAudioRef.current = audioElement;
        }
      } else {
        console.log(
          `Text too long (${plainText.length} chars). Using first ${maxChars} characters.`
        );
        const truncatedText = plainText.substring(0, maxChars);
        const finalText =
          shortenTTSText(truncatedText.trim()) +
          "... (feedback truncated due to length)";

        const audioElement = await synthesizeSpeech(finalText);
        if (audioElement) {
          ttsAudioRef.current = audioElement;
        }
      }
    } catch (error: unknown) {
      console.error("Error in TTS playback:", error);

      let errorMessage =
        "Failed to play feedback audio. Please try again later.";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === "object" &&
        error !== null &&
        "message" in error
      ) {
        const msg = (error as any).message;
        if (typeof msg === "string") {
          errorMessage = msg;
        }
      } else {
        errorMessage = String(error);
      }

      setTtsError(errorMessage);
    } finally {
      setTtsLoading(false);
    }
  };

  const awardOneHeart = useCallback(async () => {
    if (hearts < 5) {
      setHearts((prev) => Math.min(prev + 1, 5));
    }
  }, [hearts]);

  const deductOneHeart = useCallback(async () => {
    if (userSubscription?.isActive) {
      console.log("Subscription active; no hearts deducted for memorization.");
      return;
    }
    if (hearts <= 0) {
      console.log("No hearts left to deduct.");
      return;
    }
    try {
      const response = await reduceHeartsForMemorization();

      if (response?.error === "hearts") {
        toast.error("You have 0 hearts. Please refill or wait.");
        return;
      }
      if (!response?.error) {
        setHearts((prev) => Math.max(prev - 1, 0));
      }
    } catch (err) {
      console.error("Error deducting heart:", err);
    }
  }, [hearts, userSubscription]);

  /* ------------------------------------------------------------------ */
  /*  🆕   ENHANCED RETRY LOGIC FUNCTION                                 */
  /* ------------------------------------------------------------------ */
  const attemptRetryWithBackoff = useCallback(
    async (
      retryFunction: () => Promise<any>,
      currentRetryCount: number,
      maxRetries: number,
      operation: string
    ): Promise<any> => {
      const baseDelay = 1000; // 1 second base delay
      const backoffMultiplier = 2;
      const delay = baseDelay * Math.pow(backoffMultiplier, currentRetryCount);

      console.log(
        `[RETRY] Attempting retry ${
          currentRetryCount + 1
        }/${maxRetries} for ${operation} in ${delay}ms`
      );

      await new Promise((resolve) => setTimeout(resolve, delay));

      try {
        return await retryFunction();
      } catch (error) {
        if (currentRetryCount < maxRetries - 1) {
          console.log(
            `[RETRY] Retry ${
              currentRetryCount + 1
            } failed for ${operation}, will try again`
          );
          return attemptRetryWithBackoff(
            retryFunction,
            currentRetryCount + 1,
            maxRetries,
            operation
          );
        } else {
          console.error(
            `[RETRY] All ${maxRetries} retries exhausted for ${operation}`
          );
          throw error;
        }
      }
    },
    []
  );

  // ====================== ENHANCED SUBMIT LOGIC (Whisper) ======================
  const handleSubmit = async () => {
    if (!audioURL) {
      toast.error("No audio recording available. Please record first.");
      return;
    }

    if (!userSubscription?.isActive && hearts <= 0) {
      toast.error("You have 0 hearts. Please refill or wait.");
      return;
    }

    setSubLoading(true);
    setSelectedCitation(null);
    setActiveFeedbackSection("overview");
    setLastApiError(null); // 🆕 Clear previous errors

    try {
      // Clone displayedWords before modifying it for API call to prevent stale closures
      const previousDisplayedWords = cloneDeep(displayedWords);

      // 🆕 Enhanced displayedWords validation before processing
      const displayedWordsValidation =
        validateDisplayedWordsStructure(displayedWords);
      if (!displayedWordsValidation.isValid) {
        console.error(
          "DisplayedWords validation failed:",
          displayedWordsValidation.errors
        );
        throw new Error("Internal state error. Please refresh and try again.");
      }

      if (displayedWordsValidation.warnings.length > 0) {
        console.warn(
          "DisplayedWords warnings:",
          displayedWordsValidation.warnings
        );
      }

      setDisplayedWords((current) =>
        produce(current, (draft) => {
          Object.keys(draft).forEach((verseNumStr) => {
            const verseNum = Number(verseNumStr);
            if (!memorizedVersesState.includes(verseNum)) {
              draft[verseNum] = draft[verseNum].map((wordObj: any) => ({
                ...wordObj,
                matched: false,
                almostMatched: false,
                highlighted: false,
                textColor: undefined,
                displayText:
                  wordObj.displayText !== "•••" ? wordObj.text : "•••",
              }));
            }
          });
        })
      );

      /* ------------------------------------------------------------------ */
      /*  🆕   ENHANCED WORD METADATA ARRAY CREATION WITH VALIDATION         */
      /* ------------------------------------------------------------------ */
      const wordMetadataArray: Array<{
        text: string;
        surah: number;
        ayah: number;
        wordInAyahIndex: number;
      }> = [];
      const versesToProcess = activeVerses.length > 0 ? activeVerses : verses;

      if (!versesToProcess || versesToProcess.length === 0) {
        throw new Error("No verses available for processing");
      }

      let expectedNonGivenWords = 0;
      versesToProcess.forEach((verse) => {
        if (
          !verse ||
          typeof verse.verseNumber !== "number" ||
          typeof verse.text !== "string"
        ) {
          console.warn(`Invalid verse structure:`, verse);
          return;
        }

        if (
          verse.verseNumber > 2 &&
          !memorizedVersesState.includes(verse.verseNumber)
        ) {
          const wordsInVerse = verse.text.split(" ");
          wordsInVerse.forEach((wordText, wordIdx) => {
            if (!wordText || wordText.trim() === "") {
              console.warn(
                `Empty word at verse ${verse.verseNumber}, index ${wordIdx}`
              );
              return;
            }

            // Check if this specific word in displayedWords is a target word
            const displayedWordObj =
              displayedWords[verse.verseNumber]?.[wordIdx];
            if (displayedWordObj && !displayedWordObj.given) {
              wordMetadataArray.push({
                text: wordText, // The correct word text
                surah: surahNumber, // Component prop
                ayah: verse.verseNumber,
                wordInAyahIndex: wordIdx + 1, // QuranCDN uses 1-based index for words
              });
              expectedNonGivenWords++;
            }
          });
        }
      });

      console.log(
        `[handleSubmit] Created wordMetadataArray with ${wordMetadataArray.length} words (expected: ${expectedNonGivenWords})`
      );

      if (wordMetadataArray.length === 0) {
        throw new Error(
          "No words available for processing. All verses may be memorized or given."
        );
      }

      /* ------------------------------------------------------------------ */
      /*  🆕   ENHANCED FORMDATA PREPARATION WITH VALIDATION                 */
      /* ------------------------------------------------------------------ */
      const prepareFormData = async (): Promise<FormData> => {
        try {
          const response = await fetch(audioURL);
          if (!response.ok) {
            throw new Error(
              `Failed to fetch audio: ${response.status} ${response.statusText}`
            );
          }

          const audioBlob = await response.blob();

          if (audioBlob.size === 0) {
            throw new Error("Audio file is empty");
          }

          if (audioBlob.type !== "audio/webm") {
            console.warn(
              `Unexpected audio type: ${audioBlob.type}, expected: audio/webm`
            );
            // Continue anyway as some browsers may report different MIME types
          }

          console.log(
            `[handleSubmit] Audio blob prepared: ${audioBlob.size} bytes, type: ${audioBlob.type}`
          );

          const formData = new FormData();
          formData.append("audio", audioBlob);
          formData.append("wordMetadata", JSON.stringify(wordMetadataArray));

          return formData;
        } catch (error) {
          console.error("[handleSubmit] Error preparing FormData:", error);
          throw new Error(
            `Failed to prepare audio data: ${
              error instanceof Error ? error.message : "Unknown error"
            }`
          );
        }
      };

      /* ------------------------------------------------------------------ */
      /*  🆕   ENHANCED API CALL WITH RETRY LOGIC                           */
      /* ------------------------------------------------------------------ */
      const makeApiCall = async (): Promise<any> => {
        const formData = await prepareFormData();

        const response = await fetch("/api/whisper", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response
            .json()
            .catch(() => ({ error: "Unknown API error" }));
          const errorMessage =
            errorData.error ||
            `API error: ${response.status} ${response.statusText}`;
          throw new Error(errorMessage);
        }

        return await response.json();
      };

      // 🆕 Execute API call with retry logic
      let data;
      try {
        if (retryCount > 0 && retryCount < maxRetries) {
          // Use retry logic if we've had previous failures
          data = await attemptRetryWithBackoff(
            makeApiCall,
            retryCount,
            maxRetries,
            "API submission"
          );
          setRetryCount(0); // Reset on success
        } else {
          // First attempt or max retries exceeded
          data = await makeApiCall();
        }
      } catch (error: any) {
        const errorMessage = error.message || "Failed to process audio";
        setLastApiError(errorMessage);

        if (retryCount < maxRetries - 1) {
          setRetryCount((prev) => prev + 1);
          toast.error(
            `${errorMessage}. Retry ${retryCount + 1}/${maxRetries} available.`
          );
        } else {
          toast.error(`${errorMessage}. All retries exhausted.`);
        }
        throw error;
      }

      /* ------------------------------------------------------------------ */
      /*  🆕   ENHANCED API RESPONSE VALIDATION                              */
      /* ------------------------------------------------------------------ */
      if (!data || typeof data !== "object") {
        throw new Error("Invalid API response: Response is not an object");
      }

      if (
        !data.text ||
        typeof data.text !== "string" ||
        data.text.trim() === ""
      ) {
        throw new Error(
          "Invalid API response: Missing or empty transcription text"
        );
      }

      // 🆕 Validate and process API word-level timestamps
      let apiWordTimestamps: ApiWordFeedback[] = [];
      let timestampAttachmentValidation: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: [],
      };

      if (data.words && Array.isArray(data.words)) {
        console.log(
          `[handleSubmit] API returned ${data.words.length} word tokens for ${wordMetadataArray.length} expected words`
        );

        // Enhanced API response validation
        const apiValidation = validateApiResponse(
          data,
          wordMetadataArray.length
        );

        if (!apiValidation.isValid) {
          console.error(
            "[handleSubmit] API response validation failed:",
            apiValidation.errors
          );
          // Continue processing but log the issues
          toast.warning(
            "Audio timing data has some issues but processing will continue"
          );
        }

        if (apiValidation.warnings.length > 0) {
          console.warn(
            "[handleSubmit] API response warnings:",
            apiValidation.warnings
          );
        }

        setTranscribedTokens(data.words as ApiWordFeedback[]);
        apiWordTimestamps = data.words as ApiWordFeedback[];

        // 🆕 IMPORTANT: This is where correctWordInfo (with audioUrl) is attached
        // to the displayedWords structure. This logic is already correct.
        const { updatedWords, validation } = attachTimestampsToDisplayedWords(
          data.words as ApiWordFeedback[],
          previousDisplayedWords
        );

        timestampAttachmentValidation = validation;

        if (!validation.isValid) {
          console.error(
            "[handleSubmit] Timestamp attachment failed:",
            validation.errors
          );
          toast.warning("Audio timing data could not be attached properly");
        } else {
          // Correctly update the cloned `previousDisplayedWords` with the attached metadata
          // This ensures the comparison function gets words with correctWordInfo
          Object.assign(previousDisplayedWords, updatedWords);
          console.log(
            "[handleSubmit] Audio metadata successfully attached to displayedWords"
          );
        }

        if (validation.warnings.length > 0) {
          console.warn(
            "[handleSubmit] Timestamp attachment warnings:",
            validation.warnings
          );
        }
      } else {
        console.warn(
          "[handleSubmit] API response missing or invalid words array"
        );
        setTranscribedTokens([]);
        apiWordTimestamps = [];
      }

      setResponseText(data.text);

      console.log(
        "[handleSubmit] Calling handleRecitationComparison with enhanced validation"
      );

      const comparisonResult = await handleRecitationComparison(
        data.text,
        correctWordsList,
        previousDisplayedWords, // Pass the words that now include correctWordInfo
        activeVerses.length > 0 ? activeVerses : verses,
        sessionId,
        difficulty,
        apiWordTimestamps
      );

      if (!comparisonResult) {
        throw new Error("Recitation comparison failed to return results");
      }

      const {
        updatedDisplayedWords,
        score: newScore,
        feedbackText: newFeedbackText,
        feedbackRefs: newFeedbackRefs,
        combinedFeedback: newCombinedFeedback,
        feedbackData: newFeedbackData,
        citationMap: newCitationMap,
      } = comparisonResult;

      // 🆕 Enhanced validation of comparison results
      const updatedWordsValidation = validateDisplayedWordsStructure(
        updatedDisplayedWords
      );
      if (!updatedWordsValidation.isValid) {
        console.error(
          "Updated displayedWords validation failed:",
          updatedWordsValidation.errors
        );
        throw new Error(
          "Comparison processing resulted in invalid word structure"
        );
      }

      const changedVerses = new Set<number>();
      Object.entries(updatedDisplayedWords).forEach(
        ([verseNumStr, verseWords]) => {
          const verseNumber = Number(verseNumStr);
          const previousVerseWords = previousDisplayedWords[verseNumber];
          if (
            JSON.stringify(verseWords) !== JSON.stringify(previousVerseWords)
          ) {
            changedVerses.add(verseNumber);
          }
        }
      );

      const newlyMemorizedVerses = Object.entries(updatedDisplayedWords)
        .filter(([verseNumStr, verseWords]) => {
          const verseNumber = Number(verseNumStr);
          return (
            changedVerses.has(verseNumber) &&
            verseWords.every((word: any) => word.matched || word.given) &&
            verseNumber >= 3 &&
            !memorizedVersesState.includes(verseNumber)
          );
        })
        .map(([verseNumStr]) => Number(verseNumStr));

      const finalDisplayedWords = produce(previousDisplayedWords, (draft) => {
        Object.entries(updatedDisplayedWords).forEach(
          ([verseNumStr, verseWords]) => {
            const verseNumber = Number(verseNumStr);
            // Ensure correctWordInfo is preserved if it was already set from API
            if (draft[verseNumber]) {
              draft[verseNumber] = verseWords.map(
                (currentWord: any, idx: number) => {
                  const prevWord = draft[verseNumber][idx];
                  // Merge new comparison results with existing word properties,
                  // specifically preserving correctWordInfo if it exists
                  return {
                    ...prevWord, // Start with previous word state (includes correctWordInfo if present)
                    ...currentWord, // Apply new comparison results (matched, almostMatched, displayText, etc.)
                    // Explicitly ensure correctWordInfo from API is preserved if currentWord doesn't override it
                    correctWordInfo:
                      prevWord.correctWordInfo || currentWord.correctWordInfo,
                    // Override textColor for memorized/matched verses
                    textColor:
                      newlyMemorizedVerses.includes(verseNumber) &&
                      verseNumber > 2
                        ? "text-emerald-500"
                        : currentWord.textColor || prevWord.textColor,
                    // Ensure displayText is correct for newly matched words in memorized verses
                    displayText:
                      newlyMemorizedVerses.includes(verseNumber) &&
                      verseNumber > 2
                        ? currentWord.text // Reveal text if verse is memorized
                        : currentWord.displayText, // Otherwise use comparison result (e.g., "•••" or actual text)
                  };
                }
              );
            }
          }
        );
      });

      setDisplayedWords(finalDisplayedWords);
      previousDisplayedWordsRef.current = finalDisplayedWords;
      setScore(newScore);
      setFeedbackText(newFeedbackText);
      setFeedbackRefs(newFeedbackRefs);
      setCombinedFeedback(newCombinedFeedback || newFeedbackText);
      setFeedbackData(newFeedbackData || null);
      setCitationMap(() => newCitationMap || {});

      const newUnmatchedWords: string[] = [];
      Object.entries(finalDisplayedWords)
        .filter(
          ([verseNumStr]) => !memorizedVersesState.includes(Number(verseNumStr))
        )
        .forEach(([_, verseWords]) => {
          verseWords.forEach((wordObj: any) => {
            if (!wordObj.matched && !wordObj.almostMatched && !wordObj.given) {
              newUnmatchedWords.push(wordObj.text);
            }
          });
        });

      setUnmatchedWords(newUnmatchedWords);

      if (newlyMemorizedVerses.length > 0) {
        await storeMemorizedVerses(newlyMemorizedVerses, surahNumber);
        setMemorizedVersesState((prev) => [...prev, ...newlyMemorizedVerses]);
        await awardOneHeart();
      }

      if (newUnmatchedWords.length > 0) {
        await deductOneHeart();
      } else {
        await awardOneHeart();
      }

      // 🆕 Show validation warnings if any occurred during processing
      if (timestampAttachmentValidation.warnings.length > 0) {
        console.info(
          "Audio processing completed with some timing issues. Functionality remains normal."
        );
      }
    } catch (error: any) {
      console.error("Error in enhanced handleSubmit:", error);
      setResponseText("");

      // 🆕 Enhanced error handling with retry suggestion
      const errorMessage = error.message || "Unknown error occurred";

      if (retryCount < maxRetries - 1) {
        toast.error(`${errorMessage} (Retry ${retryCount + 1}/${maxRetries})`);
      } else {
        toast.error(`${errorMessage} (All retries exhausted)`);
      }
    } finally {
      setSubLoading(false);
    }
  };

  // ====================== RECITATION COMPARISON ======================
  const handleRecitationComparison = useCallback(
    async (
      transcribedText: string,
      correctWordsListParam: string[],
      currentDisplayedWordsParam: Record<number, any[]>,
      versesDataParam: Verse[],
      sessionIdParam: number,
      difficultyParam: Difficulty,
      apiTokens: ApiWordFeedback[]
    ) => {
      try {
        if (
          typeof transcribedText !== "string" ||
          transcribedText.trim() === ""
        ) {
          console.error("Invalid transcribedText:", transcribedText);
          throw new Error("Invalid transcription text received");
        }

        // 🆕 Enhanced validation of input parameters
        if (
          !Array.isArray(correctWordsListParam) ||
          correctWordsListParam.length === 0
        ) {
          console.error(
            "Invalid correctWordsListParam:",
            correctWordsListParam
          );
          throw new Error("No correct words available for comparison");
        }

        if (
          !currentDisplayedWordsParam ||
          typeof currentDisplayedWordsParam !== "object"
        ) {
          console.error(
            "Invalid currentDisplayedWordsParam:",
            currentDisplayedWordsParam
          );
          throw new Error("Invalid displayed words structure");
        }

        if (!Array.isArray(versesDataParam) || versesDataParam.length === 0) {
          console.error("Invalid versesDataParam:", versesDataParam);
          throw new Error("No verse data available for comparison");
        }

        if (!Array.isArray(apiTokens)) {
          console.warn("API tokens is not an array, using empty array");
          apiTokens = [];
        }

        const wordVerseNumbers = Object.entries(currentDisplayedWordsParam)
          .filter(
            ([verseNumber]) =>
              Number(verseNumber) >= 3 &&
              !memorizedVersesState.includes(Number(verseNumber))
          )
          .flatMap(([verseNumber, verseWords]) =>
            Array(verseWords.length).fill(Number(verseNumber))
          );

        console.log(
          `[handleRecitationComparison] Processing ${transcribedText.length} chars of text against ${correctWordsListParam.length} words`
        );

        // 🆕 Enhanced data structure preparation with validation
        const { transcribedWords, correctWords, userTimings } =
          prepareDataStructures(
            transcribedText,
            correctWordsListParam,
            memorizedVersesState,
            wordVerseNumbers,
            apiTokens
          );

        console.log(
          `[handleRecitationComparison] Prepared ${transcribedWords.length} transcribed words, ${correctWords.length} correct words`
        );

        const matchedCorrectWords = initializeMatchedWords(
          currentDisplayedWordsParam,
          memorizedVersesState,
          wordVerseNumbers
        );

        // 🆕 Enhanced length mismatch handling
        let finalCorrectWords = correctWords;
        let finalCorrectWordsList = correctWordsListParam;
        let finalWordVerseNumbers = wordVerseNumbers;
        let finalUserTimings = userTimings;

        if (correctWords.length !== correctWordsListParam.length) {
          console.warn(
            `[handleRecitationComparison] Length mismatch: correctWords=${correctWords.length}, correctWordsList=${correctWordsListParam.length}`
          );

          const minLength = Math.min(
            correctWords.length,
            correctWordsListParam.length
          );
          finalCorrectWords = correctWords.slice(0, minLength);
          finalCorrectWordsList = correctWordsListParam.slice(0, minLength);
          finalWordVerseNumbers = wordVerseNumbers.slice(0, minLength);
          finalUserTimings = userTimings.slice(0, transcribedWords.length);

          console.log(
            `[handleRecitationComparison] Arrays adjusted to length ${minLength}`
          );
        }

        // 🆕 Enhanced word comparison with better error handling
        const { wordAlignment, feedbackPairs } = performWordComparisonAligned(
          transcribedWords,
          finalCorrectWords,
          finalCorrectWordsList,
          memorizedVersesState,
          finalWordVerseNumbers,
          matchedCorrectWords,
          difficultyParam,
          finalUserTimings,
          apiTokens // ✅ Make sure this is passed, not an empty array
        );

        console.log(
          `[handleRecitationComparison] Generated ${feedbackPairs.length} feedback pairs with timing data`
        );

        const { finalUnmatchedCorrectWords, updatedDisplayedWords } =
          finalizeAlignment(
            finalCorrectWordsList,
            wordAlignment,
            currentDisplayedWordsParam,
            0.7,
            memorizedVersesState,
            finalWordVerseNumbers,
            difficultyParam
          );

        const completionRate = calculateCompletionRate(
          updatedDisplayedWords,
          memorizedVersesState
        );

        const feedbackRefsTemp: {
          [key: string]: React.RefObject<HTMLDivElement>;
        } = {};

        const missingPairs = feedbackPairs.filter((p) => p.type === "missing");

        // 2) Everything else stays for your normal per-word cards
        const otherPairs = feedbackPairs.filter((p) => p.type !== "missing");
        // ==================================================================
        // END OF MODIFICATION for feedbackPairs split
        // ==================================================================

        // 🆕 Enhanced feedback generation with validation
        const {
          combinedFeedback,
          feedbackData,
          citationMap: localCitationMap,
        } = generateFeedback(
          wordAlignment,
          finalUnmatchedCorrectWords, // still feeds *all* missing-word info
          otherPairs, // <<< MODIFIED: was feedbackPairs before
          updatedDisplayedWords,
          versesDataParam,
          sessionIdParam,
          memorizedVersesState,
          finalWordVerseNumbers,
          feedbackRefsTemp,
          difficultyParam,
          completionRate,
          playSegment
        );

        if (!Array.isArray(combinedFeedback)) {
          console.warn(
            "[handleRecitationComparison] combinedFeedback is not an array"
          );
        }

        if (!localCitationMap || typeof localCitationMap !== "object") {
          console.warn(
            "[handleRecitationComparison] localCitationMap is invalid"
          );
        }

        const finalUpdatedDisplayedWords = produce(
          updatedDisplayedWords,
          (draft) => {
            Object.entries(draft).forEach(([verseNumStr, verseWords]) => {
              verseWords.forEach((word, wordIndex) => {
                if (
                  word.almostMatched ||
                  word.letterCountMismatch ||
                  !word.matched
                ) {
                  const uniqueKey = CitationSystem.generateUniqueKey({
                    text: word.text,
                    verseNumber: parseInt(verseNumStr),
                    wordIndex,
                  });
                  if (localCitationMap && localCitationMap[uniqueKey]) {
                    word.feedbackNumber = localCitationMap[uniqueKey];
                  }
                }
              });
            });
          }
        );

        console.log(
          `[handleRecitationComparison] Successfully completed comparison with ${completionRate.toFixed(
            1
          )}% completion rate`
        );

        return {
          updatedDisplayedWords: finalUpdatedDisplayedWords,
          score: completionRate,
          feedbackText: combinedFeedback,
          feedbackRefs: feedbackRefsTemp,
          combinedFeedback,
          feedbackData,
          citationMap: localCitationMap,
        };
      } catch (error) {
        console.error(
          "[handleRecitationComparison] Error in recitation comparison:",
          error
        );

        // Return safe fallback values
        return {
          updatedDisplayedWords: currentDisplayedWordsParam,
          score: initialPercentage,
          feedbackText: [],
          feedbackRefs: {},
          combinedFeedback: [],
          feedbackData: null,
          citationMap: {},
        };
      }
    },
    [memorizedVersesState, initialPercentage, playSegment]
  );

  const validateUpdatedDisplayedWords = useCallback((data: any) => {
    const validation = validateDisplayedWordsStructure(data);
    if (!validation.isValid) {
      console.error("validateUpdatedDisplayedWords failed:", validation.errors);
    }
    return validation.isValid;
  }, []);

  const handleDifficultyChange = useCallback(
    (newDifficulty: Difficulty) => {
      if (newDifficulty === difficulty) return;
      setDifficulty(newDifficulty);
      setFeedbackText([]);
      setCombinedFeedback([]);
      setFeedbackData(null);
      setCitationMap({});
      setFeedbackRefs({});
      setSelectedCitation(null);
      setActiveFeedbackSection("overview");
      setLastApiError(null); // 🆕 Clear errors on difficulty change
      setRetryCount(0); // 🆕 Reset retry count
      resetFinishSound();

      // If Surah is below threshold, we simply re-init all verses
      // If above threshold, user must reselect the range
      if (totalAyahs <= DYNAMIC_FETCH_THRESHOLD) {
        initializeDisplayedWords(verses);
      } else {
        handleClearRangeSelection();
      }

      setScore(initialPercentage);
    },
    [
      difficulty,
      initializeDisplayedWords,
      initialPercentage,
      resetFinishSound,
      setScore,
      totalAyahs,
      verses,
      handleClearRangeSelection,
    ]
  );

  useEffect(() => {
    if (feedbackText && feedbackText.length > 0) {
      const correctSound = new Audio("/correct.wav");
      correctSound.play().catch((error) => {
        console.error("Error playing correct sound:", error);
      });
    }
  }, [feedbackText]);

  useEffect(() => {
    const started = Object.values(displayedWords).some((verseWords) =>
      verseWords.some(
        (word) =>
          (word.displayText !== "•••" &&
            word.displayText !== "..." &&
            !word.given) ||
          word.matched ||
          word.almostMatched
      )
    );
    if (started !== hasExerciseStarted) {
      setHasExerciseStarted(started);
    }
  }, [displayedWords, hasExerciseStarted]);

  useEffect(() => {
    const hasStartedExercise = Object.values(displayedWords).some(
      (verseWords) =>
        verseWords.some(
          (word) =>
            word.displayText !== "•••" &&
            word.displayText !== "..." &&
            !word.given
        )
    );
    if (!hasStartedExercise) {
      if (exerciseCompleted) {
        setExerciseCompleted(false);
      }
      return;
    }

    const completionRate = calculateCompletionRate(
      displayedWords,
      memorizedVersesState
    );
    const isCompleted = completionRate >= 95;
    if (isCompleted !== exerciseCompleted) {
      setExerciseCompleted(isCompleted);
    }
  }, [displayedWords, memorizedVersesState, exerciseCompleted]);

  useEffect(() => {
    if (feedbackText && feedbackText.length > 0) {
      const allCitationNumbers = extractAllCitationNumbers(displayedWords);
      if (allCitationNumbers.length > 0) {
        console.log(
          `Immediate prefetching for ${allCitationNumbers.length} citations`
        );
        prefetchCitationSummaries(
          feedbackText as JSX.Element[],
          allCitationNumbers
        ).catch((err) =>
          console.error("Error prefetching citation summaries:", err)
        );

        const commonPhrases = [
          "This word needs correction",
          "You pronounced this incorrectly",
          "Missing word",
          "Extra word",
          "Let me help you with this",
          "Focus on this letter",
        ];
        batchPrefetchTTS(commonPhrases).catch((err) =>
          console.error("Error prefetching common phrases:", err)
        );
      }
    }
  }, [feedbackText, displayedWords]);

  useEffect(() => {
    // ✅ FIX 4: Capture ref.current in a variable to use in the cleanup function
    const analysisRef = audioAnalysisRef.current;
    return () => {
      if (analysisRef.animationFrameId) {
        cancelAnimationFrame(analysisRef.animationFrameId);
      }
      if (analysisRef.audioContext) {
        try {
          analysisRef.audioContext.close();
        } catch (err) {
          console.error("Error closing audio context:", err);
        }
      }
    };
  }, []);

  const surah = {
    ...surahData,
    id: surahData.id ?? surahData.number,
  };

  if (loading) {
    return <LoadingPage />;
  }

  // ====================== CONDITIONAL UI ======================
  return (
    <div
      className="
      flex flex-col
      h-screen
      overflow-hidden
      bg-white
      text-sm
    "
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`, // Keep your SVG background
        backgroundRepeat: "repeat",
      }}
    >
      {/* HEADER - MODIFIED FOR LARGER SIZE */}
      <header className="flex-none sticky top-0 z-10 bg-white border-b border-gray-200">
        {" "}
        {/* Removed h-14 md:h-16 and px-4 */}
        {/* Main flex container for the header content */}
        <div className="px-6 py-4 flex items-center justify-between gap-4">
          {" "}
          {/* Added px-6 py-4, removed h-full */}
          {/* Left Part: Back Button, Divider, Title Block */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push("/surahGrid")}
              className="p-1.5 md:p-2 rounded-full hover:bg-gray-100 transition-all duration-200 group"
            >
              <ArrowLeft className="h-4 w-4 md:h-5 md:w-5 text-gray-600 group-hover:text-gray-900 transition-colors duration-200" />
            </button>

            {/* Vertical Divider */}
            <div className="h-6 w-px bg-gray-300"></div>

            {/* Title Block */}
            <div>
              <h1 className="text-lg font-bold text-gray-900">
                {" "}
                {/* Kept text-lg as per your code */}
                Hifz
              </h1>
              <p className="text-sm text-gray-500 mt-0.5">
                Practice on your memorization
              </p>
            </div>
          </div>
          {/* Right Part: Controls (Difficulty, Score, Hearts, Error) */}
          <div className="flex items-center gap-3 md:gap-4 mr-12">
            {" "}
            {/* mr-12 is kept */}
            {/* Difficulty toggles */}
            <div className="bg-white rounded-full border border-gray-200 p-2 flex">
              <button
                onClick={() => handleDifficultyChange("easy")}
                className={`
                px-4 py-2 rounded-full text-sm font-medium transition-all duration-200
                ${
                  difficulty === "easy"
                    ? "bg-black text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                }
              `}
              >
                Easy
              </button>
              <button
                onClick={() => handleDifficultyChange("hard")}
                className={`
                px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
                ${
                  difficulty === "hard"
                    ? "bg-black text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                }
              `}
              >
                Hard
              </button>
            </div>
            {/* Score Display */}
            <div className="flex items-center gap-3 px-3 md:px-4 py-1.5 rounded-full bg-white border border-gray-200">
              <div className="w-24 h-2.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-emerald-400 transition-all duration-300"
                  style={{
                    width: `${Math.round(score)}%`,
                  }}
                ></div>
              </div>
              <p className="text-sm text-black font-medium w-9 text-right tabular-nums">
                {`${Math.round(score)}%`}
              </p>
            </div>
            {/* Hearts */}
            <div className="text-rose-500 flex items-center font-bold">
              <svg
                viewBox="0 0 128 128"
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-7 md:w-7 mr-1 md:mr-2"
                aria-hidden="true"
              >
                <g>
                  <polygon
                    fill="#FF5050"
                    points="114,34 114,24 104,24 104,14 74,14 74,24 64,24 54,24 54,14 24,14 24,24 14,24 14,34 4,34 4,64 14,64 14,74 24,74 24,84 34,84 34,94 44,94 44,104 54,104 54,114 64,114 124,54 124,34"
                  />
                </g>
                <g>
                  <rect fill="#FFFFFF" height="10" width="10" x="24" y="34" />
                </g>
                <g>
                  <g>
                    <rect
                      fill="#9B0270"
                      height="10"
                      width="10"
                      x="64"
                      y="104"
                    />
                  </g>
                  <g>
                    <rect fill="#9B0270" height="10" width="10" x="74" y="94" />
                  </g>
                  <g>
                    <rect fill="#9B0270" height="10" width="10" x="84" y="84" />
                  </g>
                  <g>
                    <rect fill="#9B0270" height="10" width="10" x="94" y="74" />
                  </g>
                  <g>
                    <rect
                      fill="#9B0270"
                      height="10"
                      width="10"
                      x="104"
                      y="64"
                    />
                  </g>
                  <g>
                    <rect
                      fill="#9B0270"
                      height="10"
                      width="10"
                      x="114"
                      y="54"
                    />
                  </g>
                </g>
              </svg>
              <span className="text-lg md:text-lg">{hearts}</span>
            </div>
            {/* Enhanced Error Display and Retry Button */}
            {lastApiError && retryCount < maxRetries && (
              <div className="flex items-center gap-2">
                <span className="text-base text-red-600">Error</span>
                <button
                  onClick={handleSubmit}
                  disabled={subLoading}
                  className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
                >
                  Retry
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* MAIN LAYOUT */}
      <main className="flex-1 grid grid-cols-1 lg:grid-cols-12 gap-4 px-4 py-6 overflow-hidden">
        {/* Left (SurahDisplay with range selection props passed in) */}
        <div className="lg:col-span-5 bg-white rounded-xl border border-gray-200 flex flex-col min-h-0 transition-shadow duration-300 shadow-sm">
          <div className="flex-1 overflow-auto min-h-0">
            <SurahDisplay
              surah={surah}
              displayedWords={displayedWords}
              feedbackRefs={feedbackRefs}
              difficulty={difficulty}
              onUnmatchedWordClick={handleUnmatchedWordClick}
              isExerciseCompleted={hasExerciseStarted && exerciseCompleted}
              score={score}
              onCitationClick={handleCitationClick}
              selectedCitation={selectedCitation}
              totalAyahs={totalAyahs}
              selectedRange={selectedRange}
              availableRanges={availableRanges}
              onRangeSelectionRequest={handleRangeSelectionRequest}
              onClearRangeSelection={handleClearRangeSelection}
              // >>> PASS NEW NAVIGATION PROPS DOWN <<<
              previousSurahNumber={previousSurahNumber}
              nextSurahNumber={nextSurahNumber}
              onNavigateSurah={handleNavigateSurah}
            />
          </div>
        </div>

        {/* Right (Sections) */}
        <div className="lg:col-span-7 bg-white rounded-xl border border-gray-200 grid grid-rows-[auto,1fr,auto] min-h-0 transition-shadow duration-300 shadow-sm">
          {/* Top Nav for sections */}
          <nav className="p-4 border-b border-gray-100 bg-white rounded-t-xl">
            <div className="flex gap-3 h-full">
              {/* *** MODIFIED THIS LINE: "Feedback" now comes before "Assistant" *** */}
              {["Feedback", "Assistant", "Transcription"].map((option) => (
                <button
                  key={option}
                  onClick={() => setSelectedOption(option)}
                  className={`
          relative flex-1 rounded-2xl font-medium transition-all duration-200 px-2 py-3 text-base ${
            selectedOption === option
              ? "bg-black text-white"
              : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-black"
          }
        `}
                >
                  {option === "Feedback" ? (
                    <span className="relative inline-flex items-center">
                      Feedback
                      {feedbackCount > 0 && (
                        <span className="absolute -top-2 -right-6 inline-flex items-center justify-center rounded-full bg-rose-500 text-white text-base w-5 h-5">
                          {feedbackCount}
                        </span>
                      )}
                    </span>
                  ) : (
                    option
                  )}
                </button>
              ))}
            </div>
          </nav>

          {/* Middle Section (Content rendering logic based on selectedOption remains the same) */}
          <div className="p-4 flex flex-col overflow-auto min-h-0">
            {/*
              THIS IS THE ONLY CHANGE.
              By adding `flex flex-col` here, this container will now correctly
              instruct its children (like AssistantSection) to use the full
              available vertical space, allowing them to center themselves.
            */}
            <div className="flex-1 flex flex-col">
              {selectedOption === "Feedback" ? (
                <FeedbackSection
                  feedback={combinedFeedback}
                  feedbackData={feedbackData}
                  citationMap={citationMap}
                  verses={activeVerses.length > 0 ? activeVerses : verses}
                  displayedWords={displayedWords}
                  feedbackRefs={feedbackRefs}
                  difficulty={difficulty}
                  score={score}
                  onCitationClick={handleCitationClick}
                  selectedCitation={selectedCitation}
                  activeFeedbackSection={
                    activeFeedbackSection === "assistant"
                      ? "overview"
                      : activeFeedbackSection
                  }
                  onFeedbackSectionChange={handleFeedbackSectionChange}
                  loading={subLoading}
                  onFeedbackCountChange={(count) => setFeedbackCount(count)}
                  audioURL={audioURL}
                />
              ) : selectedOption === "Assistant" ? (
                <AssistantSection
                  status={connectionStatus}
                  inputAmplitude={inputAmplitude}
                />
              ) : selectedOption === "Transcription" ? (
                <TranscriptionSection
                  responseText={responseText}
                  score={score}
                  onSubmit={handleSubmit}
                  loading={subLoading}
                  isRecording={isRecording}
                  audioURL={audioURL}
                />
              ) : null}
            </div>

            {/* Enhanced Error Display Section */}
            {lastApiError && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <svg
                    className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-red-800 font-medium">
                      Processing Error
                    </p>
                    <p className="text-xs text-red-700 mt-1 break-words">
                      {lastApiError}
                    </p>
                    {retryCount < maxRetries && (
                      <div className="mt-2 flex gap-2">
                        <button
                          onClick={handleSubmit}
                          disabled={subLoading}
                          className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 disabled:opacity-50"
                        >
                          {subLoading
                            ? "Retrying..."
                            : `Retry (${retryCount + 1}/${maxRetries})`}
                        </button>
                        <button
                          onClick={() => {
                            setLastApiError(null);
                            setRetryCount(0);
                          }}
                          className="text-xs text-red-600 hover:text-red-800"
                        >
                          Dismiss
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Bottom: MediaPlayer */}
          <div className="border-t border-gray-100 bg-white h-16 p-1 rounded-b-xl">
            <MediaPlayer
              onRecordingComplete={setAudioURL}
              onRecordingStart={() => setIsRecording(true)}
              onAmplitudeUpdate={handleAmplitudeUpdate}
              onSubmit={handleSubmit}
              loading={subLoading}
              displayedWords={displayedWords}
              setDisplayedWords={setDisplayedWords}
              verses={activeVerses.length > 0 ? activeVerses : verses}
              ttsAmplitude={ttsAmplitude}
              frequencyBands={frequencyBands}
              isTTSPlaying={isTTSPlaying}
            />
          </div>
        </div>
      </main>

      <footer className="flex-none h-4 bg-white text-xs"></footer>
    </div>
  );
};

export default Memorization;
