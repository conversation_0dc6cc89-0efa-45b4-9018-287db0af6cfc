"use client";

import React, {
  useState,
  useRef,
  useEffect,
  Dispatch,
  SetStateAction,
  useCallback,
} from "react";
import {
  Mic,
  Square,
  Play,
  Pause,
  ArrowUp,
  RefreshCw,
  AlertCircle,
  StopCircle,
  Volume2,
  Loader2,
  Clock,
} from "lucide-react";

// Moved outside component to make them stable constants, fixing exhaustive-deps warnings.
const VISUALIZER_BASE_HEIGHTS = [65, 85, 100, 65];
const VISUALIZER_PILL_ANIMATION_SPEEDS = [1, 1.2, 0.9, 1.3];

// AudioWaveVisualizer component with full implementation
interface AudioWaveVisualizerProps {
  isPlaying?: boolean;
  amplitude?: number;
  frequencyBands?: number[] | null;
  color?: string;
  backgroundColor?: string;
  containerWidth?: number;
  containerHeight?: number;
  pillWidth?: number;
  spacing?: number;
  className?: string;
  freshStart?: boolean;
  punchiness?: number;
  startAsCircles?: boolean;
  transitionDuration?: number;
}

export const AudioWaveVisualizer: React.FC<AudioWaveVisualizerProps> = ({
  isPlaying,
  amplitude = 0,
  frequencyBands = null,
  color = "#000000",
  backgroundColor = "transparent",
  containerWidth = 120,
  containerHeight = 40,
  pillWidth = 16,
  spacing = 6,
  className = "",
  freshStart = false,
  punchiness = 5,
  startAsCircles = true,
  transitionDuration = 700,
}) => {
  const pillRefs = useRef<(HTMLDivElement | null)[]>([null, null, null, null]);
  const lastAmplitudeRef = useRef<number>(0);
  const animationStartTimeRef = useRef<number | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const initialAnimationStartedRef = useRef<boolean>(false);

  const [visualizationMode, setVisualizationMode] = useState<
    "circles" | "transitioning" | "pills"
  >(startAsCircles ? "circles" : "pills");

  const [pillAnimationPhases, setPillAnimationPhases] = useState<number[]>([
    0, 0.25, 0.5, 0.75,
  ]);

  const transformDuration = freshStart ? 250 : 400;

  useEffect(() => {
    if (visualizationMode === "circles" && (isPlaying || amplitude > 0)) {
      setVisualizationMode("transitioning");
      const timer = setTimeout(() => {
        setVisualizationMode("pills");
      }, transitionDuration);
      return () => clearTimeout(timer);
    }
    if (
      visualizationMode === "pills" &&
      !isPlaying &&
      amplitude === 0 &&
      !initialAnimationStartedRef.current
    ) {
      setVisualizationMode("circles");
    }
  }, [isPlaying, amplitude, visualizationMode, transitionDuration]);

  useEffect(() => {
    const advancePhases = () => {
      setPillAnimationPhases((prevPhases) =>
        prevPhases.map((phase, index) => {
          const speed = VISUALIZER_PILL_ANIMATION_SPEEDS[index] * 0.02;
          return (phase + speed) % 1;
        })
      );
      const timer = setTimeout(advancePhases, 50);
      return () => clearTimeout(timer);
    };
    const timer = setTimeout(advancePhases, 50);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const animatePills = () => {
      const silenceThreshold = 5;
      const playing =
        typeof amplitude === "number"
          ? amplitude > silenceThreshold
          : isPlaying ?? false;

      if (playing && !initialAnimationStartedRef.current) {
        initialAnimationStartedRef.current = true;
      }

      const now = Date.now();
      const amplitudeChanged =
        Math.abs(amplitude - lastAmplitudeRef.current) > 3;
      if (amplitudeChanged && amplitude > silenceThreshold) {
        animationStartTimeRef.current = now;
        lastAmplitudeRef.current = amplitude;
      }

      const elapsed = animationStartTimeRef.current
        ? now - animationStartTimeRef.current
        : 0;

      const isTransitioning = elapsed < transformDuration;
      const transitionProgress = Math.min(elapsed / transformDuration, 1);
      const easeOutQuad = (t: number) => t * (2 - t);
      const easedProgress = easeOutQuad(transitionProgress);

      const getAmplitudeForPill = (index: number): number => {
        if (frequencyBands && frequencyBands.length >= 4) {
          return frequencyBands[index];
        }
        return amplitude;
      };

      pillRefs.current.forEach((pill, index) => {
        if (!pill) return;

        const baseSize = pillWidth;
        const targetHeight =
          containerHeight * (VISUALIZER_BASE_HEIGHTS[index] / 100);
        const isCircleMode = visualizationMode === "circles";
        const isTransitioningMode = visualizationMode === "transitioning";

        let shapeProgress = isCircleMode
          ? 0
          : isTransitioningMode
          ? (now % transformDuration) / transformDuration
          : 1;

        const borderRadius = isCircleMode
          ? "50%"
          : isTransitioningMode
          ? `${Math.max(pillWidth / 2, (1 - shapeProgress) * 50)}%`
          : `${pillWidth / 2}px`;

        pill.style.width = `${pillWidth}px`;
        pill.style.backgroundColor = color;
        pill.style.borderRadius = borderRadius;

        const heightTransitionSpeed = isTransitioningMode
          ? `height ${transformDuration}ms cubic-bezier(0.34, 1.56, 0.64, 1), border-radius ${transformDuration}ms cubic-bezier(0.34, 1.56, 0.64, 1)`
          : `height ${transformDuration / 2}ms ease-out, transform 150ms ease`;
        pill.style.transition = heightTransitionSpeed;

        if (playing) {
          const pillAmplitude = getAmplitudeForPill(index);
          if (
            typeof pillAmplitude === "number" &&
            pillAmplitude > silenceThreshold
          ) {
            const normalizedAmplitude = Math.min(pillAmplitude / 30, 1);
            const pillFactor = index === 0 || index === 3 ? 0.8 : 1.2;
            const phaseOffset =
              0.5 + 0.5 * Math.sin(2 * Math.PI * pillAnimationPhases[index]);

            let currentHeight = baseSize;
            if (isCircleMode) {
              currentHeight = baseSize;
            } else if (isTransitioningMode) {
              const transitionHeight =
                baseSize + (targetHeight - baseSize) * shapeProgress;
              const amplitudeBoost =
                normalizedAmplitude *
                targetHeight *
                pillFactor *
                phaseOffset *
                shapeProgress;
              currentHeight = transitionHeight + amplitudeBoost;
            } else {
              const heightBoost =
                normalizedAmplitude * targetHeight * pillFactor * phaseOffset;
              currentHeight = baseSize + heightBoost;
            }

            const scaleFactor = 1 + normalizedAmplitude * 0.3 * phaseOffset;
            pill.style.height = `${currentHeight}px`;
            pill.style.transform = !isCircleMode
              ? `scaleY(${scaleFactor})`
              : "scale(1)";
          } else if (isPlaying) {
            if (isCircleMode) {
              pill.style.height = `${baseSize}px`;
              pill.style.transform = "scale(1)";
            } else if (isTransitioningMode) {
              const transitionHeight =
                baseSize + (targetHeight - baseSize) * shapeProgress;
              pill.style.height = `${transitionHeight}px`;
              pill.style.transform = "scaleY(1)";
            } else {
              const phaseOffset =
                0.5 + 0.5 * Math.sin(2 * Math.PI * pillAnimationPhases[index]);
              const pulseFactor = 0.05 * phaseOffset;
              pill.style.height = `${targetHeight}px`;
              pill.style.transform = `scaleY(${1 + pulseFactor})`;
            }
          } else {
            if (isCircleMode) {
              pill.style.height = `${baseSize}px`;
              pill.style.transform = "scale(1)";
            } else if (isTransitioningMode) {
              const reverseProgress = 1 - shapeProgress;
              const currentHeight =
                baseSize + (targetHeight - baseSize) * reverseProgress;
              pill.style.height = `${currentHeight}px`;
              pill.style.transform = "scaleY(1)";
            } else {
              pill.style.height = `${baseSize}px`;
              pill.style.transform = "scaleY(1)";
            }
          }
        } else {
          if (isCircleMode) {
            pill.style.height = `${baseSize}px`;
            pill.style.transform = "scale(1)";
          } else if (isTransitioningMode) {
            const reverseProgress = 1 - shapeProgress;
            const currentHeight =
              baseSize + (targetHeight - baseSize) * reverseProgress;
            pill.style.height = `${currentHeight}px`;
            pill.style.transform = "scaleY(1)";
          } else {
            pill.style.height = `${baseSize}px`;
            pill.style.transform = "scaleY(1)";
          }
        }
      });

      animationFrameRef.current = requestAnimationFrame(animatePills);
    };
    animationFrameRef.current = requestAnimationFrame(animatePills);
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [
    isPlaying,
    amplitude,
    frequencyBands,
    freshStart,
    containerHeight,
    pillWidth,
    transformDuration,
    punchiness,
    visualizationMode,
    transitionDuration,
    color,
    pillAnimationPhases,
  ]);

  return (
    <div
      className={`assistant-visualizer ${className}`}
      style={{
        width: `${containerWidth}px`,
        height: `${containerHeight}px`,
        backgroundColor: backgroundColor,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: `${spacing}px`,
      }}
    >
      {[0, 1, 2, 3].map((index: number) => (
        <div
          key={index}
          ref={(el) => {
            pillRefs.current[index] = el;
          }}
          className="audio-pill"
          style={{
            width: `${pillWidth}px`,
            height: `${pillWidth}px`,
            backgroundColor: color,
            borderRadius:
              visualizationMode === "circles" ? "50%" : `${pillWidth / 2}px`,
            transformOrigin: "center center",
            transition: `height ${
              transformDuration / 2
            }ms ease-out, transform 150ms ease, border-radius ${transitionDuration}ms cubic-bezier(0.34, 1.56, 0.64, 1)`,
            willChange: "height, transform, border-radius",
          }}
          data-index={index}
        />
      ))}
    </div>
  );
};

// ================================================================== //
// FIXED MEDIAPLAYER PROPS WITH PROPER TYPESCRIPT DEFINITIONS
// ================================================================== //
interface MediaPlayerProps {
  // --- Core Callbacks (used by both Memorization and UstadTutor) ---
  onSubmit: () => void;
  onRecordingComplete: (url: string) => void;
  onRecordingStart: () => void;
  onAmplitudeUpdate?: (amplitude: number) => void;

  // --- Enhanced External State Controls (for UstadTutor) ---
  isConversationActive?: boolean;
  isProcessing?: boolean;
  isAssistantSpeaking?: boolean;
  onStartConversation?: () => void;

  // --- Enhanced Processing State Props ---
  processingStage?:
    | "idle"
    | "recording"
    | "transcribing"
    | "thinking"
    | "speaking";
  processingProgress?: number;
  hasError?: boolean;
  errorMessage?: string;
  canRetry?: boolean;
  onRetry?: () => void;
  onClearError?: () => void;

  // --- Enhanced Recording State Props ---
  recordingQuality?: {
    isValid: boolean;
    issues: string[];
    duration: number;
    size: number;
  } | null;

  // --- Compatibility Props (for Memorization.tsx) ---
  loading?: boolean;
  displayedWords?: { [verseNumber: number]: any[] };
  setDisplayedWords?: Dispatch<
    SetStateAction<{ [verseNumber: number]: any[] }>
  >;
  verses?: any[];
  ttsAmplitude?: number;
  frequencyBands?: number[];
  isTTSPlaying?: boolean;
}

// Enhanced recording state management
interface RecordingState {
  isRecording: boolean;
  isLiveRecording: boolean;
  recordingStartTime: number;
  recordingDuration: number;
  maxDuration: number;
  audioLevel: number;
  hasPermission: boolean;
  permissionRequested: boolean;
}

// Enhanced error state for MediaPlayer
interface MediaPlayerError {
  hasError: boolean;
  type:
    | "permission"
    | "hardware"
    | "network"
    | "format"
    | "timeout"
    | "unknown";
  message: string;
  canRetry: boolean;
  timestamp: number;
}

const MediaPlayer: React.FC<MediaPlayerProps> = ({
  onSubmit,
  onRecordingComplete,
  onRecordingStart,
  onAmplitudeUpdate,
  isConversationActive = true,
  isProcessing = false,
  isAssistantSpeaking = false,
  onStartConversation = () => {},
  processingStage = "idle",
  processingProgress = 0,
  hasError = false,
  errorMessage = "",
  canRetry = false,
  onRetry = () => {},
  onClearError = () => {},
  recordingQuality = null,
  loading = false,
  // Compatibility props with defaults
  displayedWords = {},
  setDisplayedWords = () => {},
  verses = [],
  ttsAmplitude = 0,
  frequencyBands = [],
  isTTSPlaying = false,
}) => {
  // Enhanced state management
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isLiveRecording: false,
    recordingStartTime: 0,
    recordingDuration: 0,
    maxDuration: 30, // 30 seconds max
    audioLevel: 0,
    hasPermission: false,
    permissionRequested: false,
  });

  const [audioURL, setAudioURL] = useState<string | null>(null);
  const recordingTimer = useRef<number>();
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  // Enhanced playback state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Enhanced error state
  const [internalError, setInternalError] = useState<MediaPlayerError>({
    hasError: false,
    type: "unknown",
    message: "",
    canRetry: false,
    timestamp: 0,
  });

  const progressBarRef = useRef<HTMLDivElement>(null);
  const [hoverPosition, setHoverPosition] = useState<number | null>(null);

  // Enhanced audio analysis
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const rafIdRef = useRef<number | null>(null);

  const dataArrayRef = useRef<Uint8Array<ArrayBuffer> | null>(null);

  // Enhanced state derived values
  const isPostRecording: boolean = !!audioURL && !recordingState.isRecording;
  const finalIsProcessing = isProcessing || loading;
  const canRecord =
    isConversationActive && !finalIsProcessing && !isAssistantSpeaking;
  const showRecordingIndicator =
    recordingState.isRecording || recordingState.isLiveRecording;

  // Enhanced utility functions
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const setError = (
    type: MediaPlayerError["type"],
    message: string,
    canRetry: boolean = true
  ) => {
    console.error(`[MEDIAPLAYER-ERROR] ${type}: ${message}`);
    setInternalError({
      hasError: true,
      type,
      message,
      canRetry,
      timestamp: Date.now(),
    });
  };

  const clearError = () => {
    console.log("[MEDIAPLAYER] Clearing internal error state");
    setInternalError({
      hasError: false,
      type: "unknown",
      message: "",
      canRetry: false,
      timestamp: 0,
    });
    onClearError?.();
  };

  // Enhanced recording quality validation
  const validateRecordingQuality = (blob: Blob): boolean => {
    console.log(
      `[MEDIAPLAYER-VALIDATE] Validating recording - Size: ${blob.size}, Type: ${blob.type}`
    );

    if (blob.size < 1000) {
      setError(
        "format",
        "Recording too short. Please speak for at least 1 second.",
        true
      );
      return false;
    }

    if (blob.size > 50 * 1024 * 1024) {
      setError("format", "Recording too large. Please try again.", true);
      return false;
    }

    return true;
  };

  // Enhanced recording completion handler
  const handleRecordingComplete = (url: string) => {
    console.log(
      `[MEDIAPLAYER] Recording completed, URL generated: ${url.substring(
        0,
        50
      )}...`
    );
    console.log(
      `[MEDIAPLAYER] Recording duration: ${recordingState.recordingDuration}s`
    );

    setAudioURL(url);
    onRecordingComplete(url);

    setRecordingState((prev) => ({
      ...prev,
      recordingDuration: 0,
    }));

    // Validate the completed recording
    fetch(url)
      .then((res) => res.blob())
      .then((blob) => {
        if (!validateRecordingQuality(blob)) {
          setAudioURL(null); // Clear invalid recording
        }
      })
      .catch((err) => {
        console.error("[MEDIAPLAYER] Error validating recording:", err);
        setError("format", "Could not validate recording quality.", true);
      });
  };

  // Enhanced audio analysis cleanup
  const cleanupAudioAnalysis = useCallback(() => {
    console.log("[MEDIAPLAYER] Cleaning up audio analysis resources");

    if (rafIdRef.current) {
      cancelAnimationFrame(rafIdRef.current);
      rafIdRef.current = null;
    }

    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current
        .close()
        .catch((err) =>
          console.warn("[MEDIAPLAYER] Error closing audio context:", err)
        );
    }

    audioContextRef.current = null;
    analyserRef.current = null;
    dataArrayRef.current = null;
    onAmplitudeUpdate?.(0);
  }, [onAmplitudeUpdate]);

  // Enhanced recording start handler
  const handleStartRecording = async () => {
    console.log("[MEDIAPLAYER] Starting recording process");

    if (!navigator.mediaDevices?.getUserMedia) {
      console.error("[MEDIAPLAYER] MediaDevices API not supported");
      setError("hardware", "Microphone not supported in this browser.", false);
      return;
    }

    // Clear any existing errors and audio
    clearError();
    setAudioURL(null);

    try {
      console.log("[MEDIAPLAYER] Requesting microphone access");
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
        },
      });

      console.log("[MEDIAPLAYER] Microphone access granted");
      streamRef.current = stream;

      // Update permission state
      setRecordingState((prev) => ({
        ...prev,
        hasPermission: true,
        permissionRequested: true,
      }));

      // Set up MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm;codecs=opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        console.log(
          "[MEDIAPLAYER] MediaRecorder stopped, processing audio blob"
        );
        const blob = new Blob(chunksRef.current, { type: "audio/webm" });
        handleRecordingComplete(URL.createObjectURL(blob));
        chunksRef.current = [];

        // Clean up stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error("[MEDIAPLAYER] MediaRecorder error:", event);
        setError("hardware", "Recording failed. Please try again.", true);
      };

      // Start recording
      mediaRecorder.start(100);

      const startTime = Date.now();
      setRecordingState((prev) => ({
        ...prev,
        isRecording: true,
        isLiveRecording: true,
        recordingStartTime: startTime,
        recordingDuration: 0,
      }));

      // Set up enhanced audio analysis
      try {
        audioContextRef.current = new (window.AudioContext ||
          (window as any).webkitAudioContext)();
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 2048;
        analyserRef.current.smoothingTimeConstant = 0.3;
        source.connect(analyserRef.current);

        dataArrayRef.current = new Uint8Array(
          analyserRef.current.frequencyBinCount
        );

        const analyse = () => {
          if (
            !analyserRef.current ||
            !dataArrayRef.current ||
            !recordingState.isLiveRecording
          ) {
            return;
          }

          analyserRef.current.getByteTimeDomainData(dataArrayRef.current);

          let sum = 0;
          for (const v of dataArrayRef.current) {
            const normalizedV = v / 128.0 - 1.0;
            sum += normalizedV * normalizedV;
          }

          const amplitude = Math.sqrt(sum / dataArrayRef.current.length) * 100;
          setRecordingState((prev) => ({ ...prev, audioLevel: amplitude }));
          onAmplitudeUpdate?.(amplitude);

          rafIdRef.current = requestAnimationFrame(analyse);
        };

        analyse();
        console.log("[MEDIAPLAYER] Audio analysis started");
      } catch (audioError) {
        console.warn("[MEDIAPLAYER] Audio analysis setup failed:", audioError);
      }

      onRecordingStart();

      // Start recording timer
      recordingTimer.current = window.setInterval(() => {
        setRecordingState((prev) => {
          const newDuration = prev.recordingDuration + 1;

          if (newDuration >= prev.maxDuration) {
            console.log(
              "[MEDIAPLAYER] Maximum recording duration reached, auto-stopping"
            );
            handleStopRecording();
            return prev;
          }

          return { ...prev, recordingDuration: newDuration };
        });
      }, 1000);

      console.log("[MEDIAPLAYER] Recording started successfully");
    } catch (error) {
      console.error("[MEDIAPLAYER] getUserMedia error:", error);

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          setError(
            "permission",
            "Microphone access denied. Please enable microphone permissions.",
            true
          );
          setRecordingState((prev) => ({
            ...prev,
            permissionRequested: true,
            hasPermission: false,
          }));
        } else if (error.name === "NotFoundError") {
          setError(
            "hardware",
            "No microphone found. Please connect a microphone.",
            false
          );
        } else {
          setError(
            "hardware",
            "Could not access microphone. Please try again.",
            true
          );
        }
      } else {
        setError("unknown", "An unexpected error occurred.", true);
      }
    }
  };

  // Enhanced recording stop handler
  const handleStopRecording = () => {
    console.log("[MEDIAPLAYER] Stopping recording");
    console.log(
      `[MEDIAPLAYER] Final recording duration: ${recordingState.recordingDuration}s`
    );

    if (mediaRecorderRef.current && recordingState.isRecording) {
      mediaRecorderRef.current.stop();
    }

    cleanupAudioAnalysis();

    setRecordingState((prev) => ({
      ...prev,
      isRecording: false,
      isLiveRecording: false,
      audioLevel: 0,
    }));

    if (recordingTimer.current) {
      window.clearInterval(recordingTimer.current);
      recordingTimer.current = undefined;
    }
  };

  // Helper function to get the text for the mic button on hover
  const getMicButtonHoverText = (isPostRecordingState: boolean) => {
    if (recordingState.isRecording) {
      return "Stop Recording";
    }
    if (isPostRecordingState) {
      return "New Recording";
    }
    return "Start Recording";
  };

  // Enhanced microphone button handler
  const handleMicButtonClick = () => {
    console.log(
      `[MEDIAPLAYER] Mic button clicked - Current state: recording=${recordingState.isRecording}, canRecord=${canRecord}`
    );

    if (recordingState.isRecording) {
      handleStopRecording();
    } else if (canRecord) {
      if (isPlaying) {
        console.log("[MEDIAPLAYER] Pausing playback before recording");
        audioRef.current?.pause();
        setIsPlaying(false);
      }
      handleStartRecording();
    } else {
      console.warn(
        "[MEDIAPLAYER] Cannot record - conversation not ready or processing"
      );
    }
  };

  // Enhanced stop button handler (NEW)
  const handleStopButtonClick = () => {
    console.log(
      "[MEDIAPLAYER] Stop button clicked - force stopping all activity"
    );

    if (recordingState.isRecording || recordingState.isLiveRecording) {
      handleStopRecording();
    }

    if (isPlaying) {
      audioRef.current?.pause();
      setIsPlaying(false);
    }

    // Clear any existing audio
    setAudioURL(null);
    clearError();

    console.log("[MEDIAPLAYER] All activity stopped");
  };

  // Enhanced playback controls
  const togglePlayPause = () => {
    console.log(
      `[MEDIAPLAYER] Toggle play/pause - Current state: ${isPlaying}`
    );

    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch((err) => {
          console.error("[MEDIAPLAYER] Playback error:", err);
          setError("format", "Could not play recording.", true);
        });
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Enhanced progress bar handlers
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current && duration) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const newTime = (clickX / rect.width) * duration;
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
      console.log(
        `[MEDIAPLAYER] Progress clicked - seek to: ${newTime.toFixed(2)}s`
      );
    }
  };

  const handleProgressDrag = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging && audioRef.current && duration) {
      const rect = e.currentTarget.getBoundingClientRect();
      let dragX = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
      const newTime = (dragX / rect.width) * duration;
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (progressBarRef.current) {
      const rect = progressBarRef.current.getBoundingClientRect();
      setHoverPosition((e.clientX - rect.left) / rect.width);
    }
  };

  const handleMouseLeave = () => setHoverPosition(null);

  // Enhanced submit handler with validation
  const handleSubmitClick = () => {
    console.log("[MEDIAPLAYER] Submit button clicked");
    console.log(`[MEDIAPLAYER] Audio URL exists: ${!!audioURL}`);
    console.log(`[MEDIAPLAYER] Processing state: ${finalIsProcessing}`);
    console.log(
      `[MEDIAPLAYER] Recording quality: ${
        recordingQuality ? "present" : "none"
      }`
    );

    if (!audioURL) {
      setError(
        "format",
        "No recording to submit. Please record something first.",
        false
      );
      return;
    }

    if (finalIsProcessing) {
      console.warn("[MEDIAPLAYER] Already processing, ignoring submit");
      return;
    }

    if (recordingQuality && !recordingQuality.isValid) {
      setError(
        "format",
        `Recording quality issues: ${recordingQuality.issues.join(", ")}`,
        true
      );
      return;
    }

    console.log("[MEDIAPLAYER] Submitting recording for processing");
    onSubmit();
  };

  // Effect: Handle assistant speaking state
  useEffect(() => {
    if (isAssistantSpeaking) {
      console.log("[MEDIAPLAYER] Assistant speaking, clearing user audio");
      setAudioURL(null);
      if (isPlaying) setIsPlaying(false);
    }
  }, [isAssistantSpeaking, isPlaying]);

  // Effect: Audio element event listeners
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      const onTimeUpdate = () => {
        if (!isDragging) {
          setCurrentTime(audio.currentTime);
        }
      };

      const onLoadedMeta = () => {
        setDuration(audio.duration);
        console.log(
          `[MEDIAPLAYER] Audio loaded - duration: ${audio.duration.toFixed(2)}s`
        );
      };

      const onEnded = () => {
        setIsPlaying(false);
        console.log("[MEDIAPLAYER] Audio playback ended");
      };

      const onError = (e: Event) => {
        console.error("[MEDIAPLAYER] Audio playback error:", e);
        setError("format", "Could not play audio.", true);
        setIsPlaying(false);
      };

      audio.addEventListener("timeupdate", onTimeUpdate);
      audio.addEventListener("loadedmetadata", onLoadedMeta);
      audio.addEventListener("ended", onEnded);
      audio.addEventListener("error", onError);

      return () => {
        audio.removeEventListener("timeupdate", onTimeUpdate);
        audio.removeEventListener("loadedmetadata", onLoadedMeta);
        audio.removeEventListener("ended", onEnded);
        audio.removeEventListener("error", onError);
      };
    }
  }, [isDragging]);

  // Effect: Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupAudioAnalysis();
      if (recordingTimer.current) {
        window.clearInterval(recordingTimer.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, [cleanupAudioAnalysis]);

  // Enhanced state synchronization logging
  useEffect(() => {
    console.log(
      `[MEDIAPLAYER] State sync - Conversation active: ${isConversationActive}, Processing: ${finalIsProcessing}, Stage: ${processingStage}`
    );
  }, [isConversationActive, finalIsProcessing, processingStage]);

  // Calculate progress width and hover time
  const progressWidth =
    duration === 0 ? "0%" : `${(currentTime / duration) * 100}%`;
  const hoverTime =
    hoverPosition === null || duration === 0
      ? null
      : formatTime(hoverPosition * duration);

  // Enhanced error display logic
  const displayError = hasError || internalError.hasError;
  const displayErrorMessage = hasError ? errorMessage : internalError.message;
  const displayCanRetry = hasError ? canRetry : internalError.canRetry;

  // Enhanced processing stage display
  const getProcessingStageInfo = () => {
    switch (processingStage) {
      case "recording":
        return { icon: Mic, text: "Recording...", color: "text-red-500" };
      case "transcribing":
        return {
          icon: Volume2,
          text: "Understanding...",
          color: "text-blue-500",
        };
      case "thinking":
        return {
          icon: Loader2,
          text: "AI thinking...",
          color: "text-yellow-500",
        };
      case "speaking":
        return {
          icon: Play,
          text: "AI responding...",
          color: "text-green-500",
        };
      default:
        return { icon: Clock, text: "Ready", color: "text-gray-500" };
    }
  };

  const stageInfo = getProcessingStageInfo();

  return (
    <div className="w-full h-full flex items-center justify-center">
      {audioURL && <audio ref={audioRef} src={audioURL} className="hidden" />}

      {/* Enhanced Error Display */}
      {displayError && (
        <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 px-4 py-2 bg-red-100 backdrop-blur-sm border border-red-200 rounded-xl flex items-center space-x-2 text-red-800 text-xs shadow-lg max-w-xs">
          <AlertCircle className="w-3 h-3 text-red-700 flex-shrink-0" />
          <span className="truncate">{displayErrorMessage}</span>
          {displayCanRetry && (
            <button
              onClick={hasError ? onRetry : () => {}}
              className="ml-1 px-2 py-1 bg-red-200 hover:bg-red-300 rounded text-xs transition-colors flex-shrink-0"
            >
              Retry
            </button>
          )}
          <button
            onClick={hasError ? onClearError : clearError}
            className="text-red-600 hover:text-red-800 flex-shrink-0"
          >
            ×
          </button>
        </div>
      )}

      {/* Enhanced Processing Indicator */}
      {finalIsProcessing && (
        <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-gray-800 text-white rounded-lg flex items-center space-x-2 text-xs shadow-lg">
          <stageInfo.icon
            className={`w-3 h-3 ${stageInfo.color} ${
              processingStage === "thinking" ? "animate-spin" : "animate-pulse"
            }`}
          />
          <span>{stageInfo.text}</span>
          {processingProgress > 0 && (
            <div className="w-12 h-1 bg-gray-600 rounded-full overflow-hidden">
              <div
                className="h-full bg-white transition-all duration-300"
                style={{ width: `${Math.min(processingProgress, 100)}%` }}
              />
            </div>
          )}
        </div>
      )}

      {/* Recording Quality Warning */}
      {recordingQuality && !recordingQuality.isValid && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-yellow-100 border border-yellow-200 text-yellow-800 rounded text-xs">
          <div className="flex items-center space-x-1">
            <AlertCircle className="w-3 h-3" />
            <span>Quality: {recordingQuality.issues.join(", ")}</span>
          </div>
        </div>
      )}

      {/* Start Conversation Button */}
      {!isConversationActive ? (
        <button
          onClick={onStartConversation}
          disabled={finalIsProcessing}
          className="px-4 py-2 bg-black text-white rounded-lg flex items-center space-x-2 font-medium transition-transform transform hover:scale-105 active:scale-95 shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Mic className="w-4 h-4" />
          <span>Start Conversation</span>
        </button>
      ) : (
        <div className="w-full h-14 flex items-center">
          <div className="w-full flex flex-col relative">
            {/* Enhanced Recording Indicator */}
            {showRecordingIndicator && (
              <div className="flex items-center justify-between absolute top-0 left-2 right-2 -translate-y-full">
                <div className="flex items-center">
                  <span className="relative flex h-2 w-2 mr-1">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                  </span>
                  <span className="text-base font-medium text-gray-700">
                    {formatTime(recordingState.recordingDuration)} /{" "}
                    {formatTime(recordingState.maxDuration)}
                  </span>
                </div>

                {/* Audio Level Indicator */}
                {recordingState.audioLevel > 0 && (
                  <div className="flex items-center space-x-1">
                    <Volume2 className="w-3 h-3 text-gray-600" />
                    <div className="w-12 h-1 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-green-500 transition-all duration-150"
                        style={{
                          width: `${Math.min(recordingState.audioLevel, 100)}%`,
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Main Control Area */}
            {!isPostRecording ? (
              <div className="w-full flex items-center justify-center px-4">
                <div className="flex items-center space-x-2">
                  {/* Enhanced Microphone Button */}
                  <button
                    onClick={handleMicButtonClick}
                    disabled={!canRecord}
                    className={`
                      group
                      relative flex items-center justify-center
                      h-14 w-14 ${
                        !recordingState.isRecording ? "hover:w-48" : ""
                      }
                      rounded-full transition-all duration-300 ease-in-out
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black
                      disabled:transform-none disabled:cursor-not-allowed
                      active:scale-95
                      ${
                        recordingState.isRecording
                          ? "bg-red-500 text-white shadow-lg"
                          : canRecord
                          ? "bg-black text-white shadow-md"
                          : "bg-gray-400 text-gray-600 cursor-not-allowed"
                      }
                    `}
                    // Removed the title attribute
                  >
                    {recordingState.isRecording ? (
                      <>
                        <Square className="w-4 h-4" />
                        <span className="absolute -top-1 -right-1 w-2.5 h-2.5">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75" />
                          <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-red-500" />
                        </span>
                      </>
                    ) : (
                      <>
                        <Mic className="w-5 h-5" />
                        <span
                          className={`
                          whitespace-nowrap
                          transition-all duration-300
                          opacity-0 max-w-0
                          group-hover:ml-2 group-hover:opacity-100 group-hover:max-w-full
                        `}
                        >
                          {getMicButtonHoverText(isPostRecording)}
                        </span>
                      </>
                    )}
                  </button>

                  {/* NEW: Stop Button */}
                  {(recordingState.isRecording || isPostRecording) && (
                    <button
                      onClick={handleStopButtonClick}
                      className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-600 hover:bg-gray-700 text-white transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transform hover:scale-105 active:scale-95 shadow-sm"
                      aria-label="Stop All Activity"
                    >
                      <StopCircle className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            ) : (
              <div className="w-full flex items-center justify-between px-2">
                {/* Left Controls */}
                <div className="flex items-center justify-center space-x-4">
                  <button
                    onClick={handleMicButtonClick}
                    disabled={!canRecord}
                    className={`
                      relative flex items-center justify-center
                      h-10 w-10
                      rounded-full transition-all duration-300 ease-in-out
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black
                      disabled:transform-none disabled:cursor-not-allowed
                      active:scale-95
                      ${
                        recordingState.isRecording
                          ? "bg-red-500 text-white"
                          : canRecord
                          ? "bg-black text-white"
                          : "bg-gray-400 text-gray-600 cursor-not-allowed"
                      }
                    `}
                    aria-label={getMicButtonHoverText(isPostRecording)}
                  >
                    {recordingState.isRecording ? (
                      <>
                        <Square className="w-4 h-4" />
                        <span className="absolute -top-1 -right-1 w-2.5 h-2.5">
                          <span className="absolute inline-flex w-full h-full rounded-full bg-red-400 opacity-75 animate-ping" />
                          <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-red-500" />
                        </span>
                      </>
                    ) : (
                      <>
                        <Mic className="w-5 h-5" />
                        <span
                          className={`
                          whitespace-nowrap
                          transition-all duration-300
                          opacity-0 max-w-0
                        `}
                        >
                          {getMicButtonHoverText(isPostRecording)}
                        </span>
                      </>
                    )}
                  </button>

                  {/* Playback Button */}
                  {isPostRecording && (
                    <button
                      onClick={togglePlayPause}
                      disabled={finalIsProcessing}
                      className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 bg-gray-900 hover:bg-black text-white ${
                        finalIsProcessing ? "opacity-50 cursor-not-allowed" : ""
                      } transform hover:scale-105 active:scale-95 shadow-sm`}
                      aria-label={isPlaying ? "Pause Audio" : "Play Audio"}
                    >
                      {isPlaying ? (
                        <Pause className="w-5 h-5" />
                      ) : (
                        <Play className="w-4 h-4 ml-0.5" />
                      )}
                    </button>
                  )}

                  {/* NEW: Stop Button for Post-Recording */}
                  <button
                    onClick={handleStopButtonClick}
                    className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-600 hover:bg-gray-700 text-white transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transform hover:scale-105 active:scale-95 shadow-sm"
                    aria-label="Clear Recording"
                  >
                    <StopCircle className="w-4 h-4" />
                  </button>
                </div>

                {/* Enhanced Progress Bar */}
                {isPostRecording && (
                  <div className="flex-1 mx-2 h-1">
                    <div
                      className="relative w-full h-1 group"
                      onMouseMove={handleMouseMove}
                      onMouseLeave={handleMouseLeave}
                    >
                      <div
                        ref={progressBarRef}
                        className="w-full h-1 bg-gray-200 rounded-full cursor-pointer overflow-hidden relative"
                        onClick={handleProgressClick}
                        onMouseDown={() => setIsDragging(true)}
                        onMouseUp={() => setIsDragging(false)}
                        onMouseMove={handleProgressDrag}
                        onMouseLeave={() => {
                          setIsDragging(false);
                          handleMouseLeave();
                        }}
                      >
                        <div
                          className="absolute h-full bg-black rounded-full transition-all duration-150"
                          style={{ width: progressWidth }}
                        />
                      </div>

                      {/* Enhanced Hover Time Display */}
                      {hoverTime !== null && hoverPosition !== null && (
                        <div
                          className="absolute bottom-4 bg-gray-800 text-white text-xs px-1.5 py-0.5 rounded pointer-events-none opacity-80 transform -translate-x-1/2"
                          style={{ left: `${hoverPosition * 100}%` }}
                        >
                          {hoverTime}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Right Controls */}
                <div className="flex justify-end">
                  {isPostRecording && (
                    <button
                      onClick={handleSubmitClick}
                      disabled={finalIsProcessing || !audioURL}
                      className={`flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                        finalIsProcessing || !audioURL
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-black hover:bg-gray-800"
                      } transform hover:scale-105 active:scale-95 shadow-sm`}
                      aria-label="Submit Recording"
                    >
                      {finalIsProcessing ? (
                        <RefreshCw className="w-4 h-4 text-white animate-spin" />
                      ) : (
                        <ArrowUp className="w-6 h-6 text-white" />
                      )}
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaPlayer;

/**
 * NEW SKELETON COMPONENT FOR MEDIAPLAYER
 * This component acts as a lightweight, non-interactive placeholder
 * that matches the real MediaPlayer's layout to prevent content jank
 * while the main component is being dynamically loaded.
 */
export const MediaPlayerSkeleton: React.FC = () => {
  return (
    <div className="w-full h-full flex items-center justify-center animate-pulse">
      <div className="w-full h-14 flex items-center justify-center px-4">
        {/* Placeholder for the main microphone button */}
        <div className="h-14 w-14 bg-gray-200 rounded-full"></div>
      </div>
    </div>
  );
};
