// app/memorization/thinkingIcon.tsx
"use client";

import React from "react";

// RENAME the function to start with an uppercase letter
const ThinkingIcon: React.FC = () => {
  return (
    <div className="flex items-center justify-center">
      <div className="loader mr-2"></div>
      <span className="text-neutral-500 font-semibold">Thinking...</span>
      <style jsx>{`
        .loader {
          width: 40px;
          aspect-ratio: 1.154;
          --_g: no-repeat radial-gradient(farthest-side, #000 90%, #0000);
          background: var(--_g) 50% 0, var(--_g) 0 100%, var(--_g) 100% 100%;
          background-size: 35% calc(35% * 1.154);
          animation: l16 1s infinite;
        }
        @keyframes l16 {
          50%,
          100% {
            background-position: 100% 100%, 50% 0, 0 100%;
          }
        }
      `}</style>
    </div>
  );
};

// RENAME the default export to match the component name
export default ThinkingIcon;
