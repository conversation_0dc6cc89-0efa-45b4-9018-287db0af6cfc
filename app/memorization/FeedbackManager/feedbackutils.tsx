// ====================== feedbackutils.tsx ======================
import React from "react";
import { removeVowelMarkings, getWordPositionInVerse } from "./alignmentUtils";
import { CitationSystem, Word } from "../citationSystem";
import {
  getBestAudioUrl,
  playAudioFromUrl,
  constructAudioUrl,
  shouldUseProxy,
  getProxyUrl,
  type AudioValidationResult,
} from "../Audio/audioUtils";
import { Volume2 } from "lucide-react";

/* ------------------------------------------------------------------ */
/*  🆕 ENHANCED TYPES & INTERFACES                                    */
/* ------------------------------------------------------------------ */
interface FeedbackPairForDisplay {
  userWord: string | null; // Allow null
  correctWord: string | null; // Allow null
  letterFeedback?: any[];
  userStart?: number;
  userEnd?: number;
  similarity?: number;
  type?: string;
  verseNumber?: number | null;
  letterCountMismatch?: boolean;
  correctIndex?: number;
  correctWordInfoFromApi?: {
    text: string;
    surah: number;
    ayah: number;
    wordInAyahIndex: number;
    audioUrl: string;
  };
}

interface AudioPlaybackResult {
  success: boolean;
  error?: string;
  method?: "direct" | "proxy" | "fallback";
}

interface CorrectWordDisplayProps {
  word: string;
  correctWordInfoFromApi?: {
    text: string;
    surah: number;
    ayah: number;
    wordInAyahIndex: number;
    audioUrl: string;
  };
}

type Verse = {
  surahNumber: number;
  verseNumber: number;
  text: string;
};

type Difficulty = "easy" | "hard";

/* ------------------------------------------------------------------ */
/*  🆕 ENHANCED AUDIO UTILITIES                                       */
/* ------------------------------------------------------------------ */

const validateAudioMetadata = (correctWordInfo: any): boolean => {
  if (!correctWordInfo || typeof correctWordInfo !== "object") {
    return false;
  }
  const { text, surah, ayah, wordInAyahIndex, audioUrl } = correctWordInfo;
  return !!(
    text &&
    typeof text === "string" &&
    surah &&
    typeof surah === "number" &&
    surah >= 1 &&
    surah <= 114 &&
    ayah &&
    typeof ayah === "number" &&
    ayah >= 1 &&
    wordInAyahIndex &&
    typeof wordInAyahIndex === "number" &&
    wordInAyahIndex >= 1 &&
    audioUrl &&
    typeof audioUrl === "string"
  );
};

const playCorrectWordAudio = async (
  word: string,
  correctWordInfo?: {
    text: string;
    surah: number;
    ayah: number;
    wordInAyahIndex: number;
    audioUrl: string;
  }
): Promise<AudioPlaybackResult> => {
  console.log(
    `[Enhanced Audio Playback] Starting playback for word: "${word}"`
  );
  if (!correctWordInfo) {
    console.warn(
      `[Enhanced Audio Playback] No correctWordInfo provided for word: "${word}"`
    );
    return { success: false, error: "No audio metadata available" };
  }
  if (!validateAudioMetadata(correctWordInfo)) {
    console.error(
      `[Enhanced Audio Playback] Invalid audio metadata for word: "${word}"`,
      correctWordInfo
    );
    return { success: false, error: "Invalid audio metadata" };
  }
  const { surah, ayah, wordInAyahIndex, audioUrl } = correctWordInfo;
  try {
    if (audioUrl && audioUrl.trim() !== "") {
      console.log(
        `[Enhanced Audio Playback] Strategy 1 - Direct URL: ${audioUrl}`
      );
      const directResult = await playAudioFromUrl(audioUrl);
      if (directResult) {
        console.log(
          `[Enhanced Audio Playback] Strategy 1 succeeded for word: "${word}"`
        );
        return { success: true, method: "direct" };
      }
      console.warn(
        `[Enhanced Audio Playback] Strategy 1 failed for word: "${word}"`
      );
    }
    console.log(
      `[Enhanced Audio Playback] Strategy 2 - Using getBestAudioUrl for ${surah}:${ayah}:${wordInAyahIndex}`
    );
    const bestUrl = await getBestAudioUrl(surah, ayah, wordInAyahIndex);
    if (bestUrl) {
      const bestUrlResult = await playAudioFromUrl(bestUrl);
      if (bestUrlResult) {
        const method = bestUrl.includes("/api/audio-proxy")
          ? "proxy"
          : "direct";
        console.log(
          `[Enhanced Audio Playback] Strategy 2 succeeded with ${method} for word: "${word}"`
        );
        return { success: true, method };
      }
      console.warn(
        `[Enhanced Audio Playback] Strategy 2 failed for word: "${word}"`
      );
    }
    console.log(
      `[Enhanced Audio Playback] Strategy 3 - Fallback URL construction for ${surah}:${ayah}:${wordInAyahIndex}`
    );
    try {
      const fallbackUrl = constructAudioUrl(surah, ayah, wordInAyahIndex);
      const fallbackResult = await playAudioFromUrl(fallbackUrl);
      if (fallbackResult) {
        console.log(
          `[Enhanced Audio Playback] Strategy 3 succeeded for word: "${word}"`
        );
        return { success: true, method: "fallback" };
      }
    } catch (constructionError) {
      console.error(
        `[Enhanced Audio Playback] URL construction failed:`,
        constructionError
      );
    }
    const useProxy = await shouldUseProxy(surah, ayah, wordInAyahIndex);
    if (useProxy) {
      console.log(
        `[Enhanced Audio Playback] Strategy 4 - Proxy URL for ${surah}:${ayah}:${wordInAyahIndex}`
      );
      const proxyUrl = getProxyUrl(surah, ayah, wordInAyahIndex);
      const proxyResult = await playAudioFromUrl(proxyUrl);
      if (proxyResult) {
        console.log(
          `[Enhanced Audio Playback] Strategy 4 succeeded for word: "${word}"`
        );
        return { success: true, method: "proxy" };
      }
    }
    console.error(
      `[Enhanced Audio Playback] All strategies failed for word: "${word}"`
    );
    return {
      success: false,
      error: "Audio unavailable - all playback strategies failed",
    };
  } catch (error: any) {
    console.error(
      `[Enhanced Audio Playback] Unexpected error for word: "${word}":`,
      error
    );
    return {
      success: false,
      error: `Playback error: ${error.message || "Unknown error"}`,
    };
  }
};

/* ------------------------------------------------------------------ */
/*  🆕 ENHANCED RENDER FUNCTIONS                                      */
/* ------------------------------------------------------------------ */

function renderUserWord(
  word: string | null,
  start?: number,
  end?: number,
  playSegment?: (s: number, e: number) => void
) {
  if (!word) return null;
  return (
    <span
      className="bg-red-50 text-red-600 text-4xl px-3 py-1.5 font-normal rounded-lg relative cursor-pointer border border-transparent hover:border-red-400 transition-all duration-200"
      role="button"
      aria-label="Play your pronunciation"
      onClick={(e) => {
        e.stopPropagation();
        console.log(
          "[feedbackutils renderUserWord onClick] Clicked word:",
          word,
          "Attempting to play segment. Start:",
          start,
          "End:",
          end,
          "Is playSegment function available?:",
          !!playSegment
        );
        if (playSegment && start !== undefined && end !== undefined) {
          console.log(
            "[feedbackutils renderUserWord onClick] Conditions met. Calling playSegment(",
            start,
            ",",
            end,
            ")"
          );
          playSegment(start, end);
        } else {
          console.warn(
            "[feedbackutils renderUserWord onClick] Conditions NOT met. Not calling playSegment. Details:",
            {
              hasPlaySegment: !!playSegment,
              hasStart: start !== undefined,
              startVal: start,
              hasEnd: end !== undefined,
              endVal: end,
            }
          );
        }
      }}
    >
      {word}
    </span>
  );
}

const CorrectWordDisplay: React.FC<CorrectWordDisplayProps> = ({
  word,
  correctWordInfoFromApi,
}) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [lastError, setLastError] = React.useState<string | null>(null);

  const handleClick = async (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (isPlaying) {
      console.log(`[CorrectWordDisplay] Already playing audio for: "${word}"`);
      return;
    }
    setIsPlaying(true);
    setLastError(null);
    console.log(
      `[CorrectWordDisplay] Click handler triggered for word: "${word}"`
    );
    console.log(
      `[CorrectWordDisplay] CorrectWordInfo:`,
      correctWordInfoFromApi
    );
    try {
      const result = await playCorrectWordAudio(word, correctWordInfoFromApi);
      if (!result.success) {
        setLastError(result.error || "Audio playback failed");
        console.error(
          `[CorrectWordDisplay] Playback failed for "${word}":`,
          result.error
        );
        setTimeout(() => setLastError(null), 3000);
      } else {
        console.log(
          `[CorrectWordDisplay] Playback succeeded for "${word}" using ${result.method} method`
        );
      }
    } catch (error: any) {
      const errorMessage = `Unexpected error: ${
        error.message || "Unknown error"
      }`;
      setLastError(errorMessage);
      console.error(
        `[CorrectWordDisplay] Unexpected error for "${word}":`,
        error
      );
      setTimeout(() => setLastError(null), 3000);
    } finally {
      setIsPlaying(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      handleClick(e);
    }
  };

  const hasAudioData = validateAudioMetadata(correctWordInfoFromApi);
  const baseClasses =
    "text-4xl px-3 py-1.5 font-normal rounded-lg transition-all duration-200 border border-transparent";
  let className = baseClasses;
  let ariaLabel = `Word: ${word}`;

  if (lastError) {
    className +=
      " bg-red-100 text-red-600 cursor-pointer hover:border-red-500 hover:bg-red-200";
    ariaLabel += ` (Audio error: ${lastError})`;
  } else if (hasAudioData) {
    className +=
      " bg-green-50 text-green-600 cursor-pointer hover:border-green-400 hover:bg-green-100";
  } else {
    className += " bg-gray-100 text-gray-600 cursor-not-allowed";
    ariaLabel += " (Audio not available)";
  }

  return (
    <span
      className={className}
      role="button"
      tabIndex={hasAudioData ? 0 : -1}
      aria-label={ariaLabel}
      onClick={hasAudioData ? handleClick : undefined}
      onKeyDown={hasAudioData ? handleKeyDown : undefined}
    >
      {word}
      {lastError && <span className="ml-1 text-xs">⚠️</span>}
    </span>
  );
};

/* ------------------------------------------------------------------ */
/*  SMALL HELPERS                                                     */
/* ------------------------------------------------------------------ */
function extractTextFromJSX(node: React.ReactNode): string {
  if (typeof node === "string" || typeof node === "number") {
    return node.toString();
  }
  if (Array.isArray(node)) {
    return node.map(extractTextFromJSX).join(" ");
  }
  if (React.isValidElement(node)) {
    return extractTextFromJSX(node.props.children);
  }
  return "";
}

function groupExtraAndMissingLetters(letterFeedback: any[]): any[] {
  const groupedFeedback: any[] = [];
  let i = 0;
  while (i < letterFeedback.length) {
    const currentItem = letterFeedback[i];
    if (currentItem.type === "extra") {
      const extras = [currentItem];
      i++;
      while (i < letterFeedback.length && letterFeedback[i].type === "extra") {
        extras.push(letterFeedback[i]);
        i++;
      }
      groupedFeedback.push({ type: "extraGroup", extras });
    } else if (currentItem.type === "missing") {
      const missing = [currentItem];
      i++;
      while (
        i < letterFeedback.length &&
        letterFeedback[i].type === "missing"
      ) {
        missing.push(letterFeedback[i]);
        i++;
      }
      groupedFeedback.push({ type: "missingGroup", missing });
    } else {
      groupedFeedback.push(currentItem);
      i++;
    }
  }
  return groupedFeedback;
}

/* ------------------------------------------------------------------ */
/*  CONSTANTS                                                         */
/* ------------------------------------------------------------------ */

export const vowelMap: { [key: string]: string } = {
  "\u064B": "Fathatan",
  "\u064C": "Dammatan",
  "\u064D": "Kasratan",
  "\u064E": "Fatha",
  "\u064F": "Damma",
  "\u0650": "Kasra",
  "\u0651": "Shadda",
  "\u0652": "Sukun",
  "\u0653": "Maddah",
  "\u0654": "Hamza Above",
  "\u0655": "Hamza Below",
};

/* ================================================================== */
/*  EXTRA-WORDS  (no audio needed)                                    */
/* ================================================================== */
export const extraWordsFeedback = (
  wordAlignment: any[],
  sessionId: number | null,
  feedbackData: any[],
  feedbackRefs: { [key: string]: React.RefObject<HTMLDivElement> },
  difficulty: Difficulty
): { feedback: JSX.Element[]; unmatchedWords: string[] } => {
  const feedback: JSX.Element[] = [];
  const unmatchedWords: string[] = [];

  wordAlignment.forEach((alignment) => {
    if (
      !alignment.matched &&
      alignment.correctWord === null &&
      alignment.userWord
    ) {
      if (typeof alignment.userWord === "string") {
        unmatchedWords.push(alignment.userWord);
        // feedbackRefs[alignment.userWord] = React.createRef(); // Keep this commented if not essential for individual extra words
      }
    }
  });

  if (unmatchedWords.length > 0) {
    const refKey = "extra-words-group";
    feedbackRefs[refKey] = React.createRef();

    feedback.push(
      <div
        className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        key="extra-words-group"
        ref={feedbackRefs[refKey]}
        data-word="extra-words-group"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-2.5 h-2.5 bg-red-500 rounded-full" />
            <h3 className="text-lg font-semibold text-gray-900 tracking-tight">
              {difficulty === "easy"
                ? "Extra Words"
                : "Oops! Extra Words Found"}
            </h3>
          </div>
          <div className="flex flex-wrap gap-2 mt-3">
            {unmatchedWords.map(
              (
                word,
                index // Added index for key
              ) => (
                <span
                  key={`extra-word-${index}-${word}`} // Ensure unique key
                  className={`bg-red-50 text-red-600 text-2xl px-3 py-1.5 rounded-lg font-normal tracking-wide`}
                  data-word={word}
                >
                  {word}
                </span>
              )
            )}
          </div>
          <div className="bg-gray-50 rounded-xl p-3 mt-1">
            <p className="text-sm text-gray-700 leading-relaxed">
              {difficulty === "easy"
                ? "Extra words were detected. Keep practicing to improve your accuracy."
                : "These words weren't part of the original text. Try to stick to the exact words in your next attempt."}
            </p>
          </div>
        </div>
      </div>
    );
  }
  return { feedback, unmatchedWords };
};

/* ================================================================== */
/*  🆕 SIMPLIFIED MISSING-WORDS FEEDBACK                              */
/* ================================================================== */
export const missingWordsFeedback = (
  finalUnmatchedCorrectWords: any[],
  feedbackRefs: { [key: string]: React.RefObject<HTMLDivElement> },
  difficulty: Difficulty
): { feedback: JSX.Element[]; unmatchedWords: string[] } => {
  const feedback: JSX.Element[] = [];
  const unmatchedWords: string[] = [];

  const missedWords: { text: string; correctWordInfoFromApi?: any }[] = []; // Store with potential audio info
  finalUnmatchedCorrectWords.forEach((unmatched) => {
    // Assuming finalUnmatchedCorrectWords items are of type MissedWordInfo or similar
    // which should include { text: string, ..., correctWordInfoFromApi?: ... } if available
    if (unmatched && typeof unmatched.text === "string") {
      missedWords.push({
        text: unmatched.text,
        correctWordInfoFromApi: unmatched.correctWordInfoFromApi,
      });
      unmatchedWords.push(unmatched.text);
      // feedbackRefs[unmatched.text] = React.createRef(); // Keep commented if not essential
    } else if (unmatched && typeof unmatched.correctWord === "string") {
      // Fallback for older structure
      missedWords.push({
        text: unmatched.correctWord,
        correctWordInfoFromApi: unmatched.correctWordInfoFromApi,
      });
      unmatchedWords.push(unmatched.correctWord);
    }
  });

  if (missedWords.length > 0) {
    const refKey = "missed-words-group";
    feedbackRefs[refKey] = React.createRef();

    feedback.push(
      <div
        className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        key={refKey}
        ref={feedbackRefs[refKey]}
        data-word={refKey}
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-2.5 h-2.5 bg-blue-500 rounded-full" />
            <h3 className="text-lg font-semibold text-gray-900 tracking-tight">
              {difficulty === "easy" ? "Missed Words" : "Words You Didn't Say"}
            </h3>
          </div>
          <div className="flex flex-wrap gap-2 mt-3">
            {missedWords.map((wordData, index) => (
              <CorrectWordDisplay // Using CorrectWordDisplay for consistency
                key={`missed-word-${index}-${wordData.text}`}
                word={wordData.text}
                correctWordInfoFromApi={wordData.correctWordInfoFromApi}
              />
            ))}
          </div>
          <div className="bg-gray-50 rounded-xl p-3 mt-1">
            <p className="text-sm text-gray-700 leading-relaxed">
              {difficulty === "easy"
                ? `You missed ${missedWords.length} word${
                    missedWords.length > 1 ? "s" : ""
                  } in your recitation. Try including ${
                    missedWords.length > 1 ? "these words" : "this word"
                  } in your next attempt.`
                : `These ${missedWords.length} word${
                    missedWords.length > 1 ? "s were" : " was"
                  } not detected in your audio. Make sure to clearly pronounce all words.`}
            </p>
          </div>
        </div>
      </div>
    );
  }
  return { feedback, unmatchedWords };
};

/* ================================================================== */
/*  🆕 ENHANCED PARTIALLY-MATCHED with simplified matching           */
/* ================================================================== */
export const partiallyMatchedWordsFeedback = (
  feedbackPairs: FeedbackPairForDisplay[],
  sessionId: number | null,
  feedbackData: any[],
  feedbackRefs: { [key: string]: React.RefObject<HTMLDivElement> },
  difficulty: Difficulty,
  existingCitationMap: Record<string, number> = {},
  playSegment?: (start: number, end: number) => void
): {
  feedback: JSX.Element[];
  unmatchedWords: string[];
  citationMap: Record<string, number>;
} => {
  // GUARD: Never handle missing-type here—those flow through missingWordsFeedback()
  feedbackPairs = feedbackPairs.filter((p) => p.type !== "missing");

  const feedback: JSX.Element[] = [];
  const unmatchedWords: string[] = [];
  const citationMap: Record<string, number> = { ...existingCitationMap }; // Start with existing
  let nextCitationNumber = 1;
  if (Object.keys(citationMap).length > 0) {
    nextCitationNumber =
      Math.max(0, ...Object.values(citationMap).map(Number).filter(isFinite)) +
      1;
  }

  console.log(
    `[Enhanced partiallyMatchedWordsFeedback] Processing ${feedbackPairs.length} feedback pairs (after filtering 'missing')`
  );

  const enhancedFeedbackPairs: FeedbackPairForDisplay[] = feedbackPairs.map(
    (pair, index) => {
      console.log(
        `[Enhanced partiallyMatchedWordsFeedback] Processing pair ${index}:`,
        {
          correctWord: pair.correctWord,
          hasCorrectWordInfo: !!pair.correctWordInfoFromApi,
          audioUrl: pair.correctWordInfoFromApi?.audioUrl,
          userStart: pair.userStart,
          userEnd: pair.userEnd,
        }
      );
      if (
        pair.correctWordInfoFromApi &&
        validateAudioMetadata(pair.correctWordInfoFromApi)
      ) {
        console.log(
          `[Enhanced partiallyMatchedWordsFeedback] Pair ${index} has valid audio metadata`
        );
        return pair;
      }
      if (!pair.correctWordInfoFromApi) {
        console.warn(
          `[Enhanced partiallyMatchedWordsFeedback] Pair ${index} missing correctWordInfoFromApi, attempting recovery`
        );
        if (pair.correctIndex !== undefined && pair.verseNumber) {
          console.log(
            `[Enhanced partiallyMatchedWordsFeedback] Attempting metadata recovery for pair ${index}`
          );
        }
      } else if (!validateAudioMetadata(pair.correctWordInfoFromApi)) {
        console.warn(
          `[Enhanced partiallyMatchedWordsFeedback] Pair ${index} has invalid audio metadata:`,
          pair.correctWordInfoFromApi
        );
      }
      return pair;
    }
  );

  const sortedPairs = [...enhancedFeedbackPairs].sort((a, b) => {
    const aVerseNum = a.verseNumber ?? Infinity;
    const bVerseNum = b.verseNumber ?? Infinity;
    if (aVerseNum !== bVerseNum) {
      return aVerseNum - bVerseNum;
    }
    const aCorrectIndex = a.correctIndex ?? Infinity;
    const bCorrectIndex = b.correctIndex ?? Infinity;
    return aCorrectIndex - bCorrectIndex;
  });

  sortedPairs.forEach((pair, pairIndex) => {
    if (typeof pair.correctWord !== "string") {
      console.warn(
        "[partiallyMatchedWordsFeedback] Skipping pair due to null/undefined correctWord:",
        pair
      );
      return;
    }
    const tempWord: Word = {
      text: pair.correctWord,
      verseNumber: pair.verseNumber ?? 0,
      wordIndex: Math.floor((pair.correctIndex ?? 0) % 100),
      letterCountMismatch: pair.letterCountMismatch,
      matched: false,
      almostMatched: true,
    };
    const uniqueKey = CitationSystem.generateUniqueKey(tempWord);

    if (!citationMap[uniqueKey]) {
      // Only assign new if not in existing
      citationMap[uniqueKey] = nextCitationNumber++;
    }

    const normalizedWord = removeVowelMarkings(pair.correctWord);
    feedbackRefs[normalizedWord] = React.createRef();

    const hasValidAudioMetadata = validateAudioMetadata(
      pair.correctWordInfoFromApi
    );

    console.log(
      `[Enhanced partiallyMatchedWordsFeedback] Pair ${pairIndex} audio validation:`,
      {
        correctWord: pair.correctWord,
        hasValidAudioMetadata,
        audioUrl: pair.correctWordInfoFromApi?.audioUrl,
      }
    );

    if (pair.letterCountMismatch) {
      feedback.push(
        <div
          key={`letter-mismatch-${normalizedWord}-${uniqueKey}`}
          ref={feedbackRefs[normalizedWord]}
          data-word={normalizedWord}
          data-unique-key={uniqueKey}
          data-citation-number={citationMap[uniqueKey].toString()}
          className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-2.5">
              <div
                data-is-citation-badge
                className="inline-flex items-center justify-center w-6 h-6 bg-rose-600 text-white text-base font-medium rounded-full"
              >
                {citationMap[uniqueKey]}
              </div>
            </div>
            <div className="flex items-center justify-center space-x-6 my-4">
              <div className="flex flex-col items-center">
                <span className="text-xs font-medium text-gray-700 mb-1.5">
                  You said
                </span>
                {renderUserWord(
                  pair.userWord,
                  pair.userStart,
                  pair.userEnd,
                  playSegment
                )}
                {playSegment &&
                  pair.userStart !== undefined &&
                  pair.userEnd !== undefined && (
                    <div className="mt-1 text-gray-600">
                      <Volume2 size={18} />
                    </div>
                  )}
              </div>
              <div className="text-gray-300">→</div>
              <div className="flex flex-col items-center">
                <span className="text-xs font-medium text-gray-700 mb-1.5">
                  Correct word
                </span>
                <CorrectWordDisplay
                  word={pair.correctWord}
                  correctWordInfoFromApi={pair.correctWordInfoFromApi}
                />
                {hasValidAudioMetadata && (
                  <div className="mt-1 text-gray-600">
                    <Volume2 size={18} />
                  </div>
                )}
              </div>
            </div>
            <div className="bg-white rounded-xl space-y-4 border border-gray-100">
              {(() => {
                const groupedLetterFeedback = groupExtraAndMissingLetters(
                  pair.letterFeedback || []
                );
                return groupedLetterFeedback.map(
                  (feedbackItem: any, index: number) => {
                    if (!feedbackItem) return null;
                    if (feedbackItem.type === "extraGroup") {
                      return (
                        <div
                          key={`extra-group-${index}`}
                          className="p-3 rounded-lg space-y-1.5"
                        >
                          {" "}
                          <div className="flex items-center justify-between">
                            {" "}
                            <div className="flex items-center space-x-2">
                              {" "}
                              <div className="text-sm font-medium text-red-600">
                                {" "}
                                Extra Letters{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="text-sm text-red-500">
                              {" "}
                              {feedbackItem.extras[0].position}{" "}
                            </div>{" "}
                          </div>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            You said extra letters:{" "}
                            <span className="ml-2 space-x-1">
                              {" "}
                              {feedbackItem.extras.map(
                                (ex: any, idx: number) => (
                                  <span
                                    key={idx}
                                    className="bg-red-50 text-red-600 rounded-lg text-2xl px-2.5 py-1.5 inline-block"
                                  >
                                    {" "}
                                    {ex.userLetter}{" "}
                                  </span>
                                )
                              )}{" "}
                            </span>{" "}
                          </p>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            These letters should be removed from your recitation{" "}
                          </p>{" "}
                        </div>
                      );
                    }
                    if (feedbackItem.type === "missingGroup") {
                      return (
                        <div
                          key={`missing-group-${index}`}
                          className="p-3 rounded-lg space-y-1.5"
                        >
                          {" "}
                          <div className="flex items-center justify-between">
                            {" "}
                            <div className="flex items-center space-x-2">
                              {" "}
                              <div className="text-sm font-medium text-blue-600">
                                {" "}
                                Missing Letters{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="text-sm text-blue-500">
                              {" "}
                              {feedbackItem.missing[0].position}{" "}
                            </div>{" "}
                          </div>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            You missed these letters:{" "}
                            <span className="ml-2 space-x-1">
                              {" "}
                              {feedbackItem.missing.map(
                                (m: any, idx: number) => (
                                  <span
                                    key={idx}
                                    className="bg-blue-50 text-blue-600 rounded-lg text-2xl px-2.5 py-1.5 inline-block"
                                  >
                                    {" "}
                                    {m.correctLetter}{" "}
                                  </span>
                                )
                              )}{" "}
                            </span>{" "}
                          </p>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            These letters need to be added to your recitation{" "}
                          </p>{" "}
                        </div>
                      );
                    }
                    switch (feedbackItem.type) {
                      case "substitution":
                        return (
                          <div
                            key={`letter-feedback-${index}`}
                            className="space-y-3 p-2.5"
                          >
                            {" "}
                            <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                              {" "}
                              <div className="flex items-center space-x-3">
                                {" "}
                                <div className="flex flex-col items-center">
                                  {" "}
                                  <span className="text-xs font-medium text-gray-500 mb-1">
                                    {" "}
                                    Your letter{" "}
                                  </span>{" "}
                                  <span className="bg-red-50 text-red-600 rounded-lg text-3xl px-2.5 py-1.5">
                                    {" "}
                                    {feedbackItem.userLetter}{" "}
                                  </span>{" "}
                                </div>{" "}
                                <div className="text-gray-300">→</div>{" "}
                                <div className="flex flex-col items-center">
                                  {" "}
                                  <span className="text-xs font-medium text-gray-500 mb-1">
                                    {" "}
                                    Correct letter{" "}
                                  </span>{" "}
                                  <span className="bg-green-50 text-green-600 rounded-lg text-3xl px-2.5 py-1.5">
                                    {" "}
                                    {feedbackItem.correctLetter}{" "}
                                  </span>{" "}
                                </div>{" "}
                              </div>{" "}
                              <div className="text-sm text-gray-500">
                                {" "}
                                {feedbackItem.position}{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="ml-3 space-y-1.5">
                              {" "}
                              <p className="text-sm text-gray-700">
                                {" "}
                                <span className="font-medium">
                                  {" "}
                                  Pronunciation Tip:{" "}
                                </span>{" "}
                                {feedbackItem.correctLetterSound}{" "}
                              </p>{" "}
                              {feedbackItem.vowelInfo !==
                                "no vowel markings" && (
                                <p className="text-sm text-gray-700">
                                  {" "}
                                  <span className="font-medium">
                                    Vowel:
                                  </span>{" "}
                                  {feedbackItem.vowelInfo}{" "}
                                  {feedbackItem.vowelExplanation &&
                                    ` - ${feedbackItem.vowelExplanation}`}{" "}
                                </p>
                              )}{" "}
                            </div>{" "}
                          </div>
                        );
                      case "extra":
                        return (
                          <div
                            key={`letter-feedback-${index}`}
                            className="p-3 rounded-lg space-y-1.5"
                          >
                            {" "}
                            <div className="flex items-center justify-between">
                              {" "}
                              <div className="flex items-center space-x-2">
                                {" "}
                                <span className="bg-red-50 text-red-600 rounded-lg text-xl px-2.5 py-1.5">
                                  {" "}
                                  {feedbackItem.userLetter}{" "}
                                </span>{" "}
                                <div className="text-sm font-medium text-red-600">
                                  {" "}
                                  Extra Letter{" "}
                                </div>{" "}
                              </div>{" "}
                              <div className="text-sm text-red-500">
                                {" "}
                                {feedbackItem.position}{" "}
                              </div>{" "}
                            </div>{" "}
                            <p className="text-sm text-gray-700">
                              {" "}
                              This letter should be removed from your recitation{" "}
                            </p>{" "}
                          </div>
                        );
                      case "missing":
                        return (
                          <div
                            key={`letter-feedback-${index}`}
                            className="p-3 rounded-lg space-y-1.5"
                          >
                            {" "}
                            <div className="flex items-center justify-between">
                              {" "}
                              <div className="flex items-center space-x-2">
                                {" "}
                                <span className="bg-blue-50 text-blue-600 rounded-lg text-xl px-2.5 py-1.5">
                                  {" "}
                                  {feedbackItem.correctLetter}{" "}
                                </span>{" "}
                                <div className="text-sm font-medium text-blue-600">
                                  {" "}
                                  Missing Letter{" "}
                                </div>{" "}
                              </div>{" "}
                              <div className="text-sm text-blue-500">
                                {" "}
                                {feedbackItem.position}{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="space-y-1.5">
                              {" "}
                              <p className="text-sm text-gray-700">
                                {" "}
                                This letter needs to be added to your recitation{" "}
                              </p>{" "}
                              {feedbackItem.vowelInfo !==
                                "no vowel markings" && (
                                <p className="text-sm text-gray-700">
                                  {" "}
                                  <span className="font-medium">
                                    Vowel:
                                  </span>{" "}
                                  {feedbackItem.vowelInfo}{" "}
                                  {feedbackItem.vowelExplanation &&
                                    ` - ${feedbackItem.vowelExplanation}`}{" "}
                                </p>
                              )}{" "}
                              <p className="text-sm text-gray-700">
                                {" "}
                                <span className="font-medium">
                                  {" "}
                                  Pronunciation Tip:{" "}
                                </span>{" "}
                                {feedbackItem.correctLetterSound}{" "}
                              </p>{" "}
                            </div>{" "}
                          </div>
                        );
                      default:
                        return null;
                    }
                  }
                );
              })()}
            </div>
            {/* START: New Tajweed Rule Section */}
            <div className="bg-indigo-50 rounded-xl p-3 mt-4 border border-indigo-100">
              <p className="text-sm text-gray-800 leading-relaxed">
                <span className="font-semibold text-indigo-700">
                  Tajweed Rule:
                </span>
                {/* Placeholder for future dynamic content */}
              </p>
            </div>
            {/* END: New Tajweed Rule Section */}
            <div className="bg-gray-50 rounded-xl p-3 mt-1">
              <p className="text-sm text-gray-700 leading-relaxed">
                {difficulty === "easy"
                  ? "You're close! Review the letter comparisons above and keep practicing to perfect your pronunciation."
                  : "Good try! Check the detailed letter feedback above to improve your accuracy."}
              </p>
            </div>
          </div>
        </div>
      );
    } else {
      feedback.push(
        <div
          key={`partial-feedback-${normalizedWord}-${uniqueKey}`}
          ref={feedbackRefs[normalizedWord]}
          data-word={normalizedWord}
          data-unique-key={uniqueKey}
          data-citation-number={citationMap[uniqueKey].toString()}
          className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-2.5">
              <div
                data-is-citation-badge
                className="inline-flex items-center justify-center w-6 h-6 bg-rose-600 text-white text-base font-medium rounded-full"
              >
                {citationMap[uniqueKey]}
              </div>
            </div>
            <div className="flex items-center justify-center space-x-6 my-4">
              <div className="flex flex-col items-center">
                <span className="text-xs font-medium text-gray-700 mb-1.5">
                  You said
                </span>
                {renderUserWord(
                  pair.userWord,
                  pair.userStart,
                  pair.userEnd,
                  playSegment
                )}
                {playSegment &&
                  pair.userStart !== undefined &&
                  pair.userEnd !== undefined && (
                    <div className="mt-1 text-gray-600">
                      <Volume2 size={18} />
                    </div>
                  )}
              </div>
              <div className="text-gray-300">→</div>
              <div className="flex flex-col items-center">
                <span className="text-xs font-medium text-gray-700 mb-1.5">
                  Correct word
                </span>
                <CorrectWordDisplay
                  word={pair.correctWord}
                  correctWordInfoFromApi={pair.correctWordInfoFromApi}
                />
                {hasValidAudioMetadata && (
                  <div className="mt-1 text-gray-600">
                    <Volume2 size={18} />
                  </div>
                )}
              </div>
            </div>
            <div className="bg-white rounded-xl space-y-4 border border-gray-100">
              {(() => {
                const groupedLetterFeedback = groupExtraAndMissingLetters(
                  pair.letterFeedback || []
                );
                return groupedLetterFeedback.map(
                  (feedbackItem: any, index: number) => {
                    if (!feedbackItem) return null;
                    if (feedbackItem.type === "extraGroup") {
                      return (
                        <div
                          key={`extra-group-${index}`}
                          className="p-3 rounded-lg space-y-1.5"
                        >
                          {" "}
                          <div className="flex items-center justify-between">
                            {" "}
                            <div className="flex items-center space-x-2">
                              {" "}
                              <div className="text-sm font-medium text-red-600">
                                {" "}
                                Extra Letters{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="text-sm text-red-500">
                              {" "}
                              {feedbackItem.extras[0].position}{" "}
                            </div>{" "}
                          </div>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            You said extra letters:{" "}
                            <span className="ml-2 space-x-1">
                              {" "}
                              {feedbackItem.extras.map(
                                (ex: any, idx: number) => (
                                  <span
                                    key={idx}
                                    className="bg-red-50 text-red-600 rounded-lg text-2xl px-2.5 py-1.5 inline-block"
                                  >
                                    {" "}
                                    {ex.userLetter}{" "}
                                  </span>
                                )
                              )}{" "}
                            </span>{" "}
                          </p>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            These letters should be removed from your recitation{" "}
                          </p>{" "}
                        </div>
                      );
                    }
                    if (feedbackItem.type === "missingGroup") {
                      return (
                        <div
                          key={`missing-group-${index}`}
                          className="p-3 rounded-lg space-y-1.5"
                        >
                          {" "}
                          <div className="flex items-center justify-between">
                            {" "}
                            <div className="flex items-center space-x-2">
                              {" "}
                              <div className="text-sm font-medium text-blue-600">
                                {" "}
                                Missing Letters{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="text-sm text-blue-500">
                              {" "}
                              {feedbackItem.missing[0].position}{" "}
                            </div>{" "}
                          </div>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            You missed these letters:{" "}
                            <span className="ml-2 space-x-1">
                              {" "}
                              {feedbackItem.missing.map(
                                (m: any, idx: number) => (
                                  <span
                                    key={idx}
                                    className="bg-blue-50 text-blue-600 rounded-lg text-2xl px-2.5 py-1.5 inline-block"
                                  >
                                    {" "}
                                    {m.correctLetter}{" "}
                                  </span>
                                )
                              )}{" "}
                            </span>{" "}
                          </p>{" "}
                          <p className="text-sm text-gray-700">
                            {" "}
                            These letters need to be added to your recitation{" "}
                          </p>{" "}
                        </div>
                      );
                    }
                    switch (feedbackItem.type) {
                      case "substitution":
                        return (
                          <div
                            key={`letter-feedback-${index}`}
                            className="space-y-3 p-2.5"
                          >
                            {" "}
                            <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                              {" "}
                              <div className="flex items-center space-x-3">
                                {" "}
                                <div className="flex flex-col items-center">
                                  {" "}
                                  <span className="text-xs font-medium text-gray-500 mb-1">
                                    {" "}
                                    Your letter{" "}
                                  </span>{" "}
                                  <span className="bg-red-50 text-red-600 rounded-lg text-3xl px-2.5 py-1.5">
                                    {" "}
                                    {feedbackItem.userLetter}{" "}
                                  </span>{" "}
                                </div>{" "}
                                <div className="text-gray-300">→</div>{" "}
                                <div className="flex flex-col items-center">
                                  {" "}
                                  <span className="text-xs font-medium text-gray-500 mb-1">
                                    {" "}
                                    Correct letter{" "}
                                  </span>{" "}
                                  <span className="bg-green-50 text-green-600 rounded-lg text-3xl px-2.5 py-1.5">
                                    {" "}
                                    {feedbackItem.correctLetter}{" "}
                                  </span>{" "}
                                </div>{" "}
                              </div>{" "}
                              <div className="text-sm text-gray-500">
                                {" "}
                                {feedbackItem.position}{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="ml-3 space-y-1.5">
                              {" "}
                              <p className="text-sm text-gray-700">
                                {" "}
                                <span className="font-medium">
                                  {" "}
                                  Pronunciation Tip:{" "}
                                </span>{" "}
                                {feedbackItem.correctLetterSound}{" "}
                              </p>{" "}
                              {feedbackItem.vowelInfo !==
                                "no vowel markings" && (
                                <p className="text-sm text-gray-700">
                                  {" "}
                                  <span className="font-medium">
                                    Vowel:
                                  </span>{" "}
                                  {feedbackItem.vowelInfo}{" "}
                                  {feedbackItem.vowelExplanation &&
                                    ` - ${feedbackItem.vowelExplanation}`}{" "}
                                </p>
                              )}{" "}
                            </div>{" "}
                          </div>
                        );
                      case "extra":
                        return (
                          <div
                            key={`letter-feedback-${index}`}
                            className="p-3 rounded-lg space-y-1.5"
                          >
                            {" "}
                            <div className="flex items-center justify-between">
                              {" "}
                              <div className="flex items-center space-x-2">
                                {" "}
                                <span className="bg-red-50 text-red-600 rounded-lg text-xl px-2.5 py-1.5">
                                  {" "}
                                  {feedbackItem.userLetter}{" "}
                                </span>{" "}
                                <div className="text-sm font-medium text-red-600">
                                  {" "}
                                  Extra Letter{" "}
                                </div>{" "}
                              </div>{" "}
                              <div className="text-sm text-red-500">
                                {" "}
                                {feedbackItem.position}{" "}
                              </div>{" "}
                            </div>{" "}
                            <p className="text-sm text-gray-700">
                              {" "}
                              This letter should be removed from your recitation{" "}
                            </p>{" "}
                          </div>
                        );
                      case "missing":
                        return (
                          <div
                            key={`letter-feedback-${index}`}
                            className="p-3 rounded-lg space-y-1.5"
                          >
                            {" "}
                            <div className="flex items-center justify-between">
                              {" "}
                              <div className="flex items-center space-x-2">
                                {" "}
                                <span className="bg-blue-50 text-blue-600 rounded-lg text-xl px-2.5 py-1.5">
                                  {" "}
                                  {feedbackItem.correctLetter}{" "}
                                </span>{" "}
                                <div className="text-sm font-medium text-blue-600">
                                  {" "}
                                  Missing Letter{" "}
                                </div>{" "}
                              </div>{" "}
                              <div className="text-sm text-blue-500">
                                {" "}
                                {feedbackItem.position}{" "}
                              </div>{" "}
                            </div>{" "}
                            <div className="space-y-1.5">
                              {" "}
                              <p className="text-sm text-gray-700">
                                {" "}
                                This letter needs to be added to your recitation{" "}
                              </p>{" "}
                              {feedbackItem.vowelInfo !==
                                "no vowel markings" && (
                                <p className="text-sm text-gray-700">
                                  {" "}
                                  <span className="font-medium">
                                    Vowel:
                                  </span>{" "}
                                  {feedbackItem.vowelInfo}{" "}
                                  {feedbackItem.vowelExplanation &&
                                    ` - ${feedbackItem.vowelExplanation}`}{" "}
                                </p>
                              )}{" "}
                              <p className="text-sm text-gray-700">
                                {" "}
                                <span className="font-medium">
                                  {" "}
                                  Pronunciation Tip:{" "}
                                </span>{" "}
                                {feedbackItem.correctLetterSound}{" "}
                              </p>{" "}
                            </div>{" "}
                          </div>
                        );
                      default:
                        return null;
                    }
                  }
                );
              })()}
            </div>
            {/* START: New Tajweed Rule Section */}
            <div className="bg-indigo-50 rounded-xl p-3 mt-4 border border-indigo-100">
              <p className="text-sm text-gray-800 leading-relaxed">
                <span className="font-semibold text-indigo-700">
                  Tajweed Rule:
                </span>
                {/* Placeholder for future dynamic content */}
              </p>
            </div>
            {/* END: New Tajweed Rule Section */}
            <div className="bg-gray-50 rounded-xl p-3 mt-1">
              <p className="text-sm text-gray-700 leading-relaxed">
                {difficulty === "easy"
                  ? "You're close! Review the letter comparisons above and keep practicing to perfect your pronunciation."
                  : "Good try! Check the detailed letter feedback above to improve your accuracy."}
              </p>
            </div>
          </div>
        </div>
      );
    }

    if (sessionId && pair.correctWord) {
      feedbackData.push({
        sessionId,
        verseId: null,
        feedbackType: "Pronunciation",
        feedbackDetails: `Partial match in word '${
          pair.correctWord
        }'. User said: '${pair.userWord || ""}'`,
      });
    }
    if (pair.correctWord) unmatchedWords.push(pair.correctWord);
  });

  const pairsWithAudio = enhancedFeedbackPairs.filter((pair) =>
    validateAudioMetadata(pair.correctWordInfoFromApi)
  ).length;
  console.log(`[Enhanced partiallyMatchedWordsFeedback] Final result:`, {
    totalPairs: enhancedFeedbackPairs.length,
    pairsWithValidAudio: pairsWithAudio,
    feedbackItemsGenerated: feedback.length,
    citationsCreated: Object.keys(citationMap).length,
  });

  return { feedback, unmatchedWords, citationMap };
};

/* ================================================================== */
/*  MATCHED-WORDS  (still disabled)                                   */
/* ================================================================== */
export const matchedWordsFeedback = (
  wordAlignment: any[],
  feedbackData: any[],
  feedbackRefs: { [key: string]: React.RefObject<HTMLDivElement> },
  difficulty: Difficulty,
  sessionId: number | null
): { feedback: JSX.Element[]; unmatchedWords: string[] } => {
  return { feedback: [], unmatchedWords: [] };
};

/* ================================================================== */
/*  🆕 ENHANCED GENERATE FEEDBACK – with simplified missing words     */
/* ================================================================== */
export const generateFeedback = (
  wordAlignment: any[],
  finalUnmatchedCorrectWords: any[],
  feedbackPairs: any[], // This is already otherPairs from memorization.tsx
  updatedDisplayedWords: { [verseNumber: number]: any[] },
  verses: Verse[],
  sessionId: number | null,
  memorizedVerseNumbers: number[],
  wordVerseNumbers: number[],
  feedbackRefs: { [key: string]: React.RefObject<HTMLDivElement> },
  difficulty: "easy" | "hard",
  completionRate: number,
  playSegment?: (s: number, e: number) => void
): {
  combinedFeedback: JSX.Element[];
  feedbackData: any[];
  citationMap: Record<string, number>;
  plainFeedbackText?: string; // Kept as per original signature
} => {
  const feedbackDataInternal: any[] = []; // Use a local var to pass to sub-functions
  let finalCitationMap: Record<string, number> = {}; // Initialize to be built by existing logic

  console.log(
    `[Enhanced generateFeedback] Starting with ${feedbackPairs.length} feedback pairs`
  );

  const pairsWithAudio = feedbackPairs.filter(
    (pair) =>
      pair.correctWordInfoFromApi &&
      validateAudioMetadata(pair.correctWordInfoFromApi)
  ).length;

  console.log(`[Enhanced generateFeedback] Audio metadata validation:`, {
    totalPairs: feedbackPairs.length,
    pairsWithValidAudio: pairsWithAudio,
    audioAvailabilityRate:
      feedbackPairs.length > 0
        ? ((pairsWithAudio / feedbackPairs.length) * 100).toFixed(1) + "%"
        : "0%",
  });

  const existingCitationMap: Record<string, number> = {};
  Object.entries(updatedDisplayedWords).forEach(([verseNumber, words]) => {
    words.forEach((word, wordIndex) => {
      if (word.feedbackNumber !== undefined) {
        const tempWord: Word = {
          text: word.text,
          verseNumber: parseInt(verseNumber),
          wordIndex,
          letterCountMismatch: word.letterCountMismatch || false,
          matched: word.matched || false,
          almostMatched: word.almostMatched || false,
        };
        const uniqueKey = CitationSystem.generateUniqueKey(tempWord);
        existingCitationMap[uniqueKey] = word.feedbackNumber;
        existingCitationMap[`text:${word.text}`] = word.feedbackNumber;
      }
    });
  });

  const flattenedWordsForCitationBackup = CitationSystem.flattenDisplayedWords(
    updatedDisplayedWords
  );
  flattenedWordsForCitationBackup.forEach((word) => {
    if (!word.matched && (word.almostMatched || word.letterCountMismatch)) {
      const uniqueKey = CitationSystem.generateUniqueKey(word);
      const displayedWord = updatedDisplayedWords[word.verseNumber]?.find(
        (w) => w.text === word.text && w.feedbackNumber !== undefined
      );
      if (displayedWord && displayedWord.feedbackNumber) {
        existingCitationMap[uniqueKey] = displayedWord.feedbackNumber;
        existingCitationMap[`text:${word.text}`] = displayedWord.feedbackNumber;
      }
    }
  });

  // Call extraWordsFeedback as in the original code
  const { feedback: extraFeedbackGenerated } = extraWordsFeedback(
    wordAlignment.filter(
      ({ verseNumber }: any) =>
        verseNumber === null || !memorizedVerseNumbers.includes(verseNumber)
    ),
    sessionId,
    feedbackDataInternal, // Pass internal accumulator
    feedbackRefs,
    difficulty
  );

  // MODIFIED CALL: Use finalUnmatchedCorrectWords directly for missingWordsFeedback
  const { feedback: missingFeedbackGenerated } = missingWordsFeedback(
    finalUnmatchedCorrectWords, // Pass directly
    feedbackRefs,
    difficulty
  );

  // Call partiallyMatchedWordsFeedback as in the original code (with its own filtering)
  const {
    feedback: partialFeedbackGenerated,
    citationMap: newCitationMapFromPartial,
  } = partiallyMatchedWordsFeedback(
    feedbackPairs.filter(
      (pair) =>
        pair.verseNumber !== null &&
        pair.verseNumber !== undefined &&
        !memorizedVerseNumbers.includes(pair.verseNumber)
    ),
    sessionId,
    feedbackDataInternal, // Pass internal accumulator
    feedbackRefs,
    difficulty,
    existingCitationMap,
    playSegment
  );

  // Preserve original citation map logic
  finalCitationMap = { ...finalCitationMap, ...newCitationMapFromPartial };

  const feedbackUniqueKeys = new Set<string>();
  partialFeedbackGenerated.forEach((element) => {
    const uniqueKey = element.props["data-unique-key"];
    if (uniqueKey) {
      feedbackUniqueKeys.add(uniqueKey);
    }
  });

  const flattenedWords = CitationSystem.flattenDisplayedWords(
    updatedDisplayedWords
  );
  const systemGeneratedCitations =
    CitationSystem.generateCitations(flattenedWords);

  const enhancedCitations: Record<string, number> = {};
  Object.entries(finalCitationMap).forEach(([key, value]) => {
    enhancedCitations[key] = value;
  });
  Object.entries(systemGeneratedCitations).forEach(
    ([uniqueKey, citationNumber]) => {
      enhancedCitations[uniqueKey] =
        finalCitationMap[uniqueKey] || citationNumber;
    }
  );

  let highestCitationNumber = 0;
  Object.values(enhancedCitations).forEach((num) => {
    if (num > highestCitationNumber) {
      highestCitationNumber = num;
    }
  });

  Object.entries(updatedDisplayedWords).forEach(
    ([verseNumberStr, verseWords]) => {
      const verseNumber = parseInt(verseNumberStr);
      if (verseNumber <= 2 || memorizedVerseNumbers.includes(verseNumber))
        return;
      verseWords.forEach((word, wordIndex) => {
        if (word.given) return;
        if (
          (word.almostMatched || word.letterCountMismatch || !word.matched) &&
          word.text
        ) {
          const uniqueKey = CitationSystem.generateUniqueKey({
            text: word.text,
            verseNumber: verseNumber,
            wordIndex,
            letterCountMismatch: word.letterCountMismatch || false,
            matched: word.matched || false,
            almostMatched: word.almostMatched || false,
          });
          if (!enhancedCitations[uniqueKey]) {
            enhancedCitations[uniqueKey] = ++highestCitationNumber;
            console.log(
              `Assigned new citation #${highestCitationNumber} to word "${word.text}" (${uniqueKey})`
            );
          }
        }
      });
    }
  );

  CitationSystem.applyCitations(updatedDisplayedWords, enhancedCitations);
  finalCitationMap = enhancedCitations; // This is the final map based on original logic

  const updateCitationInFeedback = (
    elements: JSX.Element[],
    currentCitationMap: Record<string, number> // Use the most up-to-date map
  ): JSX.Element[] => {
    return elements.map((element: React.ReactElement<any>) => {
      const elementProps = element.props as any;
      const uniqueKey = elementProps["data-unique-key"];
      const dataWord = elementProps["data-word"];
      let citationToUse: number | undefined = undefined;

      if (uniqueKey && currentCitationMap[uniqueKey]) {
        citationToUse = currentCitationMap[uniqueKey];
      } else if (dataWord && currentCitationMap[`text:${dataWord}`]) {
        // Fallback
        citationToUse = currentCitationMap[`text:${dataWord}`];
      }

      if (citationToUse === undefined) return element; // No citation found for this element

      const updateNode = (node: React.ReactNode): React.ReactNode => {
        if (!React.isValidElement(node)) return node;
        const nodeProps = { ...(node.props as any) };
        if (nodeProps["data-is-citation-badge"]) {
          return React.cloneElement(node as React.ReactElement<any>, {
            ...nodeProps,
            children: citationToUse,
          });
        }
        if (nodeProps.children) {
          if (Array.isArray(nodeProps.children)) {
            nodeProps.children = React.Children.map(
              nodeProps.children,
              (child) => updateNode(child)
            );
          } else {
            nodeProps.children = updateNode(nodeProps.children);
          }
        }
        return React.cloneElement(node as React.ReactElement<any>, nodeProps);
      };
      const updatedChildren = updateNode(elementProps.children);
      return React.cloneElement(element, {
        ...elementProps,
        "data-citation-number": citationToUse.toString(),
        children: updatedChildren,
      } as any);
    });
  };

  const updatedPartialFeedbackElements = updateCitationInFeedback(
    partialFeedbackGenerated,
    finalCitationMap // Use the fully reconciled map
  );

  const extractWordsFromFeedback = (feedbackArray: JSX.Element[]): string[] => {
    const words: string[] = [];
    feedbackArray.forEach((element) => {
      const word = element.props?.["data-word"];
      if (word && typeof word === "string") {
        words.push(word);
      }
    });
    return words;
  };

  const extraWordsFromFeedback = extractWordsFromFeedback(
    extraFeedbackGenerated
  );
  const partialWordsFromFeedback = extractWordsFromFeedback(
    updatedPartialFeedbackElements
  );
  const alternativeFeedbackWords = new Set([
    ...extraWordsFromFeedback,
    ...partialWordsFromFeedback,
  ]);

  // Use missingFeedbackGenerated (from the modified call) for filtering
  const filteredMissingFeedbackElements = missingFeedbackGenerated.filter(
    (element) => {
      const word = element.props?.["data-word"];
      if (word && alternativeFeedbackWords.has(word)) {
        return false;
      }
      return true;
    }
  );

  // MODIFIED ORDER for combinedFeedback
  const combinedFeedbackResult = [
    ...filteredMissingFeedbackElements, // Missing first (after filtering)
    ...updatedPartialFeedbackElements, // Then partial (after citation updates)
    ...extraFeedbackGenerated, // Then extra
  ];

  // --- Preserve original summary card logic ---
  const nonInitialVerses = verses.filter((v) => v.verseNumber > 2);
  const allVersesMemorized = nonInitialVerses.every((v) =>
    memorizedVerseNumbers.includes(v.verseNumber)
  );
  const surahFullyMemorized = completionRate >= 99.9;
  const allWordsTrulyMatched = !Object.values(updatedDisplayedWords)
    .flat()
    .some(
      (word) => word && word.verseNumber > 2 && !word.given && !word.matched
    );

  if (surahFullyMemorized && allWordsTrulyMatched) {
    combinedFeedbackResult.unshift(
      /* Congratulations! Perfect Recitation ... */
      <div
        className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        key="congratulations-feedback"
        data-word="congratulations-feedback"
      >
        {" "}
        <div className="flex items-center space-x-3">
          {" "}
          <div className="w-2.5 h-2.5 bg-green-500 rounded-full" />{" "}
          <p className="text-lg font-semibold text-gray-900 tracking-tight">
            {" "}
            Congratulations! Perfect Recitation{" "}
          </p>{" "}
        </div>{" "}
        <div className="bg-gray-50 rounded-xl p-3 mt-4">
          {" "}
          <p className="text-sm text-gray-700 leading-relaxed">
            {" "}
            Outstanding achievement! You&apos;ve perfectly recited all the
            required verses. Your dedication shows in your flawless delivery.{" "}
          </p>{" "}
        </div>{" "}
      </div>
    );
  } else if (surahFullyMemorized && !allWordsTrulyMatched) {
    combinedFeedbackResult.unshift(
      /* Congratulations! You've completed all verses ... */
      <div
        className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        key="all-memorized-with-issues"
        data-word="all-memorized-with-issues"
      >
        {" "}
        <div className="flex items-center space-x-3">
          {" "}
          <div className="w-2.5 h-2.5 bg-amber-500 rounded-full" />{" "}
          <p className="text-lg font-semibold text-gray-900 tracking-tight">
            {" "}
            Congratulations! You&apos;ve completed all verses{" "}
          </p>{" "}
        </div>{" "}
        <div className="bg-gray-50 rounded-xl p-3 mt-4">
          {" "}
          <p className="text-sm text-gray-700 leading-relaxed">
            {" "}
            Excellent work completing all the verses! There are still a few
            words marked for review (check citations). Practice these specific
            words to achieve perfection.{" "}
          </p>{" "}
        </div>{" "}
      </div>
    );
  } else if (!surahFullyMemorized && allWordsTrulyMatched) {
    combinedFeedbackResult.unshift(
      /* Excellent Recitation! ... */
      <div
        className="mb-6 bg-white rounded-2xl p-5 shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md"
        key="perfect-recitation-partial"
        data-word="perfect-recitation-partial"
      >
        {" "}
        <div className="flex items-center space-x-3">
          {" "}
          <div className="w-2.5 h-2.5 bg-green-500 rounded-full" />{" "}
          <p className="text-lg font-semibold text-gray-900 tracking-tight">
            {" "}
            Excellent Recitation!{" "}
          </p>{" "}
        </div>{" "}
        <div className="bg-gray-50 rounded-xl p-3 mt-4">
          {" "}
          <p className="text-sm text-gray-700 leading-relaxed">
            {" "}
            Masha&apos;Allah! You&apos;ve recited the current verses perfectly.
            Keep up the great work as you continue memorizing!{" "}
          </p>{" "}
        </div>{" "}
      </div>
    );
  }

  const plainFeedbackTextResult = combinedFeedbackResult // Use the final combined result
    .map((element) => extractTextFromJSX(element))
    .join(" ");

  console.log(`[Enhanced generateFeedback] Final result:`, {
    combinedFeedbackItems: combinedFeedbackResult.length,
    citationsGenerated: Object.keys(finalCitationMap).length,
    surahFullyMemorized,
    allWordsTrulyMatched,
    audioMetadataPreserved: pairsWithAudio > 0,
  });

  return {
    combinedFeedback: combinedFeedbackResult,
    feedbackData: feedbackDataInternal, // Return the accumulated data
    citationMap: finalCitationMap, // Return the fully processed citation map
    plainFeedbackText: plainFeedbackTextResult, // Return plain text as originally done
  };
};
