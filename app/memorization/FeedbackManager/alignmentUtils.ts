// alignmentUtils.ts

import levenshtein from "js-levenshtein";
import { getVowelExplanation, getPhoneticSound } from "../phonetics";
import { cloneDeep } from "lodash";

/* ------------------------------------------------------------------ */
/*  🆕  ENHANCED TYPES WITH AUDIO METADATA SUPPORT                     */
/* ------------------------------------------------------------------ */
export interface WordTiming {
  start?: number; // seconds
  end?: number; // seconds
}

export interface Letter {
  baseLetter: string;
  vowelMarks: string[];
  originalChar: string;
}

// Define the Difficulty type
type Difficulty = "easy" | "hard";

// 🆕 Enhanced interfaces for API word feedback with audio metadata
export interface CorrectWordInfo {
  text: string;
  surah: number;
  ayah: number;
  wordInAyahIndex: number;
  audioUrl: string;
}

export interface ApiWordFeedback {
  word: string;
  start: number;
  end: number;
  correctWordInfo?: CorrectWordInfo;
}

// 🆕 Enhanced alignment item interface with audio metadata
export interface AlignedWordWithCorrectInfo {
  userWord: string | null;
  correctWord: string | null;
  matched: boolean;
  similarity: number;
  userIndex: number;
  correctIndex: number;
  verseNumber: number | null;
  start?: number; // User's word start time
  end?: number; // User's word end time
  correctWordInfoFromApi?: CorrectWordInfo; // Audio metadata for the correct word
}

// 🆕 Enhanced feedback pair interface with audio metadata
export interface FeedbackPairForDetailedCard {
  userWord: string | null; // Allow null
  correctWord: string; // The displayable correct word (with original vowels)
  letterFeedback?: any[];
  userStart?: number;
  userEnd?: number;
  similarity?: number;
  type?: string; // "letter_count_mismatch", "partial", "unmatched", "missing", "extra"
  verseNumber?: number | null;
  letterCountMismatch?: boolean;
  correctIndex?: number;
  userLetterCount?: number;
  correctLetterCount?: number;
  correctWordInfoFromApi?: CorrectWordInfo; // This is the crucial part that passes the audio URL
}

/* ------------------------------------------------------------------ */
/*  CONSTANTS & LOOK-UPS                                              */
/* ------------------------------------------------------------------ */
export const vowelMap: { [key: string]: string } = {
  "\u064B": "Fathatan",
  "\u064C": "Dammatan",
  "\u064D": "Kasratan",
  "\u064E": "Fatha",
  "\u064F": "Damma",
  "\u0650": "Kasra",
  "\u0651": "Shadda",
  "\u0652": "Sukun",
  "\u0653": "Maddah",
  "\u0654": "Hamza Above",
  "\u0655": "Hamza Below",
};

// ====================== Normalization & Confusion Handling ======================

/**
 * Normalizes Arabic text for comparison purposes by:
 * 1. Standardizing different forms of Alif (ٱأإآء) to plain Alif (ا).
 * 2. Standardizing Alif Maqsura (ى) to Yaa (ي).
 * 3. Normalizing Taa Marbuta (ة) to Haa (ه).
 * 4. Replacing dagger alif (ٰ) following any Arabic base letter with standard Alif (ا).
 * 5. Removing all diacritics (vowel markings, shadda, sukoon etc. U+064B to U+065F).
 * 6. Removing Tatweel (kashida U+0640).
 */
export const normalizeForComparison = (text: string): string => {
  if (typeof text !== "string") {
    return "";
  }

  let normalized = text;

  // Standardize letter variations
  normalized = normalized
    .replace(/[ٱأإآء]/g, "ا") // Alif variations
    .replace(/ى/g, "ي") // Alif Maqsura to Yaa
    .replace(/ة/g, "ه"); // Taa Marbuta to Haa

  // Handle dagger alif following any letter
  normalized = normalized.replace(
    /([\u0621-\u064A\u0671-\u06D3][\u064B-\u065F]*)\u0670/g,
    "$1ا"
  );

  // Handle standalone dagger alif
  normalized = normalized.replace(/\u0670/g, "ا");

  // Remove diacritics
  normalized = normalized.replace(/[\u064B-\u065F]/g, "");

  // Remove Tatweel
  normalized = normalized.replace(/\u0640/g, "");

  return normalized;
};

// Define pairs/groups of letters that the transcription AI often confuses
// and which should NOT trigger substitution feedback in compareLetters.
// Key: Correct Letter, Value: Array of commonly confused transcriptions for that letter
const TRANSCRIPTION_CONFUSIONS: Record<string, string[]> = {
  ا: ["ع", "ء", "ٱ", "أ", "إ", "آ"],
  ع: ["ا", "ء", "ٱ", "أ", "إ", "آ"],
  ي: ["ى"],
  ى: ["ي"],
  ه: ["ة"],
  ة: ["ه", "ت", "ط"],
  ت: ["ة", "ط", "ض", "د"],
  ط: ["ت", "ة", "د", "ض"], // <-- ADDED 'ض' here
  ظ: ["ذ", "ض", "ز"],
  ذ: ["ظ", "ز"],
  ض: ["ظ", "ت", "ث", "ط"], // <-- ADDED 'ط' here
  ث: ["س", "ص", "ض"],
  س: ["ص", "ث", "ز"],
  ص: ["س", "ث"],
  ق: ["ك", "غ"],
  ك: ["ق"],
  ح: ["ه", "خ"],
  خ: ["ح", "غ"],
  د: ["ت", "ط"],
  // Add more based on observed transcription errors
};

// Helper function to check if a pair is an acceptable confusion
function isAcceptableConfusion(correctBase: string, userBase: string): boolean {
  if (!correctBase || !userBase) return false;
  // Normalize before checking confusion map for consistency
  const normCorrect = normalizeForComparison(correctBase);
  const normUser = normalizeForComparison(userBase);
  if (normCorrect === normUser) return true; // Already handled by normalization

  // Check confusion map using original base letters
  if (TRANSCRIPTION_CONFUSIONS[correctBase]?.includes(userBase)) {
    return true;
  }
  if (TRANSCRIPTION_CONFUSIONS[userBase]?.includes(correctBase)) {
    return true;
  }
  return false;
}

// ====================== Text Processing Functions ======================

export const removeVowelMarkings = (text: string): string => {
  if (typeof text !== "string") {
    return "";
  }
  try {
    // Keep the original range U+064B to U+0652 if it worked better for simple removal
    // Or use the wider U+064B to U+065F range if normalizeForComparison handles it fine
    const result = text.replace(/[\u064B-\u0652]/g, ""); // Original range
    // const result = text.replace(/[\u064B-\u065F]/g, ""); // Wider range
    return result;
  } catch (error: any) {
    return text;
  }
};

// Enhanced strictLetterCount function (no changes needed)
export const strictLetterCount = (word: string): number => {
  if (typeof word !== "string") return 0;
  const decomposed = word.normalize("NFD");
  const letters = decomposed.match(/[\u0621-\u064A]/g);
  return letters ? letters.length : 0;
};

export const getVowelInfo = (letter: Letter): string => {
  if (
    !letter ||
    typeof letter !== "object" ||
    !Array.isArray(letter.vowelMarks)
  ) {
    return "unknown vowel information";
  }
  if (letter.vowelMarks.length === 0) {
    return "no vowel markings";
  }
  const vowelNames = letter.vowelMarks.map((vowelMark) => {
    return vowelMap[vowelMark] || "unknown vowel";
  });
  return vowelNames.join(", ");
};

// --- REVERTED calculateSimilarity ---
// Calculates similarity based on original strings without normalization
export const calculateSimilarity = (a: string, b: string): number => {
  if (typeof a !== "string" || typeof b !== "string") {
    return 0;
  }

  const lenA = a.length;
  const lenB = b.length;

  if (lenA === 0 && lenB === 0) {
    return 1.0; // Both are empty, considered identical
  }
  if (lenA === 0 || lenB === 0) {
    return 0.0; // One is empty, the other isn't, completely dissimilar
  }

  // Calculate Levenshtein distance on original strings
  const distance = levenshtein(a, b);
  const similarity = 1 - distance / Math.max(lenA, lenB);

  // Ensure similarity is between 0 and 1
  return Math.max(0, Math.min(1, similarity));
};
// --- END REVERT ---

// ===== 2. ENHANCED parseWord() =====
export const parseWord = (word: string): Letter[] => {
  if (typeof word !== "string") {
    return [];
  }
  try {
    const letters: Letter[] = [];
    const normalized = word.normalize("NFD");

    // IMPROVED: Enhanced regex to better handle dagger alif
    // - Now explicitly separates dagger alif into the vowel marks group
    const regex =
      /([\u0621-\u064A\u0671-\u06D3])([ؘؘؙؙؙؚؚؚؐؑؒؓؔؕؖؗؐؑؒؓؔؕؖؗ\u064B-\u065F\u0670]*)|([^\u0600-\u06FF])/g;

    let match;
    while ((match = regex.exec(normalized)) !== null) {
      // Match[1] is the Arabic base letter, Match[2] are the diacritics (including dagger alif)
      // Match[3] is a non-Arabic character
      const baseLetter = match[1] || match[3] || "";
      const vowelMarks = match[2] ? match[2].split("") : [];

      // IMPROVED: Debug logging for dagger alif detection
      if (vowelMarks.includes("\u0670")) {
        console.log(
          "Detected dagger alif in word:",
          word,
          "at base letter:",
          baseLetter
        );
      }

      // Recompose the original character for display/reference if needed
      const originalChar = match[0].normalize("NFC");

      if (baseLetter) {
        // Only push if we have a base letter or non-Arabic char
        letters.push({
          baseLetter,
          vowelMarks,
          originalChar,
        });
      }
    }
    return letters;
  } catch (error: any) {
    console.error("Error parsing word:", word, error);
    return [];
  }
};

export const getWordPositionInVerse = (
  wordIndex: number,
  verseLength: number
): string => {
  if (
    typeof wordIndex !== "number" ||
    typeof verseLength !== "number" ||
    verseLength <= 0 // Ensure verseLength is positive
  ) {
    return "unknown position";
  }
  // Ensure wordIndex is within bounds
  const idx = Math.max(0, Math.min(wordIndex, verseLength - 1));

  const third = Math.ceil(verseLength / 3);
  if (idx < third) {
    return "near the start";
  } else if (idx < 2 * third) {
    return "near the middle";
  } else {
    return "near the end";
  }
};

/* ================================================================== */
/*  🆕 ENHANCED DATA-PREP (now returns timings and validates data)     */
/* ================================================================== */
export const prepareDataStructures = (
  transcribedText: string,
  correctWordsList: string[],
  memorizedVerseNumbers: number[],
  wordVerseNumbers: number[],
  /* 🆕 */ apiWordFeedbackFromWhisper?: ApiWordFeedback[] // New parameter for API tokens
): {
  transcribedWords: string[];
  correctWords: string[];
  /* 🆕 */ userTimings: WordTiming[];
} => {
  if (
    !transcribedText ||
    !Array.isArray(correctWordsList) ||
    !Array.isArray(memorizedVerseNumbers) ||
    !Array.isArray(wordVerseNumbers)
  ) {
    console.error(
      "[alignmentUtils prepareDataStructures] Invalid input provided."
    );
    return { transcribedWords: [], correctWords: [], userTimings: [] };
  }

  console.log(
    "[alignmentUtils prepareDataStructures] Input - transcribedText (first 50 chars):",
    transcribedText.substring(0, 50)
  );
  console.log(
    "[alignmentUtils prepareDataStructures] Input - apiWordFeedbackFromWhisper (sample):",
    apiWordFeedbackFromWhisper
      ? JSON.stringify(apiWordFeedbackFromWhisper.slice(0, 3))
      : "undefined"
  );

  try {
    const memorizedSet = new Set(memorizedVerseNumbers);

    /* ------------ USER SIDE ------------ */
    const rawUserWords = transcribedText
      .split(/\s+/)
      .filter((w) => w.trim() !== "");

    const transcribedWords = rawUserWords.map((w) => normalizeForComparison(w));

    console.log(
      "[alignmentUtils prepareDataStructures] rawUserWords count:",
      rawUserWords.length,
      "transcribedWords count:",
      transcribedWords.length
    );

    /* 🆕 Enhanced timing array building with validation */
    let userTimings: WordTiming[] = new Array(transcribedWords.length).fill({}); // Initialize with empty objects

    if (Array.isArray(apiWordFeedbackFromWhisper)) {
      console.log(
        `[alignmentUtils prepareDataStructures] apiWordFeedbackFromWhisper.length: ${apiWordFeedbackFromWhisper.length}, rawUserWords.length: ${rawUserWords.length}`
      );

      // We derive userTimings directly from `apiWordFeedbackFromWhisper` as it contains the raw token timings.
      // This implicitly assumes that the `rawUserWords` (from splitting transcribedText) align with `apiWordFeedbackFromWhisper`.
      // If they don't, timings might be mismatched.
      userTimings = apiWordFeedbackFromWhisper.map((token, idx) => {
        if (
          token &&
          typeof token.start === "number" &&
          typeof token.end === "number"
        ) {
          if (
            token.start >= 0 &&
            token.end > token.start &&
            token.end - token.start < 30
          ) {
            return { start: token.start, end: token.end };
          } else {
            console.warn(
              `[alignmentUtils prepareDataStructures] Invalid timing values for token at index ${idx}: start=${token.start}, end=${token.end}`
            );
            return {};
          }
        }
        return {};
      });

      if (rawUserWords.length !== apiWordFeedbackFromWhisper.length) {
        console.warn(
          `[alignmentUtils prepareDataStructures] WARNING: rawUserWords.length (${rawUserWords.length}) !== apiWordFeedbackFromWhisper.length (${apiWordFeedbackFromWhisper.length}). This may cause misalignment of user timings.`
        );
      }
    } else {
      console.log(
        "[alignmentUtils prepareDataStructures] apiWordFeedbackFromWhisper is not an array or is undefined. User timings will be empty."
      );
    }

    console.log(
      "[alignmentUtils prepareDataStructures] userTimings built (sample):",
      JSON.stringify(userTimings.slice(0, 5))
    );
    console.log(
      "[alignmentUtils prepareDataStructures] Count of userTimings with actual start times:",
      userTimings.filter((t) => t.start !== undefined).length
    );

    /* ------------ CORRECT SIDE ------------ */
    const correctWordsWithVowels = correctWordsList.filter((_, index) => {
      // Check if wordVerseNumbers[index] exists before accessing it
      const verseNumber =
        wordVerseNumbers.length > index ? wordVerseNumbers[index] : undefined;
      // Only include if verse number is defined and not memorized
      return verseNumber !== undefined && !memorizedSet.has(verseNumber);
    });

    // Prepare correct words for alignment: normalize for alignment base
    const correctWords = correctWordsWithVowels.map((word) =>
      normalizeForComparison(word)
    );

    return { transcribedWords, correctWords, userTimings };
  } catch (error: any) {
    console.error("Error in prepareDataStructures:", error);
    return { transcribedWords: [], correctWords: [], userTimings: [] };
  }
};

// ====================== Matched Words Initialization ======================

// No changes needed in initializeMatchedWords
export const initializeMatchedWords = (
  displayedWords: { [verseNumber: number]: any[] },
  memorizedVerseNumbers: number[],
  wordVerseNumbers: number[]
): Set<number> => {
  const memorizedSet = new Set(memorizedVerseNumbers);
  const matchedCorrectWords = new Set<number>();

  const verseStartIndices: { [verseNumber: number]: number } = {};
  let runningIndex = 0; // Keep track of the global index in the original correctWordsList
  for (let idx = 0; idx < wordVerseNumbers.length; idx++) {
    const verseNumber = wordVerseNumbers[idx];
    // Only map start indices for non-memorized verses as they correspond to the filtered list
    if (!memorizedSet.has(verseNumber)) {
      if (!(verseNumber in verseStartIndices)) {
        verseStartIndices[verseNumber] = runningIndex;
      }
      runningIndex++; // Increment global index only for words included in the comparison
    }
  }

  Object.entries(displayedWords).forEach(([verseNumStr, verseWords]) => {
    const verseNumber = Number(verseNumStr);
    if (memorizedSet.has(verseNumber)) {
      return;
    }

    // Check if verseStartIndex is defined for this verse number
    const verseStartIndex = verseStartIndices[verseNumber];
    if (verseStartIndex === undefined) {
      // This verse wasn't included in the comparison list (likely because it was filtered out)
      // Or there's an issue with indexing. For safety, skip.
      // console.warn(`Verse start index not found for verse ${verseNumber} during matched word initialization.`);
      return;
    }

    verseWords.forEach((wordObj: any, wordIndexInVerse: number) => {
      // wordObj.correctIndex should ideally refer to the index in the *original* full correctWordsList
      // However, the alignment functions work with the *filtered* list.
      // We need the index within the *filtered* list for the `matchedCorrectWords` set.
      if (wordObj.matched) {
        const correctFilteredIndex = verseStartIndex + wordIndexInVerse;
        // Add safety check
        if (correctFilteredIndex >= 0) {
          matchedCorrectWords.add(correctFilteredIndex);
        } else {
          // console.warn(`Calculated invalid negative index ${correctFilteredIndex} for word ${wordObj.text} in verse ${verseNumber}`);
        }
      }
    });
  });

  return matchedCorrectWords;
};

// ====================== Alignment Functions ======================

// No changes needed in wordSimilarityScore
export const wordSimilarityScore = (
  word1: string,
  word2: string,
  difficulty: Difficulty
): number => {
  if (typeof word1 !== "string" || typeof word2 !== "string") {
    return 0;
  }
  // Uses the reverted calculateSimilarity (based on original strings)
  const similarity = calculateSimilarity(word1, word2);

  const matchThreshold = difficulty === "easy" ? 0.6 : 0.7;
  const partialMatchThreshold = difficulty === "easy" ? 0.4 : 0.5;
  const matchScore = difficulty === "easy" ? 3 : 2;
  const partialMatchPenalty = difficulty === "easy" ? -0.5 : -1;
  const noMatchPenalty = difficulty === "easy" ? -1 : -2;

  let score: number;
  if (similarity >= matchThreshold) {
    score = matchScore;
  } else if (similarity >= partialMatchThreshold) {
    score = partialMatchPenalty * (1 - similarity);
  } else {
    score = noMatchPenalty;
  }
  return score;
};

// No changes needed in initializeScoringMatrix
export const initializeScoringMatrix = (
  userWords: string[],
  correctWords: string[],
  gapPenalty: number
): number[][] => {
  if (
    !Array.isArray(userWords) ||
    !Array.isArray(correctWords) ||
    typeof gapPenalty !== "number"
  ) {
    return [[]];
  }
  const rows = userWords.length + 1;
  const cols = correctWords.length + 1;
  const matrix: number[][] = Array.from({ length: rows }, () =>
    Array(cols).fill(0)
  );
  for (let i = 1; i < rows; i++) {
    matrix[i][0] = matrix[i - 1][0] + gapPenalty;
  }
  for (let j = 1; j < cols; j++) {
    matrix[0][j] = matrix[0][j - 1] + gapPenalty;
  }
  return matrix;
};

// No changes needed in fillScoringMatrix
export const fillScoringMatrix = (
  matrix: number[][],
  userWords: string[],
  correctWords: string[],
  gapPenalty: number,
  difficulty: Difficulty
): void => {
  if (
    !Array.isArray(matrix) ||
    !Array.isArray(userWords) ||
    !Array.isArray(correctWords) ||
    typeof gapPenalty !== "number"
  ) {
    return;
  }
  try {
    for (let i = 1; i <= userWords.length; i++) {
      for (let j = 1; j <= correctWords.length; j++) {
        const word1 = userWords[i - 1];
        const word2 = correctWords[j - 1];
        const match =
          matrix[i - 1][j - 1] + wordSimilarityScore(word1, word2, difficulty);
        const deleteOp = matrix[i - 1][j] + gapPenalty;
        const insertOp = matrix[i][j - 1] + gapPenalty;
        matrix[i][j] = Math.max(match, deleteOp, insertOp);
      }
    }
  } catch (error: any) {
    console.error("Error filling scoring matrix:", error);
  }
};

// No changes needed in tracebackAlignment
export const tracebackAlignment = (
  matrix: number[][],
  userWords: string[],
  correctWords: string[],
  gapPenalty: number,
  difficulty: Difficulty
): {
  userWord: string | null;
  correctWord: string | null;
  matched: boolean;
  similarity: number;
  userIndex: number;
  correctIndex: number;
}[] => {
  if (
    !Array.isArray(matrix) ||
    !Array.isArray(userWords) ||
    !Array.isArray(correctWords) ||
    typeof gapPenalty !== "number"
  ) {
    return [];
  }

  try {
    let i = userWords.length;
    let j = correctWords.length;
    const alignment: {
      userWord: string | null;
      correctWord: string | null;
      matched: boolean;
      similarity: number;
      userIndex: number;
      correctIndex: number;
    }[] = [];
    const matchThreshold = difficulty === "easy" ? 0.6 : 0.7;

    while (i > 0 || j > 0) {
      // Check bounds for matrix access
      const currentScore =
        i < matrix.length && j < matrix[0].length ? matrix[i][j] : -Infinity;

      if (i > 0 && j > 0) {
        const scoreDiag =
          i > 0 && j > 0 && i - 1 < matrix.length && j - 1 < matrix[0].length
            ? matrix[i - 1][j - 1]
            : -Infinity;
        const scoreDel =
          i > 0 && i - 1 < matrix.length && j < matrix[0].length
            ? matrix[i - 1][j]
            : -Infinity;
        const scoreIns =
          j > 0 && i < matrix.length && j - 1 < matrix[0].length
            ? matrix[i][j - 1]
            : -Infinity;

        const word1 = userWords[i - 1];
        const word2 = correctWords[j - 1];
        // calculateSimilarity uses original strings now
        const similarity = calculateSimilarity(word1, word2);
        const matchScoreVal = wordSimilarityScore(word1, word2, difficulty);

        // Check for diagonal move (match/mismatch)
        // Use a small tolerance for floating point comparison if needed, e.g., Math.abs(currentScore - (scoreDiag + matchScoreVal)) < 1e-6
        if (Math.abs(currentScore - (scoreDiag + matchScoreVal)) < 1e-6) {
          alignment.unshift({
            userWord: word1,
            correctWord: word2,
            matched: similarity >= matchThreshold, // Based on original similarity
            similarity: similarity,
            userIndex: i - 1,
            correctIndex: j - 1,
          });
          i--;
          j--;
          continue; // Continue to next iteration
        }
      }

      // Check for deletion (moving up)
      // Check bounds before accessing matrix[i-1][j]
      const scoreDel =
        i > 0 && i - 1 < matrix.length && j < matrix[0].length
          ? matrix[i - 1][j]
          : -Infinity;
      if (i > 0 && Math.abs(currentScore - (scoreDel + gapPenalty)) < 1e-6) {
        alignment.unshift({
          userWord: userWords[i - 1],
          correctWord: null,
          matched: false,
          similarity: 0,
          userIndex: i - 1,
          correctIndex: -1, // No corresponding correct word
        });
        i--;
        continue; // Continue to next iteration
      }

      // Check for insertion (moving left)
      // Check bounds before accessing matrix[i][j-1]
      const scoreIns =
        j > 0 && i < matrix.length && j - 1 < matrix[0].length
          ? matrix[i][j - 1]
          : -Infinity;
      if (j > 0 && Math.abs(currentScore - (scoreIns + gapPenalty)) < 1e-6) {
        alignment.unshift({
          userWord: null,
          correctWord: correctWords[j - 1],
          matched: false,
          similarity: 0,
          userIndex: -1, // No corresponding user word
          correctIndex: j - 1,
        });
        j--;
        continue; // Continue to next iteration
      }

      // If none of the conditions match (should not happen with correct matrix filling, but as a safeguard)
      console.warn("Traceback stuck at:", { i, j, currentScore });
      break; // Avoid infinite loop
    }
    return alignment;
  } catch (error: any) {
    console.error("Error during traceback:", error);
    return [];
  }
};

/* ================================================================== */
/*  MODIFIED performWordComparisonAligned                             */
/* ================================================================== */
export const performWordComparisonAligned = (
  transcribedWords: string[],
  correctWordsForAlignment: string[], // Base words without vowels for alignment
  correctWordsWithVowels: string[], // Original words with vowels for feedback context
  memorizedVerseNumbers: number[],
  wordVerseNumbers: number[], // Maps index in correctWords* lists to verse number
  matchedCorrectWords: Set<number>, // Indices in correctWords* lists already matched
  difficulty: Difficulty,
  /* 🆕 */ userTimings: WordTiming[] = [],
  /* 🆕 */ apiTokensForAlignment: ApiWordFeedback[] = [] // New parameter for API tokens
): {
  wordAlignment: AlignedWordWithCorrectInfo[]; // Updated return type
  feedbackPairs: FeedbackPairForDetailedCard[]; // Updated return type
} => {
  // Input validation checks
  if (
    !Array.isArray(transcribedWords) ||
    !Array.isArray(correctWordsForAlignment) ||
    !Array.isArray(correctWordsWithVowels) ||
    !Array.isArray(memorizedVerseNumbers) ||
    !Array.isArray(wordVerseNumbers) ||
    !(matchedCorrectWords instanceof Set) ||
    typeof difficulty !== "string"
  ) {
    console.error("Invalid input to performWordComparisonAligned");
    return { wordAlignment: [], feedbackPairs: [] };
  }

  // Ensure correctWordsForAlignment and correctWordsWithVowels have the same length
  if (correctWordsForAlignment.length !== correctWordsWithVowels.length) {
    console.error(
      "Mismatch between correctWordsForAlignment and correctWordsWithVowels lengths."
    );
    return { wordAlignment: [], feedbackPairs: [] };
  }

  // *** CRITICAL DIAGNOSTIC LOG & WARNING ***
  // This warning is here because if apiTokensForAlignment (from Whisper)
  // doesn't align with correctWordsForAlignment (your target words),
  // then mapping correctWordInfo using correctIndex might be off.
  if (apiTokensForAlignment.length !== correctWordsForAlignment.length) {
    console.warn(
      `[alignmentUtils PWC] WARNING: Length mismatch between apiTokensForAlignment (${apiTokensForAlignment.length}) and correctWordsForAlignment (${correctWordsForAlignment.length}). This may lead to incorrect correctWordInfoFromApi mapping.`
    );
  }

  console.log(
    "[alignmentUtils PWC] Input - transcribedWords (sample):",
    JSON.stringify(transcribedWords.slice(0, 5))
  );
  console.log(
    "[alignmentUtils PWC] Input - userTimings (sample):",
    JSON.stringify(userTimings.slice(0, 5))
  );
  console.log(
    `[alignmentUtils PWC] Input - transcribedWords.length: ${transcribedWords.length}, userTimings.length: ${userTimings.length}`
  );
  console.log(
    "[alignmentUtils PWC] Input - Count of userTimings with actual start times:",
    userTimings.filter((t) => t.start !== undefined).length
  );
  console.log(
    "[alignmentUtils PWC] Input - apiTokensForAlignment (sample):",
    JSON.stringify(apiTokensForAlignment.slice(0, 3))
  );

  try {
    /* ------------ ALIGN AS BEFORE ------------ */
    const gapPenalty = difficulty === "easy" ? -1 : -2;

    const scoringMatrix = initializeScoringMatrix(
      transcribedWords,
      correctWordsForAlignment, // Use normalized words for matrix
      gapPenalty
    );

    fillScoringMatrix(
      scoringMatrix,
      transcribedWords,
      correctWordsForAlignment, // Use normalized words for matrix
      gapPenalty,
      difficulty
    );

    const rawAlignment = tracebackAlignment(
      scoringMatrix,
      transcribedWords,
      correctWordsForAlignment, // Use normalized words for traceback
      gapPenalty,
      difficulty
    );

    /* ------------ 🆕 Enhanced metadata propagation ------------ */
    const wordAlignment: AlignedWordWithCorrectInfo[] = rawAlignment.map(
      (rawItem) => {
        const verseNumber =
          rawItem.correctIndex !== -1 &&
          rawItem.correctIndex < wordVerseNumbers.length
            ? wordVerseNumbers[rawItem.correctIndex]
            : null;

        let currentUserWordTiming: WordTiming = {};
        // User timing is based on the user's word index from transcription
        if (
          rawItem.userIndex !== -1 &&
          rawItem.userIndex < userTimings.length
        ) {
          const timing = userTimings[rawItem.userIndex];
          if (
            timing &&
            typeof timing.start === "number" &&
            typeof timing.end === "number" &&
            timing.start >= 0 &&
            timing.end > timing.start
          ) {
            currentUserWordTiming = timing;
          } else {
            // console.log(`[alignmentUtils PWC MAP] Invalid or missing timing for userIndex ${rawItem.userIndex}:`, timing);
          }
        }

        let currentCorrectWordInfo: CorrectWordInfo | undefined = undefined;
        // Correct word info is based on the correct word's index from the API tokens.
        // This assumes apiTokensForAlignment are ordered corresponding to correctWordsForAlignment.
        if (
          rawItem.correctIndex !== -1 &&
          rawItem.correctIndex < apiTokensForAlignment.length
        ) {
          currentCorrectWordInfo =
            apiTokensForAlignment[rawItem.correctIndex]?.correctWordInfo;
          if (!currentCorrectWordInfo) {
            console.warn(
              `[alignmentUtils PWC MAP] No correctWordInfo found in apiTokensForAlignment at correctIndex: ${rawItem.correctIndex}. This is expected for 'extra' user words or if API did not provide it for this correct word.`
            );
          }
        } else if (rawItem.correctIndex !== -1) {
          console.warn(
            `[alignmentUtils PWC MAP] correctIndex ${rawItem.correctIndex} out of bounds for apiTokensForAlignment (length ${apiTokensForAlignment.length}). correctWordInfoFromApi will be undefined.`
          );
        }

        return {
          userWord: rawItem.userWord,
          correctWord: rawItem.correctWord, // Normalized correct word
          matched: rawItem.matched,
          similarity: rawItem.similarity,
          userIndex: rawItem.userIndex,
          correctIndex: rawItem.correctIndex, // Index in filtered lists
          verseNumber: verseNumber,
          start: currentUserWordTiming.start, // User's word start time
          end: currentUserWordTiming.end, // User's word end time
          correctWordInfoFromApi: currentCorrectWordInfo, // Audio metadata for the correct word
        };
      }
    );

    const feedbackPairs: FeedbackPairForDetailedCard[] = [];
    const matchThreshold = difficulty === "hard" ? 0.7 : 0.6;
    const partialMatchThreshold = difficulty === "hard" ? 0.5 : 0.4;

    // Process each aligned pair with enhanced metadata handling
    wordAlignment.forEach((alignmentItem) => {
      const {
        userWord,
        correctWord: normalizedCorrectWord, // This is the normalized version from alignment
        matched: isMatchBySimilarityOnly, // Based on original similarity score
        similarity, // Original similarity score
        correctIndex, // Index in the *filtered* correct word lists
        verseNumber,
        /* 🆕 */ start, // User's word start time
        /* 🆕 */ end, // User's word end time
        /* 🆕 */ correctWordInfoFromApi, // Audio metadata for the correct word
      } = alignmentItem;

      console.log(
        `[alignmentUtils PWC ForEach] Processing alignment item - User: "${userWord}", Correct: "${normalizedCorrectWord}", Start: ${start}, End: ${end}, HasAudioInfo: ${!!correctWordInfoFromApi}`
      );

      // Case: Both user and correct words exist (potential match/mismatch)
      if (userWord && normalizedCorrectWord) {
        // Skip if already marked as matched or is memorized
        if (matchedCorrectWords.has(correctIndex)) return;
        if (verseNumber !== null && memorizedVerseNumbers.includes(verseNumber))
          return;

        // Safety check: Ensure correctIndex is valid for correctWordsWithVowels
        if (correctIndex < 0 || correctIndex >= correctWordsWithVowels.length) {
          console.error(
            `Invalid correctIndex ${correctIndex} for correctWordsWithVowels (length ${correctWordsWithVowels.length})`
          );
          return; // Skip this alignment item
        }

        // Get the correct word with its original vowel markings
        const correctWordWithVowelsActual =
          correctWordsWithVowels[correctIndex];

        // Calculate letter counts using the appropriate words
        const userLetterCount = strictLetterCount(userWord); // Count letters in user's transcribed word
        const correctLetterCount = strictLetterCount(
          correctWordWithVowelsActual
        ); // Count letters in correct word WITH VOWELS

        // ENHANCED: Special handling for dagger alif when determining letter count mismatch
        // Check for dagger alif scenario that might cause letter count difference
        const hasDaggerAlif = correctWordWithVowelsActual.includes("\u0670");
        const letterCountDifference = Math.abs(
          userLetterCount - correctLetterCount
        );
        const isLikelyDaggerAlifMismatch =
          hasDaggerAlif &&
          letterCountDifference <= 1 && // Usually dagger alif causes a difference of 1
          normalizeForComparison(userWord).includes("ا");

        // Final determination of letter count mismatch with dagger alif exception
        const letterCountMismatch =
          userLetterCount !== correctLetterCount && !isLikelyDaggerAlifMismatch;

        if (hasDaggerAlif && userLetterCount !== correctLetterCount) {
          console.log(
            `Dagger alif detected: userLetterCount=${userLetterCount}, correctLetterCount=${correctLetterCount}, isLikelyDaggerAlifMismatch=${isLikelyDaggerAlifMismatch}`
          );
        }

        // --- START: ENHANCED FEEDBACK GENERATION LOGIC ---

        if (isMatchBySimilarityOnly && !letterCountMismatch) {
          // Case 1: High similarity AND correct letter count. True match.
          matchedCorrectWords.add(correctIndex); // Mark as matched
          // NO feedback pair generated
        } else {
          // ---- ADDED CHECK: Override if normalized forms are identical ----
          // Normalize both the user's word and the correct word (with vowels)
          // This handles cases like 'في' vs 'فِى' where similarity might be slightly off due to vowels
          const normalizedUserWord = normalizeForComparison(userWord);
          const normalizedCorrectWord = normalizeForComparison(
            correctWordWithVowelsActual
          );

          if (normalizedUserWord === normalizedCorrectWord) {
            // If the normalized forms match perfectly, treat it as a full match,
            // overriding lower similarity scores or apparent letter count differences caused by normalization.
            // This specifically addresses the 'في' vs 'فِى' example.
            matchedCorrectWords.add(correctIndex); // Mark as matched
            // NO feedback generated for this case.
          }
          // ENHANCED: Special handling for dagger alif matching
          else if (hasDaggerAlif && isLikelyDaggerAlifMismatch) {
            // Additional check - if we have dagger alif situation that we identified,
            // treat it as a match even if normalized forms differ slightly
            console.log(
              `Treating as match due to dagger alif: "${userWord}" vs "${correctWordWithVowelsActual}"`
            );
            matchedCorrectWords.add(correctIndex); // Mark as matched
            // NO feedback generated for this special dagger alif case
          }
          // ---- END ADDED CHECK ----
          // If the normalized forms ALSO differ, then proceed to check the original reasons for mismatch:
          else if (letterCountMismatch) {
            // Case 2: Letter count mismatch (and normalized forms differ).
            const letterFeedback = compareLetters(
              userWord,
              correctWordWithVowelsActual
            );

            // Generate feedback only if similarity was also low (Case 2a)
            if (!isMatchBySimilarityOnly) {
              feedbackPairs.push({
                userWord,
                correctWord: correctWordWithVowelsActual,
                letterFeedback: letterFeedback || undefined, // Convert null to undefined for TypeScript compatibility
                letterCountMismatch: true,
                userLetterCount,
                correctLetterCount,
                similarity,
                type: "letter_count_mismatch",
                verseNumber,
                correctIndex, // 🆕 Include correctIndex
                /* 🆕 */ userStart: start, // Add timing data to feedback pair
                /* 🆕 */ userEnd: end, // Add timing data to feedback pair
                /* 🆕 */ correctWordInfoFromApi, // Add audio metadata to feedback pair
              });
            } else {
              // Case 2b: Mismatch BUT high similarity (but normalized forms differ). Treat as matched based on similarity.
              matchedCorrectWords.add(correctIndex); // Treat as matched overall based on similarity flag
              // NO feedback generated as per user requirement for high-similarity words
            }
          } else if (similarity >= partialMatchThreshold) {
            // Case 3: Partial similarity (normalized forms differ, letter count matches).
            const letterFeedback = compareLetters(
              userWord,
              correctWordWithVowelsActual
            );
            feedbackPairs.push({
              userWord,
              correctWord: correctWordWithVowelsActual,
              letterFeedback: letterFeedback || undefined, // Convert null to undefined for TypeScript compatibility
              letterCountMismatch: false,
              userLetterCount,
              correctLetterCount,
              similarity,
              type: "partial",
              verseNumber,
              correctIndex, // 🆕 Include correctIndex
              /* 🆕 */ userStart: start, // Add timing data to feedback pair
              /* 🆕 */ userEnd: end, // Add timing data to feedback pair
              /* 🆕 */ correctWordInfoFromApi, // Add audio metadata to feedback pair
            });
          } else {
            // Case 4: Low similarity (normalized forms differ, letter count matches).
            feedbackPairs.push({
              userWord,
              correctWord: correctWordWithVowelsActual,
              similarity,
              type: "unmatched",
              verseNumber,
              correctIndex, // 🆕 Include correctIndex
              /* 🆕 */ userStart: start, // Add timing data to feedback pair
              /* 🆕 */ userEnd: end, // Add timing data to feedback pair
              /* 🆕 */ correctWordInfoFromApi, // Add audio metadata to feedback pair
              // letterFeedback could be added here too if desired for unmatched words
            });
          }
        }
        // ---- END: ENHANCED FEEDBACK GENERATION LOGIC ----

        // Case: Correct word exists, user word is missing (Deletion)
      } else if (normalizedCorrectWord && !userWord) {
        // Using normalizedCorrectWord here because it's guaranteed from alignmentItem
        // Handle missing words (Deletion)
        if (
          correctIndex !== -1 &&
          !matchedCorrectWords.has(correctIndex) &&
          (verseNumber === null ||
            !memorizedVerseNumbers.includes(verseNumber)) &&
          correctIndex < correctWordsWithVowels.length // Bounds check
        ) {
          const correctWordWithVowelsActual =
            correctWordsWithVowels[correctIndex];

          // ENHANCED: Check if this is potentially a dagger alif case to avoid unnecessary "missing" feedback
          const hasDaggerAlif = correctWordWithVowelsActual.includes("\u0670");
          const isPotentialDaggerAlifCase =
            hasDaggerAlif &&
            // Check if there's a nearby word in user's input with alif that might be matching this
            transcribedWords.some((w) =>
              normalizeForComparison(w).includes("ا")
            );

          if (!isPotentialDaggerAlifCase) {
            feedbackPairs.push({
              userWord: null, // Explicitly null for missing user words
              correctWord: correctWordWithVowelsActual,
              similarity: 0,
              type: "missing",
              verseNumber,
              correctIndex, // 🆕 Include correctIndex
              /* 🆕 */ userStart: start, // Add timing data (may be undefined for missing words)
              /* 🆕 */ userEnd: end, // Add timing data (may be undefined for missing words)
              /* 🆕 */ correctWordInfoFromApi, // Add audio metadata to feedback pair
            });
          } else {
            console.log(
              `Suppressing 'missing word' feedback for potential dagger alif case: "${correctWordWithVowelsActual}"`
            );
          }
        }
        // Case: User word exists, correct word is missing (Insertion)
      } else if (userWord && !normalizedCorrectWord) {
        // Using userWord and checking if normalizedCorrectWord is null
        // ENHANCED: Check if this might be an alif that corresponds to a dagger alif in the correct text
        const normalizedUserWord = normalizeForComparison(userWord);
        const containsAlif = normalizedUserWord.includes("ا");
        const isPotentialDaggerAlifCase =
          containsAlif &&
          // Check if there's a nearby word in correct text with dagger alif
          correctWordsWithVowels.some((w) => w.includes("\u0670"));

        if (!isPotentialDaggerAlifCase) {
          // Handle extra words (Insertion)
          feedbackPairs.push({
            userWord,
            correctWord: null as any, // Type assertion needed for null correct word
            similarity: 0,
            type: "extra",
            verseNumber: null, // Extra words don't align to a specific verse number via correctIndex
            /* 🆕 */ userStart: start, // Add timing data to feedback pair
            /* 🆕 */ userEnd: end, // Add timing data to feedback pair
            // Note: no correctWordInfoFromApi for extra words since there's no correct correct word
          });
        } else {
          console.log(
            `Suppressing 'extra word' feedback for potential dagger alif case: "${userWord}"`
          );
        }
      }
    });

    // 🆕 Enhanced validation and logging
    const feedbackPairsWithAudio = feedbackPairs.filter(
      (pair) => pair.correctWordInfoFromApi?.audioUrl
    ).length;
    const feedbackPairsWithTiming = feedbackPairs.filter(
      (pair) => pair.userStart !== undefined && pair.userEnd !== undefined
    ).length;

    console.log(
      `[alignmentUtils PWC FINAL] Generated ${feedbackPairs.length} feedback pairs:`,
      {
        withAudioMetadata: feedbackPairsWithAudio,
        withTiming: feedbackPairsWithTiming,
        totalWordAlignment: wordAlignment.length,
      }
    );

    // Return the results
    return { wordAlignment, feedbackPairs };
  } catch (error: any) {
    console.error("Error in performWordComparisonAligned:", error);
    return { wordAlignment: [], feedbackPairs: [] };
  }
};

/* ================================================================== */
/*  MODIFIED finalizeAlignment                                        */
/* ================================================================== */
export const finalizeAlignment = (
  correctWordsList: string[], // FULL list with vowels (original words)
  wordAlignment: AlignedWordWithCorrectInfo[], // Alignment results based on FILTERED correct words
  currentDisplayedWords: { [verseNumber: number]: any[] },
  feedbackThreshold: number, // Keep param, but note usage
  memorizedVerseNumbers: number[],
  wordVerseNumbersFull: number[], // Maps FULL correctWordsList indices to verse numbers
  difficulty: Difficulty
): {
  finalUnmatchedCorrectWords: Array<{
    text: string;
    correctIndex: number; // Original index in correctWordsList
    pairedUserWord: string | null;
    correctWordInfoFromApi?: CorrectWordInfo; // 🆕 ADDED this to the type
  }>;
  updatedDisplayedWords: { [verseNumber: number]: any[] };
} => {
  // Input validation
  if (
    !Array.isArray(correctWordsList) ||
    !Array.isArray(wordAlignment) ||
    typeof currentDisplayedWords !== "object" ||
    typeof feedbackThreshold !== "number" ||
    !Array.isArray(memorizedVerseNumbers) ||
    !Array.isArray(wordVerseNumbersFull) ||
    typeof difficulty !== "string"
  ) {
    console.error("Invalid input to finalizeAlignment");
    return {
      finalUnmatchedCorrectWords: [],
      updatedDisplayedWords: currentDisplayedWords,
    };
  }

  try {
    // Setup
    const memorizedSet = new Set(memorizedVerseNumbers);
    const finalUnmatchedCorrectWords: Array<{
      text: string;
      correctIndex: number; // Original index in correctWordsList
      pairedUserWord: string | null;
      correctWordInfoFromApi?: CorrectWordInfo; // 🆕 ADDED this to the array object type
    }> = [];
    const partialMatchThreshold = difficulty === "easy" ? 0.4 : 0.5;
    const newlyMemorizedVerses = new Set<number>();
    const updatedDisplayedWordsClone = cloneDeep(currentDisplayedWords);

    // Precompute verse start indices (Full list)
    const verseStartIndicesFull: { [verseNumber: number]: number } = {};
    for (let idx = 0; idx < wordVerseNumbersFull.length; idx++) {
      const verseNumber = wordVerseNumbersFull[idx];
      if (!(verseNumber in verseStartIndicesFull)) {
        verseStartIndicesFull[verseNumber] = idx;
      }
    }

    // Map filtered index to original index
    const filteredToOriginalIndexMap: number[] = [];
    wordVerseNumbersFull.forEach((verseNum, originalIndex) => {
      // This mapping populates `filteredToOriginalIndexMap` such that
      // `filteredToOriginalIndexMap[filteredIndex]` gives the `originalIndex`
      // of the word in the `correctWordsList`.
      // `performWordComparisonAligned`'s `correctIndex` is the `filteredIndex`.
      if (!memorizedSet.has(verseNum)) {
        // This creates a dense array where the index IS the filtered index
        filteredToOriginalIndexMap.push(originalIndex);
      }
    });

    // Identify newly memorized verses
    Object.entries(updatedDisplayedWordsClone).forEach(
      ([verseNumStr, verseWords]) => {
        const verseNumber = Number(verseNumStr);
        if (verseNumber > 2 && !memorizedSet.has(verseNumber)) {
          const allMatchedOrGiven = verseWords.every(
            (word: any) => word.matched || word.given
          );
          if (allMatchedOrGiven) {
            newlyMemorizedVerses.add(verseNumber);
          }
        }
      }
    );

    const wordsWithDaggerAlif = new Set<number>();
    correctWordsList.forEach((word, index) => {
      if (word.includes("\u0670")) {
        wordsWithDaggerAlif.add(index);
        console.log(
          `Detected word with dagger alif at original index ${index}: "${word}"`
        );
      }
    });

    // **NEW: Keep track of correct word indices that have been used in a direct user-word vs correct-word comparison feedback**
    // This means they were the `correctWord` in a `feedbackPair` that wasn't a 'missing' or 'extra' type.
    // We can infer this if a `displayedWord` ends up being 'almostMatched' (rose) or 'unmatched' (red) *due to a direct comparison*.
    const correctIndicesUsedInComparisonFeedback = new Set<number>();

    wordAlignment.forEach((alignment) => {
      const {
        userWord, // This is the normalized user word
        correctWord: normalizedCorrectWordFromAlignment, // This is the normalized correct word
        matched: isMatchBySimilarityOnly,
        similarity,
        correctIndex: correctFilteredIndex, // This is the index in the *filtered* list used for alignment
        /* 🆕 */ start, // User's word start time
        /* 🆕 */ end, // User's word end time
        /* 🆕 */ correctWordInfoFromApi, // New property from API (for the correct word)
      } = alignment;

      // Only process if there is a corresponding correct word in the alignment result
      if (
        correctFilteredIndex === -1 ||
        normalizedCorrectWordFromAlignment === null
      ) {
        // This item is an 'extra' user word or an alignment gap without a correct word.
        // We don't update displayedWords for these based on correctIndex.
        return;
      }

      // Map the filtered correctIndex back to the original index in correctWordsList
      const correctOriginalIndex =
        filteredToOriginalIndexMap[correctFilteredIndex];

      // Process only if we have a valid mapping to an original word
      if (correctOriginalIndex === undefined) {
        console.warn(
          `Could not map filtered index ${correctFilteredIndex} to original index in finalizeAlignment.`
        );
        return;
      }

      const verseNumber = wordVerseNumbersFull[correctOriginalIndex];

      // Skip verses 1, 2, and already memorized verses (unless they become newly memorized)
      if (verseNumber <= 2 || memorizedSet.has(verseNumber)) {
        return;
      }

      const verseStartIndex = verseStartIndicesFull[verseNumber];
      if (verseStartIndex === undefined) return;

      const wordIndexInVerse = correctOriginalIndex - verseStartIndex;
      const verseWords = updatedDisplayedWordsClone[verseNumber];

      // Ensure indices are valid within the displayedWords structure
      if (
        verseWords &&
        wordIndexInVerse >= 0 &&
        wordIndexInVerse < verseWords.length
      ) {
        const currentWordDisplayData = verseWords[wordIndexInVerse];
        const isNewlyMemorizedVerse = newlyMemorizedVerses.has(verseNumber);

        // Only update if not already matched (unless part of a newly memorized verse)
        if (!currentWordDisplayData.matched || isNewlyMemorizedVerse) {
          // Get correct word with vowels for checks
          const correctWordWithVowels = correctWordsList[correctOriginalIndex];
          let treatAsMatched = false; // Flag to determine final state
          let participatedInDirectComparison = false; // **NEW FLAG**

          // --- START: MIRROR FEEDBACK SUPPRESSION LOGIC FOR HIGHLIGHTING ---

          // ENHANCED: If this word has dagger alif, always treat as matched
          const isDaggerAlifWord =
            wordsWithDaggerAlif.has(correctOriginalIndex);

          if (isDaggerAlifWord && userWord) {
            // Check if user actually said something
            treatAsMatched = true;
            if (userWord) participatedInDirectComparison = true; // Dagger alif words, if paired with a user word, are considered "compared"
            console.log(
              `Word with dagger alif marked as matched: "${correctWordWithVowels}"`
            );
          }
          // Condition A: High similarity AND correct letter count?
          else {
            let letterCountMismatch = false;
            if (userWord !== null) {
              // Need userWord for letter count check
              const userLetterCount = strictLetterCount(userWord);
              const correctLetterCount = strictLetterCount(
                correctWordWithVowels
              );

              // ENHANCED: Check for dagger alif impact on letter count
              const hasDaggerAlifInternal =
                correctWordWithVowels.includes("\u0670");
              const letterCountDifference = Math.abs(
                userLetterCount - correctLetterCount
              );
              const isLikelyDaggerAlifMismatch =
                hasDaggerAlifInternal &&
                letterCountDifference <= 1 &&
                normalizeForComparison(userWord).includes("ا");

              letterCountMismatch =
                userLetterCount !== correctLetterCount &&
                !isLikelyDaggerAlifMismatch;
            } else {
              // If userWord is null (missing word), letter count inherently mismatches
              letterCountMismatch = true;
            }

            if (isMatchBySimilarityOnly && !letterCountMismatch) {
              treatAsMatched = true;
              if (userWord) participatedInDirectComparison = true; // Matched means it was compared
            }
            // Condition B: Failed A. Do normalized forms match? (Requires userWord)
            else if (
              userWord !== null &&
              normalizeForComparison(userWord) ===
                normalizeForComparison(correctWordWithVowels)
            ) {
              treatAsMatched = true;
              participatedInDirectComparison = true; // Normalized match implies comparison
            }
            // Condition C: Failed A & B. Is there letter count mismatch BUT original similarity was high? (Mirroring Case 2b suppression)
            else if (
              letterCountMismatch &&
              isMatchBySimilarityOnly &&
              userWord !== null
            ) {
              treatAsMatched = true;
              participatedInDirectComparison = true; // High similarity despite letter mismatch implies comparison
            } else if (userWord !== null) {
              // If none of the above "treatAsMatched" conditions met, but there *was* a userWord
              // It was aligned and compared, even if poorly.
              participatedInDirectComparison = true;
            }
          }

          // If it participated in a direct comparison (resulted in a feedback card like "You said X -> Correct Y"),
          // add its original index to the set.
          if (participatedInDirectComparison) {
            correctIndicesUsedInComparisonFeedback.add(correctOriginalIndex);
          }

          if (treatAsMatched || isNewlyMemorizedVerse) {
            // Set state to MATCHED (Emerald/Default)
            verseWords[wordIndexInVerse] = {
              ...currentWordDisplayData,
              matched: true,
              almostMatched: false,
              displayText: currentWordDisplayData.text,
              textColor: isNewlyMemorizedVerse ? "text-emerald-500" : undefined,
              /* 🆕 Enhanced: Preserve timing and audio metadata */
              userStart: start,
              userEnd: end,
              ...(correctWordInfoFromApi && {
                correctWordInfo: correctWordInfoFromApi,
              }), // Add correctWordInfo if available
            };
          } else {
            // If not treated as matched, determine Rose or Red based on partial threshold
            if (userWord === null || similarity < partialMatchThreshold) {
              // This word is either completely missing (userWord is null) OR its similarity is very low.
              // We only add it to `finalUnmatchedCorrectWords` IF it was NOT already subject to a direct comparison feedback.
              if (
                !correctIndicesUsedInComparisonFeedback.has(
                  correctOriginalIndex
                )
              ) {
                finalUnmatchedCorrectWords.push({
                  text: correctWordWithVowels,
                  correctIndex: correctOriginalIndex,
                  pairedUserWord: userWord, // Will be null for missing words, or the low-similarity user word
                  correctWordInfoFromApi: correctWordInfoFromApi, // 🆕 Crucial: Carry over the CWI for missing words
                });
              }
              // Update displayedWord color to red, regardless of whether it's added to finalUnmatchedCorrectWords
              verseWords[wordIndexInVerse] = {
                ...currentWordDisplayData,
                matched: false,
                almostMatched: false,
                displayText: currentWordDisplayData.text, // Keep original text visible but color it
                textColor: "text-red-600",
                userStart: start,
                userEnd: end,
                ...(correctWordInfoFromApi && {
                  correctWordInfo: correctWordInfoFromApi,
                }), // Add correctWordInfo if available
              };
            } else {
              // This is a "partial" match (similarity >= partialMatchThreshold) that didn't become `treatAsMatched`
              verseWords[wordIndexInVerse] = {
                ...currentWordDisplayData,
                matched: false,
                almostMatched: true,
                displayText: currentWordDisplayData.text,
                textColor: "text-rose-600",
                userStart: start,
                userEnd: end,
                ...(correctWordInfoFromApi && {
                  correctWordInfo: correctWordInfoFromApi,
                }), // Add correctWordInfo if available
              };
              // If it's an almost match, it definitely participated in comparison feedback.
              correctIndicesUsedInComparisonFeedback.add(correctOriginalIndex);
            }
          }
          // --- END: MIRROR FEEDBACK SUPPRESSION LOGIC FOR HIGHLIGHTING ---
        }
      } else {
        // console.warn(`Invalid index or verseWords for verse ${verseNumber}, wordIndexInVerse ${wordIndexInVerse}`);
      }
    });

    // **NEW STEP: Filter `finalUnmatchedCorrectWords` again after all `wordAlignment` items are processed.**
    // This handles cases where a word might have been added to `finalUnmatchedCorrectWords`
    // (e.g., if `userWord` was null for that alignment entry), but a *different* `userWord`
    // later aligned with the *same correct word* and caused it to be included in
    // `correctIndicesUsedInComparisonFeedback`.
    const trulyFinalUnmatchedCorrectWords = finalUnmatchedCorrectWords.filter(
      (item) => !correctIndicesUsedInComparisonFeedback.has(item.correctIndex)
    );

    newlyMemorizedVerses.forEach((verseNumber) => {
      if (verseNumber <= 2) return;
      const verseWords = updatedDisplayedWordsClone[verseNumber];
      if (verseWords) {
        updatedDisplayedWordsClone[verseNumber] = verseWords.map(
          (word: any) => ({
            ...word,
            matched: true,
            almostMatched: false,
            displayText: word.text,
            textColor: "text-emerald-500",
            // 🆕 Enhanced: Preserve existing timing data and audio metadata if present
            userStart: word.userStart,
            userEnd: word.userEnd,
            ...(word.correctWordInfo && {
              correctWordInfo: word.correctWordInfo,
            }),
          })
        );
      }
    });

    const wordsWithAudioMetadata = Object.values(updatedDisplayedWordsClone)
      .flat()
      .filter((word) => word.correctWordInfo?.audioUrl).length;

    const wordsWithTiming = Object.values(updatedDisplayedWordsClone)
      .flat()
      .filter(
        (word) => word.userStart !== undefined && word.userEnd !== undefined
      ).length;

    console.log(`[alignmentUtils finalizeAlignment] Final state validation:`, {
      totalWords: Object.values(updatedDisplayedWordsClone).flat().length,
      wordsWithAudioMetadata,
      wordsWithTiming,
      finalUnmatchedCount: trulyFinalUnmatchedCorrectWords.length, // Use the filtered list count
    });

    return {
      finalUnmatchedCorrectWords: trulyFinalUnmatchedCorrectWords, // Return the truly unmatched words
      updatedDisplayedWords: updatedDisplayedWordsClone,
    };
  } catch (error: any) {
    console.error("Error in finalizeAlignment:", error);
    return {
      finalUnmatchedCorrectWords: [],
      updatedDisplayedWords: currentDisplayedWords,
    };
  }
};

// ====================== Letter Comparison Function ======================

// Use the enhanced compareLetters from the previous step
// ===== 3. ENHANCED compareLetters() =====
export const compareLetters = (
  userWord: string,
  correctWordWithVowels: string
): any[] | null => {
  if (
    typeof userWord !== "string" ||
    typeof correctWordWithVowels !== "string"
  ) {
    return null;
  }

  try {
    const userWordForParsing = removeVowelMarkings(userWord);
    const userLetters = parseWord(userWordForParsing);
    const correctLetters = parseWord(correctWordWithVowels);

    const userLen = userLetters.length;
    const correctLen = correctLetters.length;

    const dp: number[][] = Array(userLen + 1)
      .fill(null)
      .map(() => Array(correctLen + 1).fill(0));
    const backtrack: string[][] = Array(userLen + 1)
      .fill(null)
      .map(() => Array(correctLen + 1).fill(""));

    // IMPROVED: More comprehensive dagger alif detection
    // Track if the correct word has a dagger alif or alif-related letters
    const hasDaggerAlifInCorrectWord = correctLetters.some((letter) =>
      letter.vowelMarks.includes("\u0670")
    );

    // NEW: Track positions of letters with dagger alif for more precise handling
    const daggerAlifPositions = new Set<number>();
    correctLetters.forEach((letter, index) => {
      if (letter.vowelMarks.includes("\u0670")) {
        daggerAlifPositions.add(index);
        console.log(
          "Dagger alif detected at position",
          index,
          "in correct word"
        );
      }
    });

    for (let i = 0; i <= userLen; i++) {
      dp[i][0] = i;
      backtrack[i][0] = "up";
    }
    for (let j = 0; j <= correctLen; j++) {
      dp[0][j] = j;
      backtrack[0][j] = "left";
    }

    for (let i = 1; i <= userLen; i++) {
      for (let j = 1; j <= correctLen; j++) {
        const userLetter = userLetters[i - 1];
        const correctLetter = correctLetters[j - 1];

        // Determine match cost with normalization/confusion check
        const userBase = userLetter.baseLetter;
        const correctBase = correctLetter.baseLetter;
        const normalizedUserBase = normalizeForComparison(userBase);
        const normalizedCorrectBase = normalizeForComparison(correctBase);

        let matchCost = 1; // Default cost is 1 (mismatch)

        if (userBase === correctBase) {
          matchCost = 0; // Exact match
        } else if (normalizedUserBase === normalizedCorrectBase) {
          matchCost = 0; // Normalized match (e.g., ا vs ٱ) - Treat as match for DP path
        } else if (isAcceptableConfusion(correctBase, userBase)) {
          // Specific transcription confusion (e.g., ع vs ا) - Treat as match for DP path
          matchCost = 0;
        } else if (
          // IMPROVED: Enhanced dagger alif handling with more conditions
          (hasDaggerAlifInCorrectWord &&
            normalizedUserBase === "ا" &&
            daggerAlifPositions.has(j - 1)) ||
          (hasDaggerAlifInCorrectWord &&
            correctLetter.vowelMarks.includes("\u0670") &&
            (userBase === "ا" || normalizedUserBase === "ا"))
        ) {
          // If we're at a position with dagger alif and user wrote alif or something that normalizes to alif
          matchCost = 0;
          console.log(
            "Dagger Alif match: User base:",
            userBase,
            "Correct base with dagger:",
            correctBase
          );
        }

        const substituteCost = dp[i - 1][j - 1] + matchCost;
        const deleteCost = dp[i - 1][j] + 1;
        const insertCost = dp[i][j - 1] + 1;

        if (substituteCost <= deleteCost && substituteCost <= insertCost) {
          dp[i][j] = substituteCost;
          backtrack[i][j] = "diag";
        } else if (deleteCost <= insertCost) {
          dp[i][j] = deleteCost;
          backtrack[i][j] = "up";
        } else {
          dp[i][j] = insertCost;
          backtrack[i][j] = "left";
        }
      }
    }

    const feedbackArray: any[] = [];
    let i = userLen;
    let j = correctLen;

    while (i > 0 || j > 0) {
      // Position calculation based on correct word's index 'j'
      const position =
        j <= Math.ceil(correctLen / 3)
          ? "near the start"
          : j <= Math.ceil((2 * correctLen) / 3)
          ? "in the middle"
          : "near the end";

      if (i > 0 && j > 0 && backtrack[i][j] === "diag") {
        const userLetter = userLetters[i - 1];
        const correctLetter = correctLetters[j - 1];
        const userBase = userLetter.baseLetter;
        const correctBase = correctLetter.baseLetter;

        // Generate substitution feedback ONLY if the ORIGINAL letters differ
        // AND they are NOT considered an acceptable normalization/confusion.
        if (userBase !== correctBase) {
          const normalizedUserBase = normalizeForComparison(userBase);
          const normalizedCorrectBase = normalizeForComparison(correctBase);
          const isNormalizedMatch =
            normalizedUserBase === normalizedCorrectBase;
          const isConfusedMatch = isAcceptableConfusion(correctBase, userBase);

          // IMPROVED: Enhanced dagger alif check for substitution
          const isDaggerAlifEquivalent =
            (hasDaggerAlifInCorrectWord &&
              correctLetter.vowelMarks.includes("\u0670") &&
              (userBase === "ا" || normalizedUserBase === "ا")) ||
            (daggerAlifPositions.has(j - 1) && normalizedUserBase === "ا");

          // Only generate feedback if it's NOT a normalized match,
          // NOT an acceptable confusion, and NOT a dagger alif equivalent
          if (
            !isNormalizedMatch &&
            !isConfusedMatch &&
            !isDaggerAlifEquivalent
          ) {
            feedbackArray.unshift({
              userLetter: userLetter.originalChar,
              correctLetter: correctLetter.originalChar,
              userLetterSound: getPhoneticSound(userBase),
              correctLetterSound: getPhoneticSound(correctBase),
              vowelInfo: getVowelInfo(correctLetter),
              vowelExplanation: getVowelExplanation(correctLetter),
              position: `${position} of the word`,
              type: "substitution",
            });
          }
        }
        i--;
        j--;
      } else if (i > 0 && (j === 0 || backtrack[i][j] === "up")) {
        const userLetter = userLetters[i - 1];

        // IMPROVED: Check if this "extra" letter is an alif that might correspond to a dagger alif
        const isPotentialDaggerAlifReplacement =
          hasDaggerAlifInCorrectWord &&
          (userLetter.baseLetter === "ا" ||
            normalizeForComparison(userLetter.baseLetter) === "ا");

        // Only add feedback if it's not a potential dagger alif equivalent
        if (!isPotentialDaggerAlifReplacement) {
          feedbackArray.unshift({
            userLetter: userLetter.originalChar,
            correctLetter: null,
            userLetterSound: getPhoneticSound(userLetter.baseLetter),
            correctLetterSound: null,
            position: `${position} of the word`, // Position relative to alignment
            type: "extra",
            note: "This letter appears to be extra",
          });
        } else {
          console.log(
            "Suppressed 'extra letter' feedback for potential dagger alif:",
            userLetter.baseLetter
          );
        }
        i--;
      } else if (j > 0 && (i === 0 || backtrack[i][j] === "left")) {
        // Ensure j > 0 for accessing correctLetters
        const correctLetter = correctLetters[j - 1];

        // IMPROVED: Enhanced check for dagger alif missing
        const isDaggerAlifMissing =
          hasDaggerAlifInCorrectWord &&
          (correctLetter.vowelMarks.includes("\u0670") ||
            daggerAlifPositions.has(j - 1) ||
            (normalizeForComparison(correctLetter.baseLetter) === "ا" &&
              i > 0 &&
              userLetters[i - 1].baseLetter === "ا"));

        // Only generate missing letter feedback if not a dagger alif scenario
        if (!isDaggerAlifMissing) {
          feedbackArray.unshift({
            userLetter: null,
            correctLetter: correctLetter.originalChar,
            userLetterSound: null,
            correctLetterSound: getPhoneticSound(correctLetter.baseLetter),
            vowelInfo: getVowelInfo(correctLetter),
            vowelExplanation: getVowelExplanation(correctLetter),
            position: `${position} of the word`,
            type: "missing",
            note: "This letter seems to be missing",
          });
        } else {
          console.log(
            "Suppressed 'missing letter' feedback for dagger alif at position",
            j - 1
          );
        }
        j--;
      } else {
        // Break if no valid move is found to prevent infinite loops
        break;
      }
    }

    return feedbackArray.length > 0 ? feedbackArray : null;
  } catch (error) {
    console.error("Error in compareLetters:", error);
    return null;
  }
};
