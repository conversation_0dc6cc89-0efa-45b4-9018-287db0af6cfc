"use client";
import React from "react";

const Thinking = () => {
  return (
    <div className="flex items-center justify-center">
      <div
        className="w-20 h-20 relative overflow-hidden shadow-lg"
        style={{
          backgroundImage:
            "linear-gradient(-45deg, #a2f4fd, #a2f4fd, #00bcff, #53eafd)",
          backgroundSize: "400% 400%",
          animation:
            "gradientShift 6s ease-in-out infinite, shapeShift 4s ease-in-out infinite, rotateShape 4s ease-in-out infinite",
          borderRadius: "50%",
        }}
      />

      {/* Define the animation keyframes as a global style */}
      <style jsx global>{`
        @keyframes gradientShift {
          0% {
            background-position: 0% 50%;
          }
          25% {
            background-position: 50% 100%;
          }
          50% {
            background-position: 100% 50%;
          }
          75% {
            background-position: 50% 0%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        @keyframes shapeShift {
          0% {
            border-radius: 50%; /* Circle */
          }
          50% {
            border-radius: 8px; /* Rounded Square */
          }
          100% {
            border-radius: 50%; /* Circle */
          }
        }

        @keyframes rotateShape {
          0% {
            transform: rotate(0deg);
          }
          25% {
            transform: rotate(0deg);
          }
          50% {
            transform: rotate(90deg);
          }
          75% {
            transform: rotate(90deg);
          }
          100% {
            transform: rotate(
              360deg
            ); /* Complete a full rotation instead of going back */
          }
        }
      `}</style>
    </div>
  );
};

export default Thinking;
