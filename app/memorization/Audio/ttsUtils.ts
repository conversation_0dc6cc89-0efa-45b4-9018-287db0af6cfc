// Enhanced ttsUtils.ts - Optimized for conversation-style TTS with priority queuing and Arabic support

// Enhanced cache system for TTS audio with conversation context
const ttsCache = new Map<
  string,
  {
    url: string;
    audioElement: HTMLAudioElement;
    timestamp: number;
    usageCount: number;
    context: "lesson" | "conversation" | "urgent";
    priority: number;
    voiceProfile: string;
    isArabicOptimized: boolean;
  }
>();

// NEW: Define the specific context types for the queue
type TTSContext = "lesson" | "conversation" | "feedback" | "instruction";

// NEW: Priority queue for conversation TTS requests
interface TTSQueueItem {
  id: string;
  text: string;
  priority: "urgent" | "high" | "normal" | "low";
  context: TTSContext;
  voiceSettings: VoiceSettings;
  resolve: (audio: HTMLAudioElement) => void;
  reject: (error: any) => void;
  timestamp: number;
  maxRetries: number;
  retryCount: number;
}

// NEW: Voice settings for different contexts
interface VoiceSettings {
  voice: string;
  speed: number;
  pitch?: number;
  emphasis?: number;
  pauseDuration?: number;
  arabicMode?: boolean;
  emotionalTone?: "encouraging" | "neutral" | "corrective" | "celebratory";
}

// NEW: Conversation state for TTS management
interface ConversationState {
  isActive: boolean;
  currentSpeaker: "user" | "ai" | "none";
  canInterrupt: boolean;
  urgentMode: boolean;
  lastResponseTime: number;
  conversationId: string;
}

// Enhanced cache configuration
const MAX_CACHE_SIZE = 150; // Increased for conversation cache
const CACHE_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours
const CONVERSATION_CACHE_EXPIRY_MS = 2 * 60 * 60 * 1000; // 2 hours for conversation cache
const URGENT_CACHE_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes for urgent responses

// Define the TTS API endpoint URL
const TTS_API_URL = "https://api.openai.com/v1/audio/speech";

// Enhanced rate limiting for conversation mode
let lastApiCallTime = 0;
const MIN_API_CALL_INTERVAL = 200; // Reduced for conversation mode
const CONVERSATION_MIN_INTERVAL = 100; // Even faster for urgent conversation responses

// Track in-flight requests to avoid duplicate calls for the same text
const pendingRequests = new Map<string, Promise<HTMLAudioElement>>();

// NEW: Priority queue for TTS requests
const ttsQueue: TTSQueueItem[] = [];
let isProcessingQueue = false;
let queueProcessingInterval: NodeJS.Timeout | null = null;

// NEW: Current conversation state
let conversationState: ConversationState = {
  isActive: false,
  currentSpeaker: "none",
  canInterrupt: false,
  urgentMode: false,
  lastResponseTime: 0,
  conversationId: "",
};

// NEW: Currently playing TTS for interruption control
let currentTTSAudio: HTMLAudioElement | null = null;
let currentTTSController: AbortController | null = null;

// NEW: Voice profiles optimized for different contexts
const VOICE_PROFILES = {
  arabic_teacher: {
    voice: "alloy",
    speed: 0.9,
    emphasis: 1.2,
    arabicMode: true,
    emotionalTone: "encouraging" as const,
  },
  conversation_friendly: {
    voice: "nova",
    speed: 1.1,
    emphasis: 1.0,
    arabicMode: false,
    emotionalTone: "neutral" as const,
  },
  feedback_corrective: {
    voice: "echo",
    speed: 0.85,
    emphasis: 1.3,
    arabicMode: true,
    emotionalTone: "corrective" as const,
  },
  celebration: {
    voice: "shimmer",
    speed: 1.0,
    emphasis: 1.4,
    arabicMode: false,
    emotionalTone: "celebratory" as const,
  },
  instruction_clear: {
    voice: "onyx",
    speed: 0.8,
    emphasis: 1.1,
    arabicMode: false,
    emotionalTone: "neutral" as const,
  },
};

/**
 * Generate an enhanced cache key from text with context awareness
 */
function generateEnhancedCacheKey(
  text: string,
  context: string = "lesson",
  voiceProfile: string = "arabic_teacher"
): string {
  const normalizedText = text.trim().toLowerCase().replace(/\s+/g, " ");

  if (normalizedText.length > 100) {
    let hash = 0;
    for (let i = 0; i < normalizedText.length; i++) {
      const char = normalizedText.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return `tts_${context}_${voiceProfile}_${hash}_${normalizedText.substring(
      0,
      50
    )}`;
  }

  return `tts_${context}_${voiceProfile}_${normalizedText}`;
}

/**
 * Generate a consistent cache key from text (legacy support)
 */
function generateCacheKey(text: string): string {
  return generateEnhancedCacheKey(text, "lesson", "arabic_teacher");
}

/**
 * Sleep function for async/await
 */
function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Enhanced cache cleanup with context-aware retention
 */
function cleanUpCache(): void {
  if (ttsCache.size <= MAX_CACHE_SIZE * 0.8) {
    return;
  }

  console.log(
    `Enhanced cache cleanup triggered. Current size: ${ttsCache.size}`
  );

  const entries = Array.from(ttsCache.entries());
  const now = Date.now();

  // Separate entries by context and priority
  const urgentEntries = entries.filter(
    ([_, entry]) => entry.context === "urgent"
  );
  const conversationEntries = entries.filter(
    ([_, entry]) => entry.context === "conversation"
  );
  const lessonEntries = entries.filter(
    ([_, entry]) => entry.context === "lesson"
  );

  // Sort each category by usage and age
  const sortByUsageAndAge = (a: any, b: any) => {
    const usageDiff = b[1].usageCount - a[1].usageCount;
    if (usageDiff !== 0) return usageDiff;
    return b[1].timestamp - a[1].timestamp;
  };

  urgentEntries.sort(sortByUsageAndAge);
  conversationEntries.sort(sortByUsageAndAge);
  lessonEntries.sort(sortByUsageAndAge);

  // Remove expired entries first
  let removedCount = 0;
  const targetRemoveCount = Math.ceil(MAX_CACHE_SIZE * 0.3);

  // Remove expired urgent entries (30 min)
  urgentEntries.forEach(([key, entry]) => {
    if (removedCount >= targetRemoveCount) return;
    if (now - entry.timestamp > URGENT_CACHE_EXPIRY_MS) {
      URL.revokeObjectURL(entry.url);
      ttsCache.delete(key);
      removedCount++;
    }
  });

  // Remove expired conversation entries (2 hours)
  conversationEntries.forEach(([key, entry]) => {
    if (removedCount >= targetRemoveCount) return;
    if (now - entry.timestamp > CONVERSATION_CACHE_EXPIRY_MS) {
      URL.revokeObjectURL(entry.url);
      ttsCache.delete(key);
      removedCount++;
    }
  });

  // Remove expired lesson entries (24 hours)
  lessonEntries.forEach(([key, entry]) => {
    if (removedCount >= targetRemoveCount) return;
    if (now - entry.timestamp > CACHE_EXPIRY_MS) {
      URL.revokeObjectURL(entry.url);
      ttsCache.delete(key);
      removedCount++;
    }
  });

  // If still need to remove more, remove least used lesson entries first
  if (
    removedCount < targetRemoveCount &&
    ttsCache.size > MAX_CACHE_SIZE * 0.7
  ) {
    const remainingToRemove = targetRemoveCount - removedCount;
    const leastUsedLessons = lessonEntries
      .filter(([key, _]) => ttsCache.has(key))
      .slice(-remainingToRemove);

    leastUsedLessons.forEach(([key, entry]) => {
      URL.revokeObjectURL(entry.url);
      ttsCache.delete(key);
      removedCount++;
    });
  }

  console.log(
    `Enhanced cache cleanup: removed ${removedCount} entries. New size: ${ttsCache.size}`
  );
}

/**
 * Check if text is already cached with context awareness
 */
export function isTextCached(
  text: string,
  context: string = "lesson",
  voiceProfile: string = "arabic_teacher"
): boolean {
  const cacheKey = generateEnhancedCacheKey(text, context, voiceProfile);
  return ttsCache.has(cacheKey);
}

/**
 * Legacy support for isTextCached
 */
export function isTextCachedLegacy(text: string): boolean {
  const cacheKey = generateCacheKey(text);
  return ttsCache.has(cacheKey);
}

/**
 * NEW: Initialize conversation mode for TTS
 */
export function initializeConversationMode(conversationId: string): void {
  conversationState = {
    isActive: true,
    currentSpeaker: "none",
    canInterrupt: true,
    urgentMode: false,
    lastResponseTime: 0,
    conversationId,
  };

  // Start queue processing for conversation mode
  if (!queueProcessingInterval) {
    queueProcessingInterval = setInterval(processQueue, 50); // Process every 50ms for responsiveness
  }

  console.log("Conversation mode initialized:", conversationId);
}

/**
 * NEW: End conversation mode
 */
export function endConversationMode(): void {
  conversationState.isActive = false;

  if (queueProcessingInterval) {
    clearInterval(queueProcessingInterval);
    queueProcessingInterval = null;
  }

  // Clear conversation queue
  ttsQueue.length = 0;

  // Stop current audio if speaking
  if (currentTTSAudio) {
    interruptCurrentTTS();
  }

  console.log("Conversation mode ended");
}

/**
 * NEW: Add TTS request to priority queue
 */
export function queueTTSRequest(
  text: string,
  priority: "urgent" | "high" | "normal" | "low" = "normal",
  context: TTSContext = "conversation",
  voiceProfile: string = "conversation_friendly"
): Promise<HTMLAudioElement> {
  return new Promise((resolve, reject) => {
    const voiceSettings =
      VOICE_PROFILES[voiceProfile as keyof typeof VOICE_PROFILES] ||
      VOICE_PROFILES.conversation_friendly;

    const queueItem: TTSQueueItem = {
      id: `tts_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      text,
      priority,
      context,
      voiceSettings,
      resolve,
      reject,
      timestamp: Date.now(),
      maxRetries: priority === "urgent" ? 5 : 3,
      retryCount: 0,
    };

    // Insert item based on priority
    const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 };
    const insertIndex = ttsQueue.findIndex(
      (item) => priorityOrder[item.priority] > priorityOrder[priority]
    );

    if (insertIndex === -1) {
      ttsQueue.push(queueItem);
    } else {
      ttsQueue.splice(insertIndex, 0, queueItem);
    }

    console.log(
      `TTS request queued: ${priority} priority, queue length: ${ttsQueue.length}`
    );

    // If urgent and conversation is active, potentially interrupt current TTS
    if (
      priority === "urgent" &&
      conversationState.isActive &&
      conversationState.canInterrupt
    ) {
      interruptCurrentTTS();
    }
  });
}

/**
 * NEW: Process TTS queue
 */
async function processQueue(): Promise<void> {
  if (isProcessingQueue || ttsQueue.length === 0) {
    return;
  }

  isProcessingQueue = true;

  try {
    const item = ttsQueue.shift();
    if (!item) {
      isProcessingQueue = false;
      return;
    }

    console.log(
      `Processing TTS queue item: ${item.priority} - "${item.text.substring(
        0,
        50
      )}..."`
    );

    try {
      const audioElement = await generateAndCacheAudioEnhanced(
        item.text,
        item.context,
        item.voiceSettings,
        false // Don't auto-play from queue
      );

      item.resolve(audioElement);
    } catch (error) {
      console.error("TTS queue item failed:", error);

      if (item.retryCount < item.maxRetries) {
        item.retryCount++;
        console.log(
          `Retrying TTS request (${item.retryCount}/${item.maxRetries})`
        );

        // Re-queue with slight delay
        setTimeout(() => {
          ttsQueue.unshift(item);
        }, 200 * item.retryCount);
      } else {
        item.reject(error);
      }
    }
  } finally {
    isProcessingQueue = false;
  }
}

/**
 * NEW: Interrupt current TTS playback
 */
export function interruptCurrentTTS(): void {
  if (currentTTSAudio) {
    currentTTSAudio.pause();
    currentTTSAudio.currentTime = 0;
    currentTTSAudio = null;
    console.log("Current TTS interrupted");
  }

  if (currentTTSController) {
    currentTTSController.abort();
    currentTTSController = null;
  }
}

/**
 * NEW: Set conversation urgency mode
 */
export function setUrgentMode(urgent: boolean): void {
  conversationState.urgentMode = urgent;

  if (urgent && currentTTSAudio) {
    interruptCurrentTTS();
  }

  console.log("Urgent mode:", urgent);
}

/**
 * NEW: Get optimized voice settings for content
 */
function getOptimizedVoiceSettings(
  text: string,
  context: string,
  emotionalTone?: string
): VoiceSettings {
  // Detect Arabic content
  const hasArabic = /[\u0600-\u06FF]/.test(text);

  // Choose voice profile based on context and content
  let profileKey = "conversation_friendly";

  if (hasArabic && context === "lesson") {
    profileKey = "arabic_teacher";
  } else if (context === "feedback") {
    profileKey = "feedback_corrective";
  } else if (emotionalTone === "celebratory") {
    profileKey = "celebration";
  } else if (context === "instruction") {
    profileKey = "instruction_clear";
  }

  const baseSettings =
    VOICE_PROFILES[profileKey as keyof typeof VOICE_PROFILES];

  // Apply Arabic optimizations if needed
  if (hasArabic) {
    return {
      ...baseSettings,
      speed: Math.max(0.7, baseSettings.speed - 0.2), // Slower for Arabic
      emphasis: baseSettings.emphasis + 0.1,
      pauseDuration: 0.3,
      arabicMode: true,
    };
  }

  return baseSettings;
}

/**
 * Actively preload and prime an audio element to ensure immediate playback
 * This forces the browser to actually load and decode the audio data
 */
export function aggressivelyPreloadAudio(
  audioElement: HTMLAudioElement
): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!audioElement) {
      reject(new Error("No audio element provided"));
      return;
    }

    console.log("🔥 Aggressively preloading audio element");

    const originalVolume = audioElement.volume;
    audioElement.volume = 0.001;

    audioElement.load();

    if (audioElement.readyState >= 3) {
      console.log(
        "✅ Audio already loaded enough (readyState:",
        audioElement.readyState,
        ")"
      );
      audioElement.volume = originalVolume;
      resolve();
      return;
    }

    const onCanPlay = () => {
      console.log("✅ Audio canplay event fired");
      cleanup();
      audioElement.volume = originalVolume;
      resolve();
    };

    const onLoadedData = () => {
      console.log("✅ Audio loadeddata event fired");
    };

    const onError = (e: Event) => {
      cleanup();
      audioElement.volume = originalVolume;
      reject(
        new Error(
          `Audio preload error: ${(e as ErrorEvent).message || "unknown error"}`
        )
      );
    };

    const cleanup = () => {
      audioElement.removeEventListener("canplay", onCanPlay);
      audioElement.removeEventListener("loadeddata", onLoadedData);
      audioElement.removeEventListener("error", onError);
    };

    audioElement.addEventListener("canplay", onCanPlay);
    audioElement.addEventListener("loadeddata", onLoadedData);
    audioElement.addEventListener("error", onError);

    try {
      const playPromise = audioElement.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log("✅ Preload play succeeded - pausing");
            setTimeout(() => {
              audioElement.pause();
              audioElement.currentTime = 0;
            }, 10);
          })
          .catch((err) => {
            console.warn("⚠️ Silent preload play failed:", err);
            audioElement.volume = originalVolume;
            resolve();
          });
      }
    } catch (e) {
      console.warn("⚠️ Error in preload play attempt:", e);
      audioElement.volume = originalVolume;
      resolve();
    }

    setTimeout(() => {
      if (audioElement.readyState >= 2) {
        console.log(
          "⚠️ Preload timeout but audio seems ready (readyState:",
          audioElement.readyState,
          ")"
        );
        cleanup();
        audioElement.volume = originalVolume;
        resolve();
      } else {
        console.warn("⚠️ Preload timeout and audio not ready");
        cleanup();
        audioElement.volume = originalVolume;
        reject(new Error("Audio preload timeout"));
      }
    }, 3000);
  });
}

/**
 * Enhanced prefetch TTS audio without playing it
 * Returns the audio element that's already been initialized
 */
export async function prefetchTTS(
  text: string,
  priority = 0,
  context: string = "lesson",
  voiceProfile: string = "arabic_teacher"
): Promise<HTMLAudioElement | undefined> {
  if (!text || text.trim().length < 5) {
    return undefined;
  }

  const cacheKey = generateEnhancedCacheKey(text, context, voiceProfile);

  if (ttsCache.has(cacheKey)) {
    const cacheEntry = ttsCache.get(cacheKey)!;
    cacheEntry.usageCount++;
    cacheEntry.timestamp = Date.now();
    return cacheEntry.audioElement;
  }

  if (pendingRequests.has(cacheKey)) {
    return await pendingRequests.get(cacheKey);
  }

  try {
    const voiceSettings = getOptimizedVoiceSettings(text, context);
    const fetchPromise = generateAndCacheAudioEnhanced(
      text,
      context,
      voiceSettings,
      false
    );
    pendingRequests.set(cacheKey, fetchPromise);

    const audioElement = await fetchPromise;
    return audioElement;
  } catch (error) {
    console.warn(
      `Enhanced prefetch failed for "${text.substring(0, 30)}...": ${error}`
    );
    return undefined;
  } finally {
    pendingRequests.delete(cacheKey);
  }
}

/**
 * Enhanced batch prefetch multiple texts with context awareness
 */
export async function batchPrefetchTTS(
  texts: string[],
  concurrentLimit = 2,
  context: string = "lesson",
  voiceProfile: string = "arabic_teacher"
): Promise<void> {
  const uncachedTexts = texts.filter(
    (text) =>
      !isTextCached(text, context, voiceProfile) &&
      text &&
      text.trim().length >= 5
  );

  if (uncachedTexts.length === 0) {
    return;
  }

  console.log(
    `Enhanced batch prefetching ${uncachedTexts.length} TTS items for context: ${context}`
  );

  uncachedTexts.sort((a, b) => a.length - b.length);

  for (let i = 0; i < uncachedTexts.length; i += concurrentLimit) {
    const batch = uncachedTexts.slice(i, i + concurrentLimit);

    await Promise.all(
      batch.map((text) =>
        prefetchTTS(text, 0, context, voiceProfile).catch((err) => {
          console.warn(`Enhanced batch prefetch error: ${err}`);
          return undefined;
        })
      )
    );

    if (i + concurrentLimit < uncachedTexts.length) {
      await sleep(500);
    }
  }
}

/**
 * Enhanced batch prefetch with aggressive preloading and context optimization
 */
export async function enhancedBatchPrefetchTTS(
  texts: string[],
  concurrentLimit = 2,
  context: string = "lesson",
  voiceProfile: string = "arabic_teacher"
): Promise<void> {
  const uncachedTexts = texts.filter(
    (text) =>
      !isTextCached(text, context, voiceProfile) &&
      text &&
      text.trim().length >= 5
  );

  if (uncachedTexts.length === 0) {
    return;
  }

  console.log(
    `Enhanced batch prefetching ${uncachedTexts.length} TTS items with aggressive preloading`
  );

  // Prioritize by context and content type
  uncachedTexts.sort((a, b) => {
    const aHasArabic = /[\u0600-\u06FF]/.test(a);
    const bHasArabic = /[\u0600-\u06FF]/.test(b);

    if (aHasArabic && !bHasArabic) return -1;
    if (!aHasArabic && bHasArabic) return 1;

    return a.length - b.length;
  });

  for (let i = 0; i < uncachedTexts.length; i += concurrentLimit) {
    const batch = uncachedTexts.slice(i, i + concurrentLimit);

    const audioElements = await Promise.all(
      batch.map(async (text) => {
        try {
          return await prefetchTTS(text, 0, context, voiceProfile);
        } catch (err) {
          console.warn(`Enhanced batch prefetch error: ${err}`);
          return undefined;
        }
      })
    );

    await Promise.all(
      audioElements
        .filter((el) => el !== undefined)
        .map((audioElement) =>
          aggressivelyPreloadAudio(audioElement as HTMLAudioElement).catch(
            (err) => console.warn(`Aggressive preload error: ${err}`)
          )
        )
    );

    if (i + concurrentLimit < uncachedTexts.length) {
      await sleep(300); // Reduced delay for conversation mode
    }
  }

  console.log("Enhanced batch prefetch complete with context optimization");
}

/**
 * Enhanced core function to generate and cache audio with conversation features
 */
async function generateAndCacheAudioEnhanced(
  text: string,
  context: string = "lesson",
  voiceSettings: VoiceSettings,
  autoPlay = false
): Promise<HTMLAudioElement> {
  if (!text || text.trim() === "") {
    throw new Error("No text provided for TTS synthesis.");
  }

  const cacheKey = generateEnhancedCacheKey(text, context, voiceSettings.voice);

  if (
    pendingRequests.has(cacheKey) &&
    cacheKey !== pendingRequests.keys().next().value
  ) {
    return pendingRequests.get(cacheKey)!;
  }

  try {
    cleanUpCache();

    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error("API key for OpenAI TTS is not configured.");
    }

    // Enhanced payload with voice optimization
    const payload = {
      model:
        context === "conversation" || context === "urgent" ? "tts-1" : "tts-1", // Keep consistent for now
      voice: voiceSettings.voice,
      input: text,
      speed: voiceSettings.speed,
      // Note: OpenAI API doesn't support all these parameters, but we prepare for future enhancements
    };

    // Enhanced rate limiting for conversation mode
    const now = Date.now();
    const minInterval = conversationState.isActive
      ? CONVERSATION_MIN_INTERVAL
      : MIN_API_CALL_INTERVAL;
    const timeSinceLastCall = now - lastApiCallTime;

    if (timeSinceLastCall < minInterval) {
      console.log(
        `Enhanced rate limiting: waiting ${
          minInterval - timeSinceLastCall
        }ms for context: ${context}`
      );
      await sleep(minInterval - timeSinceLastCall);
    }

    lastApiCallTime = Date.now();

    console.log(
      `Generating TTS for context: ${context}, voice: ${voiceSettings.voice}, Arabic mode: ${voiceSettings.arabicMode}`
    );

    // Create abort controller for interruption support
    currentTTSController = new AbortController();

    const response = await fetch(TTS_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
      signal: currentTTSController.signal,
    });

    currentTTSController = null;

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error("Rate limited by OpenAI API. Try again in a moment.");
      } else if (response.status === 401) {
        throw new Error("API key authentication error.");
      } else {
        const errorText = await response.text();
        throw new Error(`TTS API Error (${response.status}): ${errorText}`);
      }
    }

    const audioBlob = await response.blob();
    const audioUrl = URL.createObjectURL(audioBlob);

    const audioElement = new Audio(audioUrl);
    audioElement.load();

    // Enhanced cache entry with context and voice information
    const priority =
      context === "urgent" ? 10 : context === "conversation" ? 5 : 1;

    ttsCache.set(cacheKey, {
      url: audioUrl,
      audioElement,
      timestamp: Date.now(),
      usageCount: 1,
      context: context as "lesson" | "conversation" | "urgent",
      priority,
      voiceProfile: voiceSettings.voice,
      isArabicOptimized: voiceSettings.arabicMode || false,
    });

    if (autoPlay) {
      try {
        currentTTSAudio = audioElement;
        await audioElement.play();

        audioElement.onended = () => {
          currentTTSAudio = null;
          conversationState.lastResponseTime = Date.now();
        };
      } catch (playError) {
        console.error("Error auto-playing enhanced TTS audio:", playError);
      }
    }

    console.log(`Enhanced TTS generated successfully for context: ${context}`);
    return audioElement;
  } catch (error) {
    console.error("Error generating enhanced TTS audio:", error);
    throw error;
  }
}

/**
 * Legacy core function to generate and cache audio (preserved for compatibility)
 */
async function generateAndCacheAudio(
  text: string,
  autoPlay = false
): Promise<HTMLAudioElement> {
  const defaultVoiceSettings: VoiceSettings = {
    voice: "alloy",
    speed: 1.2,
    arabicMode: false,
    emotionalTone: "neutral",
  };

  return generateAndCacheAudioEnhanced(
    text,
    "lesson",
    defaultVoiceSettings,
    autoPlay
  );
}

/**
 * Enhanced main function to synthesize speech with conversation support
 */
export async function synthesizeSpeech(
  text: string,
  maxRetries = 3,
  playAudio = true,
  // FIX: Changed the type of the context parameter from a generic string
  // to a specific union type to ensure type safety in recursive calls.
  context:
    | "lesson"
    | "conversation"
    | "feedback"
    | "instruction"
    | "urgent" = "lesson",
  priority: "urgent" | "high" | "normal" | "low" = "normal",
  voiceProfile: string = "arabic_teacher"
): Promise<HTMLAudioElement | undefined> {
  try {
    // If in conversation mode and priority allows, use queue system
    if (conversationState.isActive && priority !== "urgent") {
      console.log("Using conversation queue for TTS request");
      // This cast is safe because 'urgent' is handled above
      return await queueTTSRequest(
        text,
        priority,
        context as TTSContext,
        voiceProfile
      );
    }

    const cacheKey = generateEnhancedCacheKey(text, context, voiceProfile);

    if (ttsCache.has(cacheKey)) {
      const cacheEntry = ttsCache.get(cacheKey)!;
      cacheEntry.usageCount++;
      cacheEntry.timestamp = Date.now();

      console.log("Using cached enhanced TTS audio ✓");

      if (playAudio) {
        try {
          const audio = cacheEntry.audioElement;
          audio.currentTime = 0;

          currentTTSAudio = audio;
          await audio.play();

          audio.onended = () => {
            currentTTSAudio = null;
            conversationState.lastResponseTime = Date.now();
          };

          return audio;
        } catch (playError) {
          console.warn(
            "Error playing cached enhanced audio, recreating element:",
            playError
          );
          const newAudio = new Audio(cacheEntry.url);
          cacheEntry.audioElement = newAudio;
          currentTTSAudio = newAudio;
          await newAudio.play();
          return newAudio;
        }
      }
      return cacheEntry.audioElement;
    }

    let audioElement: HTMLAudioElement;

    if (pendingRequests.has(cacheKey)) {
      console.log("Enhanced request already in progress, waiting for result");
      audioElement = await pendingRequests.get(cacheKey)!;
    } else {
      const voiceSettings = getOptimizedVoiceSettings(text, context);
      const fetchPromise = generateAndCacheAudioEnhanced(
        text,
        context,
        voiceSettings,
        playAudio
      );
      pendingRequests.set(cacheKey, fetchPromise);

      try {
        audioElement = await fetchPromise;
      } finally {
        pendingRequests.delete(cacheKey);
      }
    }

    return audioElement;
  } catch (error) {
    console.error("Error in enhanced synthesizeSpeech:", error);

    // Retry logic for conversation mode
    if (conversationState.isActive && maxRetries > 0) {
      console.log(
        `Retrying enhanced TTS in conversation mode (${maxRetries} retries left)`
      );
      await sleep(200);
      return synthesizeSpeech(
        text,
        maxRetries - 1,
        playAudio,
        context,
        priority,
        voiceProfile
      );
    }

    throw error instanceof Error
      ? error
      : new Error("Failed to generate speech. Please try again.");
  }
}

/**
 * NEW: Conversation-optimized TTS synthesis
 */
export async function synthesizeConversationSpeech(
  text: string,
  priority: "urgent" | "high" | "normal" | "low" = "normal",
  emotionalTone?: "encouraging" | "neutral" | "corrective" | "celebratory",
  playImmediately = true
): Promise<HTMLAudioElement | undefined> {
  // Choose voice profile based on content and tone
  let voiceProfile = "conversation_friendly";
  const hasArabic = /[\u0600-\u06FF]/.test(text);

  if (hasArabic) {
    voiceProfile = "arabic_teacher";
  } else if (emotionalTone === "corrective") {
    voiceProfile = "feedback_corrective";
  } else if (emotionalTone === "celebratory") {
    voiceProfile = "celebration";
  }

  return synthesizeSpeech(
    text,
    3,
    playImmediately,
    "conversation",
    priority,
    voiceProfile
  );
}

/**
 * NEW: Quick response for urgent conversation needs
 */
export async function synthesizeUrgentResponse(
  text: string,
  playImmediately = true
): Promise<HTMLAudioElement | undefined> {
  // Interrupt current audio if playing
  if (currentTTSAudio && conversationState.canInterrupt) {
    interruptCurrentTTS();
  }

  return synthesizeSpeech(
    text,
    5, // More retries for urgent responses
    playImmediately,
    "urgent",
    "urgent",
    "conversation_friendly"
  );
}

/**
 * Clear the enhanced TTS cache with context filtering
 */
export function clearTTSCache(context?: string): void {
  if (context) {
    // Clear only specific context
    const keysToDelete: string[] = [];
    ttsCache.forEach((entry, key) => {
      if (entry.context === context) {
        URL.revokeObjectURL(entry.url);
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach((key) => ttsCache.delete(key));
    console.log(`TTS cache cleared for context: ${context}`);
  } else {
    // Clear all cache
    ttsCache.forEach((entry) => {
      URL.revokeObjectURL(entry.url);
    });
    ttsCache.clear();
    console.log("Enhanced TTS cache cleared completely");
  }
}

/**
 * Preload an audio element without playing it (preserved)
 */
export function preloadAudioElement(audioElement: HTMLAudioElement): void {
  audioElement.volume = 0;
  audioElement.muted = true;

  audioElement.load();

  const playPromise = audioElement.play();
  if (playPromise !== undefined) {
    playPromise
      .then(() => {
        audioElement.pause();
        audioElement.currentTime = 0;
        audioElement.volume = 1;
        audioElement.muted = false;
      })
      .catch((err) => {
        console.warn("Preloading audio failed:", err);
        audioElement.volume = 1;
        audioElement.muted = false;
      });
  }
}

/**
 * NEW: Get conversation statistics
 */
export function getConversationStats() {
  const stats = {
    conversationActive: conversationState.isActive,
    queueLength: ttsQueue.length,
    cacheSize: ttsCache.size,
    lastResponseTime: conversationState.lastResponseTime,
    currentSpeaker: conversationState.currentSpeaker,
    urgentMode: conversationState.urgentMode,
  };

  return stats;
}

/**
 * NEW: Optimize cache for conversation performance
 */
export function optimizeCacheForConversation(): void {
  console.log("Optimizing TTS cache for conversation performance");

  // Prioritize conversation and urgent entries
  const entries = Array.from(ttsCache.entries());
  const conversationEntries = entries.filter(
    ([_, entry]) =>
      entry.context === "conversation" || entry.context === "urgent"
  );

  // Preload conversation entries
  conversationEntries.forEach(([_, entry]) => {
    aggressivelyPreloadAudio(entry.audioElement).catch(console.warn);
  });

  console.log(`Optimized ${conversationEntries.length} conversation entries`);
}

/**
 * NEW: Set conversation speaker state
 */
export function setCurrentSpeaker(speaker: "user" | "ai" | "none"): void {
  conversationState.currentSpeaker = speaker;

  if (speaker === "user" && currentTTSAudio && conversationState.canInterrupt) {
    interruptCurrentTTS();
  }
}

/**
 * NEW: Enable/disable TTS interruption
 */
export function setInterruptionMode(canInterrupt: boolean): void {
  conversationState.canInterrupt = canInterrupt;
  console.log("TTS interruption mode:", canInterrupt);
}
