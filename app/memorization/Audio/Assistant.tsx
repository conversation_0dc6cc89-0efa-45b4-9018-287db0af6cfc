"use client";

import React, { ReactNode, useMemo } from "react";
import { useVoiceStore } from "@/store/useVoiceStore";
import Image from "next/image";

/* ────────────────────────────────────────────────────────────
   PROPS
   ──────────────────────────────────────────────────────────── */
interface AssistantVisualProps {
  inputAmplitude?: number; // mic level 0-1 (still passed as prop for performance)
  className?: string;
  sphereSize?: number;
}

const Assistant: React.FC<AssistantVisualProps> = ({
  inputAmplitude = 0,
  className = "",
  sphereSize = 180,
}) => {
  // Get state from voice store with individual subscriptions for better reactivity
  const isPlaying = useVoiceStore((state) => state.isSpeaking);
  const isRecording = useVoiceStore((state) => state.isListening);
  const transcript = useVoiceStore((state) => state.transcript);
  const aiResponse = useVoiceStore((state) => state.aiResponse);
  const conversationActive = useVoiceStore((state) => state.conversationActive);
  const connectionStatus = useVoiceStore((state) => state.connectionStatus);
  const isAssistantSpeaking = useVoiceStore(
    (state) => state.isAssistantSpeaking
  );

  // Derived backend loading state
  const isBackendLoading = useMemo(() => {
    return (
      conversationActive &&
      !isRecording &&
      transcript !== "" &&
      aiResponse === ""
    );
  }, [conversationActive, isRecording, transcript, aiResponse]);

  // Derived TTS loading state
  const isTTSLoading = useMemo(() => {
    return aiResponse !== "" && !isPlaying;
  }, [aiResponse, isPlaying]);

  /* Mic-pulse scale (clamped to +30 %) - simplified conditions for better reactivity */
  const pulseScaleFactor = useMemo(() => {
    // Only scale when we have amplitude and are actively recording
    if (isRecording && inputAmplitude > 0.001) {
      const amplified = inputAmplitude * 1.5; // Increased multiplier for more visible scaling
      return 1 + Math.min(amplified, 0.5); // Increased max scale to 50%
    }
    return 1;
  }, [inputAmplitude, isRecording]);

  /* 
    Dev-only diagnostics.
    This log is now off by default. To enable it for a debugging session,
    open your browser's console and type:
    window.DEBUG_ASSISTANT = true
  */
  if (
    process.env.NODE_ENV === "development" &&
    typeof window !== "undefined" &&
    (window as any).DEBUG_ASSISTANT === true
  ) {
    // eslint-disable-next-line no-console
    console.log(
      `[Assistant] rec=${isRecording} amp=${inputAmplitude.toFixed(
        3
      )} scale=${pulseScaleFactor.toFixed(3)} speaking=${isAssistantSpeaking}`
    );
  }

  /* Status message */
  let statusMessage: ReactNode = (
    <>
      Click a citation when they appear to{" "}
      <strong className="text-base font-black">hear</strong> feedback
    </>
  );

  if (isBackendLoading) {
    statusMessage = "Processing your recitation...";
  } else if (isTTSLoading) {
    statusMessage = "Loading audio feedback...";
  } else if (isRecording && inputAmplitude > 0.0005) {
    statusMessage = "Listening to your recitation...";
  } else if (isPlaying || isAssistantSpeaking) {
    statusMessage = "Now playing feedback...";
  } else if (conversationActive && connectionStatus === "connected") {
    statusMessage = "Ready to listen...";
  } else if (conversationActive && connectionStatus === "connecting") {
    statusMessage = "Connecting...";
  }

  /* ──────────────────────────────────────────────────────────
     RENDER
     ────────────────────────────────────────────────────────── */
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div style={{ height: "20px" }} />

      {/* Container for glow + sphere */}
      <div
        className="mt-6 flex items-center justify-center"
        style={{
          width: sphereSize + 60,
          height: sphereSize + 60,
          position: "relative",
        }}
      >
        {/* Purple glow while TTS plays - enhanced conditions */}
        {(isPlaying || isAssistantSpeaking) && (
          <div
            style={{
              position: "absolute",
              inset: "5%",
              borderRadius: "50%",
              background:
                "radial-gradient(circle at center, rgba(128,0,128,0.8) 0%, rgba(128,0,128,0.4) 40%, rgba(128,0,128,0.2) 70%, transparent 100%)",
              filter: "blur(8px)",
              transition: "opacity 0.3s ease-in-out",
              opacity: 1,
              zIndex: 1,
              animation: "pulse 2s ease-in-out infinite",
            }}
          />
        )}

        {/* Blue glow while listening */}
        {isRecording && (
          <div
            style={{
              position: "absolute",
              inset: "5%",
              borderRadius: "50%",
              background:
                "radial-gradient(circle at center, rgba(59,130,246,0.7) 0%, rgba(59,130,246,0.3) 40%, rgba(59,130,246,0.1) 70%, transparent 100%)",
              filter: "blur(6px)",
              transition: "opacity 0.3s ease",
              zIndex: 1,
            }}
          />
        )}

        {/* ███  Sphere  ███ */}
        <div
          style={{
            width: `${sphereSize}px`,
            height: `${sphereSize}px`,
            borderRadius: "50%",
            overflow: "hidden",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            backgroundColor: "transparent",

            // Simplified scaling logic - scale when recording and has amplitude
            transform: `scale(${pulseScaleFactor})`,
            transition:
              isRecording && inputAmplitude > 0.001
                ? "transform 100ms ease-out"
                : "transform 300ms ease-out",
            willChange: "transform",
            zIndex: 2,
          }}
        >
          <Image
            src="/DarkGradient.svg"
            alt="Audio Visualizer Background"
            fill
            className="opacity-90 hover:opacity-100 object-cover"
          />
        </div>
      </div>

      {/* Add CSS for purple glow animation */}
      <style jsx>{`
        @keyframes pulse {
          0%,
          100% {
            opacity: 0.8;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.05);
          }
        }
      `}</style>
    </div>
  );
};

export default Assistant;
