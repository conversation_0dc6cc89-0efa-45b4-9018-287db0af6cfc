// ====================== Enhanced summaryFeedbackUtils.ts ======================
import React from "react";

/**
 * Extract plain text from JSX elements
 * @param node React node to extract text from
 * @returns Plain text content
 */
export function extractTextFromJSX(node: React.ReactNode): string {
  if (typeof node === "string" || typeof node === "number") {
    return node.toString();
  }
  if (Array.isArray(node)) {
    return node.map(extractTextFromJSX).join(" ");
  }
  if (React.isValidElement(node)) {
    return extractTextFromJSX(node.props.children);
  }
  return "";
}

/**
 * Parse feedback elements to identify their type based on content and structure
 * @param feedbackElement The React element containing feedback
 * @returns The identified feedback type and key information
 */
export function parseFeedbackElement(feedbackElement: JSX.Element): {
  type: string;
  title: string;
  userWord?: string;
  correctWord?: string;
  letterFeedback?: any[];
  citationNumber?: number;
  generalMessage?: string;
  priority?: number; // Added priority field for sorting feedback
} {
  if (!feedbackElement || !feedbackElement.props) {
    return { type: "unknown", title: "" };
  }

  // Try to get the citation number if available
  const citationNumber = feedbackElement.props["data-citation-number"]
    ? parseInt(feedbackElement.props["data-citation-number"])
    : undefined;

  // Extract the title from the h3 element if present
  let title = "";
  let generalMessage = "";
  let userWord = "";
  let correctWord = "";
  let letterFeedback: any[] = [];

  // Recursively search for title, user word, correct word, and letter feedback
  const extractInfo = (node: React.ReactNode) => {
    if (!node) return;

    if (React.isValidElement(node)) {
      // Look for heading elements that might contain the title
      if (
        node.type === "h3" ||
        (node.props &&
          node.props.className &&
          node.props.className.includes("text-lg font-semibold"))
      ) {
        title = extractTextFromJSX(node);
      }

      // Look for the general message/explanation
      if (
        node.props &&
        node.props.className &&
        node.props.className.includes("text-sm text-gray-700 leading-relaxed")
      ) {
        generalMessage = extractTextFromJSX(node);
      }

      // Look for the user word (usually in a span with bg-red-50)
      if (
        node.props &&
        node.props.className &&
        node.props.className.includes("bg-red-50 text-red-600") &&
        !node.props.className.includes("rounded-lg text-xl") // Exclude letter-level feedback
      ) {
        userWord = extractTextFromJSX(node);
      }

      // Look for the correct word (usually in a span with bg-green-50)
      if (
        node.props &&
        node.props.className &&
        node.props.className.includes("bg-green-50 text-green-600") &&
        !node.props.className.includes("rounded-lg text-xl") // Exclude letter-level feedback
      ) {
        correctWord = extractTextFromJSX(node);
      }

      // Process children
      if (node.props && node.props.children) {
        if (Array.isArray(node.props.children)) {
          node.props.children.forEach(extractInfo);
        } else {
          extractInfo(node.props.children);
        }
      }
    }
  };

  extractInfo(feedbackElement);

  // Determine the feedback type based on the title and content
  let type = "unknown";
  let priority = 3; // Default priority (medium)

  if (
    title.includes("Different Letter Count") ||
    title.includes("Oops! Different Letter Count")
  ) {
    type = "letter_count_mismatch";
    priority = 2; // High priority
  } else if (
    title.includes("Close to Correct") ||
    title.includes("Good Effort! Close to Correct")
  ) {
    type = "partial_match";
    priority = 3; // Medium priority
  } else if (
    title.includes("Extra Words") ||
    title.includes("Oops! Extra Words Found")
  ) {
    type = "extra_words";
    priority = 4; // Lower priority
  } else if (title.includes("Perfect Recitation")) {
    type = "perfect_recitation";
    priority = 0; // Highest priority - congratulatory feedback
  } else if (title.includes("completed all verses")) {
    type = "completed_with_issues";
    priority = 1; // High priority - completion feedback
  } else if (title.includes("Missing Words")) {
    type = "missing_words";
    priority = 1; // High priority
  } else if (title.includes("Congratulations")) {
    type = "congratulations";
    priority = 0; // Highest priority
  }

  return {
    type,
    title,
    userWord,
    correctWord,
    letterFeedback,
    citationNumber,
    generalMessage,
    priority,
  };
}

/**
 * Extract letter feedback issues in a structured format
 * @param feedbackElement The React element containing feedback
 * @returns Array of letter feedback issues
 */
export function extractLetterFeedback(feedbackElement: JSX.Element): Array<{
  type: string;
  userLetters?: string[];
  correctLetters?: string[];
  position?: string;
  vowelInfo?: string;
  sound?: string;
}> {
  const result: Array<{
    type: string;
    userLetters?: string[];
    correctLetters?: string[];
    position?: string;
    vowelInfo?: string;
    sound?: string;
  }> = [];

  // Recursively search for letter feedback
  const extractLetterInfo = (node: React.ReactNode) => {
    if (!node) return;

    if (React.isValidElement(node)) {
      // Check if this node represents an Extra Letters group
      if (
        node.props &&
        node.props.className &&
        node.props.className.includes("rounded-lg space-y-1.5") &&
        extractTextFromJSX(node).includes("Extra Letters")
      ) {
        // Find all the extra letters (usually in spans with bg-red-50)
        const extraLetters: string[] = [];
        const extractExtraLetters = (innerNode: React.ReactNode) => {
          if (!innerNode) return;

          if (React.isValidElement(innerNode)) {
            if (
              innerNode.props &&
              innerNode.props.className &&
              innerNode.props.className.includes(
                "bg-red-50 text-red-600 rounded-lg text-xl"
              )
            ) {
              extraLetters.push(extractTextFromJSX(innerNode));
            }

            if (innerNode.props && innerNode.props.children) {
              if (Array.isArray(innerNode.props.children)) {
                innerNode.props.children.forEach(extractExtraLetters);
              } else {
                extractExtraLetters(innerNode.props.children);
              }
            }
          }
        };

        extractExtraLetters(node);

        // Get position text
        let position = "";
        const textContent = extractTextFromJSX(node);
        const positionMatch = textContent.match(
          /(near the start|in the middle|near the end)/i
        );
        if (positionMatch) {
          position = positionMatch[0];
        }

        if (extraLetters.length > 0) {
          result.push({
            type: "extra_letters",
            userLetters: extraLetters,
            position,
          });
        }
      }

      // Check if this node represents a Missing Letters group
      if (
        node.props &&
        node.props.className &&
        node.props.className.includes("rounded-lg space-y-1.5") &&
        extractTextFromJSX(node).includes("Missing Letters")
      ) {
        // Find all the missing letters (usually in spans with bg-blue-50)
        const missingLetters: string[] = [];
        const extractMissingLetters = (innerNode: React.ReactNode) => {
          if (!innerNode) return;

          if (React.isValidElement(innerNode)) {
            if (
              innerNode.props &&
              innerNode.props.className &&
              innerNode.props.className.includes(
                "bg-blue-50 text-blue-600 rounded-lg text-xl"
              )
            ) {
              missingLetters.push(extractTextFromJSX(innerNode));
            }

            if (innerNode.props && innerNode.props.children) {
              if (Array.isArray(innerNode.props.children)) {
                innerNode.props.children.forEach(extractMissingLetters);
              } else {
                extractMissingLetters(innerNode.props.children);
              }
            }
          }
        };

        extractMissingLetters(node);

        // Get position text
        let position = "";
        const textContent = extractTextFromJSX(node);
        const positionMatch = textContent.match(
          /(near the start|in the middle|near the end)/i
        );
        if (positionMatch) {
          position = positionMatch[0];
        }

        if (missingLetters.length > 0) {
          result.push({
            type: "missing_letters",
            correctLetters: missingLetters,
            position,
          });
        }
      }

      // Check for substitution feedback
      if (
        node.props &&
        node.props.className &&
        node.props.className.includes("space-y-3 p-2.5") &&
        extractTextFromJSX(node).includes("Your letter") &&
        extractTextFromJSX(node).includes("Correct letter")
      ) {
        let userLetter = "";
        let correctLetter = "";
        let sound = "";
        let vowelInfo = "";
        let position = "";

        // Extract position info
        const text = extractTextFromJSX(node);
        const positionMatch = text.match(
          /(near the start|in the middle|near the end) of the word/i
        );
        if (positionMatch) {
          position = positionMatch[0];
        }

        // Extract user letter, correct letter, sound and vowel info
        const extractSubstitutionInfo = (innerNode: React.ReactNode) => {
          if (!innerNode) return;

          if (React.isValidElement(innerNode)) {
            // User letter
            if (
              innerNode.props &&
              innerNode.props.className &&
              innerNode.props.className.includes(
                "bg-red-50 text-red-600 rounded-lg text-xl"
              )
            ) {
              userLetter = extractTextFromJSX(innerNode);
            }

            // Correct letter
            if (
              innerNode.props &&
              innerNode.props.className &&
              innerNode.props.className.includes(
                "bg-green-50 text-green-600 rounded-lg text-xl"
              )
            ) {
              correctLetter = extractTextFromJSX(innerNode);
            }

            // Sound and vowel info
            if (
              innerNode.props &&
              innerNode.props.className &&
              innerNode.props.className.includes("text-sm text-gray-700")
            ) {
              const infoText = extractTextFromJSX(innerNode);
              if (infoText.includes("Sound:")) {
                sound = infoText.replace("Sound:", "").trim();
              } else if (infoText.includes("Vowel:")) {
                vowelInfo = infoText.replace("Vowel:", "").trim();
              }
            }

            if (innerNode.props && innerNode.props.children) {
              if (Array.isArray(innerNode.props.children)) {
                innerNode.props.children.forEach(extractSubstitutionInfo);
              } else {
                extractSubstitutionInfo(innerNode.props.children);
              }
            }
          }
        };

        extractSubstitutionInfo(node);

        if (userLetter && correctLetter) {
          result.push({
            type: "substitution",
            userLetters: [userLetter],
            correctLetters: [correctLetter],
            position,
            sound,
            vowelInfo,
          });
        }
      }

      // Process children
      if (node.props && node.props.children) {
        if (Array.isArray(node.props.children)) {
          node.props.children.forEach(extractLetterInfo);
        } else {
          extractLetterInfo(node.props.children);
        }
      }
    }
  };

  extractLetterInfo(feedbackElement);
  return result;
}

// Define templates for variety in language
const feedbackTemplates = {
  extraLetters: [
    "You added {letter} that isn't needed",
    "Remove {letter} from your recitation",
    "The extra {letter} should be omitted",
  ],
  missingLetters: [
    "You need to include {letter}",
    "Add {letter} to your recitation",
    "The letter {letter} is missing",
  ],
  substitution: [
    "Replace {userLetter} with {correctLetter}",
    "You said {userLetter} instead of {correctLetter}",
    "{userLetter} should be pronounced as {correctLetter}",
  ],
  position: {
    "near the start": ["at the beginning", "in the first part", "at the start"],
    "in the middle": ["in the middle", "in the center part", "midway through"],
    "near the end": ["at the end", "in the final part", "towards the end"],
  },
  citationIntro: [
    "For word {number}: ",
    "Word {number}: ",
    "Correction {number}: ",
  ],
  soundCue: [
    "Sound: {sound}",
    "Pronounce it as: {sound}",
    "It sounds like: {sound}",
  ],
};

/**
 * Select a random template from the available options
 * @param templates Array of template strings
 * @returns A randomly selected template
 */
function getRandomTemplate(templates: string[]): string {
  const index = Math.floor(Math.random() * templates.length);
  return templates[index];
}

/**
 * Format letter feedback for TTS with variety in language
 * @param letterFeedback The extracted letter feedback
 * @returns A formatted string for TTS
 */
function formatLetterFeedbackForTTS(letterFeedback: Array<any>): string {
  if (!letterFeedback || letterFeedback.length === 0) {
    return "";
  }

  // Group feedback by type for compression
  const groupedFeedback = {
    extraLetters: [] as any[],
    missingLetters: [] as any[],
    substitutions: [] as any[],
  };

  letterFeedback.forEach((feedback) => {
    if (feedback.type === "extra_letters") {
      groupedFeedback.extraLetters.push(feedback);
    } else if (feedback.type === "missing_letters") {
      groupedFeedback.missingLetters.push(feedback);
    } else if (feedback.type === "substitution") {
      groupedFeedback.substitutions.push(feedback);
    }
  });

  const parts: string[] = [];

  // Process extra letters (compressed)
  if (groupedFeedback.extraLetters.length > 0) {
    const allExtraLetters: string[] = [];
    let position = "";

    groupedFeedback.extraLetters.forEach((feedback) => {
      if (feedback.userLetters) {
        allExtraLetters.push(...feedback.userLetters);
      }
      // Use the position from the first item (they're typically the same)
      if (!position && feedback.position) {
        position = feedback.position;
      }
    });

    // Compress multiple extra letters into one message
    if (allExtraLetters.length > 0) {
      const posTemplate = position
        ? getRandomTemplate(
            feedbackTemplates.position[
              position as keyof typeof feedbackTemplates.position
            ]
          )
        : "in the word";

      if (allExtraLetters.length === 1) {
        const template = getRandomTemplate(feedbackTemplates.extraLetters);
        parts.push(
          template.replace(
            "{letter}",
            `the letter "${allExtraLetters[0]}" ${posTemplate}`
          )
        );
      } else if (allExtraLetters.length <= 3) {
        parts.push(
          `Remove the extra letters ${allExtraLetters.join(
            ", "
          )} ${posTemplate}.`
        );
      } else {
        parts.push(
          `Remove ${allExtraLetters.length} extra letters ${posTemplate}.`
        );
      }
    }
  }

  // Process missing letters (compressed)
  if (groupedFeedback.missingLetters.length > 0) {
    const allMissingLetters: string[] = [];
    let position = "";

    groupedFeedback.missingLetters.forEach((feedback) => {
      if (feedback.correctLetters) {
        allMissingLetters.push(...feedback.correctLetters);
      }
      if (!position && feedback.position) {
        position = feedback.position;
      }
    });

    if (allMissingLetters.length > 0) {
      const posTemplate = position
        ? getRandomTemplate(
            feedbackTemplates.position[
              position as keyof typeof feedbackTemplates.position
            ]
          )
        : "in the word";

      if (allMissingLetters.length === 1) {
        const template = getRandomTemplate(feedbackTemplates.missingLetters);
        parts.push(
          template.replace(
            "{letter}",
            `the letter "${allMissingLetters[0]}" ${posTemplate}`
          )
        );
      } else if (allMissingLetters.length <= 3) {
        parts.push(
          `Add the missing letters ${allMissingLetters.join(
            ", "
          )} ${posTemplate}.`
        );
      } else {
        parts.push(
          `Add ${allMissingLetters.length} missing letters ${posTemplate}.`
        );
      }
    }
  }

  // Process substitutions (max 3 for conciseness)
  if (groupedFeedback.substitutions.length > 0) {
    // Limit to 3 most important substitutions for conciseness
    const limitedSubstitutions = groupedFeedback.substitutions.slice(0, 3);

    limitedSubstitutions.forEach((feedback, index) => {
      if (!feedback.userLetters?.[0] || !feedback.correctLetters?.[0]) return;

      const userLetter = feedback.userLetters[0];
      const correctLetter = feedback.correctLetters[0];
      const position = feedback.position || "in the word";

      let message = getRandomTemplate(feedbackTemplates.substitution)
        .replace("{userLetter}", userLetter)
        .replace("{correctLetter}", correctLetter);

      // Add position only for the first substitution to keep it concise
      if (index === 0) {
        const posText =
          position.includes("near the") || position.includes("in the")
            ? position
            : `${position} of the word`;
        message += ` ${posText}`;
      }

      // Add sound guidance for important substitutions
      if (feedback.sound) {
        // Use shortened sound guidance
        message += `. ${feedback.sound}`;
      }

      parts.push(message);
    });

    // If there are more substitutions, add a summary
    if (groupedFeedback.substitutions.length > 3) {
      parts.push(
        `Plus ${
          groupedFeedback.substitutions.length - 3
        } more letter corrections.`
      );
    }
  }

  // Join with natural transitions if multiple parts
  if (parts.length === 0) {
    return "";
  } else if (parts.length === 1) {
    return parts[0];
  } else {
    const transitions = ["Also, ", "Additionally, ", "Next, ", "Furthermore, "];

    return parts
      .map((part, index) => {
        if (index === 0) return part;
        return `${transitions[index % transitions.length]}${part
          .charAt(0)
          .toLowerCase()}${part.slice(1)}`;
      })
      .join(" ");
  }
}

/**
 * Generate a concise summary of feedback optimized for TTS with word count limits
 * @param feedbackElement The React element containing feedback
 * @returns A concise string summary suitable for TTS
 */
export function generateFeedbackSummary(feedbackElement: JSX.Element): string {
  if (!feedbackElement) {
    return "";
  }

  // Parse the feedback element to get its type and key information
  const parsedFeedback = parseFeedbackElement(feedbackElement);

  // Extract letter-level feedback if applicable
  const letterFeedback = extractLetterFeedback(feedbackElement);

  let summary = "";
  const MAX_WORDS = 50; // Base word count limit

  // Generate appropriate summary based on feedback type
  switch (parsedFeedback.type) {
    case "letter_count_mismatch":
      summary = `${getRandomTemplate(feedbackTemplates.citationIntro).replace(
        "{number}",
        String(parsedFeedback.citationNumber || "")
      )}You said "${parsedFeedback.userWord}" instead of "${
        parsedFeedback.correctWord
      }". The letter count is incorrect. `;
      summary += formatLetterFeedbackForTTS(letterFeedback);
      break;

    case "partial_match":
      summary = `${getRandomTemplate(feedbackTemplates.citationIntro).replace(
        "{number}",
        String(parsedFeedback.citationNumber || "")
      )}You said "${parsedFeedback.userWord}" - close to "${
        parsedFeedback.correctWord
      }". `;
      summary += formatLetterFeedbackForTTS(letterFeedback);
      break;

    case "extra_words":
      // Extract the extra words from the feedback element
      const extraWords: string[] = [];
      const extractExtraWords = (node: React.ReactNode) => {
        if (!node) return;

        if (React.isValidElement(node)) {
          if (
            node.props &&
            node.props.className &&
            node.props.className.includes("bg-red-50 text-red-600") &&
            node.props["data-word"]
          ) {
            extraWords.push(extractTextFromJSX(node));
          }

          if (node.props && node.props.children) {
            if (Array.isArray(node.props.children)) {
              node.props.children.forEach(extractExtraWords);
            } else {
              extractExtraWords(node.props.children);
            }
          }
        }
      };

      extractExtraWords(feedbackElement);

      if (extraWords.length > 0) {
        if (extraWords.length === 1) {
          summary = `You added "${extraWords[0]}" which isn't in the original text.`;
        } else if (extraWords.length <= 3) {
          summary = `You added extra words: ${extraWords.join(", ")}.`;
        } else {
          summary = `You added ${
            extraWords.length
          } extra words including ${extraWords
            .slice(0, 2)
            .join(", ")}, and others.`;
        }
      } else {
        summary = "You included extra words that aren't in the original text.";
      }
      break;

    case "perfect_recitation":
      summary = "Perfect! You've recited all verses correctly.";
      break;

    case "completed_with_issues":
      summary = "You've completed all verses of this surah. ";
      if (parsedFeedback.generalMessage) {
        // Clean up the general message for TTS
        let cleanedMessage = parsedFeedback.generalMessage
          .replace(
            /While there were a few extra words in your recitation, /i,
            ""
          )
          .replace(
            /While your final recitation included /i,
            "Your recitation had "
          )
          .replace(
            /you've successfully memorized the entire surah/i,
            "you've memorized the entire surah"
          )
          .replace(/Take a moment to review the feedback, then /i, "")
          .replace(/you've made great progress/i, "you've made progress");

        summary += cleanedMessage;
      } else {
        summary += "Review the feedback to refine your recitation further.";
      }
      break;

    case "missing_words":
      summary = "Your recitation is missing some words from the original text.";
      break;

    case "congratulations":
      summary = "Congratulations! Perfect recitation of the complete Surah.";
      break;

    default:
      // Fall back to extracting text directly with improvements
      const rawText = extractTextFromJSX(feedbackElement)
        .replace(/\[\d+\]/g, "") // Remove citation numbers
        .replace(/You pronounced/gi, "Pronounced")
        .replace(/You said/gi, "Said")
        .replace(/The correct pronunciation is/gi, "Correct pronunciation:")
        .replace(/Remember to/gi, "Please")
        .replace(/It should be pronounced/gi, "Pronounce it")
        .replace(/Make sure to/gi, "")
        .replace(/Try to focus on/gi, "Focus on")
        .replace(/Pay attention to/gi, "Note");

      // Split into sentences and keep only the most important ones
      const sentences = rawText
        .split(/[.!?]+/)
        .filter((s) => s.trim().length > 0);

      if (sentences.length > 3) {
        const keyPhrases = [
          "correct",
          "pronounce",
          "should",
          "instead",
          "focus",
        ];
        const importantSentences = sentences.filter((sentence, index) => {
          if (index === 0) return true;
          return keyPhrases.some((phrase) =>
            sentence.toLowerCase().includes(phrase)
          );
        });
        summary = importantSentences.slice(0, 3).join(". ") + ".";
      } else {
        summary = sentences.join(". ") + ".";
      }
  }

  // Limit word count based on feedback type
  const words = summary.split(/\s+/);
  if (words.length > MAX_WORDS) {
    // Special handling for different feedback types
    let limit = MAX_WORDS;

    if (
      parsedFeedback.type === "perfect_recitation" ||
      parsedFeedback.type === "congratulations"
    ) {
      limit = 25; // Shorter for positive feedback
    } else if (parsedFeedback.type === "extra_words") {
      limit = 35; // Medium for extra words
    } else if (
      parsedFeedback.type === "letter_count_mismatch" ||
      parsedFeedback.type === "partial_match"
    ) {
      limit = 50; // Fuller for detailed pronunciation guidance
    }

    summary = words.slice(0, limit).join(" ");

    // Make sure we end with a complete sentence
    if (
      !summary.endsWith(".") &&
      !summary.endsWith("!") &&
      !summary.endsWith("?")
    ) {
      summary += ".";
    }
  }

  return summary.trim();
}

/**
 * Generate a comprehensive summary of all feedback for TTS
 * with priority ordering and word limits
 * @param feedbackElements Array of React elements containing feedback
 * @returns A comprehensive summary string suitable for TTS
 */
export function generateComprehensiveFeedbackSummary(
  feedbackElements: JSX.Element[]
): string {
  if (!feedbackElements || feedbackElements.length === 0) {
    return "No feedback available.";
  }

  // Parse and prioritize feedback elements
  const parsedFeedbacks = feedbackElements.map((element) => ({
    element,
    parsed: parseFeedbackElement(element),
  }));

  // Sort by priority (lowest number first)
  parsedFeedbacks.sort(
    (a, b) => (a.parsed.priority || 5) - (b.parsed.priority || 5)
  );

  // Group feedback elements by type for organization
  const feedbackByType: Record<string, JSX.Element[]> = {};

  parsedFeedbacks.forEach(({ element, parsed }) => {
    if (!feedbackByType[parsed.type]) {
      feedbackByType[parsed.type] = [];
    }
    feedbackByType[parsed.type].push(element);
  });

  // Generate summary sections for each type
  const summaryParts: string[] = [];
  const MAX_TOTAL_WORDS = 250; // Maximum total words for comprehensive summary

  // Handle perfect recitation or congratulations first (if present)
  if (
    feedbackByType["perfect_recitation"] ||
    feedbackByType["congratulations"]
  ) {
    const element = (feedbackByType["perfect_recitation"] ||
      feedbackByType["congratulations"])[0];
    summaryParts.push(generateFeedbackSummary(element));
    return summaryParts.join(" "); // Return immediately as no other feedback needed
  }

  // Handle completed_with_issues
  if (feedbackByType["completed_with_issues"]) {
    const element = feedbackByType["completed_with_issues"][0];
    summaryParts.push(generateFeedbackSummary(element));
  }

  // Handle missing words (high priority)
  if (
    feedbackByType["missing_words"] &&
    feedbackByType["missing_words"].length > 0
  ) {
    const count = feedbackByType["missing_words"].length;

    // Simplified count-based summary for missing words
    if (count === 1) {
      summaryParts.push("You missed 1 word from the text.");
    } else {
      summaryParts.push(`You missed ${count} words from the text.`);
    }
  }

  // Handle extra words
  if (
    feedbackByType["extra_words"] &&
    feedbackByType["extra_words"].length > 0
  ) {
    summaryParts.push(
      generateFeedbackSummary(feedbackByType["extra_words"][0])
    );
  }

  // Handle letter count mismatches and partial matches
  const wordLevelFeedback = [
    ...(feedbackByType["letter_count_mismatch"] || []),
    ...(feedbackByType["partial_match"] || []),
  ];

  if (wordLevelFeedback.length > 0) {
    // Sort by citation number
    wordLevelFeedback.sort((a, b) => {
      const aCitation = a.props["data-citation-number"]
        ? parseInt(a.props["data-citation-number"])
        : 999;
      const bCitation = b.props["data-citation-number"]
        ? parseInt(b.props["data-citation-number"])
        : 999;
      return aCitation - bCitation;
    });

    // Limit to a reasonable number of word-level feedback items (3 max)
    const maxWordFeedback = Math.min(3, wordLevelFeedback.length);

    // Add introduction if multiple word-level feedbacks
    if (maxWordFeedback > 1) {
      summaryParts.push(
        `Here are the ${maxWordFeedback} most important corrections:`
      );
    }

    // Generate summaries for each word-level feedback
    for (let i = 0; i < maxWordFeedback; i++) {
      summaryParts.push(generateFeedbackSummary(wordLevelFeedback[i]));
    }

    // Add brief note if there are more word-level issues
    if (wordLevelFeedback.length > maxWordFeedback) {
      summaryParts.push(
        `Plus ${wordLevelFeedback.length - maxWordFeedback} more corrections.`
      );
    }
  }

  // Limit total word count for full summary
  let result = summaryParts.join(" ");
  const totalWords = result.split(/\s+/);

  if (totalWords.length > MAX_TOTAL_WORDS) {
    result = totalWords.slice(0, MAX_TOTAL_WORDS).join(" ");

    // Make sure we end with a complete sentence
    if (
      !result.endsWith(".") &&
      !result.endsWith("!") &&
      !result.endsWith("?")
    ) {
      result += ".";
    }

    // Add note about additional feedback
    result += " Additional feedback available on screen.";
  }

  return result;
}

/**
 * Generate a clean, focused TTS summary for a specific citation number
 * @param feedbackElements All feedback elements
 * @param citationNumber The citation number to generate summary for
 * @returns A focused TTS summary string
 */
export function generateCitationFocusedSummary(
  feedbackElements: JSX.Element[],
  citationNumber: number
): string {
  // Basic sanity checks
  if (
    !feedbackElements ||
    !Array.isArray(feedbackElements) ||
    feedbackElements.length === 0
  ) {
    return `No feedback available for citation number ${citationNumber}.`;
  }

  // First try to find direct matches based on data-citation-number attribute
  let citationFeedback = feedbackElements.filter(
    (element) =>
      element.props &&
      element.props["data-citation-number"] &&
      element.props["data-citation-number"].toString() ===
        citationNumber.toString()
  );

  // If no direct match found, look for citation badges inside the elements
  if (citationFeedback.length === 0) {
    for (const element of feedbackElements) {
      if (!element || !element.props || !element.props.children) continue;

      // Helper to check if this element or any of its children has the citation number
      const hasCitation = (node: React.ReactNode): boolean => {
        if (!node) return false;

        if (React.isValidElement(node)) {
          // Check if this is a citation badge with the right number
          if (
            node.props &&
            node.props["data-is-citation-badge"] &&
            node.props.children &&
            node.props.children.toString() === citationNumber.toString()
          ) {
            return true;
          }

          // Check children recursively
          if (node.props && node.props.children) {
            if (Array.isArray(node.props.children)) {
              return node.props.children.some(hasCitation);
            } else {
              return hasCitation(node.props.children);
            }
          }
        }

        return false;
      };

      if (hasCitation(element.props.children)) {
        citationFeedback.push(element);
      }
    }
  }

  // If still no feedback found, return a clear error message
  if (citationFeedback.length === 0) {
    console.error(`No feedback found for citation number ${citationNumber}`);
    return `No feedback found for citation number ${citationNumber}`;
  }

  const mainFeedback = citationFeedback[0];
  const parsedFeedback = parseFeedbackElement(mainFeedback);
  const letterFeedback = extractLetterFeedback(mainFeedback);

  let summary = `Word ${citationNumber}: `;

  // If we found the userWord and correctWord, include them. Otherwise, fallback.
  if (parsedFeedback.userWord && parsedFeedback.correctWord) {
    // Keep it concise by using a template
    const templates = [
      `You said "{userWord}" instead of "{correctWord}". `,
      `"{userWord}" should be "{correctWord}". `,
      `Replace "{userWord}" with "{correctWord}". `,
    ];

    const template = getRandomTemplate(templates)
      .replace("{userWord}", parsedFeedback.userWord)
      .replace("{correctWord}", parsedFeedback.correctWord);

    summary += template;
  } else {
    summary += `This word needs correction. `;
  }

  // If there's letter-level feedback, include it; else use the general message if it exists
  if (letterFeedback.length > 0) {
    summary += formatLetterFeedbackForTTS(letterFeedback);
  } else if (parsedFeedback.generalMessage) {
    // Extract the most important part of the general message
    const generalMsg = parsedFeedback.generalMessage;
    const sentences = generalMsg
      .split(/[.!?]+/)
      .filter((s) => s.trim().length > 0);

    if (sentences.length > 0) {
      // Take just the first sentence, which is usually the most important
      summary += sentences[0].trim() + ".";
    }
  }

  // Add a brief conclusion if needed
  if (
    !summary.toLowerCase().includes("focus") &&
    !summary.toLowerCase().includes("practice")
  ) {
    const conclusions = [
      " Practice this word again.",
      " Practice this word again.",
      " Pay attention to this correction.",
    ];
    summary += getRandomTemplate(conclusions);
  }

  return summary.trim();
}

/**
 * Get feedback summary for a specific citation number
 * @param feedbackElements Array of all feedback elements
 * @param citationNumber The citation number to find feedback for
 * @returns A summary string for the specific citation, or null if not found
 */
export function getFeedbackSummaryForCitation(
  feedbackElements: JSX.Element[],
  citationNumber: number
): string | null {
  // Look for the citation in the feedback elements
  for (const element of feedbackElements) {
    if (
      element.props &&
      element.props["data-citation-number"] === citationNumber.toString()
    ) {
      return generateFeedbackSummary(element);
    }
  }

  return null;
}

/**
 * Finds the full feedback element for a specific citation
 * @param feedbackElements Array of all feedback elements
 * @param citationNumber The citation number to find
 * @returns The element containing the feedback for this citation, or null if not found
 */
export function getFeedbackElementForCitation(
  feedbackElements: JSX.Element[],
  citationNumber: number
): JSX.Element | null {
  // Look for the citation in the feedback elements
  for (const element of feedbackElements) {
    if (
      element.props &&
      element.props["data-citation-number"] === citationNumber.toString()
    ) {
      return element;
    }
  }

  return null;
}

/**
 * Filter feedback elements based on citation number
 * @param feedbackElements All feedback elements
 * @param citationNumber The citation number to filter by
 * @returns The filtered feedback elements
 */
export function filterFeedbackByCitation(
  feedbackElements: JSX.Element[],
  citationNumber: number
): JSX.Element[] {
  return feedbackElements.filter(
    (element) =>
      element.props &&
      element.props["data-citation-number"] === citationNumber.toString()
  );
}

/**
 * Categorizes feedback elements by type for tabbed interface
 * @param feedbackElements Array of all feedback elements
 * @returns Object with categorized feedback
 */
export function categorizeFeedbackElements(feedbackElements: JSX.Element[]): {
  summary: JSX.Element[];
  missing: JSX.Element[];
  pronunciation: JSX.Element[];
  extra: JSX.Element[];
  stats: {
    total: number;
    missing: number;
    pronunciation: number;
    extra: number;
    perfect: boolean;
  };
} {
  const summary: JSX.Element[] = [];
  const missing: JSX.Element[] = [];
  const pronunciation: JSX.Element[] = [];
  const extra: JSX.Element[] = [];

  feedbackElements.forEach((element) => {
    const dataWord = element.props?.["data-word"] || "";
    if (
      dataWord.includes("congratulations") ||
      dataWord.includes("perfect-recitation") ||
      dataWord.includes("all-memorized")
    ) {
      summary.push(element);
    } else {
      const parsedFeedback = parseFeedbackElement(element);
      if (parsedFeedback.type === "missing_words") {
        missing.push(element);
      } else if (parsedFeedback.type === "extra_words") {
        extra.push(element);
      } else {
        pronunciation.push(element);
      }
    }
  });

  return {
    summary,
    missing,
    pronunciation,
    extra,
    stats: {
      total: missing.length + pronunciation.length + extra.length,
      missing: missing.length,
      pronunciation: pronunciation.length,
      extra: extra.length,
      perfect:
        summary.length > 0 &&
        missing.length === 0 &&
        pronunciation.length === 0 &&
        extra.length === 0,
    },
  };
}

/**
 * Generate a TTS-friendly speech for a specific feedback section
 * @param feedbackSection Array of feedback elements for a specific section
 * @param sectionType The type of section (overview, pronunciation, etc.)
 * @returns A formatted string ready for TTS
 */
export function generateSectionFeedbackSpeech(
  feedbackSection: JSX.Element[],
  sectionType: string
): string {
  if (!feedbackSection || feedbackSection.length === 0) {
    return `No ${sectionType} feedback available.`;
  }

  let introText = "";
  switch (sectionType) {
    case "overview":
      introText = "Here's an overview of your recitation: ";
      break;
    case "pronunciation":
      introText = "Let's focus on your pronunciation issues: ";
      break;
    case "missing":
      introText = "The following words were missing from your recitation: ";
      break;
    case "extra":
      introText = "You added these extra words that aren't in the text: ";
      break;
    default:
      introText = "Feedback summary: ";
  }

  // Limit to a reasonable number of feedback items
  const maxFeedbackItems = Math.min(5, feedbackSection.length);
  const summaries = feedbackSection
    .slice(0, maxFeedbackItems)
    .map(generateFeedbackSummary);

  if (feedbackSection.length > maxFeedbackItems) {
    summaries.push(
      `There are ${
        feedbackSection.length - maxFeedbackItems
      } additional items not mentioned here.`
    );
  }

  return introText + summaries.join(" ");
}

// -------------------------------------------------------------------------
// Add a default export to enable default imports if needed.
// This aggregates all the named exports into one object.
// -------------------------------------------------------------------------
const summaryFeedbackUtils = {
  extractTextFromJSX,
  parseFeedbackElement,
  extractLetterFeedback,
  generateFeedbackSummary,
  generateComprehensiveFeedbackSummary,
  generateCitationFocusedSummary,
  getFeedbackSummaryForCitation,
  getFeedbackElementForCitation,
  filterFeedbackByCitation,
  categorizeFeedbackElements,
  generateSectionFeedbackSpeech,
};

export default summaryFeedbackUtils;
