// Enhanced AudioPlaybackMonitor.ts
// Advanced audio monitoring with Voice Activity Detection and conversation management

/**
 * Types of events the audio monitor can emit
 */
export type AudioMonitorEvent =
  | "play-started" // Audio has actually started playing (after any delay)
  | "play-attempted" // Play was called but might not have started yet
  | "play-delayed" // Play was called but is taking time to start
  | "play-failed" // Play was called but failed to start
  | "paused" // Audio was paused
  | "ended" // Audio playback ended naturally
  | "data-processing" // Audio data is being processed
  | "amplitude-update" // New amplitude data is available
  | "voice-activity-detected" // Voice activity detected (VAD)
  | "voice-activity-ended" // Voice activity ended (silence detected)
  | "silence-detected" // Prolonged silence detected
  | "noise-detected" // Background noise detected
  | "speech-quality-update" // Speech quality metrics update
  | "turn-taking-signal" // Signal for conversation turn-taking
  | "audio-preprocessing-complete" // Audio preprocessing completed
  | "feedback-loop-detected"
  | "conversation-mode-enabled" // NEW: Event for conversation mode
  | "error" // NEW: Event for generic errors
  | "conversation-partner-activity"; // NEW: Event for partner activity

/**
 * Enhanced options for the audio monitor with conversation features
 */
export interface AudioMonitorOptions {
  /** How long to wait before considering playback delayed (ms) */
  playbackDelayThreshold?: number;
  /** Whether to analyze audio for amplitude */
  analyzeAmplitude?: boolean;
  /** How often to emit amplitude updates (ms) */
  amplitudeUpdateInterval?: number;
  /** Whether to log detailed state changes */
  debug?: boolean;

  // NEW: Voice Activity Detection options
  /** Enable Voice Activity Detection */
  enableVAD?: boolean;
  /** VAD sensitivity threshold (0-1, lower = more sensitive) */
  vadThreshold?: number;
  /** Minimum speech duration to trigger VAD (ms) */
  minSpeechDuration?: number;
  /** Silence duration before ending voice activity (ms) */
  silenceTimeout?: number;

  // NEW: Conversation management options
  /** Enable conversation turn-taking features */
  enableConversationMode?: boolean;
  /** Threshold for detecting conversation partner audio */
  conversationAudioThreshold?: number;
  /** Enable audio preprocessing for better STT */
  enableAudioPreprocessing?: boolean;

  // NEW: Noise handling options
  /** Background noise threshold */
  noiseThreshold?: number;
  /** Enable noise suppression */
  enableNoiseSuppression?: boolean;
  /** Enable echo cancellation */
  enableEchoCancellation?: boolean;

  // NEW: Advanced analysis options
  /** Enable speech quality analysis */
  enableSpeechQuality?: boolean;
  /** Frequency analysis resolution */
  fftSize?: number;
  /** Enable real-time audio filtering */
  enableAudioFiltering?: boolean;
}

/**
 * Voice Activity Detection state
 */
export interface VADState {
  isActive: boolean;
  confidence: number;
  duration: number;
  lastActivityTime: number;
  speechQuality: SpeechQualityMetrics; // FIX: Changed from number to SpeechQualityMetrics
  noiseLevel: number;
}

/**
 * Speech quality metrics
 */
export interface SpeechQualityMetrics {
  clarity: number; // 0-1, higher is clearer
  volume: number; // 0-1, relative volume level
  consistency: number; // 0-1, how consistent the speech is
  snr: number; // Signal-to-noise ratio
  distortion: number; // 0-1, amount of distortion detected
}

/**
 * Audio preprocessing result
 */
export interface AudioPreprocessingResult {
  processedBuffer: ArrayBuffer;
  improvements: {
    noiseReduction: number;
    volumeNormalization: number;
    clarityEnhancement: number;
  };
  quality: SpeechQualityMetrics;
}

/**
 * Enhanced class to monitor audio playback state and provide conversation features
 */
export class AudioPlaybackMonitor {
  private audioElement: HTMLAudioElement | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private source: MediaElementAudioSourceNode | null = null;
  private animationFrame: number | null = null;
  private checkPlaybackTimeout: NodeJS.Timeout | null = null;
  private amplitudeInterval: NodeJS.Timeout | null = null;
  private listeners: Map<AudioMonitorEvent, Set<Function>> = new Map();
  private options: Required<AudioMonitorOptions>;
  private _isPlaying: boolean = false;
  private _isPaused: boolean = false;
  private _isAttemptingToPlay: boolean = false;
  private _playAttemptTimestamp: number = 0;
  private _currentAmplitude: number = 0;
  private _frequencyData: Uint8Array | null = null;

  // NEW: Voice Activity Detection properties
  // FIX: Renamed private property to avoid conflict with getter
  private _vadState: VADState = {
    isActive: false,
    confidence: 0,
    duration: 0,
    lastActivityTime: 0,
    // FIX: Initialized with a default SpeechQualityMetrics object
    speechQuality: {
      clarity: 0,
      volume: 0,
      consistency: 0,
      snr: 0,
      distortion: 0,
    },
    noiseLevel: 0,
  };
  private vadTimeout: NodeJS.Timeout | null = null;
  private speechStartTime: number = 0;
  private amplitudeHistory: number[] = [];
  private readonly amplitudeHistorySize = 50; // Keep last 50 amplitude readings

  // NEW: Audio preprocessing components
  private preprocessingGainNode: GainNode | null = null;
  private preprocessingFilterNode: BiquadFilterNode | null = null;
  private noiseGateNode: GainNode | null = null;
  private compressorNode: DynamicsCompressorNode | null = null;

  // NEW: Conversation management
  private isInConversationMode: boolean = false;
  private conversationPartnerActive: boolean = false;
  private feedbackLoopDetected: boolean = false;
  private lastFeedbackCheck: number = 0;

  // NEW: Speech quality tracking
  private speechQualityHistory: SpeechQualityMetrics[] = [];
  private readonly speechQualityHistorySize = 10;

  /**
   * Creates a new enhanced audio monitor
   */
  constructor(options: AudioMonitorOptions = {}) {
    this.options = {
      playbackDelayThreshold: 500,
      analyzeAmplitude: true,
      amplitudeUpdateInterval: 100,
      debug: false,

      // NEW: VAD defaults
      enableVAD: true,
      vadThreshold: 0.02,
      minSpeechDuration: 300,
      silenceTimeout: 1500,

      // NEW: Conversation defaults
      enableConversationMode: false,
      conversationAudioThreshold: 0.05,
      enableAudioPreprocessing: true,

      // NEW: Noise handling defaults
      noiseThreshold: 0.01,
      enableNoiseSuppression: true,
      enableEchoCancellation: true,

      // NEW: Advanced analysis defaults
      enableSpeechQuality: true,
      fftSize: 2048,
      enableAudioFiltering: true,

      ...options,
    };

    this.log(
      "Enhanced AudioPlaybackMonitor initialized with options:",
      this.options
    );
  }

  /**
   * Attaches the monitor to an audio element
   */
  attach(audioElement: HTMLAudioElement): void {
    if (this.audioElement) {
      this.detach();
    }

    this.audioElement = audioElement;
    this._isPlaying = !audioElement.paused;
    this._isPaused = audioElement.paused;

    // Attach event listeners to the audio element
    audioElement.addEventListener("play", this.handlePlay);
    audioElement.addEventListener("playing", this.handlePlaying);
    audioElement.addEventListener("pause", this.handlePause);
    audioElement.addEventListener("ended", this.handleEnded);
    audioElement.addEventListener("waiting", this.handleWaiting);
    audioElement.addEventListener("error", this.handleError);

    // If we should analyze amplitude, set up the audio context
    if (this.options.analyzeAmplitude) {
      this.setupAudioAnalysis();
    }

    this.log("Enhanced monitor attached to audio element");
  }

  /**
   * NEW: Attach to media stream for conversation monitoring
   */
  attachToMediaStream(mediaStream: MediaStream): void {
    try {
      this.log("Attaching to media stream for conversation monitoring");

      if (!this.audioContext) {
        this.audioContext = new AudioContext({ sampleRate: 16000 });
      }

      // Create source from media stream
      const streamSource =
        this.audioContext.createMediaStreamSource(mediaStream);

      // Set up enhanced audio analysis pipeline
      this.setupEnhancedAudioPipeline(streamSource);

      // Enable conversation mode
      this.isInConversationMode = true;
      // FIX: The event name "conversation-mode-enabled" is now valid
      this.emit("conversation-mode-enabled", { timestamp: Date.now() });
    } catch (error) {
      console.error("Error attaching to media stream:", error);
      // FIX: The event name "error" is now valid
      this.emit("error", { error, context: "media-stream-attachment" });
    }
  }

  /**
   * Detaches the monitor from the audio element
   */
  detach(): void {
    if (!this.audioElement) return;

    // Remove event listeners
    this.audioElement.removeEventListener("play", this.handlePlay);
    this.audioElement.removeEventListener("playing", this.handlePlaying);
    this.audioElement.removeEventListener("pause", this.handlePause);
    this.audioElement.removeEventListener("ended", this.handleEnded);
    this.audioElement.removeEventListener("waiting", this.handleWaiting);
    this.audioElement.removeEventListener("error", this.handleError);

    // Clean up audio analysis resources
    this.cleanupAudioAnalysis();

    // NEW: Clean up VAD and conversation resources
    this.cleanupVADResources();
    this.cleanupConversationResources();

    this.audioElement = null;
    this._isPlaying = false;
    this._isPaused = true;
    this._isAttemptingToPlay = false;
    this._currentAmplitude = 0;
    this._frequencyData = null;

    this.log("Enhanced monitor detached from audio element");
  }

  /**
   * Enhanced audio analysis setup with preprocessing pipeline
   */
  private setupAudioAnalysis(): void {
    if (!this.audioElement || !window.AudioContext) return;

    try {
      // Create audio context with optimal settings
      this.audioContext = new AudioContext({
        sampleRate: 16000, // Optimal for speech processing
        latencyHint: "interactive",
      });

      // Set up main analysis chain
      this.setupMainAnalysisChain();

      this.log("Enhanced audio analysis setup complete");
    } catch (error) {
      console.error("Error setting up enhanced audio analysis:", error);
    }
  }

  /**
   * NEW: Setup main audio analysis chain
   */
  private setupMainAnalysisChain(): void {
    if (!this.audioContext || !this.audioElement) return;

    // Create analyzer node with enhanced settings
    this.analyser = this.audioContext.createAnalyser();
    this.analyser.fftSize = this.options.fftSize;
    this.analyser.smoothingTimeConstant = 0.8;
    this.analyser.minDecibels = -90;
    this.analyser.maxDecibels = -10;

    // Create buffer for frequency data
    const bufferLength = this.analyser.frequencyBinCount;
    this.dataArray = new Uint8Array(bufferLength);
    this._frequencyData = new Uint8Array(bufferLength);

    // Connect audio element to analyzer
    this.source = this.audioContext.createMediaElementSource(this.audioElement);

    // Set up audio preprocessing if enabled
    if (this.options.enableAudioPreprocessing) {
      this.setupAudioPreprocessingChain();
    } else {
      // FIX: Add guard to ensure analyser is not null
      if (this.analyser) {
        this.source.connect(this.analyser);
      }
    }

    this.analyser.connect(this.audioContext.destination);

    // Start enhanced analysis loops
    this.startEnhancedAnalysis();
  }

  /**
   * NEW: Setup enhanced audio pipeline for media streams
   */
  private setupEnhancedAudioPipeline(source: MediaStreamAudioSourceNode): void {
    if (!this.audioContext) return;

    // Create enhanced analyzer with higher resolution
    this.analyser = this.audioContext.createAnalyser();
    this.analyser.fftSize = this.options.fftSize;
    this.analyser.smoothingTimeConstant = 0.3; // Less smoothing for real-time
    this.analyser.minDecibels = -100;
    this.analyser.maxDecibels = -10;

    const bufferLength = this.analyser.frequencyBinCount;
    this.dataArray = new Uint8Array(bufferLength);
    this._frequencyData = new Uint8Array(bufferLength);

    // Setup preprocessing chain for conversation
    if (this.options.enableAudioPreprocessing) {
      this.setupConversationPreprocessingChain(source);
    } else {
      // FIX: Add guard to ensure analyser is not null
      if (this.analyser) {
        source.connect(this.analyser);
      }
    }

    // Start real-time analysis
    this.startEnhancedAnalysis();
  }

  /**
   * NEW: Setup audio preprocessing chain
   */
  private setupAudioPreprocessingChain(): void {
    // FIX: Add guard clause to ensure all required nodes are available
    if (!this.audioContext || !this.source || !this.analyser) return;

    try {
      // Gain node for volume control
      this.preprocessingGainNode = this.audioContext.createGain();
      this.preprocessingGainNode.gain.value = 1.0;

      // High-pass filter to remove low-frequency noise
      this.preprocessingFilterNode = this.audioContext.createBiquadFilter();
      this.preprocessingFilterNode.type = "highpass";
      this.preprocessingFilterNode.frequency.value = 80; // Remove below 80Hz
      this.preprocessingFilterNode.Q.value = 1;

      // Noise gate
      this.noiseGateNode = this.audioContext.createGain();
      this.noiseGateNode.gain.value = 1.0;

      // Compressor for dynamic range control
      this.compressorNode = this.audioContext.createDynamicsCompressor();
      this.compressorNode.threshold.value = -24;
      this.compressorNode.knee.value = 30;
      this.compressorNode.ratio.value = 12;
      this.compressorNode.attack.value = 0.003;
      this.compressorNode.release.value = 0.25;

      // Connect the preprocessing chain
      this.source
        .connect(this.preprocessingGainNode)
        .connect(this.preprocessingFilterNode)
        .connect(this.noiseGateNode)
        .connect(this.compressorNode)
        .connect(this.analyser);

      this.log("Audio preprocessing chain setup complete");
    } catch (error) {
      console.error("Error setting up preprocessing chain:", error);
      // Fallback to direct connection
      this.source.connect(this.analyser);
    }
  }

  /**
   * NEW: Setup conversation-specific preprocessing chain
   */
  private setupConversationPreprocessingChain(
    source: MediaStreamAudioSourceNode
  ): void {
    // FIX: Add guard clause to ensure all required nodes are available
    if (!this.audioContext || !this.analyser) return;

    try {
      // More aggressive preprocessing for conversation
      this.preprocessingGainNode = this.audioContext.createGain();
      this.preprocessingGainNode.gain.value = 2.0; // Boost input

      // Band-pass filter optimized for speech (300Hz - 3.4kHz)
      const lowPassFilter = this.audioContext.createBiquadFilter();
      lowPassFilter.type = "lowpass";
      lowPassFilter.frequency.value = 3400;
      lowPassFilter.Q.value = 1;

      const highPassFilter = this.audioContext.createBiquadFilter();
      highPassFilter.type = "highpass";
      highPassFilter.frequency.value = 300;
      highPassFilter.Q.value = 1;

      // Adaptive noise gate
      this.noiseGateNode = this.audioContext.createGain();
      this.noiseGateNode.gain.value = 1.0;

      // Speech-optimized compressor
      this.compressorNode = this.audioContext.createDynamicsCompressor();
      this.compressorNode.threshold.value = -30;
      this.compressorNode.knee.value = 40;
      this.compressorNode.ratio.value = 6;
      this.compressorNode.attack.value = 0.001;
      this.compressorNode.release.value = 0.1;

      // Connect conversation preprocessing chain
      source
        .connect(this.preprocessingGainNode)
        .connect(highPassFilter)
        .connect(lowPassFilter)
        .connect(this.noiseGateNode)
        .connect(this.compressorNode)
        .connect(this.analyser);

      this.log("Conversation preprocessing chain setup complete");
    } catch (error) {
      console.error("Error setting up conversation preprocessing:", error);
      source.connect(this.analyser);
    }
  }

  /**
   * NEW: Start enhanced analysis with VAD and conversation features
   */
  private startEnhancedAnalysis(): void {
    if (!this.analyser || !this.dataArray) return;

    // Enhanced amplitude tracking with conversation features
    this.amplitudeInterval = setInterval(() => {
      if (this.analyser && this.dataArray) {
        this.analyser.getByteFrequencyData(this.dataArray);

        // Calculate enhanced amplitude metrics
        const amplitudeMetrics = this.calculateEnhancedAmplitude();

        this._currentAmplitude = amplitudeMetrics.rms;
        this._frequencyData = new Uint8Array(this.dataArray);

        // Update amplitude history for trend analysis
        this.updateAmplitudeHistory(amplitudeMetrics.rms);

        // NEW: Voice Activity Detection
        if (this.options.enableVAD) {
          this.processVoiceActivityDetection(amplitudeMetrics);
        }

        // NEW: Speech quality analysis
        if (this.options.enableSpeechQuality) {
          this.processSpeechQuality(amplitudeMetrics);
        }

        // NEW: Conversation management
        if (this.isInConversationMode) {
          this.processConversationManagement(amplitudeMetrics);
        }

        // Emit enhanced amplitude update
        this.emit("amplitude-update", {
          amplitude: this._currentAmplitude,
          frequencyData: this._frequencyData,
          vadState: this._vadState, // FIX: Use renamed private property
          speechQuality: this.getCurrentSpeechQuality(),
          isConversationMode: this.isInConversationMode,
        });
      }
    }, this.options.amplitudeUpdateInterval);

    this.log("Enhanced analysis started");
  }

  /**
   * NEW: Calculate enhanced amplitude metrics
   */
  private calculateEnhancedAmplitude(): {
    rms: number;
    peak: number;
    average: number;
    spectralCentroid: number;
    spectralRolloff: number;
  } {
    if (!this.dataArray) {
      return {
        rms: 0,
        peak: 0,
        average: 0,
        spectralCentroid: 0,
        spectralRolloff: 0,
      };
    }

    let sum = 0;
    let peak = 0;
    let weightedSum = 0;
    let totalMagnitude = 0;

    for (let i = 0; i < this.dataArray.length; i++) {
      const value = this.dataArray[i] / 255;
      sum += value * value;
      peak = Math.max(peak, value);

      // Calculate spectral centroid (brightness)
      const frequency =
        ((i / this.dataArray.length) *
          (this.audioContext?.sampleRate || 16000)) /
        2;
      weightedSum += frequency * value;
      totalMagnitude += value;
    }

    const rms = Math.sqrt(sum / this.dataArray.length);
    const average = sum / this.dataArray.length;
    const spectralCentroid =
      totalMagnitude > 0 ? weightedSum / totalMagnitude : 0;

    // Calculate spectral rolloff (90% of energy)
    let energySum = 0;
    let rolloffIndex = 0;
    const targetEnergy = totalMagnitude * 0.9;

    for (
      let i = 0;
      i < this.dataArray.length && energySum < targetEnergy;
      i++
    ) {
      energySum += this.dataArray[i] / 255;
      rolloffIndex = i;
    }

    const spectralRolloff =
      ((rolloffIndex / this.dataArray.length) *
        (this.audioContext?.sampleRate || 16000)) /
      2;

    return { rms, peak, average, spectralCentroid, spectralRolloff };
  }

  /**
   * NEW: Process Voice Activity Detection
   */
  private processVoiceActivityDetection(metrics: any): void {
    const { rms, spectralCentroid, spectralRolloff } = metrics;
    const currentTime = Date.now();

    // Enhanced VAD algorithm considering multiple factors
    const speechIndicators = {
      amplitude: rms > this.options.vadThreshold,
      spectralShape: spectralCentroid > 500 && spectralCentroid < 2500, // Typical speech range
      spectralRolloff: spectralRolloff > 1000 && spectralRolloff < 4000,
      consistency: this.isAmplitudeConsistent(),
    };

    const speechScore =
      Object.values(speechIndicators).filter(Boolean).length / 4;
    const vadConfidence = Math.min(
      speechScore + (rms / this.options.vadThreshold) * 0.3,
      1
    );

    // Update VAD state
    const wasActive = this._vadState.isActive;
    const shouldBeActive = vadConfidence > 0.6;

    if (shouldBeActive && !wasActive) {
      // Speech started
      this.speechStartTime = currentTime;
      this._vadState.isActive = true;
      this._vadState.lastActivityTime = currentTime;

      this.emit("voice-activity-detected", {
        timestamp: currentTime,
        confidence: vadConfidence,
        metrics: speechIndicators,
      });

      this.log("Voice activity detected", { confidence: vadConfidence });
    } else if (wasActive && shouldBeActive) {
      // Speech continuing
      this._vadState.duration = currentTime - this.speechStartTime;
      this._vadState.lastActivityTime = currentTime;

      // Clear any pending silence timeout
      if (this.vadTimeout) {
        clearTimeout(this.vadTimeout);
        this.vadTimeout = null;
      }
    } else if (wasActive && !shouldBeActive) {
      // Potential speech end - start silence timer
      if (!this.vadTimeout) {
        this.vadTimeout = setTimeout(() => {
          this._vadState.isActive = false;
          this._vadState.duration = currentTime - this.speechStartTime;

          this.emit("voice-activity-ended", {
            timestamp: currentTime,
            duration: this._vadState.duration,
            finalConfidence: this._vadState.confidence,
          });

          // Check for turn-taking signal
          if (this._vadState.duration > this.options.minSpeechDuration) {
            this.emit("turn-taking-signal", {
              timestamp: currentTime,
              speechDuration: this._vadState.duration,
              confidence: this._vadState.confidence,
            });
          }

          this.log("Voice activity ended", {
            duration: this._vadState.duration,
          });
          this.vadTimeout = null;
        }, this.options.silenceTimeout);
      }
    }

    // Update confidence and quality metrics
    this._vadState.confidence = vadConfidence;
    // FIX: This assignment is now valid because speechQuality is an object
    this._vadState.speechQuality = this.calculateSpeechQuality(metrics);
    this._vadState.noiseLevel = this.calculateNoiseLevel();

    // Detect prolonged silence
    if (
      currentTime - this._vadState.lastActivityTime >
      this.options.silenceTimeout * 3
    ) {
      this.emit("silence-detected", {
        timestamp: currentTime,
        silenceDuration: currentTime - this._vadState.lastActivityTime,
      });
    }
  }

  /**
   * NEW: Process speech quality analysis
   */
  private processSpeechQuality(metrics: any): void {
    const quality = this.calculateSpeechQuality(metrics);

    // Add to history
    this.speechQualityHistory.push(quality);
    if (this.speechQualityHistory.length > this.speechQualityHistorySize) {
      this.speechQualityHistory.shift();
    }

    // Emit quality update
    this.emit("speech-quality-update", {
      current: quality,
      average: this.getAverageSpeechQuality(),
      trend: this.getSpeechQualityTrend(),
      timestamp: Date.now(),
    });
  }

  /**
   * NEW: Process conversation management
   */
  private processConversationManagement(metrics: any): void {
    const currentTime = Date.now();

    // Check for conversation partner activity
    const partnerActive = metrics.rms > this.options.conversationAudioThreshold;

    if (partnerActive !== this.conversationPartnerActive) {
      this.conversationPartnerActive = partnerActive;

      // FIX: Event name "conversation-partner-activity" is now valid
      this.emit("conversation-partner-activity", {
        active: partnerActive,
        timestamp: currentTime,
        amplitude: metrics.rms,
      });
    }

    // Feedback loop detection (check every 500ms)
    if (currentTime - this.lastFeedbackCheck > 500) {
      this.detectFeedbackLoop(metrics);
      this.lastFeedbackCheck = currentTime;
    }

    // Adaptive noise gate adjustment
    this.adjustNoiseGate(metrics);
  }

  /**
   * NEW: Detect audio feedback loops
   */
  private detectFeedbackLoop(metrics: any): void {
    // Look for characteristic feedback patterns
    const { spectralCentroid, peak, rms } = metrics;

    // Feedback typically shows as high-frequency dominant peaks
    const feedbackIndicators = {
      highFrequencyDominance: spectralCentroid > 3000,
      suddenPeaks: peak > rms * 3,
      sustainedLevel: rms > 0.8,
      consistentFreq: this.isFrequencyPatternSustained(),
    };

    const feedbackScore =
      Object.values(feedbackIndicators).filter(Boolean).length;
    const feedbackDetected = feedbackScore >= 3;

    if (feedbackDetected && !this.feedbackLoopDetected) {
      this.feedbackLoopDetected = true;

      // Automatically reduce gain to prevent damage
      if (this.preprocessingGainNode) {
        this.preprocessingGainNode.gain.setValueAtTime(
          0.1,
          this.audioContext!.currentTime
        );
      }

      this.emit("feedback-loop-detected", {
        timestamp: Date.now(),
        indicators: feedbackIndicators,
        autoMitigated: true,
      });

      this.log("Feedback loop detected and mitigated");
    } else if (!feedbackDetected && this.feedbackLoopDetected) {
      this.feedbackLoopDetected = false;

      // Restore normal gain
      if (this.preprocessingGainNode) {
        this.preprocessingGainNode.gain.setValueAtTime(
          1.0,
          this.audioContext!.currentTime
        );
      }

      this.log("Feedback loop resolved");
    }
  }

  /**
   * NEW: Adaptive noise gate adjustment
   */
  private adjustNoiseGate(metrics: any): void {
    if (!this.noiseGateNode) return;

    const noiseLevel = this.calculateNoiseLevel();
    const speechLevel = metrics.rms;

    // Adaptive threshold based on current noise floor
    const adaptiveThreshold = Math.max(
      noiseLevel * 2,
      this.options.vadThreshold
    );

    // Smooth gate adjustment to prevent artifacts
    const targetGain =
      speechLevel > adaptiveThreshold
        ? 1.0
        : Math.pow(speechLevel / adaptiveThreshold, 2);

    this.noiseGateNode.gain.setTargetAtTime(
      targetGain,
      this.audioContext!.currentTime,
      0.05 // 50ms time constant
    );
  }

  /**
   * NEW: Calculate speech quality metrics
   */
  private calculateSpeechQuality(metrics: any): SpeechQualityMetrics {
    const { rms, peak, spectralCentroid, spectralRolloff } = metrics;

    // Clarity based on spectral characteristics
    const clarity =
      Math.min(spectralCentroid / 2000, 1) *
      (1 - Math.abs(spectralRolloff - 2500) / 2500);

    // Volume normalization
    const volume = Math.min(rms / 0.3, 1);

    // Consistency based on amplitude history variance
    const consistency = 1 - this.getAmplitudeVariance();

    // SNR estimation
    const noiseLevel = this.calculateNoiseLevel();
    const snr = noiseLevel > 0 ? Math.min(rms / noiseLevel, 10) / 10 : 1;

    // Distortion based on peak-to-RMS ratio
    const crestFactor = rms > 0 ? peak / rms : 1;
    const distortion = Math.max(0, 1 - (crestFactor - 1) / 3); // Ideal crest factor around 1-4

    return {
      clarity: Math.max(0, Math.min(1, clarity)),
      volume: Math.max(0, Math.min(1, volume)),
      consistency: Math.max(0, Math.min(1, consistency)),
      snr: Math.max(0, Math.min(1, snr)),
      distortion: Math.max(0, Math.min(1, distortion)),
    };
  }

  /**
   * NEW: Helper methods for analysis
   */
  private updateAmplitudeHistory(amplitude: number): void {
    this.amplitudeHistory.push(amplitude);
    if (this.amplitudeHistory.length > this.amplitudeHistorySize) {
      this.amplitudeHistory.shift();
    }
  }

  private isAmplitudeConsistent(): boolean {
    if (this.amplitudeHistory.length < 5) return false;

    const recent = this.amplitudeHistory.slice(-5);
    const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const variance =
      recent.reduce((acc, val) => acc + Math.pow(val - avg, 2), 0) /
      recent.length;

    return variance < 0.01; // Low variance indicates consistency
  }

  private getAmplitudeVariance(): number {
    if (this.amplitudeHistory.length < 2) return 0;

    const avg =
      this.amplitudeHistory.reduce((a, b) => a + b, 0) /
      this.amplitudeHistory.length;
    const variance =
      this.amplitudeHistory.reduce(
        (acc, val) => acc + Math.pow(val - avg, 2),
        0
      ) / this.amplitudeHistory.length;

    return Math.sqrt(variance);
  }

  private calculateNoiseLevel(): number {
    // Estimate noise level from bottom 10% of amplitude readings
    const sortedHistory = [...this.amplitudeHistory].sort((a, b) => a - b);
    const noiseFloorSamples = sortedHistory.slice(
      0,
      Math.floor(sortedHistory.length * 0.1)
    );

    return noiseFloorSamples.length > 0
      ? noiseFloorSamples.reduce((a, b) => a + b, 0) / noiseFloorSamples.length
      : 0;
  }

  private isFrequencyPatternSustained(): boolean {
    // Check if dominant frequency has been sustained (feedback indicator)
    if (!this._frequencyData || this._frequencyData.length < 10) return false;

    // Find peak frequency
    let maxIndex = 0;
    let maxValue = 0;
    for (let i = 0; i < this._frequencyData.length; i++) {
      if (this._frequencyData[i] > maxValue) {
        maxValue = this._frequencyData[i];
        maxIndex = i;
      }
    }

    // Check if peak is significantly higher than surrounding frequencies
    const surroundingAvg =
      ((this._frequencyData[Math.max(0, maxIndex - 2)] || 0) +
        (this._frequencyData[Math.max(0, maxIndex - 1)] || 0) +
        (this._frequencyData[
          Math.min(this._frequencyData.length - 1, maxIndex + 1)
        ] || 0) +
        (this._frequencyData[
          Math.min(this._frequencyData.length - 1, maxIndex + 2)
        ] || 0)) /
      4;

    return maxValue > surroundingAvg * 3; // Peak is 3x higher than surrounding
  }

  private getCurrentSpeechQuality(): SpeechQualityMetrics | null {
    return this.speechQualityHistory.length > 0
      ? this.speechQualityHistory[this.speechQualityHistory.length - 1]
      : null;
  }

  private getAverageSpeechQuality(): SpeechQualityMetrics | null {
    if (this.speechQualityHistory.length === 0) return null;

    const avg = this.speechQualityHistory.reduce(
      (acc, quality) => ({
        clarity: acc.clarity + quality.clarity,
        volume: acc.volume + quality.volume,
        consistency: acc.consistency + quality.consistency,
        snr: acc.snr + quality.snr,
        distortion: acc.distortion + quality.distortion,
      }),
      { clarity: 0, volume: 0, consistency: 0, snr: 0, distortion: 0 }
    );

    const count = this.speechQualityHistory.length;
    return {
      clarity: avg.clarity / count,
      volume: avg.volume / count,
      consistency: avg.consistency / count,
      snr: avg.snr / count,
      distortion: avg.distortion / count,
    };
  }

  private getSpeechQualityTrend(): "improving" | "declining" | "stable" {
    if (this.speechQualityHistory.length < 5) return "stable";

    const recent = this.speechQualityHistory.slice(-3);
    const older = this.speechQualityHistory.slice(-6, -3);

    const recentAvg =
      recent.reduce(
        (acc, q) =>
          acc + q.clarity + q.volume + q.consistency + q.snr + q.distortion,
        0
      ) /
      (recent.length * 5);
    const olderAvg =
      older.reduce(
        (acc, q) =>
          acc + q.clarity + q.volume + q.consistency + q.snr + q.distortion,
        0
      ) /
      (older.length * 5);

    const difference = recentAvg - olderAvg;

    if (difference > 0.1) return "improving";
    if (difference < -0.1) return "declining";
    return "stable";
  }

  /**
   * Enhanced cleanup methods
   */
  private cleanupAudioAnalysis(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    if (this.amplitudeInterval) {
      clearInterval(this.amplitudeInterval);
      this.amplitudeInterval = null;
    }

    if (this.checkPlaybackTimeout) {
      clearTimeout(this.checkPlaybackTimeout);
      this.checkPlaybackTimeout = null;
    }

    // NEW: Cleanup preprocessing nodes
    this.cleanupPreprocessingNodes();

    if (this.source) {
      try {
        this.source.disconnect();
      } catch (e) {}
      this.source = null;
    }

    if (this.analyser) {
      try {
        this.analyser.disconnect();
      } catch (e) {}
      this.analyser = null;
    }

    if (this.audioContext) {
      try {
        this.audioContext.close();
      } catch (e) {}
      this.audioContext = null;
    }

    this.dataArray = null;
    this._frequencyData = null;
    this.log("Audio analysis resources cleaned up");
  }

  /**
   * NEW: Clean up VAD resources
   */
  private cleanupVADResources(): void {
    if (this.vadTimeout) {
      clearTimeout(this.vadTimeout);
      this.vadTimeout = null;
    }

    // FIX: Use renamed private property and correct speechQuality type
    this._vadState = {
      isActive: false,
      confidence: 0,
      duration: 0,
      lastActivityTime: 0,
      speechQuality: {
        clarity: 0,
        volume: 0,
        consistency: 0,
        snr: 0,
        distortion: 0,
      },
      noiseLevel: 0,
    };

    this.amplitudeHistory = [];
    this.speechQualityHistory = [];

    this.log("VAD resources cleaned up");
  }

  /**
   * NEW: Clean up conversation resources
   */
  private cleanupConversationResources(): void {
    this.isInConversationMode = false;
    this.conversationPartnerActive = false;
    this.feedbackLoopDetected = false;
    this.lastFeedbackCheck = 0;

    this.log("Conversation resources cleaned up");
  }

  /**
   * NEW: Clean up preprocessing nodes
   */
  private cleanupPreprocessingNodes(): void {
    const nodes = [
      this.preprocessingGainNode,
      this.preprocessingFilterNode,
      this.noiseGateNode,
      this.compressorNode,
    ];

    nodes.forEach((node) => {
      if (node) {
        try {
          node.disconnect();
        } catch (e) {}
      }
    });

    this.preprocessingGainNode = null;
    this.preprocessingFilterNode = null;
    this.noiseGateNode = null;
    this.compressorNode = null;
  }

  /**
   * Original event handlers (preserved)
   */
  private handlePlay = (): void => {
    this._isAttemptingToPlay = true;
    this._playAttemptTimestamp = Date.now();
    this._isPaused = false;

    this.emit("play-attempted", {
      timestamp: this._playAttemptTimestamp,
    });

    if (this.checkPlaybackTimeout) {
      clearTimeout(this.checkPlaybackTimeout);
    }

    this.checkPlaybackTimeout = setTimeout(() => {
      if (this._isAttemptingToPlay && !this._isPlaying) {
        this.emit("play-delayed", {
          delayDuration: Date.now() - this._playAttemptTimestamp,
        });
      }
    }, this.options.playbackDelayThreshold);

    this.log("Play attempted");
  };

  private handlePlaying = (): void => {
    this._isPlaying = true;
    this._isAttemptingToPlay = false;
    this._isPaused = false;

    if (this.checkPlaybackTimeout) {
      clearTimeout(this.checkPlaybackTimeout);
      this.checkPlaybackTimeout = null;
    }

    this.emit("play-started", {
      delay: Date.now() - this._playAttemptTimestamp,
      timestamp: Date.now(),
    });

    if (this.audioContext?.state === "suspended") {
      this.audioContext.resume().catch(console.error);
    }

    this.log("Playback actually started");
  };

  private handlePause = (): void => {
    if (this.audioElement?.ended) return;

    this._isPlaying = false;
    this._isPaused = true;
    this._isAttemptingToPlay = false;

    this.emit("paused", {
      timestamp: Date.now(),
      currentTime: this.audioElement?.currentTime || 0,
    });

    this.log("Playback paused");
  };

  private handleEnded = (): void => {
    this._isPlaying = false;
    this._isPaused = true;
    this._isAttemptingToPlay = false;

    this.emit("ended", {
      timestamp: Date.now(),
    });

    this.log("Playback ended");
  };

  private handleWaiting = (): void => {
    this.emit("data-processing", {
      timestamp: Date.now(),
      currentTime: this.audioElement?.currentTime || 0,
    });

    this.log("Playback waiting (buffering)");
  };

  private handleError = (error: ErrorEvent): void => {
    this._isPlaying = false;
    this._isAttemptingToPlay = false;

    this.emit("play-failed", {
      timestamp: Date.now(),
      error: error,
    });

    this.log("Playback error:", error);
  };

  /**
   * Event system methods (preserved and enhanced)
   */
  on(event: AudioMonitorEvent, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)?.add(callback);
  }

  off(event: AudioMonitorEvent, callback: Function): void {
    this.listeners.get(event)?.delete(callback);
  }

  private emit(event: AudioMonitorEvent, data: any): void {
    this.listeners.get(event)?.forEach((callback) => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} listener:`, error);
      }
    });
  }

  private log(...args: any[]): void {
    if (this.options.debug) {
      console.log("[Enhanced AudioMonitor]", ...args);
    }
  }

  /**
   * Public getters (preserved and enhanced)
   */
  get isPlaying(): boolean {
    return this._isPlaying;
  }

  get isPaused(): boolean {
    return this._isPaused;
  }

  get isAttemptingToPlay(): boolean {
    return this._isAttemptingToPlay;
  }

  get currentAmplitude(): number {
    return this._currentAmplitude;
  }

  get frequencyData(): Uint8Array | null {
    return this._frequencyData;
  }

  // NEW: Enhanced getters
  // FIX: This getter now returns a copy of the private state, resolving the duplicate identifier error.
  get vadState(): VADState {
    return { ...this._vadState };
  }

  get isVoiceActive(): boolean {
    return this._vadState.isActive; // FIX: Use renamed private property
  }

  get speechQuality(): SpeechQualityMetrics | null {
    return this.getCurrentSpeechQuality();
  }

  get isInConversation(): boolean {
    return this.isInConversationMode;
  }

  get hasFeedbackLoop(): boolean {
    return this.feedbackLoopDetected;
  }

  /**
   * NEW: Public methods for conversation control
   */
  enableConversationMode(): void {
    this.isInConversationMode = true;
    this.log("Conversation mode enabled");
  }

  disableConversationMode(): void {
    this.isInConversationMode = false;
    this.conversationPartnerActive = false;
    this.log("Conversation mode disabled");
  }

  adjustVADSensitivity(threshold: number): void {
    this.options.vadThreshold = Math.max(0, Math.min(1, threshold));
    this.log("VAD sensitivity adjusted to:", this.options.vadThreshold);
  }

  getNoiseLevel(): number {
    return this.calculateNoiseLevel();
  }

  /**
   * NEW: Audio preprocessing control
   */
  async preprocessAudio(
    audioBuffer: ArrayBuffer
  ): Promise<AudioPreprocessingResult | null> {
    if (!this.audioContext || !this.options.enableAudioPreprocessing) {
      return null;
    }

    try {
      const audioData = await this.audioContext.decodeAudioData(
        audioBuffer.slice(0)
      );

      // Apply preprocessing
      const processedData = await this.applyAudioPreprocessing(audioData);

      // Calculate quality metrics
      const quality = this.analyzeAudioQuality(processedData);

      // Convert back to buffer
      const processedBuffer = await this.audioDataToBuffer(processedData);

      this.emit("audio-preprocessing-complete", {
        originalSize: audioBuffer.byteLength,
        processedSize: processedBuffer.byteLength,
        quality: quality,
        timestamp: Date.now(),
      });

      return {
        processedBuffer,
        improvements: {
          noiseReduction: 0.3, // Mock values - implement actual measurement
          volumeNormalization: 0.2,
          clarityEnhancement: 0.25,
        },
        quality,
      };
    } catch (error) {
      console.error("Audio preprocessing error:", error);
      return null;
    }
  }

  /**
   * NEW: Private methods for audio preprocessing
   */
  private async applyAudioPreprocessing(
    audioData: AudioBuffer
  ): Promise<AudioBuffer> {
    // This is a simplified version - implement more sophisticated preprocessing
    const processedBuffer = this.audioContext!.createBuffer(
      audioData.numberOfChannels,
      audioData.length,
      audioData.sampleRate
    );

    for (let channel = 0; channel < audioData.numberOfChannels; channel++) {
      const inputData = audioData.getChannelData(channel);
      const outputData = processedBuffer.getChannelData(channel);

      // Apply basic preprocessing
      for (let i = 0; i < inputData.length; i++) {
        outputData[i] = inputData[i];

        // Simple noise gate
        if (Math.abs(outputData[i]) < this.options.noiseThreshold) {
          outputData[i] *= 0.1;
        }

        // Simple compression
        if (Math.abs(outputData[i]) > 0.8) {
          outputData[i] = outputData[i] > 0 ? 0.8 : -0.8;
        }
      }
    }

    return processedBuffer;
  }

  private analyzeAudioQuality(audioData: AudioBuffer): SpeechQualityMetrics {
    // Simplified quality analysis
    const channelData = audioData.getChannelData(0);
    let sum = 0;
    let peak = 0;

    for (let i = 0; i < channelData.length; i++) {
      const abs = Math.abs(channelData[i]);
      sum += abs * abs;
      peak = Math.max(peak, abs);
    }

    const rms = Math.sqrt(sum / channelData.length);

    return {
      clarity: Math.min(rms * 5, 1),
      volume: Math.min(rms * 3, 1),
      consistency: 0.8, // Mock value
      snr: Math.min(peak / rms, 1),
      distortion: Math.max(0, 1 - peak),
    };
  }

  private async audioDataToBuffer(
    audioData: AudioBuffer
  ): Promise<ArrayBuffer> {
    // This is a simplified conversion - implement proper encoding
    const length = audioData.length * audioData.numberOfChannels * 2; // 16-bit samples
    const buffer = new ArrayBuffer(length);
    const view = new DataView(buffer);

    let offset = 0;
    for (let i = 0; i < audioData.length; i++) {
      for (let channel = 0; channel < audioData.numberOfChannels; channel++) {
        const sample = Math.max(
          -1,
          Math.min(1, audioData.getChannelData(channel)[i])
        );
        view.setInt16(offset, sample * 0x7fff, true);
        offset += 2;
      }
    }

    return buffer;
  }
}
