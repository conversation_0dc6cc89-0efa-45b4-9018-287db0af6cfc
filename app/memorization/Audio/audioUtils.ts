// Audio/audioUtils.ts
// Complete audio utility for handling Quran word audio playback with CORS detection and fallbacks

/* ================================================================== */
/*  TYPES & INTERFACES                                                */
/* ================================================================== */

interface AudioUrlInfo {
  surah: number;
  ayah: number;
  wordInAyahIndex: number;
}

interface AudioValidationResult {
  isValid: boolean;
  hasCorsConcerns: boolean;
  statusCode?: number;
  errorType?: "cors" | "network" | "not-found" | "invalid-format";
  errorMessage?: string;
  urlTested: string;
}

interface CacheEntry {
  result: AudioValidationResult;
  timestamp: number;
  expiresIn: number; // milliseconds
}

/* ================================================================== */
/*  CONSTANTS & CONFIGURATION                                         */
/* ================================================================== */

// Primary and fallback CDN endpoints
const AUDIO_CDN_ENDPOINTS = [
  "https://audio.qurancdn.com/wbw/",
  "https://cdn.islamic.network/quran/audio/wbw/",
  "https://audio.alquran.cloud/wbw/",
] as const;

// Cache configuration
const CACHE_DURATION = {
  SUCCESS: 24 * 60 * 60 * 1000, // 24 hours for successful validations
  FAILURE: 5 * 60 * 1000, // 5 minutes for failures (to allow retry)
  CORS_DETECTED: 60 * 60 * 1000, // 1 hour for CORS issues
} as const;

// Validation timeouts
const VALIDATION_TIMEOUT = 8000; // 8 seconds
const CORS_TEST_TIMEOUT = 5000; // 5 seconds

/* ================================================================== */
/*  CACHE MANAGEMENT                                                  */
/* ================================================================== */

class AudioUrlCache {
  private cache = new Map<string, CacheEntry>();
  private readonly maxCacheSize = 1000;

  set(key: string, result: AudioValidationResult): void {
    // Determine expiry based on result type
    let expiresIn: number;
    if (result.isValid) {
      expiresIn = CACHE_DURATION.SUCCESS;
    } else if (result.hasCorsConcerns) {
      expiresIn = CACHE_DURATION.CORS_DETECTED;
    } else {
      expiresIn = CACHE_DURATION.FAILURE;
    }

    // Clean cache if it's getting too large
    if (this.cache.size >= this.maxCacheSize) {
      this.cleanup();
    }

    this.cache.set(key, {
      result,
      timestamp: Date.now(),
      expiresIn,
    });
  }

  get(key: string): AudioValidationResult | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.expiresIn) {
      this.cache.delete(key);
      return null;
    }

    return entry.result;
  }

  clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > entry.expiresIn) {
        keysToDelete.push(key);
      }
    }

    // Remove expired entries
    keysToDelete.forEach((key) => this.cache.delete(key));

    // If still too large, remove oldest entries
    if (this.cache.size >= this.maxCacheSize) {
      const entries = Array.from(this.cache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp
      );

      const toRemove = entries.slice(0, Math.floor(this.maxCacheSize * 0.3));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }
}

// Global cache instance
const urlCache = new AudioUrlCache();

/* ================================================================== */
/*  UTILITY FUNCTIONS                                                 */
/* ================================================================== */

/**
 * Pads a number with leading zeros to specified length
 */
const padNumber = (num: number, size: number): string => {
  return num.toString().padStart(size, "0");
};

/**
 * Creates a cache key for URL validation results
 */
const createCacheKey = (
  surah: number,
  ayah: number,
  wordIndex: number,
  endpoint: string
): string => {
  return `${endpoint}:${surah}:${ayah}:${wordIndex}`;
};

/**
 * Determines if an error is likely due to CORS restrictions
 */
const isCorsError = (error: any): boolean => {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorName = error.name?.toLowerCase() || "";

  // Common CORS error indicators
  return (
    errorMessage.includes("cors") ||
    errorMessage.includes("cross-origin") ||
    errorMessage.includes("network error") ||
    errorName.includes("networkerror") ||
    // Fetch API typically returns generic error for CORS
    (errorMessage.includes("failed to fetch") && error.name === "TypeError")
  );
};

/* ================================================================== */
/*  CORE FUNCTIONS                                                    */
/* ================================================================== */

/**
 * Constructs the audio URL for a specific Quran word
 */
export const constructAudioUrl = (
  surah: number,
  ayah: number,
  wordInAyahIndex: number,
  endpoint: string = AUDIO_CDN_ENDPOINTS[0]
): string => {
  if (!surah || !ayah || !wordInAyahIndex) {
    throw new Error(
      `Invalid parameters for audio URL: surah=${surah}, ayah=${ayah}, wordIndex=${wordInAyahIndex}`
    );
  }

  if (surah < 1 || surah > 114) {
    throw new Error(
      `Invalid surah number: ${surah}. Must be between 1 and 114.`
    );
  }

  if (ayah < 1) {
    throw new Error(`Invalid ayah number: ${ayah}. Must be greater than 0.`);
  }

  if (wordInAyahIndex < 1) {
    throw new Error(
      `Invalid word index: ${wordInAyahIndex}. Must be greater than 0.`
    );
  }

  const surahPadded = padNumber(surah, 3);
  const ayahPadded = padNumber(ayah, 3);
  const wordPadded = padNumber(wordInAyahIndex, 3);

  const url = `${endpoint}${surahPadded}_${ayahPadded}_${wordPadded}.mp3`;

  console.log(`[AudioUtils] Constructed URL: ${url}`);
  return url;
};

/**
 * Tests CORS access for a given URL using a lightweight method
 */
export const testCORSAccess = async (url: string): Promise<boolean> => {
  try {
    // Use AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), CORS_TEST_TIMEOUT);

    // Make a HEAD request to test accessibility without downloading the file
    const response = await fetch(url, {
      method: "HEAD",
      mode: "cors",
      signal: controller.signal,
      cache: "no-cache",
    });

    clearTimeout(timeoutId);

    console.log(`[AudioUtils] CORS test for ${url}: ${response.status}`);
    return response.ok;
  } catch (error: any) {
    console.log(`[AudioUtils] CORS test failed for ${url}:`, error.message);

    // If it's a CORS error, return false
    if (isCorsError(error)) {
      return false;
    }

    // For other errors (like timeout), also return false but log differently
    if (error.name === "AbortError") {
      console.log(`[AudioUtils] CORS test timeout for ${url}`);
    }

    return false;
  }
};

/**
 * Validates if an audio URL returns a valid audio file
 */
export const validateAudioUrl = async (
  surah: number,
  ayah: number,
  wordInAyahIndex: number,
  endpoint: string = AUDIO_CDN_ENDPOINTS[0]
): Promise<AudioValidationResult> => {
  // Check cache first
  const cacheKey = createCacheKey(surah, ayah, wordInAyahIndex, endpoint);
  const cachedResult = urlCache.get(cacheKey);

  if (cachedResult) {
    console.log(`[AudioUtils] Using cached validation result for ${cacheKey}`);
    return cachedResult;
  }

  // Construct URL
  let url: string;
  try {
    url = constructAudioUrl(surah, ayah, wordInAyahIndex, endpoint);
  } catch (error: any) {
    const result: AudioValidationResult = {
      isValid: false,
      hasCorsConcerns: false,
      errorType: "invalid-format",
      errorMessage: error.message,
      urlTested: "",
    };
    return result;
  }

  console.log(`[AudioUtils] Validating audio URL: ${url}`);

  try {
    // Use AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), VALIDATION_TIMEOUT);

    // Make a HEAD request to check if file exists and is accessible
    const response = await fetch(url, {
      method: "HEAD",
      mode: "cors",
      signal: controller.signal,
      cache: "no-cache",
    });

    clearTimeout(timeoutId);

    const result: AudioValidationResult = {
      isValid: response.ok,
      hasCorsConcerns: false,
      statusCode: response.status,
      urlTested: url,
    };

    if (!response.ok) {
      result.errorType = response.status === 404 ? "not-found" : "network";
      result.errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    } else {
      // Check content type if available
      const contentType = response.headers.get("content-type");
      if (
        contentType &&
        !contentType.includes("audio") &&
        !contentType.includes("mpeg")
      ) {
        result.isValid = false;
        result.errorType = "invalid-format";
        result.errorMessage = `Unexpected content type: ${contentType}`;
      }
    }

    // Cache the result
    urlCache.set(cacheKey, result);

    console.log(`[AudioUtils] Validation result for ${url}:`, result);
    return result;
  } catch (error: any) {
    console.error(`[AudioUtils] Error validating ${url}:`, error);

    const result: AudioValidationResult = {
      isValid: false,
      hasCorsConcerns: isCorsError(error),
      urlTested: url,
    };

    if (error.name === "AbortError") {
      result.errorType = "network";
      result.errorMessage = "Request timeout";
    } else if (isCorsError(error)) {
      result.errorType = "cors";
      result.errorMessage = "CORS policy prevents access";
    } else {
      result.errorType = "network";
      result.errorMessage = error.message || "Network error";
    }

    // Cache the result
    urlCache.set(cacheKey, result);

    return result;
  }
};

/**
 * Tests multiple CDN endpoints to find a working one
 */
export const findWorkingEndpoint = async (
  surah: number,
  ayah: number,
  wordInAyahIndex: number
): Promise<{ endpoint: string; validation: AudioValidationResult } | null> => {
  console.log(
    `[AudioUtils] Finding working endpoint for ${surah}:${ayah}:${wordInAyahIndex}`
  );

  for (const endpoint of AUDIO_CDN_ENDPOINTS) {
    const validation = await validateAudioUrl(
      surah,
      ayah,
      wordInAyahIndex,
      endpoint
    );

    if (validation.isValid) {
      console.log(`[AudioUtils] Found working endpoint: ${endpoint}`);
      return { endpoint, validation };
    }

    // If this endpoint has CORS issues, continue to next
    if (validation.hasCorsConcerns) {
      console.log(
        `[AudioUtils] Endpoint ${endpoint} has CORS issues, trying next...`
      );
      continue;
    }
  }

  console.log(
    `[AudioUtils] No working endpoint found for ${surah}:${ayah}:${wordInAyahIndex}`
  );
  return null;
};

/**
 * Determines if proxy should be used based on CORS test results
 */
export const shouldUseProxy = async (
  surah: number,
  ayah: number,
  wordInAyahIndex: number
): Promise<boolean> => {
  // Try to find a working direct endpoint first
  const workingEndpoint = await findWorkingEndpoint(
    surah,
    ayah,
    wordInAyahIndex
  );

  if (workingEndpoint && !workingEndpoint.validation.hasCorsConcerns) {
    return false; // Direct access works
  }

  // If all endpoints have CORS concerns or don't work, suggest proxy
  console.log(
    `[AudioUtils] Recommending proxy for ${surah}:${ayah}:${wordInAyahIndex}`
  );
  return true;
};

/**
 * Constructs a proxy URL for backend audio serving
 */
export const getProxyUrl = (
  surah: number,
  ayah: number,
  wordInAyahIndex: number
): string => {
  const params = new URLSearchParams({
    surah: surah.toString(),
    ayah: ayah.toString(),
    word: wordInAyahIndex.toString(),
  });

  const proxyUrl = `/api/audio-proxy?${params.toString()}`;
  console.log(`[AudioUtils] Generated proxy URL: ${proxyUrl}`);
  return proxyUrl;
};

/**
 * Gets the best available audio URL (direct or proxy)
 */
export const getBestAudioUrl = async (
  surah: number,
  ayah: number,
  wordInAyahIndex: number
): Promise<string | null> => {
  console.log(
    `[AudioUtils] Getting best audio URL for ${surah}:${ayah}:${wordInAyahIndex}`
  );

  // Try to find working direct endpoint
  const workingEndpoint = await findWorkingEndpoint(
    surah,
    ayah,
    wordInAyahIndex
  );

  if (workingEndpoint && !workingEndpoint.validation.hasCorsConcerns) {
    const directUrl = constructAudioUrl(
      surah,
      ayah,
      wordInAyahIndex,
      workingEndpoint.endpoint
    );
    console.log(`[AudioUtils] Using direct URL: ${directUrl}`);
    return directUrl;
  }

  // Fall back to proxy
  const proxyUrl = getProxyUrl(surah, ayah, wordInAyahIndex);
  console.log(`[AudioUtils] Using proxy URL: ${proxyUrl}`);
  return proxyUrl;
};

/**
 * Plays audio from a URL with comprehensive error handling
 */
export const playAudioFromUrl = async (url: string): Promise<boolean> => {
  console.log(`[AudioUtils] Attempting to play audio: ${url}`);

  return new Promise((resolve) => {
    const audio = new Audio();
    let resolved = false;

    const cleanup = () => {
      if (!resolved) {
        resolved = true;
        audio.removeEventListener("loadstart", onLoadStart);
        audio.removeEventListener("canplay", onCanPlay);
        audio.removeEventListener("error", onError);
        audio.removeEventListener("ended", onEnded);
      }
    };

    const onLoadStart = () => {
      console.log(`[AudioUtils] Audio loading started: ${url}`);
    };

    const onCanPlay = () => {
      console.log(`[AudioUtils] Audio can play: ${url}`);
    };

    const onError = (event: Event) => {
      cleanup();
      console.error(`[AudioUtils] Audio error for ${url}:`, {
        error: audio.error,
        networkState: audio.networkState,
        readyState: audio.readyState,
        event,
      });
      resolve(false);
    };

    const onEnded = () => {
      cleanup();
      console.log(`[AudioUtils] Audio playback completed: ${url}`);
      resolve(true);
    };

    // Set up event listeners
    audio.addEventListener("loadstart", onLoadStart);
    audio.addEventListener("canplay", onCanPlay);
    audio.addEventListener("error", onError);
    audio.addEventListener("ended", onEnded);

    // Set source and attempt to play
    audio.src = url;
    audio.load();

    audio
      .play()
      .then(() => {
        console.log(`[AudioUtils] Audio play() succeeded: ${url}`);
        // Don't resolve here - wait for 'ended' event
      })
      .catch((error) => {
        cleanup();
        console.error(`[AudioUtils] Audio play() failed for ${url}:`, error);
        resolve(false);
      });

    // Timeout fallback
    setTimeout(() => {
      if (!resolved) {
        cleanup();
        console.warn(`[AudioUtils] Audio playback timeout for ${url}`);
        resolve(false);
      }
    }, 30000); // 30 second timeout
  });
};

/**
 * Cache management utilities
 */
export const audioCache = {
  clear: () => urlCache.clear(),

  prevalidate: async (words: AudioUrlInfo[]): Promise<void> => {
    console.log(`[AudioUtils] Prevalidating ${words.length} audio URLs`);

    const validationPromises = words.map(async (word) => {
      try {
        await validateAudioUrl(word.surah, word.ayah, word.wordInAyahIndex);
      } catch (error) {
        console.warn(
          `[AudioUtils] Prevalidation failed for ${word.surah}:${word.ayah}:${word.wordInAyahIndex}`,
          error
        );
      }
    });

    await Promise.allSettled(validationPromises);
    console.log(`[AudioUtils] Prevalidation completed`);
  },
};

/* ================================================================== */
/*  TYPE EXPORTS                                                      */
/* ================================================================== */

export type { AudioUrlInfo, AudioValidationResult };
