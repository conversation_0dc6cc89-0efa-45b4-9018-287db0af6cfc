import { useRef, useState, useEffect, useCallback } from "react";

interface UseClipPlayer {
  isPlaying: boolean;
  play: (start: number, end: number) => void;
  stop: () => void;
  element: HTMLAudioElement;
}

export default function useClipPlayer(src: string | null): UseClipPlayer {
  const audioRef = useRef<HTMLAudioElement>(new Audio(src || ""));
  const [isPlaying, setPlaying] = useState(false);

  // Update audio src when it changes
  useEffect(() => {
    if (src) {
      audioRef.current.src = src;
    }
  }, [src]);

  const stop = useCallback(() => {
    const a = audioRef.current;
    a.pause();
    a.currentTime = 0;
    setPlaying(false);
  }, []);

  const play = useCallback(
    (start: number, end: number) => {
      if (!src) {
        console.warn("No audio source available for playback");
        return;
      }

      const a = audioRef.current;
      stop();
      a.currentTime = start;
      a.play().catch(console.error);
      const onTime = () => {
        if (a.currentTime >= end) stop();
      };
      a.addEventListener("timeupdate", onTime, { once: false });
      a.onended = stop;
      setPlaying(true);
    },
    [stop, src]
  );

  useEffect(() => () => stop(), [stop]);

  return { isPlaying, play, stop, element: audioRef.current };
}
