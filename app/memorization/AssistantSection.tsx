"use client";

import React from "react";
// We no longer need the store hook here, but we do need the type for the new prop.
import { ConnectionStatus } from "@/store/useVoiceStore";
import Assistant from "../memorization/Audio/Assistant";

// The props interface is updated to accept the `status` from the parent.
interface AssistantSectionProps {
  status: ConnectionStatus;
  inputAmplitude?: number;
}

/**
 * Section that displays the AI assistant visualizer.
 * Now receives connection status via props to sync with the parent component.
 * Conversation history boxes have been removed for cleaner interface.
 */
export default function AssistantSection({
  status,
  inputAmplitude = 0,
}: AssistantSectionProps) {
  // This component no longer needs to fetch state from the voice store directly.
  // It relies on the `status` prop passed down from its parent.

  return (
    <div className="flex-1 flex flex-col justify-center items-center">
      {/* Assistant Visualizer - Main focus of this component */}
      <div className="flex-grow flex items-center justify-center">
        <Assistant inputAmplitude={inputAmplitude} />
      </div>
    </div>
  );
}
