// InteractionBox.tsx
"use client";

import React from "react";

type InteractionBoxProps = {
  responseText: string;
  score: number | null;
  onSubmit: () => void;
  loading: boolean;
  isRecording: boolean;
  audioURL: string | null;
};

const InteractionBox: React.FC<InteractionBoxProps> = ({
  responseText,
  score,
}) => {
  return (
    <div className="flex flex-col h-full min-h-0">
      <div className="flex-none px-4 pt-4">
        <h2 className="text-xl mb-4">Transcription</h2>
      </div>
      {/* Overflow container for scrollable content */}
      <div className="flex-1 mt-2 px-4 overflow-y-auto min-h-0">
        {responseText ? (
          <div className="mt-4 text-2xl text-sky-600">
            <p>Your recitation: {responseText}</p>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <p className="text-gray-500 text-lg">
              No transcription available yet.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InteractionBox;
