import { useEffect } from "react";
import { Card } from "./card";
import { challengeOptions, challenges } from "@/db/schema";

type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (id: number | null, side: "left" | "right") => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  disabled?: boolean;
  type: (typeof challenges.$inferSelect)["type"];
};

export const Select = ({
  options,
  onSelect,
  status,
  selectedOption,
  disabled,
  type,
}: Props) => {
  const handleOptionClick = (id: number) => {
    onSelect(id, "left");
  };

  useEffect(() => {
    console.log("Select Props:", { options, type });
  }, [options, type]);

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="flex flex-row flex-wrap justify-center overflow-x-auto w-full mt-4 gap-2">
        {options.map((option) => {
          const isAudioWave = option.imageSrc === "/AudioWave.svg";
          return (
            <Card
              key={option.id}
              id={option.id}
              imageSrc={option.imageSrc}
              audioSrc={option.audioSrc}
              text={option.text}
              selected={selectedOption === option.id}
              onClick={() => handleOptionClick(option.id)}
              disabled={disabled}
              status={status}
              type={type}
              shape="roundedRectangle"
              version="version1" // Specify version 1 for this layout
            />
          );
        })}
      </div>
    </div>
  );
};
