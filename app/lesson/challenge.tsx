// challenge.tsx

import { useEffect } from "react";
import { challengeOptions } from "@/db/schema";
import { ImageAudioSelectComponent } from "./ImageAudioSelect";
import { Select } from "./Select";
import { Assist } from "./Assist";
import { DragAndDrop } from "./DragAndDrop";
import { FillInTheBlank } from "./FillInTheBlank";
import { SpeakThis } from "./speakThis";
import { Card } from "./card";
import SpeakThisAdvanced from "./advancedChallenges/speakThisAdvanced";

type ChallengeType =
  | "SELECT"
  | "ASSIST"
  | "MATCHING"
  | "TAP_WHAT_YOU_HEAR"
  | "IMAGE_AUDIO_SELECT"
  | "DRAG_AND_DROP"
  | "SPEAK_THIS"
  | "FILL_IN_THE_BLANK"
  | "SPEAK_THIS_ADVANCED";

type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (
    id: number | null,
    side: "left" | "right" | "drag-and-drop" | "fill-in-the-blank"
  ) => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  selectedPairOption?: number | null;
  disabled?: boolean;
  type: ChallengeType;
  mediaType?: string | null;
  mediaUrl?: string | null;
  audioSrc?: string | null;
  question: string;
  sentence?: string | null;
};

export const Challenge = ({
  options,
  onSelect,
  status,
  selectedOption,
  selectedPairOption,
  disabled = false,
  type,
  mediaType,
  mediaUrl,
  audioSrc,
  question,
  sentence,
}: Props) => {
  const handleClick = (id: number) => {
    console.log("Option clicked:", id);
    onSelect(id, "left");
  };

  useEffect(() => {
    if (type === "TAP_WHAT_YOU_HEAR" && options[0]?.audioSrc) {
      const audio = new Audio(options[0].audioSrc);
      audio.play();

      // Clean up the audio instance on component unmount
      return () => {
        audio.pause();
        audio.currentTime = 0;
      };
    }
  }, [type, options]);

  const renderComponent = () => {
    if (type === "SELECT") {
      return (
        <Select
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
        />
      );
    } else if (type === "ASSIST") {
      return (
        <Assist
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={question}
          audioSrc={audioSrc}
          sentence={sentence}
        />
      );
    } else if (type === "DRAG_AND_DROP") {
      return (
        <DragAndDrop
          options={options}
          onSelect={(result) => {
            if (Array.isArray(result)) {
              onSelect(null, "drag-and-drop"); // Clear previous selections
              result.forEach((id) => onSelect(id, "drag-and-drop"));
            } else {
              onSelect(result === "correct" ? -1 : null, "drag-and-drop");
            }
          }}
          status={status}
          disabled={disabled}
          question={{ audioSrc: audioSrc || "", text: question }}
          type={type}
        />
      );
    } else if (type === "IMAGE_AUDIO_SELECT") {
      return (
        <ImageAudioSelectComponent
          options={options}
          onSelect={(id) => onSelect(id, "left")}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={{ audioSrc: audioSrc || "", text: question }}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
        />
      );
    } else if (type === "FILL_IN_THE_BLANK") {
      return (
        <FillInTheBlank
          options={options}
          onSelect={(id) => onSelect(id, "fill-in-the-blank")}
          status={status}
          selectedOptions={selectedOption ? [selectedOption] : []}
          disabled={disabled}
          sentence={sentence || ""}
          question={question}
          mediaUrl={mediaUrl}
          mediaType={mediaType}
        />
      );
    } else if (type === "SPEAK_THIS") {
      return (
        <SpeakThis
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={question}
          audioSrc={audioSrc || ""}
          sentence={sentence || ""}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
        />
      );
    } else if (type === "SPEAK_THIS_ADVANCED") {
      // ADDED: 'onSpeakThisAdvancedResult' prop to fix the TS error
      return (
        <SpeakThisAdvanced
          options={options}
          onSelect={onSelect}
          status={status}
          selectedOption={selectedOption}
          disabled={disabled}
          type={type}
          question={question}
          audioSrc={audioSrc || ""}
          sentence={sentence || ""}
          mediaType={mediaType}
          mediaUrl={mediaUrl}
          onSpeakThisAdvancedResult={(isCorrect) => {
            console.log(
              "challenge.tsx: speakThisAdvanced result =>",
              isCorrect
            );
            // For now, we do not pass this up from here,
            // because the parent (quiz.tsx) also manages SpeakThisAdvanced directly.
            // This ensures 'onSpeakThisAdvancedResult' is provided to avoid TS errors.
          }}
        />
      );
    }

    console.warn("Unsupported challenge type:", type);
    return null;
  };

  return (
    <div>
      {renderComponent()}
      {type !== "SELECT" &&
        type !== "ASSIST" &&
        type !== "DRAG_AND_DROP" &&
        type !== "FILL_IN_THE_BLANK" &&
        type !== "SPEAK_THIS" &&
        type !== "SPEAK_THIS_ADVANCED" &&
        options.map((option) => (
          <Card
            key={option.id}
            id={option.id}
            text={option.text}
            imageSrc={option.imageSrc}
            audioSrc={option.audioSrc}
            onClick={() => handleClick(option.id)}
            selected={
              selectedOption === option.id || selectedPairOption === option.id
            }
            status={status}
            disabled={disabled}
            type={type}
          />
        ))}
    </div>
  );
};
