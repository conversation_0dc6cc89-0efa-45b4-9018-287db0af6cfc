import { useEffect, useState } from "react";
import { Card } from "./card";
import { challengeOptions, challenges } from "@/db/schema";
import Image from "next/image";
import { AudioButton } from "./AudioButton";
import { cn } from "@/lib/utils";

// We extend the challenge type here to include "SPEAK_THIS_ADVANCED" so it no longer causes a type error.
type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (id: number | null, side: "left" | "right") => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  disabled?: boolean;
  type: (typeof challenges.$inferSelect)["type"] | "SPEAK_THIS_ADVANCED"; // Added "SPEAK_THIS_ADVANCED"
  question: string;
  audioSrc?: string | null;
  sentence?: string | null;
  mediaType?: string | null;
  mediaUrl?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
};

export const Assist = ({
  options,
  onSelect,
  status,
  selectedOption,
  disabled,
  type,
  question,
  audioSrc,
  sentence,
  mediaType,
  mediaUrl,
  topCardText,
  topCardAudio,
}: Props) => {
  const [dummyState, setDummyState] = useState(false);

  const handleOptionClick = (id: number) => {
    onSelect(id, "left");
  };

  useEffect(() => {
    console.log("Assist Props:", {
      options,
      type,
      selectedOption,
      mediaType,
      mediaUrl,
      topCardText,
      topCardAudio,
    });
    setDummyState((prev) => !prev);
  }, [
    options,
    type,
    selectedOption,
    mediaType,
    mediaUrl,
    topCardText,
    topCardAudio,
  ]);

  function parseHighlightIndices(text: string): number[] {
    const match = text.match(/^\(([\d,]+)\)/);
    if (!match) return [];
    return match[1]
      .split(",")
      .map((num) => parseInt(num, 10))
      .filter((n) => !isNaN(n));
  }

  function removeMarker(text: string): string {
    return text.replace(/^\(([\d,]+)\)/, "");
  }

  // Updated highlightSegments function using Intl.Segmenter
  function highlightSegments(
    text: string,
    highlightIndices: number[]
  ): JSX.Element[] {
    // Use Intl.Segmenter for proper grapheme cluster segmentation
    const segmenter = new Intl.Segmenter("ar", { granularity: "grapheme" });
    const graphemes = [...segmenter.segment(text)].map((seg) => seg.segment);

    const segments: { text: string; highlight: boolean }[] = [];
    let start = 0;
    const sortedIndices = [...highlightIndices].sort((a, b) => a - b);

    sortedIndices.forEach((idx) => {
      const realIndex = idx - 1; // Convert 1-based to 0-based
      if (realIndex >= graphemes.length) return;
      if (realIndex > start) {
        segments.push({
          text: graphemes.slice(start, realIndex).join(""),
          highlight: false,
        });
      }
      segments.push({
        text: graphemes[realIndex],
        highlight: true,
      });
      start = realIndex + 1;
    });

    if (start < graphemes.length) {
      segments.push({
        text: graphemes.slice(start).join(""),
        highlight: false,
      });
    }

    return segments.map((seg, i) =>
      seg.highlight ? (
        <span key={i} style={{ color: "white" }}>
          {seg.text}
        </span>
      ) : (
        <span key={i}>{seg.text}</span>
      )
    );
  }

  const renderProcessedSentence = (sentence: string) => {
    const parts = sentence.split(/(فارغ|blank|knalb)/);

    return parts.map((part, index) => {
      if (part === "فارغ" || part === "blank" || part === "knalb") {
        return (
          <span
            key={index}
            className="inline-block mx-2"
            style={{
              width: "2.5rem",
              borderBottom: "2px dashed #D1D5DB",
              position: "relative",
              top: "0.5em",
              height: "1.5em",
              verticalAlign: "baseline",
              marginTop: "-0.5em",
            }}
          />
        );
      }

      const highlightIndices = parseHighlightIndices(part);
      const cleanedPart = removeMarker(part);
      const highlightedOutput = highlightSegments(
        cleanedPart,
        highlightIndices
      );

      return (
        <span key={index} dir="rtl" style={{ direction: "rtl" }}>
          {highlightedOutput}
        </span>
      );
    });
  };

  return (
    <div className="flex flex-col items-center space-y-6">
      {/* Center container */}
      <div className="flex items-start gap-x-4 w-full justify-center">
        {/* Bigger Mascot images */}
        <div className="flex-shrink-0">
          <Image
            src="/KaafM.svg"
            alt="Mascot"
            height={250}
            width={250}
            className="hidden lg:block"
          />
          <Image
            src="/KaafM.svg"
            alt="Mascot"
            height={150}
            width={150}
            className="block lg:hidden"
          />
        </div>

        {/* Larger card area */}
        <div className="relative flex-grow max-w-lg">
          <div className="chat chat-start">
            <div
              className={cn(
                "chat-bubble",
                "bg-white",
                "py-6 px-12",
                // Bump text size even further
                "text-6xl font-light",
                "ml-1 mb-16",
                "max-w-full",
                "relative",
                "shadow-sm",
                "border border-gray-100",
                "rounded-2xl",
                "overflow-hidden"
              )}
            >
              {audioSrc && (
                <div className="absolute top-4 left-4">
                  <AudioButton
                    src={audioSrc}
                    className="mr-2 "
                    variant="small"
                  />
                </div>
              )}

              <div
                dir="rtl"
                className={cn(
                  "text-gray-900",
                  "tracking-tight",
                  audioSrc ? "mt-12" : "mt-2"
                )}
                style={{ textAlign: "right" }}
              >
                {sentence ? (
                  <span className="leading-relaxed">
                    {renderProcessedSentence(sentence)}
                  </span>
                ) : (
                  <span className="leading-relaxed">{question}</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full border-t border-gray-100"></div>

      <div className="flex gap-4 w-full justify-center py-2">
        {options.map((option) => (
          <Card
            key={option.id}
            id={option.id}
            imageSrc={option.imageSrc}
            audioSrc={option.audioSrc}
            text={option.text}
            selected={selectedOption === option.id}
            onClick={() => handleOptionClick(option.id)}
            disabled={disabled}
            status={status}
            type={type}
            shape="roundedRectangle"
            customClass={
              option.audioSrc && option.imageSrc
                ? "custom-card shadow-sm"
                : "shadow-sm"
            }
            // Making the option cards bigger as well:
            style={
              option.audioSrc && option.imageSrc
                ? { width: "200px", height: "80px" }
                : { width: "200px", height: "80px" }
            }
          />
        ))}
      </div>
    </div>
  );
};
