// matching.tsx
import React, { useEffect, useState, useMemo, useCallback } from "react";
import Image from "next/image";
import { Card } from "./card";
import { challengeOptions } from "@/db/schema";
import { shuffleArray } from "@/lib/utils";

type MatchingProps = {
  options: (typeof challengeOptions.$inferSelect & {
    side: "left" | "right";
  })[];
  onSelect: (id: number | null, side: "left" | "right") => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  selectedPairOption: number | null;
  disabled?: boolean;
  matchedPairs: Set<number>;
  question: string;
  audioSrc?: string | null;
  mediaType?: string | null;
  mediaUrl?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
  sentence?: string | null;
};

type ImageCallback = (resizedImageUrl: string) => void;

const resizeImage = (
  src: string,
  width: number,
  height: number,
  callback: ImageCallback
) => {
  const img: HTMLImageElement = document.createElement("img");
  img.crossOrigin = "Anonymous";
  img.src = src;

  img.onload = () => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      console.error("Failed to get canvas context");
      return;
    }

    canvas.width = width;
    canvas.height = height;

    ctx.drawImage(img, 0, 0, width, height);

    const resizedImageUrl = canvas.toDataURL("image/png");
    callback(resizedImageUrl);
  };
};

type ResizedImages = { [key: number]: string };

const MatchingInner: React.FC<MatchingProps> = ({
  options,
  onSelect,
  status,
  selectedOption,
  selectedPairOption,
  disabled,
  matchedPairs,
  question,
  audioSrc,
  mediaType,
  mediaUrl,
  topCardText,
  topCardAudio,
  sentence,
}) => {
  const leftOptions = useMemo(
    () => shuffleArray(options.filter((option) => option.side === "left")),
    [options]
  );

  const rightOptions = useMemo(
    () => shuffleArray(options.filter((option) => option.side === "right")),
    [options]
  );

  const [resizedImages, setResizedImages] = useState<ResizedImages>({});

  useEffect(() => {
    options.forEach((option) => {
      if (option.imageSrc && !resizedImages[option.id]) {
        if (option.imageSrc === "/audiowave.svg") {
          // If it's exactly "/audiowave.svg", skip resizing
          setResizedImages((prev) => ({
            ...prev,
            [option.id]: "/audiowave.svg",
          }));
        } else {
          resizeImage(option.imageSrc, 200, 80, (resizedUrl) => {
            setResizedImages((prev) => ({ ...prev, [option.id]: resizedUrl }));
          });
        }
      }
    });
  }, [options, resizedImages]);

  const handleClick = useCallback(
    (id: number, side: "left" | "right") => {
      console.log("Matching option clicked:", id);
      onSelect(id, side);
    },
    [onSelect]
  );

  const isImageVersion = useMemo(
    () => options.some((option) => option.imageSrc),
    [options]
  );

  return (
    <div className="flex flex-col gap-4 max-h-[calc(100vh-220px)]">
      {audioSrc && (
        <div className="flex justify-center mb-2">
          <audio controls>
            <source src={audioSrc} type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>
        </div>
      )}
      {mediaType && mediaUrl && (
        <div className="flex justify-center mb-2">
          {mediaType === "image" && (
            <Image
              src={mediaUrl}
              alt="Related media"
              width={500}
              height={500}
              className="max-w-full"
            />
          )}
          {mediaType === "audio" && (
            <audio controls>
              <source src={mediaUrl} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          )}
          {mediaType === "video" && (
            <video controls className="max-w-full">
              <source src={mediaUrl} type="video/mp4" />
              Your browser does not support the video element.
            </video>
          )}
        </div>
      )}
      {topCardText && (
        <div className="text-center mb-2">
          <p>{topCardText}</p>
          {topCardAudio && (
            <audio controls>
              <source src={topCardAudio} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          )}
        </div>
      )}
      {sentence && (
        <div className="text-center mb-2">
          <p>{sentence}</p>
        </div>
      )}

      <div className="flex gap-4 justify-center items-start">
        <div className="flex-1 grid gap-3 md:flex-none md:mx-auto md:max-w-sm">
          {leftOptions.map((option) => (
            <Card
              key={option.id}
              id={option.id}
              imageSrc={resizedImages[option.id] || undefined}
              audioSrc={option.audioSrc}
              text={option.text}
              onClick={() => handleClick(option.id, "left")}
              selected={selectedOption === option.id}
              status={status}
              disabled={disabled || matchedPairs.has(option.id)}
              matched={matchedPairs.has(option.id)}
              type="MATCHING"
              large={false}
              shape="roundedRectangle"
              customClass="custom-card"
              version={isImageVersion ? "version1" : "version2"}
              style={{ width: "200px", height: "80px" }}
            />
          ))}
        </div>

        <div className="flex-1 grid gap-3 md:flex-none md:mx-auto md:max-w-sm">
          {rightOptions.map((option) => (
            <Card
              key={option.id}
              id={option.id}
              imageSrc={resizedImages[option.id] || undefined}
              audioSrc={option.audioSrc}
              text={option.text}
              onClick={() => handleClick(option.id, "right")}
              selected={selectedPairOption === option.id}
              status={status}
              disabled={disabled || matchedPairs.has(option.id)}
              matched={matchedPairs.has(option.id)}
              type="MATCHING"
              large={false}
              shape="roundedRectangle"
              customClass="custom-card"
              version={isImageVersion ? "version1" : "version2"}
              style={{ width: "200px", height: "80px" }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export const Matching = React.memo(MatchingInner);
Matching.displayName = "Matching";
