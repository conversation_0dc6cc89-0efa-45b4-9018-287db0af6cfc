// app/lesson/page.tsx

import { redirect } from "next/navigation";

import { getLesson, getUserProgress, getUserSubscription } from "@/db/queries";

import { Quiz } from "./quiz";

const LessonPage = async () => {
  // Fetch data
  const lessonData = getLesson();
  const userProgressData = getUserProgress();
  const userSubscriptionData = getUserSubscription();

  // Await all promises
  const [lesson, userProgress, userSubscription] = await Promise.all([
    lessonData,
    userProgressData,
    userSubscriptionData,
  ]);

  // Redirect if lesson or user progress is not found
  if (!lesson || !userProgress) {
    redirect("/learn");
    return null;
  }

  // Calculate initial percentage of completion
  const initialPercentage =
    (lesson.challenges.filter((challenge) => challenge.completed).length /
      lesson.challenges.length) *
    100;

  // Transform challenges to ensure correct typing for the `side` property
  const transformedChallenges = lesson.challenges.map((challenge) => ({
    ...challenge,
    challengeOptions: challenge.challengeOptions.map((option) => ({
      ...option,
      side: option.side as "left" | "right", // Ensure the side property is correctly typed
    })),
  }));

  // Render Quiz component with correct props
  return (
    <Quiz
      initialLessonId={lesson.id}
      initialLessonChallenges={transformedChallenges}
      initialHearts={userProgress.hearts}
      initialPercentage={initialPercentage}
      userSubscription={userSubscription}
    />
  );
};

export default LessonPage;
