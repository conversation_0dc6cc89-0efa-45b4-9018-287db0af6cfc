import Image from "next/image";
import { InfinityIcon, X } from "lucide-react";

import { Progress } from "@/components/ui/progress";
import { useExitModal } from "@/store/use-exit-modal";

// NEW: Define referrer information type (matching quiz.tsx)
type ReferrerInfo = {
  from: "coursesplayer" | "learn" | "unknown";
  curriculumId?: string;
} | null;

type Props = {
  hearts: number;
  percentage: number;
  hasActiveSubscription: boolean;
  // === Added style to allow inline styling in Quiz.tsx ===
  style?: React.CSSProperties;
  // NEW: Add referrer information and navigation handler props
  referrerInfo?: ReferrerInfo;
  onNavigateBack?: () => void;
};

export const Header = ({
  hearts,
  percentage,
  hasActiveSubscription,
  // === Make sure to accept this style prop ===
  style,
  // NEW: Accept referrer and navigation props with defaults
  referrerInfo = null,
  onNavigateBack,
}: Props) => {
  const { open } = useExitModal();

  // NEW: Smart navigation handler for X button
  const handleXButtonClick = () => {
    console.log("Header X button clicked with referrer:", referrerInfo);

    if (onNavigateBack) {
      // Use the smart navigation function provided by Quiz component
      console.log("Using smart navigation provided by parent component");
      onNavigateBack();
    } else {
      // Fallback to existing exit modal behavior for backward compatibility
      console.log("No navigation handler provided, using default exit modal");
      open();
    }
  };

  return (
    <header
      // === Apply the style prop here ===
      style={style}
      className="lg:pt-[50px] pt-[20px] px-10 flex gap-x-7 items-center justify-between max-w-[1140px] mx-auto w-full"
    >
      <X
        onClick={handleXButtonClick} // NEW: Use smart navigation handler
        className="text-slate-500 hover:opacity-75 transition cursor-pointer"
      />
      <Progress value={percentage} />
      <div className="text-rose-500 flex items-center font-bold">
        <Image
          src="/heart.svg"
          height={28}
          width={28}
          alt="Heart"
          className="mr-2"
        />
        {hasActiveSubscription ? (
          <InfinityIcon className="h-6 w-6 stroke-[3] shrink-0" />
        ) : (
          hearts
        )}
      </div>
    </header>
  );
};
