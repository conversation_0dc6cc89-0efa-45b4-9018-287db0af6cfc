import Image from "next/image";
import { useCallback, useEffect, useRef } from "react";
import { useAudio } from "react-use";
import { cn } from "@/lib/utils";

type MainCardProps = {
  imageSrc?: string;
  audioSrc?: string;
  altText?: string;
  hasAudio?: boolean;
  customClass?: string;
  topCardText?: string | null;
  topCardAudio?: string | null;
  version?: "version1" | "version2" | "version3";
};

export const MainCard = ({
  imageSrc,
  audioSrc,
  altText,
  hasAudio,
  customClass,
  topCardText,
  topCardAudio,
  version,
}: MainCardProps) => {
  const [audio, state, controls, ref] = useAudio({
    src: topCardAudio || audioSrc || "",
  });

  useEffect(() => {
    if (ref.current) {
      console.log("MainCard: audio element is available", ref.current);
    } else {
      console.log("MainCard: audio element is not available yet");
    }
  }, [ref]);

  const handlePlayAudio = useCallback(() => {
    if (ref.current) {
      console.log("MainCard: handlePlayAudio called");
      controls.play();
    } else {
      console.log("MainCard: audio element is not available on click");
    }
  }, [controls, ref]);

  const textStyle = cn(
    "text-center text-gray-900",
    "font-medium tracking-tight",
    version === "version1"
      ? "text-7xl lg:text-8xl"
      : version === "version3"
      ? "text-3xl lg:text-4xl"
      : "text-4xl lg:text-5xl"
  );

  const baseCardStyles = cn(
    // Base layout
    "relative w-full h-72 mx-auto",
    "flex items-center justify-center",

    // Clean aesthetics
    "bg-white",
    "rounded-2xl",

    // Subtle border and shadow
    "border border-gray-200",
    "shadow-sm",

    // Padding
    "p-6 lg:p-8",

    // Interactive states
    hasAudio && "cursor-pointer hover:bg-gray-50",

    // Custom classes
    customClass
  );

  if (hasAudio || topCardAudio) {
    return (
      <div className={baseCardStyles} onClick={handlePlayAudio}>
        {audio}

        <span className="absolute top-4 left-4 text-gray-500 text-sm font-medium px-3 py-1.5 rounded-full bg-gray-100">
          Hint
        </span>

        {!topCardText && imageSrc && (
          <div className="relative w-full h-full">
            <Image
              src={imageSrc}
              layout="fill"
              objectFit="contain"
              alt={altText || "Main Image"}
              className="select-none"
            />
          </div>
        )}

        {topCardText && (
          <div className="flex flex-col items-center w-full space-y-4">
            <p className={textStyle}>{topCardText}</p>
            <div className="w-1/3 border-b-2 border-dashed border-gray-200"></div>
          </div>
        )}

        {!topCardText && (
          <button
            onClick={handlePlayAudio}
            className={cn(
              "absolute top-4 right-4",
              "p-4 rounded-full",
              "bg-sky-500",
              "shadow-sm",
              "hover:bg-sky-600",
              "focus:outline-none focus:ring-2 focus:ring-sky-500",
              "border border-sky-400"
            )}
          >
            <Image
              src="/Audio.svg"
              alt="Play Audio"
              width={24}
              height={24}
              className="opacity-90"
            />
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={baseCardStyles}>
      <span className="absolute top-4 left-4 text-gray-500 text-sm font-medium px-3 py-1.5 rounded-full bg-gray-100">
        Hint
      </span>

      {!topCardText && imageSrc && (
        <div className="relative w-full h-full">
          <Image
            src={imageSrc}
            layout="fill"
            objectFit="contain"
            alt={altText || "Main Image"}
            className="select-none"
          />
        </div>
      )}

      {topCardText && (
        <div className="flex flex-col items-center w-full space-y-4">
          <p className={textStyle}>{topCardText}</p>
          <div className="w-1/3 border-b-2 border-dashed border-gray-200"></div>
        </div>
      )}
    </div>
  );
};
