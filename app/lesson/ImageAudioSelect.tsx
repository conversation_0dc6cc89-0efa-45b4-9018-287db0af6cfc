"use client"; // Assuming this is a client component

import { useEffect } from "react";
import { challengeOptions, challenges } from "@/db/schema";
import { Card } from "./card"; // Assuming card.tsx is in the same directory or adjust path
import { MainCard } from "./MainCard"; // Assuming MainCard.tsx is in the same directory or adjust path

type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (id: number | null, side: "left" | "right") => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  selectedPairOption?: number | null;
  disabled?: boolean;
  type: (typeof challenges.$inferSelect)["type"];
  question: { text: string; audioSrc?: string | null };
  mediaUrl?: string | null;
  mediaType?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
  sentence?: string | null;
};

export const ImageAudioSelectComponent = ({
  options,
  onSelect,
  status,
  selectedOption,
  selectedPairOption,
  disabled,
  type,
  question,
  mediaUrl,
  mediaType,
  topCardText,
  topCardAudio,
  sentence,
}: Props) => {
  const handleOptionClick = (id: number) => {
    onSelect(id, "left");
  };

  useEffect(() => {
    // console.log("ImageAudioSelectComponent Props:", { // Consider removing console.logs for production
    //   mediaUrl,
    //   mediaType,
    //   question,
    //   topCardText,
    //   topCardAudio,
    //   sentence,
    //   options,
    // });
  }, [
    mediaUrl,
    mediaType,
    question,
    topCardText,
    topCardAudio,
    sentence,
    options,
  ]);

  const hasAudio = !!(question.audioSrc || topCardAudio);

  return (
    <>
      {/* Version 1: Layout for when audio is present */}
      {hasAudio && (
        <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Main Card */}
          <div className="flex-shrink-0 w-full lg:w-1/2 lg:h-full flex lg:items-center">
            <MainCard
              imageSrc={
                mediaUrl?.startsWith("/")
                  ? mediaUrl
                  : mediaUrl
                  ? `/${mediaUrl}`
                  : undefined
              }
              audioSrc={question.audioSrc || topCardAudio || undefined}
              altText={topCardText || question.text}
              hasAudio={hasAudio}
              topCardText={topCardText}
              topCardAudio={topCardAudio}
              version="version1"
            />
          </div>
          {/* Option Cards */}
          <div className="grid gap-2 w-full lg:w-1/2 grid-cols-1 lg:content-center">
            {options.map((option) => (
              <Card
                key={option.id}
                id={option.id}
                imageSrc={option.imageSrc}
                audioSrc={option.audioSrc}
                text={option.text}
                selected={selectedOption === option.id}
                onClick={() => handleOptionClick(option.id)}
                disabled={disabled}
                status={status}
                type={type}
                shape="roundedRectangle"
                customClass="custom-card"
                version="version1"
              />
            ))}
          </div>
        </div>
      )}

      {/* Version 2: Layout for when there is no audio */}
      {!hasAudio && (
        <div className="flex flex-col items-center w-full mt-4 space-y-4">
          {/* Main Card Wrapper - Now wider using max-w-lg, height maintained */}
          <div className="flex-shrink-0 w-full max-w-lg h-72">
            <MainCard
              imageSrc={
                mediaUrl?.startsWith("/")
                  ? mediaUrl
                  : mediaUrl
                  ? `/${mediaUrl}`
                  : undefined
              }
              altText={topCardText || question.text}
              topCardText={topCardText}
              version="version3" // MainCard version 3 (smaller text)
            />
          </div>
          {/* Option Cards Wrapper - cards will wrap and the row itself will be centered */}
          <div className="flex flex-row flex-wrap justify-center gap-2">
            {options.map((option) => (
              <Card
                key={option.id}
                id={option.id}
                imageSrc={option.imageSrc}
                audioSrc={option.audioSrc}
                text={option.text}
                selected={selectedOption === option.id}
                onClick={() => handleOptionClick(option.id)}
                disabled={disabled}
                status={status}
                type={type} // This will be IMAGE_AUDIO_SELECT
                shape="roundedRectangle"
                customClass="custom-card"
                version="version2" // Card version for this layout
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
};
