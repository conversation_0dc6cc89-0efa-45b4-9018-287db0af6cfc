import { redirect } from "next/navigation";
import { get<PERSON><PERSON>on, getUserProgress, getUserSubscription } from "@/db/queries";
import { Quiz } from "../quiz";
import { auth } from "@clerk/nextjs/server";

// NEW: Define referrer information type
type ReferrerInfo = {
  from: "coursesplayer" | "learn" | "unknown";
  curriculumId?: string;
} | null;

type Props = {
  params: {
    lessonId: number;
  };
  searchParams?: {
    // NEW: Add searchParams to capture URL parameters
    from?: string;
    curriculumId?: string;
  };
};

const LessonIdPage = async ({ params, searchParams }: Props) => {
  const { userId } = await auth(); // Ensure async
  if (!userId) {
    redirect("/"); // Redirect if user is not authenticated
  }

  // NEW: Extract and validate referrer information from URL parameters
  const extractReferrerInfo = (): ReferrerInfo => {
    if (!searchParams) return null;

    const { from, curriculumId } = searchParams;

    // Validate the 'from' parameter
    if (!from) return null;

    switch (from) {
      case "coursesplayer":
        return {
          from: "coursesplayer",
          curriculumId: curriculumId || undefined, // Include curriculumId if present
        };
      case "learn":
        return {
          from: "learn",
        };
      default:
        // Invalid or unknown referrer
        return {
          from: "unknown",
        };
    }
  };

  const referrerInfo = extractReferrerInfo();

  const [lesson, userProgress, userSubscription] = await Promise.all([
    getLesson(params.lessonId),
    getUserProgress(),
    getUserSubscription(),
  ]);

  if (!lesson || !userProgress) {
    redirect("/learn");
  }

  const initialPercentage =
    (lesson.challenges.filter((challenge) => challenge.completed).length /
      lesson.challenges.length) *
    100;

  const transformedChallenges = lesson.challenges.map((challenge) => ({
    ...challenge,
    challengeOptions: challenge.challengeOptions.map((option) => ({
      ...option,
      side: option.side as "left" | "right",
    })),
  }));

  return (
    <Quiz
      initialLessonId={lesson.id}
      initialLessonChallenges={transformedChallenges}
      initialHearts={userProgress.hearts}
      initialPercentage={initialPercentage}
      userSubscription={userSubscription}
      referrerInfo={referrerInfo} // NEW: Pass referrer information to Quiz component
    />
  );
};

export default LessonIdPage;
