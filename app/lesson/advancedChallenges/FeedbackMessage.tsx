import React, { <PERSON> } from "react";
import { Volume2, Volume1, Flower2, ArrowR<PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";
import { Motion, AnimatePresence } from "@/components/motion-wrapper";

interface MismatchedGroup {
  letter: string;
  base: string;
  vowels: string[];
}

interface FeedbackMessageProps {
  score: number;
  mismatchedGroups: MismatchedGroup[];
  letterFeedbackMap: { [key: string]: string };
  studentPronunciation?: string;
  targetWord?: string;
}

const FeedbackMessage: FC<FeedbackMessageProps> = ({
  score,
  mismatchedGroups,
  letterFeedbackMap,
  studentPronunciation,
  targetWord,
}) => {
  const getFeedbackTone = (score: number) => {
    if (score < 40) {
      return {
        title: "Keep Going! You Can Do This!",
        message:
          "Learning Arabic pronunciation takes practice. Let's break it down step by step:",
        class:
          "border-amber-200 bg-gradient-to-br from-amber-50 to-amber-100/50",
        icon: <Volume1 className="h-6 w-6 text-amber-600" />,
      };
    } else if (score < 70) {
      return {
        title: "Good Progress!",
        message:
          "You're getting better! Let's focus on perfecting these sounds:",
        class: "border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100/50",
        icon: <Volume2 className="h-6 w-6 text-blue-600" />,
      };
    } else {
      return {
        title: "Excellent Work!",
        message: "You're almost perfect! Just a few tiny refinements:",
        class:
          "border-emerald-200 bg-gradient-to-br from-emerald-50 to-emerald-100/50",
        icon: <Flower2 className="h-6 w-6 text-emerald-600" />,
      };
    }
  };

  const tone = getFeedbackTone(score);

  return (
    <Motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full h-full"
    >
      <div
        className={cn(
          "relative",
          "w-full h-full",
          "p-4",
          tone.class,
          "rounded-3xl",
          "border-2",
          "shadow-lg",
          "transition-all",
          "duration-300",
          "hover:shadow-xl",
          "bg-white/50",
          "backdrop-blur-sm",
          "flex flex-col"
        )}
      >
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto space-y-4 pr-2 custom-scrollbar">
          {/* Word Comparison */}
          {(studentPronunciation || targetWord) && (
            <Motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {targetWord && (
                  <div>
                    <span className="text-xs font-medium text-gray-500">
                      Target Word
                    </span>
                    <p className="text-2xl font-arabic text-gray-900 text-right mt-1">
                      {targetWord}
                    </p>
                  </div>
                )}
                {studentPronunciation && (
                  <div>
                    <span className="text-xs font-medium text-gray-500">
                      Your Pronunciation
                    </span>
                    <p className="text-2xl font-arabic text-gray-600 text-right mt-1">
                      {studentPronunciation}
                    </p>
                  </div>
                )}
              </div>
            </Motion.div>
          )}

          {/* Detailed Feedback */}
          <div className="space-y-3">
            <AnimatePresence>
              {mismatchedGroups.map((group, index) => (
                <Motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 bg-gray-50 p-3 rounded-xl">
                      <span className="inline-block text-3xl font-arabic text-gray-800">
                        {group.letter}
                      </span>
                    </div>

                    <div className="flex-grow space-y-3">
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700">
                          {letterFeedbackMap[group.base]}
                        </span>
                      </div>

                      {group.vowels.length > 0 && (
                        <div className="pl-4 border-l-2 border-gray-200">
                          <div className="flex items-center gap-2 flex-wrap">
                            <span className="text-xs text-gray-600">
                              Vowel marks:
                            </span>
                            <div className="flex gap-1">
                              {group.vowels.map((vowel) => (
                                <span
                                  key={vowel}
                                  className="px-2 py-1 bg-gray-50 rounded-lg text-lg font-arabic"
                                >
                                  {vowel}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </Motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      </div>

      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.1);
          border-radius: 20px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background-color: rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </Motion.div>
  );
};

export default FeedbackMessage;
