// arabicPronunciationUtils.ts

import {
  removeDiacritics,
  expandDiacriticsToLetters,
  getBaseLetters,
  isValidEndingVariation,
  isSpecialCompound,
  handleSpecialCompound,
  SHADDA,
  FATHA,
  FATHATAN,
  KASRATAN,
  DHAMMATAN,
  KASRA,
  DAMMA,
  commonTranscriptionConfusions,
  phoneticGroups,
  commonMispronunciations,
  vowelLengthPatterns,
  phoneticEnvironments,
  wordSegmentPatterns,
  shaddaConfusions,
} from "./vowelUtils";

export function identifyPhoneticFeatures(word: string): Set<string> {
  const features = new Set<string>();
  if (phoneticEnvironments.emphatic.test(word)) {
    features.add("emphatic");
  }
  if (phoneticEnvironments.guttural.test(word)) {
    features.add("guttural");
  }
  if (phoneticEnvironments.geminate.test(word)) {
    features.add("geminate");
  }
  if (phoneticEnvironments.glottalStop.test(word)) {
    features.add("glottalStop");
  }
  return features;
}

export function getLcsPercentage(
  expandedExpected: string,
  expandedActual: string
): number {
  if (!expandedExpected || !expandedActual) return 0;
  if (expandedExpected.length <= 2) {
    const baseExpected = removeDiacritics(expandedExpected);
    const baseActual = removeDiacritics(expandedActual);
    if (baseActual.includes(baseExpected[0])) {
      return 100;
    }
    const confusionList = commonTranscriptionConfusions[baseExpected[0]] || [];
    for (const confusedChar of confusionList) {
      if (baseActual.includes(confusedChar)) {
        return 85;
      }
    }
  }
  const lcs = getLcsLength(expandedExpected, expandedActual);
  return (lcs / expandedExpected.length) * 100;
}

export function isCommonTranscriptionConfusion(
  expected: string,
  actual: string
): boolean {
  const confusionList = commonTranscriptionConfusions[expected] || [];
  return confusionList.includes(actual);
}

export function getDetailedPhoneticFeedback(
  expected: string,
  actual: string
): string[] {
  const feedback: string[] = [];
  const expectedChars = [...expected];
  const actualChars = [...actual];
  expectedChars.forEach((expectedChar, index) => {
    if (index >= actualChars.length || expectedChar !== actualChars[index]) {
      const letterFeedback = getCharacterSpecificFeedback(expectedChar);
      if (letterFeedback) {
        feedback.push(`${expectedChar}: ${letterFeedback}`);
      }
      if (index < actualChars.length) {
        const actualChar = actualChars[index];
        const groups = Object.entries(phoneticGroups)
          .filter(
            ([, chars]) =>
              chars.includes(expectedChar) || chars.includes(actualChar)
          )
          .map(([name]) => name);
        if (groups.length > 0) {
          feedback.push(
            `Note: ${expectedChar} and ${actualChar} are both ${groups.join(
              "/"
            )} sounds, but have distinct pronunciations.`
          );
        }
      }
    }
  });
  return feedback;
}

function compareLetterPositionsWithGaps(
  expected: string,
  actual: string
): number {
  const expectedChars = [...expected];
  const actualChars = [...actual];
  const n = expectedChars.length;
  const m = actualChars.length;
  const dp: number[][] = Array.from({ length: n + 1 }, () =>
    Array(m + 1).fill(0)
  );
  const matchScore = 2;
  const mismatchScore = -1;
  const defaultGapPenalty = 0;
  const getGapPenalty = (i: number): number => {
    return 0;
  };
  for (let i = 1; i <= n; i++) {
    dp[i][0] = dp[i - 1][0] + getGapPenalty(i);
  }
  for (let j = 1; j <= m; j++) {
    dp[0][j] = dp[0][j - 1] + defaultGapPenalty;
  }
  for (let i = 1; i <= n; i++) {
    for (let j = 1; j <= m; j++) {
      const charE = expectedChars[i - 1];
      const charA = actualChars[j - 1];
      let currentMatchScore = charE === charA ? matchScore : mismatchScore;
      if (charE !== charA && charE in commonTranscriptionConfusions) {
        if (commonTranscriptionConfusions[charE].includes(charA)) {
          currentMatchScore = matchScore * 0.8;
        } else {
          for (const group of Object.values(phoneticGroups)) {
            if (group.includes(charE) && group.includes(charA)) {
              currentMatchScore = matchScore * 0.7;
              break;
            }
          }
        }
      }
      const scoreMatch = dp[i - 1][j - 1] + currentMatchScore;
      const scoreGapE = dp[i - 1][j] + getGapPenalty(i);
      const scoreGapA = dp[i][j - 1] + defaultGapPenalty;
      dp[i][j] = Math.max(scoreMatch, scoreGapE, scoreGapA);
    }
  }
  const rawScore = dp[n][m];
  const maxPossible = Math.max(n, m) * matchScore;
  const normalizedPercentage =
    ((rawScore - defaultGapPenalty * Math.abs(n - m)) / maxPossible) * 100;
  return Math.max(0, Math.min(100, normalizedPercentage));
}

function compareLetterPositions(expected: string, actual: string): number {
  const expectedChars = [...expected];
  let score = 0;
  let totalWeight = 0;
  const getPositionWeight = (index: number, length: number): number => {
    if (index === 0) return 1.5;
    if (index === length - 1) return 1.2;
    return 1.0;
  };
  const findBestMatch = (
    expectedChar: string,
    actualString: string,
    startIdx: number
  ): number => {
    for (let i = startIdx; i < actualString.length; i++) {
      if (expectedChar === actualString[i]) return 1;
      const confusionList = commonTranscriptionConfusions[expectedChar] || [];
      if (confusionList.includes(actualString[i])) return 0.8;
    }
    return 0;
  };
  expectedChars.forEach((expectedChar, index) => {
    const weight = getPositionWeight(index, expectedChars.length);
    totalWeight += weight;
    const matchScore = findBestMatch(expectedChar, actual, index);
    score += weight * matchScore;
  });
  return (score / totalWeight) * 100;
}

export function calculateCompositeScore(
  expected: string,
  actual: string
): number {
  // First check for ending variations with high base score
  if (
    expected.length === actual.length ||
    Math.abs(expected.length - actual.length) <= 2
  ) {
    const expectedBase = removeDiacritics(expected.slice(0, -2));
    const actualBase = removeDiacritics(actual.slice(0, -2));

    if (
      expectedBase === actualBase &&
      isValidEndingVariation(expected, actual)
    ) {
      return 95;
    }
  }

  // Special handling for words with both shadda and tanween
  if (
    expected.includes(SHADDA) &&
    (expected.includes(FATHATAN) ||
      expected.includes(KASRATAN) ||
      expected.includes(DHAMMATAN))
  ) {
    const baseExpected = removeDiacritics(expected.replace(SHADDA, ""));
    let baseActual = actual
      .replace(/(.)\1+/g, "$1")
      .replace(/ان$/, "")
      .replace(/ين$/, "")
      .replace(/ون$/, "");

    let matchCount = 0;
    const totalChars = Math.max(baseExpected.length, baseActual.length);

    for (let i = 0; i < baseExpected.length; i++) {
      const expectedChar = baseExpected[i];
      const actualChar = baseActual[i];

      if (expectedChar === actualChar) {
        matchCount += 1;
      } else if (
        phoneticGroups.gutturals.includes(expectedChar) &&
        phoneticGroups.gutturals.includes(actualChar)
      ) {
        matchCount += 0.9;
      } else if (isValidEndingVariation(expectedChar, actualChar)) {
        matchCount += 0.95;
      }
    }

    const baseScore = (matchCount / totalChars) * 100;
    return Math.min(baseScore + 10, 100); // Add bonus but cap at 100
  }

  // Calculate base scores with adjusted weights
  const lcsScoreRaw = getLcsPercentage(expected, actual);
  const positionScoreRaw = compareLetterPositions(expected, actual);
  const gapScoreRaw = compareLetterPositionsWithGaps(expected, actual);

  // Adjust weights to be more lenient
  const compositeRaw =
    lcsScoreRaw * 0.7 + positionScoreRaw * 0.2 + gapScoreRaw * 0.1;

  // Handle expanded forms with same adjustments
  const expandedExpected = expandDiacriticsToLetters(expected);
  const lcsScoreExpanded = getLcsPercentage(expandedExpected, actual);
  const positionScoreExpanded = compareLetterPositions(
    expandedExpected,
    actual
  );
  const gapScoreExpanded = compareLetterPositionsWithGaps(
    expandedExpected,
    actual
  );

  const compositeExpanded =
    lcsScoreExpanded * 0.7 +
    positionScoreExpanded * 0.2 +
    gapScoreExpanded * 0.1;

  // Add bonus for known variations
  let finalScore = Math.max(compositeRaw, compositeExpanded);

  // Add bonus score for legitimate variations
  if (isValidEndingVariation(expected, actual)) {
    finalScore = Math.min(finalScore + 15, 100);
  }

  // Add bonus for special compounds
  if (isSpecialCompound(expected)) {
    const compoundScore = handleSpecialCompound(expected, actual);
    if (compoundScore > finalScore) {
      finalScore = compoundScore;
    }
  }

  return finalScore;
}

export function handleVowelToLetterConversion(
  expected: string,
  actual: string
): {
  isMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  const transformations: string[] = [];
  let baseConfidence = 0;
  const expectedChars = [...expected];
  const actualChars = [...actual];
  for (let i = 0; i < expectedChars.length; i++) {
    const expectedChar = expectedChars[i];
    const nextActual = i < actualChars.length ? actualChars[i] : "";
    if (expectedChar === KASRA && nextActual === "ي") {
      transformations.push("KASRA represented as ي by transcription");
      baseConfidence += 35;
    } else if (expectedChar === FATHA && nextActual === "ا") {
      transformations.push("FATHA represented as ا by transcription");
      baseConfidence += 35;
    } else if (expectedChar === DAMMA && nextActual === "و") {
      transformations.push("DAMMA represented as و by transcription");
      baseConfidence += 35;
    }
  }
  if (expected.endsWith(KASRA) && actual.endsWith("ي")) {
    baseConfidence += 15;
  }
  return {
    isMatch: baseConfidence >= 50,
    confidence: Math.min(baseConfidence, 100),
    transformations,
  };
}

export function handlePhoneticEnvironment(
  expected: string,
  actual: string
): {
  isPhoneticMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  const transformations: string[] = [];
  let baseConfidence = 0;
  if (phoneticEnvironments.emphatic.test(expected)) {
    const expectedEmphatic = expected.match(phoneticEnvironments.emphatic)![0];
    const actualEmphatic = actual.match(phoneticEnvironments.emphatic)?.[0];
    if (actualEmphatic) {
      transformations.push(
        `Emphatic consonant environment: ${expectedEmphatic} → ${actualEmphatic}`
      );
      baseConfidence += 30;
    }
  }
  if (phoneticEnvironments.guttural.test(expected)) {
    const expectedGuttural = expected.match(phoneticEnvironments.guttural)![0];
    const actualGuttural = actual.match(phoneticEnvironments.guttural)?.[0];
    if (actualGuttural) {
      transformations.push(
        `Guttural consonant environment: ${expectedGuttural} → ${actualGuttural}`
      );
      baseConfidence += 25;
    }
  }
  if (phoneticEnvironments.geminate.test(expected)) {
    const hasActualGemination = phoneticEnvironments.geminate.test(actual);
    if (hasActualGemination) {
      transformations.push("Correct gemination detected");
      baseConfidence += 35;
    }
  }
  return {
    isPhoneticMatch: baseConfidence >= 50,
    confidence: Math.min(baseConfidence, 100),
    transformations,
  };
}

export function handleComplexShaddaCase(
  expected: string,
  actual: string
): {
  isShaddaMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  const transformations: string[] = [];
  let baseConfidence = 0;
  const stripDiacritics = (
    text: string,
    keepShadda: boolean = true
  ): string => {
    const pattern = /[\u064B-\u0652]/g;
    return text.replace(pattern, "");
  };
  const expectedBase = stripDiacritics(expected, true);
  const actualBase = stripDiacritics(actual, false);
  const firstExpectedLetter: string | undefined = expectedBase[0];
  const firstActualLetter: string | undefined = actualBase[0];
  if (
    firstExpectedLetter &&
    firstActualLetter &&
    firstExpectedLetter !== firstActualLetter
  ) {
    const confusionList: string[] =
      commonTranscriptionConfusions[firstExpectedLetter] ?? [];
    if (confusionList.includes(firstActualLetter)) {
      transformations.push(
        `Initial letter confusion: ${firstExpectedLetter} → ${firstActualLetter}`
      );
      baseConfidence += 25;
    }
  } else {
    baseConfidence += 30;
  }
  const shaddaIndex = expectedBase.indexOf(SHADDA);
  if (shaddaIndex === -1) {
    return { isShaddaMatch: false, confidence: 0, transformations };
  }
  const letterWithShadda = expectedBase[shaddaIndex - 1];
  const tanweenPatterns: string[] = ["ان", "ين", "ون", "ًا", "ٍ", "ٌ"];
  const hasTanweenEnding = tanweenPatterns.some((pattern) =>
    actualBase.endsWith(pattern)
  );
  if (hasTanweenEnding) {
    transformations.push(`Shadda interpreted as tanween ending`);
    baseConfidence += 25;
  }
  const letterDoubled = letterWithShadda + letterWithShadda;
  if (actualBase.includes(letterDoubled)) {
    transformations.push(`Shadda realized as doubled letter: ${letterDoubled}`);
    baseConfidence += 30;
  }
  const letterPlusNun = letterWithShadda + "ن";
  if (actualBase.includes(letterPlusNun)) {
    transformations.push(`Shadda realized as letter + ن: ${letterPlusNun}`);
    baseConfidence += 25;
  }
  const shaddaKey: string = letterWithShadda + SHADDA;
  const confusionPatterns = shaddaConfusions[shaddaKey] || [];
  if (confusionPatterns.length > 0) {
    for (const pattern of confusionPatterns) {
      if (actualBase.includes(pattern)) {
        transformations.push(
          `Matched known shadda confusion pattern: ${pattern}`
        );
        baseConfidence += 20;
        break;
      }
    }
  }
  const isMatch = baseConfidence >= 50;
  const finalConfidence = Math.min(Math.max(baseConfidence, 0), 100);
  return {
    isShaddaMatch: isMatch,
    confidence: finalConfidence,
    transformations,
  };
}

export function handleTanweenCase(
  expected: string,
  actual: string
): {
  isTanweenMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  const transformations: string[] = [];
  let baseConfidence = 0;
  // Handle dhammatan to ون conversion explicitly
  if (expected.includes("ٌ") && actual.endsWith("ون")) {
    return {
      isTanweenMatch: true,
      confidence: 95,
      transformations: ["Dhammatan correctly realized as ون"],
    };
  }
  const expectedBase = removeDiacritics(expected);
  const actualBase = removeDiacritics(actual);
  if (expectedBase === actualBase) {
    baseConfidence += 50;
    transformations.push("Base letters match exactly");
  }
  // Enhanced tanween ending check
  if (actual.endsWith("ون") || actual.endsWith("ين") || actual.endsWith("ان")) {
    transformations.push("Correct tanween ending form detected");
    baseConfidence += 45;
  }
  // Enhanced matching for short words
  if (expected.length <= 4 && actual.length <= 5) {
    baseConfidence += 20;
  }
  return {
    isTanweenMatch: baseConfidence >= 50,
    confidence: Math.min(baseConfidence, 100),
    transformations,
  };
}

export function handleCompoundWordSegmentsLegacy(
  expected: string,
  actual: string
): {
  isMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  for (const [word, pattern] of Object.entries({}) as Array<
    [string, { segments: string[]; variations: Record<string, string[]> }]
  >) {
    if (expected.includes(word) || actual.includes(word)) {
      const transformations: string[] = [];
      let segmentMatches = 0;
      let totalSegments = pattern.segments.length;
      pattern.segments.forEach((segment: string) => {
        const variations = pattern.variations[segment] || [segment];
        const matched = variations.some((v: string) => actual.includes(v));
        if (matched) {
          segmentMatches++;
          transformations.push(`Matched segment ${segment}`);
        }
      });
      const confidence = (segmentMatches / totalSegments) * 100;
      return { isMatch: confidence >= 70, confidence, transformations };
    }
  }
  return { isMatch: false, confidence: 0, transformations: [] };
}

export function handleCompoundWordSegments(
  expected: string,
  actual: string
): {
  isMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  for (const [word, pattern] of Object.entries(wordSegmentPatterns) as Array<
    [string, { segments: string[]; variations: Record<string, string[]> }]
  >) {
    if (expected.includes(word) || actual.includes(word)) {
      const transformations: string[] = [];
      let segmentMatches = 0;
      const totalSegments = pattern.segments.length;
      pattern.segments.forEach((segment: string) => {
        const variations: string[] = pattern.variations[segment] || [segment];
        const matched = variations.some((v: string) => actual.includes(v));
        if (matched) {
          segmentMatches++;
          transformations.push(`Matched segment "${segment}"`);
        }
      });
      const confidence = (segmentMatches / totalSegments) * 100;
      return { isMatch: confidence >= 70, confidence, transformations };
    }
  }
  return { isMatch: false, confidence: 0, transformations: [] };
}

export function compareVowelLengths(
  expected: string,
  actual: string
): {
  isLengthMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  const transformations: string[] = [];
  let baseConfidence = 0;
  vowelLengthPatterns.short.forEach((pattern) => {
    const expectedMatches = expected.match(pattern) || [];
    const actualMatches = actual.match(pattern) || [];
    if (expectedMatches.length === actualMatches.length) {
      transformations.push(
        `Correct short vowel count: ${expectedMatches.length}`
      );
      baseConfidence += 20;
    }
  });
  vowelLengthPatterns.long.forEach((pattern) => {
    const expectedMatches = expected.match(pattern) || [];
    const actualMatches = actual.match(pattern) || [];
    if (expectedMatches.length === actualMatches.length) {
      transformations.push(
        `Correct long vowel count: ${expectedMatches.length}`
      );
      baseConfidence += 25;
    }
  });
  return {
    isLengthMatch: baseConfidence >= 50,
    confidence: Math.min(baseConfidence, 100),
    transformations,
  };
}

export function handleDialectalVariations(
  expected: string,
  actual: string
): {
  isDialectalMatch: boolean;
  confidence: number;
  transformations: string[];
} {
  const transformations: string[] = [];
  let baseConfidence = 0;
  for (const [standard, variations] of Object.entries(
    commonMispronunciations
  )) {
    if (expected.includes(standard)) {
      const matchedVariation = variations.find((v) => actual.includes(v));
      if (matchedVariation) {
        transformations.push(
          `Dialectal variation: ${standard} → ${matchedVariation}`
        );
        baseConfidence += 30;
      }
    }
  }
  if (transformations.length > 1) {
    baseConfidence += 10;
  }
  return {
    isDialectalMatch: baseConfidence >= 50,
    confidence: Math.min(baseConfidence, 100),
    transformations,
  };
}

export function isOneLetterSpecialCase(
  expected: string,
  actual: string
): boolean {
  const baseExpected = getBaseLetters(expected, false);
  if (baseExpected.length === 1) {
    const baseActual = getBaseLetters(actual, false);
    return baseActual === baseExpected || baseActual === baseExpected + "ا";
  }
  return false;
}

export function getDetailedPhoneticFeedbackLegacy(
  expected: string,
  actual: string
): string[] {
  const feedback: string[] = [];
  const expectedChars = [...expected];
  const actualChars = [...actual];
  expectedChars.forEach((expectedChar, index) => {
    if (index >= actualChars.length || expectedChar !== actualChars[index]) {
      const letterFeedback = getCharacterSpecificFeedback(expectedChar);
      if (letterFeedback) {
        feedback.push(`${expectedChar}: ${letterFeedback}`);
      }
      if (index < actualChars.length) {
        const actualChar = actualChars[index];
        const groups = Object.entries(phoneticGroups)
          .filter(
            ([, chars]) =>
              chars.includes(expectedChar) || chars.includes(actualChar)
          )
          .map(([name]) => name);
        if (groups.length > 0) {
          feedback.push(
            `Note: ${expectedChar} and ${actualChar} are both ${groups.join(
              "/"
            )} sounds, but have distinct pronunciations.`
          );
        }
      }
    }
  });
  return feedback;
}

export function getCharacterSpecificFeedback(char: string): string {
  const feedbackMap: { [key: string]: string } = {
    ع: "Make a deep throat sound, similar to the 'a' in 'father' but from deeper in the throat",
    ح: "Make a breathy 'h' sound, like when you fog up a mirror",
    خ: "Like clearing your throat gently",
    ق: "Make a 'k' sound but from deeper in your throat",
    ط: "Like 't' but with your tongue touching the roof of your mouth",
    ض: "Like 'd' but with your tongue touching the roof of your mouth",
    ص: "Like 's' but with your tongue slightly raised",
    ظ: "Like 'th' in 'this' but with your tongue slightly raised",
  };
  return (
    feedbackMap[char] || `Focus on pronouncing the letter ${char} correctly`
  );
}

function getLcsLength(a: string, b: string): number {
  const dp: number[][] = Array(a.length + 1)
    .fill(null)
    .map(() => Array(b.length + 1).fill(0));
  for (let i = 1; i <= a.length; i++) {
    for (let j = 1; j <= b.length; j++) {
      if (a[i - 1] === b[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
      } else {
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
      }
    }
  }
  return dp[a.length][b.length];
}

// ----------------------------------------------------------------------
// UPDATED getPronunciationFeedback using simplified letter matching
// ----------------------------------------------------------------------
export function getPronunciationFeedback(
  expected: string,
  actual: string
): {
  accuracy: number;
  feedback: string;
  threshold: number;
  detailedFeedback?: string[];
  transcriptionNote?: string;
} {
  // Special rule for ظٍ
  if (
    expected.trim() === "ظٍ" &&
    ["دين", "ذين", "زين", "ضين", "ظين", "دن", "ذن", "ظن"].includes(
      actual.trim()
    )
  ) {
    return {
      accuracy: 100,
      feedback: "Excellent match based on special rule for ظٍ pronunciation.",
      threshold: 65,
    };
  }

  // Special rule for صٌ
  if (
    expected.trim() === "صٌ" &&
    ["سون", "صون", "سو", "صو", "سن", "صن"].includes(actual.trim())
  ) {
    return {
      accuracy: 100,
      feedback: "Excellent match based on special rule for صٌ pronunciation.",
      threshold: 65,
    };
  }

  // Special rule for غُ with قُو pronunciation
  if (
    expected.trim() === "غُ" &&
    ["قُو", "قو", "غو", "غُو"].includes(actual.trim())
  ) {
    return {
      accuracy: 100,
      feedback: "Excellent match based on special rule for غُ pronunciation.",
      threshold: 65,
    };
  }

  // Special rule: treat "غُ" as equal to "قم", "قي", or "قو"
  if (expected.trim() === "غُ" && ["قم", "قي", "قو"].includes(actual.trim())) {
    return {
      accuracy: 100,
      feedback:
        "Excellent match based on special rule: 'غُ' equals 'قم', 'قي', or 'قو'.",
      threshold: 65,
    };
  }

  // Special rule: treat "ذَ" as equal to "فا"
  if (expected.trim() === "ذَ" && ["فع", "فا", "ثا"].includes(actual.trim())) {
    return {
      accuracy: 100,
      feedback: "Excellent match based on special rule: 'ذَ' equals 'فا'.",
      threshold: 65,
    };
  }

  // For simplified scoring we now remove diacritics (i.e., use the base letters)
  // so that "أَبًّ" becomes its base "أب". We then loop through each letter
  // and check if it appears anywhere in the transcribed (base) word.
  const normalizedExpected = removeDiacritics(expected.trim());
  const normalizedActual = removeDiacritics(actual.trim());
  const expandedActual = expandDiacriticsToLetters(normalizedActual);

  let matchCount = 0;
  for (let i = 0; i < normalizedExpected.length; i++) {
    const letter = normalizedExpected[i];
    // Special rule: for "أ", allow either "أ" or the two-letter form "عا"
    if (letter === "أ") {
      if (
        normalizedActual.includes("أ") ||
        normalizedActual.includes("عا") ||
        expandedActual.includes("أ") ||
        expandedActual.includes("عا")
      ) {
        matchCount++;
        continue;
      }
    }
    if (normalizedActual.includes(letter) || expandedActual.includes(letter)) {
      matchCount++;
    }
  }
  const accuracy = Math.round((matchCount / normalizedExpected.length) * 100);
  const threshold = 65;
  if (accuracy >= threshold) {
    return {
      accuracy,
      feedback:
        "Excellent! Your pronunciation is correct based on simple letter matching.",
      threshold,
    };
  } else {
    return {
      accuracy,
      feedback: "Mismatch based on simple letter matching. Please try again.",
      threshold,
    };
  }
}
