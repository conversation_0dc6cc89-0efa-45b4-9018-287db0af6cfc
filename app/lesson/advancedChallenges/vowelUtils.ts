// vowelUtils.ts
//
// ==========================
// Constants
// ==========================
export const TAA_MARBOUTA: string = "\u0629";
export const ALIF_MAQSURA: string = "\u0649";
export const HAMZA: string = "\u0621";
export const HAMZA_ON_ALIF: string = "\u0623";
export const HAMZA_UNDER_ALIF: string = "\u0625";
export const ALIF_MADDA: string = "\u0622";
export const HAMZA_ON_WAW: string = "\u0624";
export const HAMZA_ON_YAA: string = "\u0626";
export const ALIF: string = "\u0627";
export const YAA: string = "\u064A";
export const WAW: string = "\u0648";

export const FATHA: string = "\u064E";
export const DAMMA: string = "\u064F";
export const KASRA: string = "\u0650";
export const FATHATAN: string = "\u064B";
export const DHAMMATAN: string = "\u064C";
export const KASRATAN: string = "\u064D";
export const SHADDA: string = "\u0651";
export const SUKOON: string = "\u0652";

//
// ==========================
// Additional Missing Definitions
// ==========================
export const shaddaConfusions: Record<string, string[]> = {
  زّ: ["ز", "زز"],
  لّ: ["ل", "لل"],
};

export const singleLetterTanweenPatterns: Record<string, string[]> = {
  ة: ["ت", "ه", "ات"],
  ا: ["ى", "ي"],
};

// Enhanced tanween confusions
export const tanweenConfusions: Record<string, string[]> = {
  "ٌ": ["ون", "و", "ُن", "ن"], // Dhammatan variations
  "ٍ": ["ين", "ي", "ِن", "ن"], // Kasratan variations
  "ً": ["ان", "ا", "َن", "ن"], // Fathatan variations
  "ّ": ["\u0651", "", "$1$1"], // Shadda variations (including letter doubling)
  ة: ["ت", "ه", "ات"], // Taa marbouta variations
  وة: ["ات", "اة", "وت"], // Common ending variations
};

export const wordSegmentPatterns: Record<
  string,
  { segments: string[]; variations: Record<string, string[]> }
> = {
  والزكوة: {
    segments: ["و", "ال", "زك", "وة"],
    variations: {
      و: ["و"],
      ال: ["ال"],
      زك: ["زك", "زاك"],
      وة: ["وة", "ات", "اة"],
    },
  },
};

//
// ==========================
// Dictionaries / Maps
// ==========================
export const commonTranscriptionConfusions: { [key: string]: string[] } = {
  // Group 1: Alif vs. Ayn confusion (ا vs. ع)
  أ: ["ع", "ء", "ا", "إ", "آ"],
  ع: ["أ", "ء", "ا", "إ", "آ"],
  ا: ["ع", "أ", "إ", "آ"],
  إ: ["ع", "أ", "ا", "آ"],
  آ: ["ع", "أ", "ا", "إ"],
  // Group 2: Regular Taa vs. Emphatic Taa (ت vs. ط)
  ت: ["ط", "ة"],
  ط: ["ت"],
  ة: ["ت", "ه", "ات"],
  // Group 3: Thaa vs. Seen/Sad group (ث vs. س/ص)
  ث: ["س", "ص"],
  س: ["ث", "ص", "ز"],
  ص: ["س", "ث"],
  // Group 4: Regular Haa vs. Emphatic Haa (ح vs. ه)
  ح: ["ه", "خ"],
  ه: ["ح", "خ"],
  خ: ["ح", "ه"],
  // Additional Common Confusions with Enhanced ظ/ذ Relationships
  ذ: ["ز", "ظ", "د", "ض"], // Enhanced with ظ and ض as acceptable variants
  ض: ["د", "ظ", "ذ"], // Added ذ as acceptable variant
  ظ: ["ز", "ذ", "ض", "د"], // Enhanced with ذ as acceptable variant
  // Remaining gutturals
  ق: ["غ", "ك", "ء"],
  ك: ["ق"],
  غ: ["ق", "خ", "ع"],
  // New enhanced variations for وة endings
  وة: ["ات", "اة", "وت"],
  ات: ["وة", "اة"],
  اة: ["وة", "ات"],
};

export const phoneticGroups: { [key: string]: string[] } = {
  gutturals: ["ع", "ح", "خ", "غ", "ق"],
  emphatics: ["ص", "ض", "ط", "ظ", "ذ"], // Added ذ to emphatics group
  sibilants: ["س", "ش", "ص", "ز"],
  dentals: ["ث", "ذ", "ظ"],
  stops: ["ب", "ت", "د", "ط", "ك", "ق"],
  endings: ["ة", "ت", "ات", "وة"], // New group for endings
};

// This function needs to be modified in vowelUtils.ts
// Add this custom mapping for ظٍ specifically:
export const customPronunciationMappings: Record<string, string[]> = {
  ظٍ: ["دين", "ذين", "زين", "ضين", "ظين", "دن", "ذن", "ظن"],
};

export const letterFeedbackMap: { [key: string]: string } = {
  ا: "Long vowel sound like 'a' in 'father'",
  ع: "Guttural sound from the back of the throat",
  ت: "Light 't' sound like in 'tin'",
  ط: "Emphatic 't' sound with the tongue against the palate",
  ث: "Like 'th' in 'think'",
  س: "Like 's' in 'see'",
  ص: "Emphatic 's' sound from the back of the throat",
  ح: "Breathy 'h' sound from the throat",
  ه: "Light 'h' sound like in 'help'",
  خ: "Rough 'kh' sound like in Scottish 'loch'",
  ذ: "Like 'th' in 'this', can vary to sound like 'ظ' in some dialects",
  ض: "Emphatic 'd' sound with the tongue against the palate",
  ظ: "Emphatic 'th' sound with the tongue against the palate, can be similar to 'ذ'",
  ق: "Deep 'k' sound from the back of the throat",
  ك: "Light 'k' sound like in 'kiss'",
  ة: "Soft 't' or 'h' sound at word end",
  و: "Like 'w' in 'way'",
};

export const dialectalVariations: { [key: string]: string[] } = {
  ق: ["ق", "ج", "ك", "ء"],
  ث: ["ث", "ت", "س"],
  ذ: ["ذ", "د", "ز", "ظ"], // Added ظ as acceptable variant
  ض: ["ض", "د", "ظ", "ذ"], // Added ذ as acceptable variant
  ظ: ["ظ", "ذ", "ض", "ز", "د"], // Enhanced acceptable variants
  وة: ["ات", "اة"], // New dialectal variation
};

export const specialCompounds: { [key: string]: string[] } = {
  الله: ["اللَّه", "اللّه", "الله"],
  بسم: ["بِسْم", "بسم"],
  سلام: ["سَلام", "سلام"],
  زكوة: ["زكاة", "زكات"], // New special compound
};

export const commonMispronunciations: { [key: string]: string[] } = {
  ق: ["ج", "غ", "ك"],
  ث: ["س", "ت"],
  ذ: ["د", "ز", "ظ", "ض"], // Enhanced with ظ/ض as variants
  ظ: ["ض", "ز", "ذ", "د"], // Enhanced with ذ as variant
  ال: ["أل", "الأ", "لأ"],
  عا: ["أع", "عأ"],
  لعا: ["لأع", "لعأ"],
  ع: ["أ", "ا", "ء"],
  ح: ["ه", "خ"],
  ض: ["د", "ظ", "ذ"], // Added ذ as variant
  وة: ["ات", "اة"], // New common mispronunciation
};

export const vowelLengthPatterns: { [key: string]: RegExp[] } = {
  short: [/َ(?!ا)/, /ِ(?!ي)/, /ُ(?!و)/],
  diphthongs: [/َو(?!ّ)/, /َي(?!ّ)/, /ْو/, /ْي/],
  wordSpecific: [/العال[مي]/, /الأعل[مي]/, /لعال[مي]/],
  long: [/َا/, /ِي/, /ُو/],
  endings: [/وة$/, /ات$/, /اة$/], // New pattern for endings
};

export const commonEndingPatterns: { [key: string]: string[] } = {
  "ِ": ["ي", "ِي", ""],
  يْ: ["ي", "ِي", "ِ"],
  مِ: ["مي", "م"],
  وة: ["ات", "اة"], // New ending pattern
};

export const phoneticEnvironments = {
  emphatic: /[صضطظ][َُِ]?[اوي]?/,
  guttural: /[حخعغق][َُِ]?[اوي]?/,
  geminate: /(.)\u0651\1/,
  glottalStop: /[ءأإؤئ][َُِ]?[اوي]?/,
  endings: /(وة|ات|اة)$/, // New environment for endings
};

//
// ==========================
// Additional New Constants for Enhancements
// ==========================
export const vowelToLetterMap: Record<string, string> = {
  [FATHA]: "ا",
  [KASRA]: "ي",
  [DAMMA]: "و",
  [FATHATAN]: "ان",
  [KASRATAN]: "ين",
  [DHAMMATAN]: "ون",
  [SHADDA]: "$1$1",
  [SUKOON]: "",
};

export const compositeCharacterPatterns: { [key: string]: RegExp } = {
  shadda_fatha: new RegExp(`${SHADDA}${FATHA}`),
  shadda_kasra: new RegExp(`${SHADDA}${KASRA}`),
  shadda_damma: new RegExp(`${SHADDA}${DAMMA}`),
  shadda_tanween: new RegExp(`${SHADDA}[${FATHATAN}${KASRATAN}${DHAMMATAN}]`),
};

// New constant for ending variations
export const endingVariations: { [key: string]: string[] } = {
  وة: ["ات", "اة", "وت"],
  ات: ["وة", "اة"],
  اة: ["وة", "ات"],
};

//
// ==========================
// Normalization & Transformation Functions
// (Consolidated for reuse across the application)
// ==========================
export function removeDiacritics(text: string): string {
  return text.replace(/[\u064B-\u0652]/g, "");
}

export function normalizeLongVowel(text: string): string {
  return text.replace(/اا+/g, "ا").replace(/يي+/g, "ي").replace(/وو+/g, "و");
}

export function normalizeArabicEndings(text: string): string {
  let normalized = text;
  Object.entries(endingVariations).forEach(([ending, variations]) => {
    variations.forEach((variant) => {
      if (normalized.endsWith(variant)) {
        normalized = normalized.slice(0, -variant.length) + ending;
      }
    });
  });
  return normalized;
}

function processVowel(
  char: string,
  diacritic: string,
  hasShaddah: boolean = false
): string {
  switch (diacritic) {
    case FATHA:
      return char + "ا";
    case KASRA:
      return char + "ي";
    case DAMMA:
      return char + "و";
    case FATHATAN:
      return char + "ان";
    case KASRATAN:
      return char + "ين";
    case DHAMMATAN:
      return char + "ون";
    case SUKOON:
      return char;
    default:
      return char;
  }
}

function unifyDefiniteArticleForms(text: string): string {
  return text
    .split(/\s+/)
    .map((word) => word.replace(/^(ال[\u064B-\u0652]*)([أإآ])/, "$1"))
    .join("");
}

export function normalizeArabicTextPreserveDiacritics(input: string): string {
  input = input.trim();
  let normalized = input.normalize("NFC");
  normalized = unifyDefiniteArticleForms(normalized);
  normalized = normalized.replace(/\s+/g, "").trim();
  normalized = normalized.replace(/[أإآؤئ]/g, "ا");
  normalized = normalizeArabicEndings(normalized);
  return normalized;
}

export function normalizeArabicText(input: string): string {
  input = input.trim();
  let normalized = input.normalize("NFC");
  normalized = unifyDefiniteArticleForms(normalized);
  normalized = normalized.replace(/\s+/g, "").trim();
  normalized = normalized.replace(/[أإآؤئ]/g, "ا");
  normalized = normalizeArabicEndings(normalized);
  return removeDiacritics(normalized);
}

export function normalizeVowelsAndTanween(text: string): string {
  let normalized = text;
  normalized = normalized
    .replace(/[ًا]|ان$/g, "ا")
    .replace(/[ٍ]|ين$/g, "ي")
    .replace(/[ٌ]|ون$/g, "و");
  normalized = normalized
    .replace(new RegExp(`${FATHA}ا`, "g"), FATHA)
    .replace(new RegExp(`${KASRA}ي`, "g"), KASRA)
    .replace(new RegExp(`${DAMMA}و`, "g"), DAMMA);
  return normalized;
}

export function normalizeTanweenRepresentation(text: string): string {
  let normalized = text;
  normalized = normalized.replace(/(يين|ين)[ْ]?$/g, "ين");
  // New normalization for وة/ات endings
  normalized = normalized.replace(/وة$/g, "ات");
  return normalized;
}

export function getBaseLetters(
  text: string,
  handleSingleLetterCase: boolean = true
): string {
  let normalized = text.replace(/\s+/g, "");
  normalized = normalized.replace(/[أإآؤئء]/g, "ا");
  normalized = removeDiacritics(normalized);
  if (
    handleSingleLetterCase &&
    normalized.length === 2 &&
    normalized.endsWith("ا")
  ) {
    normalized = normalized.charAt(0);
  }
  // Handle common ending variations
  normalized = normalizeArabicEndings(normalized);
  return normalized.trim();
}

export function expandDiacriticsToLetters(word: string): string {
  let result = word;

  // Handle compound diacritics first (like shadda + vowel)
  Object.entries(compositeCharacterPatterns).forEach(([key, pattern]) => {
    result = result.replace(pattern, (match) => {
      const baseChar = result[result.indexOf(match) - 1];
      return baseChar + baseChar + (vowelToLetterMap[match[1]] || "");
    });
  });

  // Then handle single diacritics
  Object.entries(vowelToLetterMap).forEach(([diacritic, letter]) => {
    result = result.replace(new RegExp(diacritic, "g"), letter);
  });

  // Handle special endings
  if (result.endsWith("وة")) {
    result = result.slice(0, -2) + "ات";
  }

  return result;
}

export function isValidEndingVariation(
  expected: string,
  actual: string
): boolean {
  const expectedEnding = expected.slice(-2);
  const actualEnding = actual.slice(-2);

  return (
    expectedEnding === actualEnding ||
    endingVariations[expectedEnding]?.includes(actualEnding) ||
    endingVariations[actualEnding]?.includes(expectedEnding)
  );
}

function getVowelMatchScore(expected: string, actual: string): number {
  const expectedVowels = [...expected].filter((char) =>
    Object.keys(vowelToLetterMap).includes(char)
  );
  const actualLetters = [...actual];
  let score = 0;

  expectedVowels.forEach((vowel) => {
    const expansions = vowelToLetterMap[vowel];
    if (actualLetters.some((letter) => expansions.includes(letter))) {
      score += 1;
    }
  });

  // Bonus score for correct ending variations
  if (isValidEndingVariation(expected, actual)) {
    score += expectedVowels.length * 0.2; // 20% bonus for correct ending
  }

  return (score / expectedVowels.length) * 100;
}

export function isSpecialCompound(word: string): boolean {
  return Object.keys(specialCompounds).some(
    (compound) =>
      word.includes(compound) ||
      specialCompounds[compound].some((variant) => word.includes(variant))
  );
}

export function handleSpecialCompound(
  expected: string,
  actual: string
): number {
  for (const [base, variants] of Object.entries(specialCompounds)) {
    if (expected.includes(base) || variants.some((v) => expected.includes(v))) {
      const expectedBase = expected.replace(new RegExp(base, "g"), "");
      const actualBase = actual.replace(
        new RegExp(variants.join("|"), "g"),
        ""
      );
      if (expectedBase === actualBase) {
        return 100;
      }
      // Partial match
      return 85;
    }
  }
  return 0;
}

export function isPhoneticEquivalent(
  expected: string,
  actual: string
): boolean {
  // Check for ending variations
  if (isValidEndingVariation(expected, actual)) {
    return true;
  }

  // Check for special compounds
  if (
    isSpecialCompound(expected) &&
    handleSpecialCompound(expected, actual) > 80
  ) {
    return true;
  }

  // Check for ظ/ذ equivalence and other enhanced mappings
  const expectedChar = expected.charAt(0);
  const actualChar = actual.charAt(0);

  // Check direct equivalence in commonTranscriptionConfusions
  if (
    commonTranscriptionConfusions[expectedChar]?.includes(actualChar) ||
    commonTranscriptionConfusions[actualChar]?.includes(expectedChar)
  ) {
    return true;
  }

  // Check dialectal variations
  if (
    dialectalVariations[expectedChar]?.includes(actualChar) ||
    dialectalVariations[actualChar]?.includes(expectedChar)
  ) {
    return true;
  }

  // Check if both characters belong to the same phonetic group
  for (const group of Object.values(phoneticGroups)) {
    if (group.includes(expectedChar) && group.includes(actualChar)) {
      return true;
    }
  }

  return false;
}
