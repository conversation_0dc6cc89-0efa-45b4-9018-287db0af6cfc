"use client";

import React, {
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  useRef,
  forwardRef,
  useImperativeH<PERSON>le,
} from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/shadcn-ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Mic,
  StopCircle,
  RotateCcw,
  Check,
  AlertCircle,
  Play,
  Pause,
  Loader2,
} from "lucide-react";
import dynamic from "next/dynamic";

// Import motion wrapper components
import { Motion, AnimatePresence } from "@/components/motion-wrapper";

import { getPronunciationFeedback } from "./arabicPronunciationUtils";
import { normalizeArabicText } from "./vowelUtils";

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.5, ease: "easeInOut" },
};

const pulseAnimation = {
  scale: [1, 1.05, 1],
  opacity: [1, 0.9, 1],
  transition: {
    duration: 1.5,
    repeat: Infinity,
    ease: "easeInOut",
  },
};

const analysisVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, ease: [0.16, 1, 0.3, 1] },
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: { duration: 0.4, ease: [0.16, 1, 0.3, 1] },
  },
};

function formatTime(time: number): string {
  if (isNaN(time)) return "0:00";
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  const secondsString = seconds < 10 ? `0${seconds}` : seconds;
  return `${minutes}:${secondsString}`;
}

const letterFeedbackMap: { [key: string]: string } = {
  ي: "Make a 'y' sound, as in 'yes'",
  ا: "Pronounce the letter ا correctly",
  ح: "Breathe out from your throat, like a quiet sigh",
  د: "Put your tongue behind your teeth and make a 'd' sound, as in 'dog'",
  ن: "Pronounce the letter ن correctly",
  // Vowel (diacritic) feedback tips:
  "ِ": "Articulate the kasra properly",
  "َ": "Articulate the fatha properly",
  "ُ": "Articulate the damma properly",
  "ٍ": "Articulate the tanween properly",
  "ً": "Articulate the tanween properly",
  "ْ": "Ensure you pronounce the sukun correctly",
};

export interface SpeakThisAdvancedRef {
  handleRetry: () => void;
  startRecording: () => Promise<void>;
}

interface SpeakThisAdvancedProps {
  options: Array<{
    id: number;
    audioSrc: string | null;
    challengeId: number;
    text: string;
    correct: boolean;
    imageSrc: string | null;
    matchPairId: number | null;
    side: string | null;
    sequence: number | null;
  }>;
  onSelect: (
    id: number | null,
    side: "left" | "right" | "drag-and-drop" | "fill-in-the-blank"
  ) => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  disabled?: boolean;
  type: "SPEAK_THIS_ADVANCED";
  question: string;
  audioSrc: string;
  sentence?: string;
  mediaType?: string | null;
  mediaUrl?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
  onSpeakThisAdvancedResult: (isCorrect: boolean) => void;
}

const SpeakThisAdvanced = forwardRef<
  SpeakThisAdvancedRef,
  SpeakThisAdvancedProps
>(({ question, audioSrc, sentence, onSpeakThisAdvancedResult }, ref) => {
  const targetWord =
    sentence && sentence.trim().length > 0 ? sentence : question;
  const [recorder, setRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<BlobPart[]>([]);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [isInitializing, setIsInitializing] = useState(false);

  // Show celebration if student >= threshold
  const [showCelebration, setShowCelebration] = useState(false);

  const [state, setState] = useState<{
    isRecording: boolean;
    isDone: boolean;
    isProcessing: boolean;
    error: string | null;
    transcription: string | null;
    accuracy: number | null;
    feedback: React.ReactNode | null;
    showFeedback: boolean;
    threshold: number | null;
  }>({
    isRecording: false,
    isDone: false,
    isProcessing: false,
    error: null,
    transcription: null,
    accuracy: null,
    feedback: null,
    showFeedback: false,
    threshold: null,
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [audioCurrentTime, setAudioCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);

  // ▼ NEW: State to detect if MediaRecorder is available
  const [isMediaRecorderSupported, setIsMediaRecorderSupported] =
    useState(true);

  // ▼ NEW: Reference to a hidden file input for fallback
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleAudioPlayPause = () => {
    if (!audioRef.current) return;
    if (isAudioPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsAudioPlaying(!isAudioPlaying);
  };

  const handleAudioTimeUpdate = () => {
    if (!audioRef.current) return;
    setAudioCurrentTime(audioRef.current.currentTime);
  };

  const handleAudioLoadedMetadata = () => {
    if (!audioRef.current) return;
    setAudioDuration(audioRef.current.duration);
  };

  const handleAudioSeek = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || audioDuration === 0) return;
    const rect = event.currentTarget.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const newTime = (offsetX / rect.width) * audioDuration;
    audioRef.current.currentTime = newTime;
    setAudioCurrentTime(newTime);
  };

  const handleAudioEnded = () => {
    setIsAudioPlaying(false);
  };

  // ▼ NEW: Fallback file selection logic
  const handleFileFallbackChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }
    const file = e.target.files[0];
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    const newUrl = URL.createObjectURL(file);
    // Mimic "onstop" logic
    setAudioChunks([file]);
    setState((prev) => ({
      ...prev,
      isDone: true,
      isRecording: false,
      showFeedback: false,
      error: null,
      transcription: null,
      accuracy: null,
      feedback: null,
      threshold: null,
    }));
    setAudioUrl(newUrl);
  };

  const initializeRecorder =
    useCallback(async (): Promise<MediaRecorder | null> => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
        });

        let recorderOptions: MediaRecorderOptions = {
          audioBitsPerSecond: 128000,
        };
        if (
          typeof MediaRecorder !== "undefined" &&
          MediaRecorder.isTypeSupported &&
          MediaRecorder.isTypeSupported("audio/webm;codecs=opus")
        ) {
          recorderOptions.mimeType = "audio/webm;codecs=opus";
        } else if (
          typeof MediaRecorder !== "undefined" &&
          MediaRecorder.isTypeSupported &&
          MediaRecorder.isTypeSupported("audio/webm")
        ) {
          recorderOptions.mimeType = "audio/webm";
        } else if (
          typeof MediaRecorder !== "undefined" &&
          MediaRecorder.isTypeSupported &&
          MediaRecorder.isTypeSupported("audio/mp4")
        ) {
          recorderOptions.mimeType = "audio/mp4";
        }

        const newRecorder = new MediaRecorder(stream, recorderOptions);

        newRecorder.ondataavailable = (event: BlobEvent) => {
          if (event.data.size > 0) {
            setAudioChunks((chunks) => [...chunks, event.data]);
          }
        };

        newRecorder.onstart = () => {
          setAudioChunks([]);
          setState((prev) => ({
            ...prev,
            isRecording: true,
            error: null,
            transcription: null,
            accuracy: null,
            feedback: null,
            showFeedback: false,
            threshold: null,
          }));
          setProgress(0);
        };

        newRecorder.onstop = () => {
          setState((prev) => ({ ...prev, isRecording: false, isDone: true }));
          const audioBlob = new Blob(audioChunks, { type: "audio/webm" });
          const url = URL.createObjectURL(audioBlob);
          if (audioUrl) {
            URL.revokeObjectURL(audioUrl);
          }
          setAudioUrl(url);
        };

        setRecorder(newRecorder);
        return newRecorder;
      } catch (error) {
        setIsMediaRecorderSupported(false);
        setState((prev) => ({
          ...prev,
          error:
            "Recording is not supported by your browser or device. Please use the fallback option below.",
        }));
        return null;
      }
    }, [audioChunks, audioUrl]);

  useEffect(() => {
    if (
      typeof window === "undefined" ||
      typeof (window as any).MediaRecorder === "undefined"
    ) {
      setIsMediaRecorderSupported(false);
      setState((prev) => ({
        ...prev,
        error:
          "Recording is not supported by your browser or device. Please use the fallback option below.",
      }));
    }
    return () => {
      if (recorder && recorder.state !== "inactive") {
        recorder.stream.getTracks().forEach((track) => track.stop());
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [recorder, audioUrl]);

  const startRecording = async () => {
    if (isInitializing) return;
    // If no MediaRecorder, fallback
    if (!isMediaRecorderSupported) {
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
        fileInputRef.current.click();
      }
      return;
    }
    setIsInitializing(true);
    // Always initialize a new recorder for a fresh recording session.
    const newRecorder = await initializeRecorder();
    if (newRecorder) {
      setAudioUrl(null);
      try {
        newRecorder.start();
      } catch (error) {
        console.error("Could not start MediaRecorder:", error);
        setState((prev) => ({
          ...prev,
          error:
            "Recording is not supported by your browser or device. Please use the fallback option below.",
        }));
        setIsInitializing(false);
        return;
      }
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            newRecorder?.stop();
            return 100;
          }
          return prev + 1;
        });
      }, 100);
    }
    setIsInitializing(false);
  };

  const stopRecording = () => {
    if (recorder && recorder.state === "recording") {
      recorder.stop();
      setProgress(100);
    }
  };

  // Updated handleHfTranscribe using the centralized getPronunciationFeedback function.
  const handleHfTranscribe = useCallback(async () => {
    if (audioChunks.length === 0) {
      setState((prev) => ({
        ...prev,
        error: "No audio recorded. Please record something first.",
      }));
      return;
    }

    setState((prev) => ({ ...prev, isProcessing: true, error: null }));

    try {
      const audioBlob =
        audioChunks[0] instanceof Blob
          ? (audioChunks[0] as Blob)
          : new Blob(audioChunks, { type: "audio/webm" });

      const formData = new FormData();
      formData.append("file", audioBlob, "student_audio.webm");

      const response = await fetch("/api/hf-transcribe", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      const data = await response.json();

      // Use centralized getPronunciationFeedback function from arabicPronunciationUtils.ts
      const { accuracy, feedback, threshold } = getPronunciationFeedback(
        targetWord,
        data.text
      );
      const isCorrect = accuracy >= threshold;

      setState((prev) => ({
        ...prev,
        isProcessing: false,
        transcription: data.text,
        accuracy: accuracy,
        feedback: feedback,
        error: null,
        showFeedback: true,
        threshold: threshold,
      }));

      if (isCorrect) {
        setShowCelebration(true);
      }

      onSpeakThisAdvancedResult(isCorrect);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : "Transcription failed",
        transcription: null,
      }));
    }
  }, [audioChunks, targetWord, onSpeakThisAdvancedResult]);

  useEffect(() => {
    // Trigger transcription only if audioUrl is available and transcription hasn't been set yet.
    if (audioUrl && audioChunks.length > 0 && !state.transcription) {
      handleHfTranscribe();
    }
  }, [audioUrl, audioChunks, handleHfTranscribe, state.transcription]);

  const renderHighlightedWord = () => {
    const isPassed =
      state.accuracy !== null &&
      state.accuracy >= (state.threshold !== null ? state.threshold : 70);
    if (!state.transcription) {
      return (
        <>
          {targetWord.split("").map((letter, idx) => (
            <span key={idx} className="text-black">
              {letter}
            </span>
          ))}
        </>
      );
    }
    if (isPassed) {
      return (
        <>
          {targetWord.split("").map((letter, idx) => (
            <span key={idx} className="text-emerald-500">
              {letter}
            </span>
          ))}
        </>
      );
    } else {
      const normalizedExpected = normalizeArabicText(targetWord);
      const normalizedActual = normalizeArabicText(state.transcription);
      const expectedLetters = normalizedExpected.split("");
      const actualLetters = normalizedActual.split("");
      return (
        <>
          {targetWord.split("").map((letter, idx) => {
            const expectedLetter = expectedLetters[idx] || "";
            const actualLetter = actualLetters[idx] || "";
            const isMatch = expectedLetter === actualLetter;
            return (
              <span
                key={idx}
                className={isMatch ? "text-emerald-500" : "text-rose-500"}
              >
                {letter}
              </span>
            );
          })}
        </>
      );
    }
  };

  const handleButtonClick = () => {
    if (state.isRecording) {
      return;
    }
    if (
      state.showFeedback &&
      state.accuracy &&
      state.accuracy >= (state.threshold !== null ? state.threshold : 70)
    ) {
      return;
    }
    startRecording();
  };

  const handleRetry = async () => {
    if (recorder && recorder.state === "recording") {
      stopRecording();
    }
    if (recorder) {
      recorder.stream.getTracks().forEach((track) => track.stop());
    }
    setRecorder(null);
    setAudioUrl(null);
    setAudioChunks([]);
    setProgress(0);
    setState({
      isRecording: false,
      isDone: false,
      isProcessing: false,
      error: null,
      transcription: null,
      accuracy: null,
      feedback: null,
      showFeedback: false,
      threshold: null,
    });
    setIsMediaRecorderSupported(true);
    await startRecording();
  };

  useImperativeHandle(ref, () => ({
    handleRetry,
    startRecording,
  }));

  return (
    <Card className="w-full max-w-5xl mx-auto overflow-hidden bg-white shadow-lg">
      <div className="flex flex-col lg:flex-row min-h-[500px]">
        <div className="flex-1 p-8 lg:p-12 border-b lg:border-b-0 lg:border-r border-gray-100">
          <Motion.div
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={fadeIn}
            className="h-full flex flex-col"
          >
            <div
              className={
                "flex flex-col items-center justify-center text-center mb-12 " +
                (state.showFeedback ? "mt-16" : "")
              }
              style={{ flexGrow: 1 }}
            >
              <Motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-4xl lg:text-5xl font-bold tracking-tight text-black mb-4"
              >
                {renderHighlightedWord()}
              </Motion.div>
            </div>

            <div className="flex flex-col justify-center space-y-8">
              <AnimatePresence mode="sync">
                {!state.isRecording && (
                  <div className="relative">
                    {showCelebration && (
                      <AnimatePresence>
                        <Motion.div
                          key="celebration-confetti"
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1, rotate: 360 }}
                          exit={{ opacity: 0, scale: 0 }}
                          transition={{ duration: 1.2 }}
                          className="absolute inset-0 flex items-center justify-center pointer-events-none"
                        >
                          <div className="bg-yellow-300 w-4 h-4 rounded-full mx-1 animate-ping" />
                          <div className="bg-pink-300 w-4 h-4 rounded-full mx-1 animate-ping" />
                          <div className="bg-blue-300 w-4 h-4 rounded-full mx-1 animate-ping" />
                        </Motion.div>
                      </AnimatePresence>
                    )}
                    <Motion.div key="recording" variants={fadeIn}>
                      <button
                        onClick={handleButtonClick}
                        disabled={state.isRecording || state.isProcessing}
                        className={`w-full py-6 px-8 ${
                          state.isRecording
                            ? "bg-black text-white"
                            : "bg-gray-50 hover:bg-gray-100"
                        } rounded-xl text-lg font-medium transition-all duration-300 flex items-center justify-center space-x-4 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98]`}
                      >
                        {state.isRecording ? (
                          <Motion.div animate={pulseAnimation}>
                            <StopCircle className="h-6 w-6" />
                          </Motion.div>
                        ) : state.showFeedback ? (
                          state.accuracy &&
                          state.accuracy >=
                            (state.threshold !== null
                              ? state.threshold
                              : 70) ? (
                            "Well done!"
                          ) : (
                            <>
                              <span>Try again</span>
                              <RotateCcw className="h-6 w-6" />
                            </>
                          )
                        ) : (
                          <Mic className="h-6 w-6" />
                        )}
                        <span />
                      </button>
                    </Motion.div>
                  </div>
                )}

                {state.isRecording && (
                  <Motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-4 mt-6"
                  >
                    <Progress value={progress} className="h-1" />
                    <Button
                      onClick={stopRecording}
                      variant="outline"
                      className="w-full py-4 border-black text-black hover:bg-gray-50"
                    >
                      Stop Recording
                    </Button>
                  </Motion.div>
                )}

                {state.error && !state.showFeedback && (
                  <Motion.div
                    key="error-main"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="mt-6"
                  >
                    <Alert
                      variant="destructive"
                      className="bg-gray-50 border-gray-200 flex items-center gap-2"
                    >
                      <AlertCircle className="h-4 w-4 text-black" />
                      <AlertDescription className="text-black">
                        {state.error}
                      </AlertDescription>
                    </Alert>
                  </Motion.div>
                )}

                {state.isProcessing && (
                  <Motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="mt-6 flex items-center justify-center space-x-2"
                  >
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span>Processing audio...</span>
                  </Motion.div>
                )}
              </AnimatePresence>
            </div>
          </Motion.div>
        </div>

        <AnimatePresence>
          {state.showFeedback && (
            <Motion.div
              variants={analysisVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="flex-1 p-8 lg:p-12 bg-gray-50 h-[600px]"
            >
              <div className="h-full flex flex-col space-y-8">
                <div className="flex items-center justify-between">
                  <h3 className="text-2xl font-semibold text-black">
                    Analysis Results
                  </h3>
                  {state.accuracy !== null &&
                    state.accuracy >=
                      (state.threshold !== null ? state.threshold : 70) && (
                      <Motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{
                          type: "spring",
                          stiffness: 200,
                          damping: 10,
                        }}
                      >
                        <Check className="h-6 w-6 text-green-600" />
                      </Motion.div>
                    )}
                </div>

                {state.transcription &&
                  state.accuracy !== null &&
                  state.accuracy <
                    (state.threshold !== null ? state.threshold : 70) && (
                    <div className="p-6 bg-white rounded-xl shadow-sm">
                      <p className="text-sm text-gray-500 mb-6">
                        Your pronunciation
                      </p>
                      <p className="text-6xl font-medium text-black font-arabic">
                        {state.transcription}
                      </p>
                    </div>
                  )}

                {state.accuracy !== null && (
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Match Score</span>
                      <span className="font-medium text-black">
                        {state.accuracy}%
                      </span>
                    </div>
                    <Progress value={state.accuracy} className="h-1.5" />
                  </div>
                )}
                {state.error && (
                  <Alert
                    variant="destructive"
                    className="bg-white border-gray-200 flex items-center gap-2 rounded-xl"
                  >
                    <AlertCircle className="h-4 w-4 text-black" />
                    <AlertDescription className="text-black">
                      {state.error}
                    </AlertDescription>
                  </Alert>
                )}

                {audioSrc &&
                  state.accuracy !== null &&
                  state.accuracy <
                    (state.threshold !== null ? state.threshold : 70) && (
                    <div className="mt-auto pt-4">
                      <p className="text-sm text-gray-500 mb-2">
                        Listen below to the correct pronunciation
                      </p>
                      <div className="flex items-center space-x-6 bg-white border-violet-900 p-4 rounded-xl shadow-sm">
                        <Button
                          onClick={handleAudioPlayPause}
                          variant="ghost"
                          size="sm"
                          className="w-10 h-10 rounded-full hover:bg-violet-100 transition-colors duration-200 flex items-center justify-center"
                        >
                          {isAudioPlaying ? (
                            <Pause className="w-5 h-5 text-violet-700" />
                          ) : (
                            <Play className="w-5 h-5 text-violet-700" />
                          )}
                        </Button>
                        <div className="flex-1">
                          <div
                            className="h-2 bg-gray-100 rounded-full cursor-pointer group relative"
                            onClick={handleAudioSeek}
                          >
                            <div
                              className="absolute h-full bg-black rounded-full transition-all duration-150"
                              style={{
                                width: `${
                                  (audioCurrentTime / audioDuration) * 100
                                }%`,
                              }}
                            >
                              <div className="absolute right-0 top-1/2 -translate-y-1/2 w-4 h-4 bg-black rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                            </div>
                          </div>
                        </div>
                        <div className="text-sm font-medium text-gray-500 tabular-nums min-w-[70px] text-right">
                          {formatTime(audioCurrentTime)}/
                          {formatTime(audioDuration)}
                        </div>
                      </div>
                      <audio
                        ref={audioRef}
                        src={audioSrc}
                        onTimeUpdate={handleAudioTimeUpdate}
                        onLoadedMetadata={handleAudioLoadedMetadata}
                        onEnded={handleAudioEnded}
                        hidden
                      />
                    </div>
                  )}
              </div>
            </Motion.div>
          )}
        </AnimatePresence>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        capture={true}
        className="hidden"
        onChange={handleFileFallbackChange}
      />
    </Card>
  );
});

SpeakThisAdvanced.displayName = "SpeakThisAdvanced";

export default SpeakThisAdvanced;
