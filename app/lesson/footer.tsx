import { useKey, useMedia } from "react-use";
import { CheckCircle, XCircle } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

// NEW: Define referrer information type (matching quiz.tsx)
type ReferrerInfo = {
  from: "coursesplayer" | "learn" | "unknown";
  curriculumId?: string;
} | null;

type Props = {
  onCheck: () => void;
  status: "correct" | "wrong" | "none" | "submitting" | "completed";
  disabled?: boolean;
  lessonId?: number;
  // === Added style to allow inline styling in Quiz.tsx ===
  style?: React.CSSProperties;
  // NEW: Add referrer information and smart navigation handler
  referrerInfo?: ReferrerInfo;
  onSmartNavigate?: () => void;
};

export const Footer = ({
  onCheck,
  status,
  disabled,
  lessonId,
  style,
  // NEW: Accept referrer and navigation props with defaults
  referrerInfo = null,
  onSmartNavigate,
}: Props) => {
  useKey("Enter", onCheck, {}, [onCheck]);
  const isMobile = useMedia("(max-width: 1024px)");

  // Ensure the "Next" button is enabled when the status is "correct"
  const isButtonDisabled =
    status === "correct" || status === "completed" ? false : disabled;

  // NEW: Smart navigation handler for Continue button when completed
  const handleContinueClick = () => {
    console.log("Footer Continue button clicked with referrer:", referrerInfo);

    if (status === "completed" && onSmartNavigate) {
      // Use smart navigation for completed lessons
      console.log("Using smart navigation for lesson completion");
      onSmartNavigate();
    } else {
      // Use regular onCheck for other statuses
      console.log("Using regular onCheck for non-completed status:", status);
      onCheck();
    }
  };

  // NEW: Smart navigation handler for Practice Again button
  const handlePracticeAgainClick = () => {
    if (onSmartNavigate) {
      // Use smart navigation to return to original context
      console.log("Using smart navigation for practice again");
      onSmartNavigate();
    } else {
      // Fallback to current behavior
      console.log("No smart navigation available, using fallback");
      window.location.href = `/lesson/${lessonId}`;
    }
  };

  return (
    <footer
      // === Apply the style prop here ===
      style={style}
      className={cn(
        // Made the footer smaller (changed lg:h-[140px] h-[100px] -> lg:h-[90px] h-[60px])
        "lg:h-[90px] h-[60px] border-t-2",
        status === "correct" && "border-transparent bg-white",
        status === "wrong" && "border-transparent bg-rose-100"
      )}
    >
      <div className="max-w-[1140px] h-full mx-auto flex items-center justify-between px-6 lg:px-10">
        {status === "correct" && (
          <div className="text-emerald-700 font-bold text-base lg:text-2xl flex items-center">
            <CheckCircle className="h-6 w-6 lg:h-10 lg:w-10 mr-4" />
            Correct, nicely done!
          </div>
        )}
        {status === "wrong" && (
          <div className="text-rose-500 font-bold text-base lg:text-2xl flex items-center">
            <XCircle className="h-6 w-6 lg:h-10 lg:w-10 mr-4" />
            Try again.
          </div>
        )}
        {status === "completed" && (
          <Button
            variant="default"
            size={isMobile ? "sm" : "lg"}
            onClick={handlePracticeAgainClick} // NEW: Use smart navigation for practice again
          >
            Practice again
          </Button>
        )}
        <Button
          disabled={isButtonDisabled}
          className="ml-auto pointer-events-auto"
          onClick={handleContinueClick} // NEW: Use smart navigation handler
          size={isMobile ? "sm" : "lg"}
          variant={status === "wrong" ? "danger" : "secondary"}
        >
          {status === "none" && "Check"}
          {status === "correct" && "Next"}
          {status === "wrong" && "Retry"}
          {status === "completed" && "Continue"}
        </Button>
      </div>
    </footer>
  );
};
