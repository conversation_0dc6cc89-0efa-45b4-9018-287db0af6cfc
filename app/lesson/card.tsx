// card.tsx
import Image from "next/image";
import { useCallback, useEffect, useRef } from "react";
import { useAudio } from "react-use";
import { cn } from "@/lib/utils";
import { <PERSON> } from "next/font/google";
import { AudioLines, Volume2 } from "lucide-react";

// Define <PERSON> font for Arabic text
const mirza<PERSON><PERSON> = Mirza({
  subsets: ["arabic"],
  weight: ["400", "500", "600", "700"],
});

type ChallengeType =
  | "SELECT"
  | "ASSIST"
  | "MATCHING"
  | "TAP_WHAT_YOU_HEAR"
  | "IMAGE_AUDIO_SELECT"
  | "DRAG_AND_DROP"
  | "SPEAK_THIS"
  | "FILL_IN_THE_BLANK"
  | "SPEAK_THIS_ADVANCED";

type Props = {
  id: number;
  imageSrc?: string | null | undefined;
  audioSrc?: string | null | undefined;
  text: string;
  selected?: boolean;
  selectedPairOption?: number | null;
  onClick: () => void;
  disabled?: boolean;
  status?: "correct" | "wrong" | "none" | "submitting";
  type: ChallengeType;
  matched?: boolean;
  shape?: "card" | "roundedRectangle";
  customClass?: string;
  style?: React.CSSProperties;
  large?: boolean;
  version?: "version1" | "version2" | "version3" | "version4";
  children?: React.ReactNode;
  className?: string;
  overrideSelectionColor?: boolean;
};

// Unified card dimensions - consistent across all types
const getCardDimensions = (
  type: ChallengeType,
  imageSrc?: string | null,
  audioSrc?: string | null | undefined
) => {
  const isAudioWave = imageSrc === "/audiowave.svg";

  // Base dimensions for consistency
  const baseDimensions = {
    width: "w-[240px]",
    height: "h-[120px]",
    padding: "p-4",
  };

  // Special cases that need different dimensions
  switch (type) {
    case "SELECT":
      return isAudioWave
        ? { width: "w-[480px]", height: "h-[240px]", padding: "p-6" }
        : { width: "w-[200px]", height: "h-[200px]", padding: "p-4" };

    case "MATCHING":
      return { width: "w-[280px]", height: "h-[100px]", padding: "p-4" };

    case "TAP_WHAT_YOU_HEAR":
      return { width: "w-[200px]", height: "h-[160px]", padding: "p-4" };

    case "DRAG_AND_DROP":
      return { width: "", height: "", padding: "p-3" };

    // ***** RETAINING YOUR SPECIFIED DIMENSIONS FOR IMAGE_AUDIO_SELECT *****
    case "IMAGE_AUDIO_SELECT":
      return { width: "w-[240px]", height: "h-[90px]", padding: "p-2" };
    // ***********************************************************************

    default:
      return baseDimensions;
  }
};

// Enhanced text sizing - Arabic text gets larger sizes for better readability
const getTextSize = (text: string, type: ChallengeType) => {
  const containsArabic = text && text.split("").some(isArabic);

  // ***** MODIFIED ARABIC TEXT SIZES TO BE ONE STEP LARGER *****
  // (Based on the textSizes from your last provided code)
  const textSizes = {
    small: containsArabic ? "text-2xl" : "text-sm", // Arabic: text-xl -> text-2xl
    medium: containsArabic ? "text-3xl" : "text-base", // Arabic: text-2xl -> text-3xl
    large: containsArabic ? "text-4xl" : "text-lg", // Arabic: text-3xl -> text-4xl
    xlarge: containsArabic ? "text-5xl" : "text-xl", // Arabic: text-4xl -> text-5xl
  };
  // ************************************************************

  switch (type) {
    case "SELECT":
    case "TAP_WHAT_YOU_HEAR":
      return textSizes.xlarge;

    case "MATCHING":
    case "IMAGE_AUDIO_SELECT":
      // Now uses textSizes.large (Arabic text-4xl), which should fit h-[90px] with p-2.
      return textSizes.large;

    case "FILL_IN_THE_BLANK":
      return textSizes.medium;

    default:
      return textSizes.small;
  }
};

// Helper function to detect Arabic characters
const isArabic = (char: string): boolean => {
  const arabicPattern =
    /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicPattern.test(char);
};

// Unified text styling
const getTextStyles = (text: string) => {
  const containsArabic = text && text.split("").some(isArabic);

  return cn(
    "font-medium leading-relaxed text-center", // `leading-relaxed` (line-height: 1.625)
    containsArabic ? `${mirzaFont.className}` : "font-sans"
  );
};

export const Card = ({
  id,
  imageSrc,
  audioSrc,
  text,
  selected,
  selectedPairOption,
  onClick,
  disabled,
  status,
  type,
  matched,
  shape = "card",
  customClass,
  style,
  large = false,
  version,
  children,
  className,
  overrideSelectionColor = false,
}: Props) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const [audio, , controls] = useAudio({
    src: audioSrc || "",
    autoPlay: false,
  });

  useEffect(() => {
    if (audio && !audioRef.current) {
      (audioRef as React.MutableRefObject<HTMLAudioElement>).current =
        audio as unknown as HTMLAudioElement;
    }
  }, [audio]);

  const handleClick = useCallback(() => {
    if (disabled) return;
    if (audioRef.current) {
      controls.play();
    }
    onClick();
  }, [disabled, onClick, controls]);

  const dimensions = getCardDimensions(type, imageSrc, audioSrc);
  const textSize = getTextSize(text, type);
  const textStyles = getTextStyles(text);

  const cardStyles = cn(
    "relative flex flex-col items-center justify-center",
    "cursor-pointer transition-all duration-200 ease-in-out",
    "text-center select-none",
    dimensions.width,
    dimensions.height,
    dimensions.padding,
    "bg-white border border-gray-200 rounded-2xl",
    "shadow-sm hover:shadow-md",
    "hover:bg-gray-50",
    "active:scale-[0.98]",
    selected &&
      !matched &&
      status !== "correct" &&
      status !== "wrong" &&
      "bg-emerald-50 border-emerald-300",
    (matched || (selected && status === "correct")) &&
      "bg-emerald-100 border-emerald-400 hover:bg-emerald-100",
    selected &&
      status === "wrong" &&
      !matched &&
      "bg-rose-100 border-rose-400 hover:bg-rose-100",
    disabled && "opacity-50 cursor-not-allowed hover:bg-white hover:shadow-sm",
    customClass,
    className
  );

  const textColor = cn(
    "text-gray-900",
    selected &&
      !matched &&
      status !== "correct" &&
      status !== "wrong" &&
      "text-emerald-700",
    (matched || (selected && status === "correct")) && "text-emerald-700",
    selected && status === "wrong" && !matched && "text-rose-700",
    disabled && "text-gray-400"
  );

  return (
    <div onClick={handleClick} className={cardStyles} style={style}>
      {audio}
      {imageSrc ? (
        imageSrc === "/audiowave.svg" ? (
          <div className="flex items-center justify-center space-x-3">
            <Volume2 className="w-6 h-6 text-gray-700" strokeWidth={1.5} />
            <AudioLines className="w-8 h-8 text-gray-700" strokeWidth={1.5} />
            <AudioLines className="w-8 h-8 text-gray-700" strokeWidth={1.5} />
          </div>
        ) : (
          <div className="relative w-full h-full flex items-center justify-center">
            <Image
              src={imageSrc}
              layout="fill"
              objectFit="contain"
              alt={text}
              className="select-none"
            />
          </div>
        )
      ) : (
        <div className="flex items-center justify-center w-full h-full">
          <p
            className={cn(
              textStyles,
              textSize,
              textColor,
              "break-words w-full"
            )}
          >
            {text}
          </p>
        </div>
      )}
      {children}
    </div>
  );
};
