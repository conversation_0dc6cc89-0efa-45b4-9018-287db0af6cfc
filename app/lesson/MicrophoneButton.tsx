import { useCallback, useEffect, useRef } from "react";
import { useAudio } from "react-use";
import { Mic } from "lucide-react"; // Ensure lucide-react is installed

type Props = {
  onClick?: () => void;
  className?: string; // Optional custom className for styling
  variant?: "default" | "small"; // New prop to handle different variants
};

export const MicrophoneButton = ({
  onClick,
  className,
  variant = "default",
}: Props) => {
  const audioSrc = ""; // Provide your audio source if needed
  const [audio, , controls, ref] = useAudio({ src: audioSrc });

  useEffect(() => {
    if (ref.current) {
      console.log("MicrophoneButton: audio element is available", ref.current);
    } else {
      console.log("MicrophoneButton: audio element is not available yet");
    }
  }, [ref]);

  const handleClick = useCallback(() => {
    if (ref.current) {
      console.log("MicrophoneButton: handleClick called");
      controls.play(); // Play the audio
      if (onClick) {
        onClick();
      }
    } else {
      console.log("MicrophoneButton: audio element is not available on click");
    }
  }, [controls, onClick, ref]);

  return variant === "default" ? (
    <div
      onClick={handleClick}
      className={`relative w-24 h-24 border-cyan-500 bg-cyan-400 text-white rounded-3xl flex items-center justify-center cursor-pointer border-b-4 
      active:border-b-0 active:translate-y-1 transition-all duration-150 ${className}`}
    >
      {audio}
      <Mic color="white" size={48} />
    </div>
  ) : (
    <div
      onClick={handleClick}
      className={`relative flex items-center justify-center cursor-pointer active:translate-y-1 transition-all duration-150 ${className}`}
    >
      {audio}
      <Mic color="white" size={20} />
    </div>
  );
};
