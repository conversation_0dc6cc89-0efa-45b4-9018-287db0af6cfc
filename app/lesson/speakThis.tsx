import { useCallback } from "react";
import { challengeOptions } from "@/db/schema";
import { MicrophoneButton } from "./MicrophoneButton";
import { Card } from "./card";

type ChallengeType =
  | "SELECT"
  | "ASSIST"
  | "MATCHING"
  | "TAP_WHAT_YOU_HEAR"
  | "IMAGE_AUDIO_SELECT"
  | "DRAG_AND_DROP"
  | "SPEAK_THIS"
  | "FILL_IN_THE_BLANK";

type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (
    id: number | null,
    side: "left" | "right" | "drag-and-drop" | "fill-in-the-blank"
  ) => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOption: number | null;
  disabled: boolean;
  type: ChallengeType;
  question: string;
  audioSrc: string;
  sentence: string;
  mediaType?: string | null; // Add mediaType prop
  mediaUrl?: string | null; // Add mediaUrl prop
  topCardText?: string | null; // Add topCardText prop
  topCardAudio?: string | null; // Add topCardAudio prop
};

export const SpeakThis: React.FC<Props> = ({
  options,
  onSelect,
  status,
  selectedOption,
  disabled,
  type,
  question,
  audioSrc,
  sentence,
  mediaType, // Destructure mediaType prop
  mediaUrl, // Destructure mediaUrl prop
  topCardText, // Destructure topCardText prop
  topCardAudio, // Destructure topCardAudio prop
}) => {
  const handleOptionClick = useCallback(
    (id: number) => {
      onSelect(id, "left");
    },
    [onSelect]
  );

  // Replace "blank" or "فارغ" in the sentence with "___"
  const processedSentence = sentence.replace(/blank|فارغ/g, "___");

  return (
    <div className="flex flex-col items-center justify-center h-full bg-white p-4">
      <div className="flex flex-col items-center w-full max-w-xl space-y-8">
        <div className="flex items-center mb-4 space-x-4">
          <button
            className="flex items-center justify-center w-8 h-8"
            onClick={() => {
              const audio = new Audio(audioSrc);
              audio.play();
            }}
          >
            <img src="/AudioB.svg" alt="Play Audio" className="w-6 h-6" />
          </button>
          <span className="text-lg">
            {processedSentence.split(" ").map((part, index) => (
              <span key={index}>
                {part}
                {index < processedSentence.length - 1 && (
                  <span className="underline"> </span>
                )}
              </span>
            ))}
          </span>
        </div>

        <MicrophoneButton />

        {topCardText && (
          <div className="text-center my-4">
            <p>{topCardText}</p>
            {topCardAudio && (
              <audio controls className="w-full mt-2">
                <source src={topCardAudio} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>
            )}
          </div>
        )}

        {mediaType && mediaUrl && (
          <div className="my-4">
            {mediaType === "image" && (
              <img src={mediaUrl} alt="Media" className="max-w-full" />
            )}
            {mediaType === "audio" && (
              <audio controls className="w-full mt-4">
                <source src={mediaUrl} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>
            )}
            {mediaType === "video" && (
              <video controls className="max-w-full">
                <source src={mediaUrl} type="video/mp4" />
                Your browser does not support the video element.
              </video>
            )}
          </div>
        )}

        <div className="grid gap-4 grid-cols-2 mt-6">
          {options.map((option) => (
            <div key={option.id} className="p-1">
              <Card
                id={option.id}
                text={option.text}
                imageSrc={option.imageSrc}
                audioSrc={option.audioSrc}
                onClick={() => handleOptionClick(option.id)}
                selected={selectedOption === option.id}
                status={status}
                disabled={disabled}
                type={type}
              />
            </div>
          ))}
        </div>
        <button className="mt-6 text-gray-400">Can't speak now</button>
      </div>
    </div>
  );
};
