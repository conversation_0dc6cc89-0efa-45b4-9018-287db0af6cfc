import { useCallback, useEffect, useRef } from "react";
import { useAudio } from "react-use";
import { Volume2 } from "lucide-react";

type Props = {
  src: string;
  onClick?: () => void;
  className?: string;
  variant?: "default" | "small";
};

export const AudioButton = ({
  src,
  onClick,
  className,
  variant = "default",
}: Props) => {
  const [audio, , controls, ref] = useAudio({ src });

  useEffect(() => {
    if (ref.current) {
      console.log("AudioButton: audio element is available", ref.current);
    } else {
      console.log("AudioButton: audio element is not available yet");
    }
  }, [ref]);

  const handleClick = useCallback(() => {
    if (ref.current) {
      console.log("AudioButton: handleClick called");
      controls.play(); // Play the audio
      if (onClick) {
        onClick();
      }
    } else {
      console.log("AudioButton: audio element is not available on click");
    }
  }, [controls, onClick, ref]);

  return variant === "default" ? (
    <div
      onClick={handleClick}
      className={`relative w-16 h-16 border-gray-700 bg-gray-800 text-white rounded-2xl flex items-center justify-center cursor-pointer border-b-4 active:border-b-0 active:translate-y-1 transition-all duration-150 ${className}`}
    >
      {audio}
      <Volume2 size={40} className="text-white" />
    </div>
  ) : (
    <div
      onClick={handleClick}
      className={`relative flex items-center justify-center cursor-pointer active:translate-y-1 transition-all duration-150 ${className}`}
    >
      {audio}
      <Volume2
        size={20}
        className="text-gray-900 active:translate-y-1 transition-all duration-150"
      />
    </div>
  );
};
