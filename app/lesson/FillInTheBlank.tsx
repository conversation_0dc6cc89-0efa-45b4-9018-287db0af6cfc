// FillInTheBlank.tsx
import { useEffect, useState } from "react";
import { MainCard } from "./MainCard"; // Assuming MainCard.tsx is in the same directory or adjust path
import { Card } from "./card"; // Assuming card.tsx is in the same directory or adjust path
import { AudioButton } from "./AudioButton"; // Assuming AudioButton.tsx is in the same directory or adjust path
import { cn } from "@/lib/utils"; // Assuming you have this utility

type FillInTheBlankProps = {
  options: {
    id: number;
    text: string;
    imageSrc?: string | null;
    audioSrc?: string | null;
  }[];
  onSelect: (id: number) => void;
  status: "correct" | "wrong" | "none" | "submitting";
  selectedOptions: number[];
  disabled: boolean;
  sentence: string;
  question: string; // The main question text, e.g., "Q: What is missing?"
  mediaUrl?: string | null;
  audioSrc?: string | null; // Audio for the main challenge, if any
  mediaType?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
  optionsStacked?: boolean;
};

export const FillInTheBlank: React.FC<FillInTheBlankProps> = ({
  options,
  onSelect,
  status,
  selectedOptions,
  disabled,
  sentence,
  question,
  mediaUrl,
  audioSrc,
  mediaType,
  topCardText,
  topCardAudio,
  optionsStacked = false,
}) => {
  const [shouldBounce, setShouldBounce] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldBounce(false);
    }, 6000); // Bounce for 6 seconds

    return () => clearTimeout(timer);
  }, []);

  const handleSelect = (id: number) => {
    if (!disabled) {
      onSelect(id);
    }
  };

  const isArabic = (char: string) => /[\u0600-\u06FF]/.test(char);
  const isArabicSentence = sentence && isArabic(sentence.charAt(0));

  function parseHighlightIndices(text: string): number[] {
    const match = text.match(/^\(([\d,]+)\)/);
    if (!match) return [];
    return match[1]
      .split(",")
      .map((num) => parseInt(num, 10))
      .filter((n) => !isNaN(n));
  }

  function removeMarker(text: string): string {
    return text.replace(/^\(([\d,]+)\)/, "");
  }

  function highlightSegments(
    text: string,
    highlightIndices: number[]
  ): JSX.Element[] {
    const segmenter = new Intl.Segmenter("ar", { granularity: "grapheme" });
    const graphemes = [...segmenter.segment(text)].map((seg) => seg.segment);
    const segments: { text: string; highlight: boolean }[] = [];
    let start = 0;
    const sortedIndices = [...highlightIndices].sort((a, b) => a - b);

    sortedIndices.forEach((idx) => {
      const realIndex = idx - 1;
      if (realIndex >= graphemes.length) return;
      if (realIndex > start) {
        segments.push({
          text: graphemes.slice(start, realIndex).join(""),
          highlight: false,
        });
      }
      segments.push({
        text: graphemes[realIndex],
        highlight: true,
      });
      start = realIndex + 1;
    });

    if (start < graphemes.length) {
      segments.push({
        text: graphemes.slice(start).join(""),
        highlight: false,
      });
    }

    return segments.map((seg, i) =>
      seg.highlight ? (
        <span key={i} style={{ color: "white" }}>
          {seg.text}
        </span>
      ) : (
        <span key={i}>{seg.text}</span>
      )
    );
  }

  const renderProcessedSentence = (sentence: string) => {
    const parts = sentence.split(/(blank|فارغ|knalb)/);

    return parts.map((part, index) => {
      if (part === "فارغ" || part === "blank" || part === "knalb") {
        return (
          // Cleaner blank: series of short, thin, light gray dashes
          <span key={index} className="inline-flex items-end space-x-1 mx-1">
            {Array.from({ length: 4 }).map((_, dashIndex) => (
              <span
                key={dashIndex}
                className={cn(
                  "inline-block w-[6px] h-[1.5px]", // Dash size
                  "bg-gray-300", // Dash color
                  "rounded-sm", // Slightly rounded dash
                  shouldBounce && "animate-bounce"
                )}
                style={{
                  animationDelay: `${dashIndex * 100}ms`, // Stagger bounce
                  marginBottom: "0.1em", // Fine-tune vertical alignment
                }}
              ></span>
            ))}
          </span>
        );
      }
      const highlightIndices = parseHighlightIndices(part);
      const cleanedPart = removeMarker(part);
      const highlightedOutput = highlightSegments(
        cleanedPart,
        highlightIndices
      );
      return (
        <span key={index} className="inline-block mx-1">
          {highlightedOutput}
        </span>
      );
    });
  };

  const containerClasses = optionsStacked
    ? "flex flex-col items-start gap-3 w-full"
    : "flex flex-row flex-nowrap items-center gap-3 w-full justify-center";

  return (
    <div
      className={cn(
        "flex flex-col space-y-8 items-center w-full max-w-5xl mx-auto px-4",
        optionsStacked && "md:flex-row md:items-start md:space-x-8 md:space-y-0"
      )}
    >
      {/* === CHALLENGE SECTION === */}
      <div
        className={cn(
          "w-full flex flex-col items-center",
          optionsStacked ? "md:w-1/2 space-y-4" : "space-y-6"
        )}
      >
        {optionsStacked ? (
          <div className="bg-white border border-gray-200 rounded-lg p-4 w-full">
            {/* Image + Sentence Arrangement (optionsStacked=true) */}
            {mediaType === "image" && mediaUrl && (
              <div className="w-full">
                <div className="flex flex-col lg:flex-row items-start gap-4">
                  <div className="flex-shrink-0 w-full lg:w-auto">
                    <MainCard
                      imageSrc={
                        mediaUrl?.startsWith("/")
                          ? mediaUrl
                          : mediaUrl
                          ? `/${mediaUrl}`
                          : undefined
                      }
                      altText={sentence}
                      hasAudio={!!audioSrc}
                      customClass="w-32 h-32 lg:w-40 lg:h-40"
                    />
                  </div>
                  <div className="flex-grow flex items-center min-h-[120px] lg:min-h-[160px] px-4 py-3 bg-white rounded-2xl border border-gray-100 shadow-sm">
                    <p
                      className={cn(
                        "text-lg lg:text-xl leading-relaxed w-full",
                        isArabicSentence && "text-right"
                      )}
                    >
                      {renderProcessedSentence(sentence)}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {/* Top Card Text + Sentence Arrangement (optionsStacked=true) */}
            {topCardText && !mediaUrl && !audioSrc && (
              <div className="flex flex-col w-full items-center gap-4">
                <div className="w-full md:w-4/5 lg:w-3/5 flex justify-center">
                  <MainCard
                    topCardText={topCardText}
                    topCardAudio={topCardAudio}
                    version="version2"
                  />
                </div>
                <div className="flex justify-center items-center min-h-[80px] lg:min-h-[100px]">
                  <p
                    className={cn(
                      "text-xl lg:text-2xl font-light text-gray-800",
                      isArabicSentence && "text-right"
                    )}
                    dir={isArabicSentence ? "rtl" : "ltr"}
                  >
                    {renderProcessedSentence(sentence)}
                  </p>
                </div>
              </div>
            )}
            {/* Audio + Sentence Arrangement (optionsStacked=true) */}
            {mediaType === "audio" && audioSrc && (
              <div className="flex flex-col items-center w-full gap-4">
                <AudioButton src={audioSrc} />
                <div className="flex justify-center items-center min-h-[80px] lg:min-h-[100px]">
                  <p
                    className={cn(
                      "text-xl",
                      isArabicSentence
                        ? "lg:text-4xl text-right"
                        : "lg:text-2xl",
                      "font-light text-gray-800"
                    )}
                    dir={isArabicSentence ? "rtl" : "ltr"}
                  >
                    {renderProcessedSentence(sentence)}
                  </p>
                </div>
              </div>
            )}
            {/* Sentence-Only Arrangement (optionsStacked=true) */}
            {!mediaUrl && !audioSrc && !topCardText && !mediaType && (
              <div className="flex flex-col items-center w-full p-4 gap-4 bg-gray-50 rounded-lg">
                <p
                  className="text-xl lg:text-2xl font-light text-gray-800 text-center"
                  dir={isArabicSentence ? "rtl" : "ltr"}
                >
                  {renderProcessedSentence(sentence)}
                </p>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Image + Sentence Arrangement (optionsStacked=false) */}
            {mediaType === "image" && mediaUrl && (
              <div className="w-full max-w-4xl mx-auto">
                <div className="flex flex-col lg:flex-row items-start gap-6">
                  <div className="flex-shrink-0 w-full lg:w-auto">
                    <MainCard
                      imageSrc={
                        mediaUrl?.startsWith("/")
                          ? mediaUrl
                          : mediaUrl
                          ? `/${mediaUrl}`
                          : undefined
                      }
                      altText={sentence}
                      hasAudio={!!audioSrc}
                      customClass="w-32 h-32 lg:w-44 lg:h-44"
                    />
                  </div>
                  <div className="flex-grow flex items-center min-h-[120px] lg:min-h-[176px] px-4 py-5 bg-white rounded-2xl border border-gray-100 shadow-sm">
                    <p
                      className={cn(
                        "text-xl lg:text-2xl leading-relaxed w-full px-2",
                        isArabicSentence && "text-right"
                      )}
                    >
                      {renderProcessedSentence(sentence)}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {/* Top Card Text + Sentence Arrangement (optionsStacked=false) */}
            {topCardText && !mediaUrl && !audioSrc && (
              <div className="flex flex-col w-full items-center gap-4">
                <div className="w-full sm:w-4/5 md:w-3/5 flex justify-center">
                  <MainCard
                    topCardText={topCardText}
                    topCardAudio={topCardAudio}
                    version="version2"
                  />
                </div>
                <div className="flex justify-center items-center h-24 lg:h-32">
                  <p
                    className={cn(
                      "text-2xl lg:text-3xl font-light text-gray-800",
                      isArabicSentence && "text-right"
                    )}
                    dir={isArabicSentence ? "rtl" : "ltr"}
                  >
                    {renderProcessedSentence(sentence)}
                  </p>
                </div>
              </div>
            )}
            {/* Audio + Sentence Arrangement (optionsStacked=false) */}
            {mediaType === "audio" && audioSrc && (
              <div className="flex flex-col items-center w-full gap-6">
                <AudioButton src={audioSrc} />
                <div className="flex justify-center items-center h-24 lg:h-32">
                  <p
                    className={cn(
                      "text-2xl",
                      isArabicSentence
                        ? "lg:text-6xl text-right"
                        : "lg:text-4xl",
                      "font-light text-gray-800"
                    )}
                    dir={isArabicSentence ? "rtl" : "ltr"}
                  >
                    {renderProcessedSentence(sentence)}
                  </p>
                </div>
              </div>
            )}

            {/* ENHANCED Sentence-Only Arrangement (when optionsStacked = false) */}
            {!mediaUrl && !audioSrc && !topCardText && !mediaType && (
              <div className="inline-block mx-auto my-6 max-w-lg">
                <div className="relative inline-block pl-3">
                  {/* Speech Bubble Tail (Border) */}
                  <div
                    className="absolute left-0 top-1/2 -translate-y-1/2"
                    style={{
                      width: 0,
                      height: 0,
                      borderTop: "15px solid transparent",
                      borderBottom: "15px solid transparent",
                      borderRight: "15px solid #e5e7eb",
                      zIndex: 0,
                    }}
                  />
                  {/* Speech Bubble Tail (Fill) */}
                  <div
                    className="absolute left-[2px] top-1/2 -translate-y-1/2"
                    style={{
                      width: 0,
                      height: 0,
                      borderTop: "13px solid transparent",
                      borderBottom: "13px solid transparent",
                      borderRight: "13px solid white",
                      zIndex: 1,
                    }}
                  />
                  {/* Speech Bubble Body */}
                  <div
                    className={cn(
                      "bg-white px-6 py-4 rounded-2xl shadow-xl border border-gray-200 relative z-10"
                    )}
                  >
                    <p
                      className={cn(
                        "text-3xl md:text-4xl font-medium text-gray-800 text-center",
                        isArabicSentence && "lg:text-5xl text-right"
                      )}
                      dir={isArabicSentence ? "rtl" : "ltr"}
                    >
                      {renderProcessedSentence(sentence)}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* === OPTIONS SECTION === */}
      <div
        className={cn(
          "w-full flex flex-col items-center",
          optionsStacked ? "md:w-1/2 space-y-4" : "space-y-0"
        )}
      >
        {!mediaUrl && !audioSrc && !topCardText && !mediaType ? (
          <div className={containerClasses}>
            {options.map((option) => (
              <Card
                key={option.id}
                id={option.id}
                text={option.text}
                imageSrc={option.imageSrc || undefined}
                audioSrc={option.audioSrc || undefined}
                onClick={() => handleSelect(option.id)}
                selected={selectedOptions.includes(option.id)}
                status={status}
                disabled={disabled}
                type="FILL_IN_THE_BLANK"
                version={optionsStacked ? "version2" : "version4"}
                customClass="fill-in-the-blank-option"
              />
            ))}
          </div>
        ) : (
          <div className={containerClasses}>
            {options.map((option) => (
              <Card
                key={option.id}
                id={option.id}
                text={option.text}
                imageSrc={option.imageSrc || undefined}
                audioSrc={option.audioSrc || undefined}
                onClick={() => handleSelect(option.id)}
                selected={selectedOptions.includes(option.id)}
                status={status}
                disabled={disabled}
                type="FILL_IN_THE_BLANK"
                version={
                  optionsStacked
                    ? "version2"
                    : mediaType === "audio" && audioSrc
                    ? "version3"
                    : "version4"
                }
                customClass={
                  mediaUrl || audioSrc
                    ? "fill-in-the-blank-option"
                    : "fill-in-the-blank-rectangle"
                }
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
