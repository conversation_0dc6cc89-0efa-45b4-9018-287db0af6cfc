// app/lesson/quiz.tsx

"use client";

import { toast } from "sonner";
import Image from "next/image";
import Confetti from "react-confetti";
import { useRouter } from "next/navigation";
import { useCallback, useState, useTransition, useEffect } from "react";
import { useAudio, useWindowSize, useMount } from "react-use";

import { reduceHearts } from "@/actions/user-progress";
import { useHeartsModal } from "@/store/use-hearts-modal";
import { challengeOptions, challenges, userSubscription } from "@/db/schema";
import { usePracticeModal } from "@/store/use-practice-modal";
import { upsertChallengeProgress } from "@/actions/challenge-progress";

import Background from "./bg";
import { Header } from "./header";
import { Footer } from "./footer";
import { Challenge } from "./challenge";
import { Matching } from "./matching";
import { TapWhatYouHear } from "./TapWhatYouHear";
import { ResultCard } from "./result-card";
import { ImageAudioSelectComponent } from "./ImageAudioSelect";
import { DragAndDrop } from "./DragAndDrop";
import { FillInTheBlank } from "./FillInTheBlank";
import { Assist } from "./Assist";
import { SpeakThis } from "./speakThis";
import SpeakThisAdvanced from "./advancedChallenges/speakThisAdvanced";
import { isSequenceInOrder } from "@/lib/utils";

// NEW: Define referrer information type
type ReferrerInfo = {
  from: "coursesplayer" | "learn" | "unknown";
  curriculumId?: string;
} | null;

type Props = {
  initialPercentage: number;
  initialHearts: number;
  initialLessonId: number;
  initialLessonChallenges: (typeof challenges.$inferSelect & {
    completed: boolean;
    challengeOptions: (typeof challengeOptions.$inferSelect & {
      side: "left" | "right";
    })[];
  })[];
  userSubscription:
    | (typeof userSubscription.$inferSelect & {
        isActive: boolean;
      })
    | null;
  referrerInfo?: ReferrerInfo; // NEW: Add referrer information prop
};

export const Quiz = ({
  initialPercentage,
  initialHearts,
  initialLessonId,
  initialLessonChallenges,
  userSubscription,
  referrerInfo = null, // NEW: Default to null for backward compatibility
}: Props) => {
  console.log("Quiz component mounted");
  console.log("DEBUG: Using full viewport height for quiz container");
  console.log("Referrer info received:", referrerInfo); // NEW: Log referrer info

  const { open: openHeartsModal } = useHeartsModal();
  const { open: openPracticeModal } = usePracticeModal();
  const router = useRouter();
  const { width, height } = useWindowSize();

  // Audio hooks
  const [finishAudio, , finishControls] = useAudio({
    src: "/finish.mp3",
  });
  const [correctAudio, , correctControls] = useAudio({
    src: "/correct.wav",
  });
  const [incorrectAudio, , incorrectControls] = useAudio({
    src: "/incorrect.wav",
  });

  const [pending, startTransition] = useTransition();

  // States
  const [lessonId] = useState(initialLessonId);
  const [hearts, setHearts] = useState(initialHearts);
  const [percentage, setPercentage] = useState(() => {
    return initialPercentage === 100 ? 0 : initialPercentage;
  });
  const [challenges] = useState(initialLessonChallenges);
  const [activeIndex, setActiveIndex] = useState(() => {
    const uncompletedIndex = challenges.findIndex((ch) => !ch.completed);
    return uncompletedIndex === -1 ? 0 : uncompletedIndex;
  });
  const [quizCompleted, setQuizCompleted] = useState(false);

  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [selectedPairOption, setSelectedPairOption] = useState<number | null>(
    null
  );
  const [matchedPairs, setMatchedPairs] = useState<Set<number>>(new Set());
  const [status, setStatus] = useState<
    "correct" | "wrong" | "none" | "submitting"
  >("none");

  // State for drag-and-drop selections
  const [dragAndDropSelections, setDragAndDropSelections] = useState<number[]>(
    []
  );
  // New state for tracking drag-and-drop retry attempts
  const [dragAndDropAttempt, setDragAndDropAttempt] = useState(0);

  // NEW: Fallback navigation function for when smart navigation fails
  const handleFallbackNavigation = useCallback(() => {
    console.log("Executing fallback navigation to /learn");
    try {
      router.push("/learn");
    } catch (error) {
      console.error("Fallback navigation failed:", error);
      // Last resort: try window.location
      window.location.href = "/learn";
    }
  }, [router]);

  // NEW: Smart navigation function based on referrer information
  const handleSmartNavigation = useCallback(() => {
    console.log("Attempting smart navigation with referrer:", referrerInfo);

    if (!referrerInfo) {
      console.log("No referrer info, using fallback navigation");
      handleFallbackNavigation();
      return;
    }

    try {
      switch (referrerInfo.from) {
        case "coursesplayer":
          const coursesPlayerUrl = referrerInfo.curriculumId
            ? `/coursesPlayer?curriculumId=${referrerInfo.curriculumId}` // ✅ Correct path
            : "/coursesPlayer"; // ✅ Correct path

          console.log("Navigating back to coursesplayer:", coursesPlayerUrl);
          router.push(coursesPlayerUrl);
          break;

        case "learn":
          console.log("Navigating back to learn page");
          router.push("/learn");
          break;

        case "unknown":
        default:
          console.log("Unknown referrer, using fallback navigation");
          handleFallbackNavigation();
          break;
      }
    } catch (error) {
      console.error("Smart navigation failed:", error);
      handleFallbackNavigation();
    }
  }, [referrerInfo, router, handleFallbackNavigation]);

  useMount(() => {
    if (initialPercentage === 100) {
      openPracticeModal();
    }
  });

  // ------------------------------------------------------------------------
  // Common correct and incorrect handlers
  // ------------------------------------------------------------------------
  const handleCorrectAnswer = useCallback(() => {
    console.log("handleCorrectAnswer called");
    setStatus("correct");
    setPercentage((prev) => prev + 100 / challenges.length);
    correctControls.play();
  }, [challenges.length, correctControls]);

  const handleIncorrectAnswer = useCallback(() => {
    console.log("handleIncorrectAnswer called");
    setStatus("wrong");
    incorrectControls.play();
    setSelectedOption(null);
    setSelectedPairOption(null);
    setDragAndDropSelections([]);

    startTransition(() => {
      const currentChallenge = challenges[activeIndex];
      reduceHearts(currentChallenge.id)
        .then((response) => {
          console.log("reduceHearts response:", response);
          if (response?.error === "hearts") {
            openHeartsModal();
            return;
          }
          if (!response?.error) {
            setHearts((prev) => Math.max(prev - 1, 0));
          }
        })
        .catch(() => toast.error("Something went wrong. Please try again."));
    });
  }, [
    activeIndex,
    challenges,
    incorrectControls,
    openHeartsModal,
    startTransition,
  ]);

  // ------------------------------------------------------------------------
  // Matching handler
  // ------------------------------------------------------------------------
  const handleMatching = useCallback(() => {
    console.log(
      "Matching challenge detected:",
      selectedOption,
      selectedPairOption
    );
    if (status === "submitting") {
      console.log("Already submitting, aborting duplicate submission.");
      return;
    }
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];

    if (selectedOption === null || selectedPairOption === null) {
      console.log("One or both options are not selected.");
      return;
    }

    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    const selectedPairOptionObj = options.find(
      (option) => option.id === selectedPairOption
    );

    if (!selectedOptionObj || !selectedPairOptionObj) {
      console.log("Option objects not found.");
      return;
    }

    const isCorrectMatch =
      selectedOptionObj.matchPairId === selectedPairOptionObj.matchPairId;

    if (isCorrectMatch) {
      console.log("Correct match found.");
      const newMatchedPairs = new Set(matchedPairs);
      newMatchedPairs.add(selectedOptionObj.id);
      newMatchedPairs.add(selectedPairOptionObj.id);

      setMatchedPairs(newMatchedPairs);
      setSelectedOption(null);
      setSelectedPairOption(null);

      if (newMatchedPairs.size === options.length) {
        console.log("All pairs matched, submitting progress...");
        setStatus("submitting");
        startTransition(() => {
          upsertChallengeProgress(
            challenge.id,
            selectedOptionObj.id,
            selectedPairOptionObj.id
          )
            .then((response) => {
              if (response?.error === "hearts") {
                openHeartsModal();
                return;
              }
              console.log("Challenge progress submitted successfully.");
              handleCorrectAnswer();
            })
            .catch((error) => {
              console.error("Error in upserting challenge progress:", error);
              toast.error("Something went wrong. Please try again.");
              setStatus("none");
            });
        });
      } else {
        console.log("Not all pairs matched yet. Waiting for remaining pairs.");
        setStatus("none");
      }
    } else {
      console.log("Match incorrect.");
      setStatus("wrong");
      setTimeout(() => {
        setStatus("none");
      }, 1000);
      handleIncorrectAnswer();
    }
  }, [
    activeIndex,
    challenges,
    handleCorrectAnswer,
    handleIncorrectAnswer,
    matchedPairs,
    openHeartsModal,
    selectedOption,
    selectedPairOption,
    startTransition,
    status,
  ]);

  // ------------------------------------------------------------------------
  // useEffect that calls handleMatching
  // ------------------------------------------------------------------------
  // 1️⃣   stay-as-is effect for matching logic
  useEffect(() => {
    if (selectedOption !== null && selectedPairOption !== null) {
      handleMatching();
    }
  }, [selectedOption, selectedPairOption, handleMatching]);

  // 2️⃣   new, dedicated effect for the celebration sound
  useEffect(() => {
    if (quizCompleted) {
      finishControls.play(); // runs exactly once when quizCompleted flips to true
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quizCompleted]); //  ✅ finishControls intentionally omitted

  // ------------------------------------------------------------------------
  // Navigation
  // ------------------------------------------------------------------------
  const onNext = () => {
    console.log("onNext called");
    if (activeIndex + 1 >= challenges.length) {
      console.log("All challenges completed, marking quiz as completed.");
      setQuizCompleted(true);
    } else {
      setActiveIndex((current) => current + 1);
      setMatchedPairs(new Set());
      setSelectedOption(null);
      setSelectedPairOption(null);
      setDragAndDropSelections([]);
      setStatus("none");
      console.log("Moved to next challenge. Reset matched pairs.");
    }
  };

  // ------------------------------------------------------------------------
  // onSelect logic for selecting or matching items
  // ------------------------------------------------------------------------
  const onSelect = (
    id: number | null,
    side: "left" | "right" | "drag-and-drop" | "fill-in-the-blank"
  ) => {
    console.log("onSelect called with id:", id, "and side:", side);
    if (status !== "none") return;

    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];

    if (side === "left") {
      setSelectedOption((prev) => (prev === id ? null : id));
    } else if (side === "right") {
      setSelectedPairOption((prev) => (prev === id ? null : id));
    } else if (side === "drag-and-drop") {
      if (id === null) return;
      setDragAndDropSelections((prevSelections) => {
        console.log("Previous dragAndDropSelections:", prevSelections);
        let newSelections;
        if (prevSelections.includes(id)) {
          newSelections = prevSelections.filter(
            (selection) => selection !== id
          );
        } else {
          newSelections = [...prevSelections, id];
        }
        console.log("Updated dragAndDropSelections:", newSelections);
        return newSelections;
      });
    } else if (side === "fill-in-the-blank") {
      setSelectedOption((prev) => (prev === id ? null : id));
    }
  };

  // ------------------------------------------------------------------------
  // The main continue action in the quiz
  // ------------------------------------------------------------------------
  const onContinue = () => {
    console.log("onContinue called");
    console.log("Current state before onContinue:", {
      selectedOption,
      selectedPairOption,
      status,
      hearts,
      matchedPairs,
    });

    if (status === "wrong") {
      console.log("Status was wrong, resetting state.");
      resetState();
      return;
    }

    if (status === "correct") {
      console.log("Status was correct, moving to next.");
      onNext();
      return;
    }

    const challenge = challenges[activeIndex];
    switch (challenge.type) {
      case "MATCHING":
        handleMatching();
        break;
      case "TAP_WHAT_YOU_HEAR":
        handleTapWhatYouHear();
        break;
      case "ASSIST":
        handleAssist();
        break;
      case "SELECT":
        handleSelect();
        break;
      case "IMAGE_AUDIO_SELECT":
        handleImageAudioSelect();
        break;
      case "DRAG_AND_DROP":
        handleDragAndDrop();
        break;
      case "FILL_IN_THE_BLANK":
        handleFillInTheBlank();
        break;
      case "SPEAK_THIS":
        handleSpeakThis();
        break;
      case "SPEAK_THIS_ADVANCED":
        console.log(
          "onContinue called for SPEAK_THIS_ADVANCED, but logic is handled by child callback."
        );
        break;
      default:
        console.log("Unknown challenge type:", challenge.type);
    }
  };

  const resetState = () => {
    console.log("resetState called");
    // If current challenge is DRAG_AND_DROP, increment the attempt counter to trigger reshuffling in DragAndDrop component
    const currentChallenge = challenges[activeIndex];
    if (currentChallenge.type === "DRAG_AND_DROP") {
      setDragAndDropAttempt((prev) => prev + 1);
    }
    setStatus("none");
    setSelectedOption(null);
    setSelectedPairOption(null);
    setDragAndDropSelections([]);
    setMatchedPairs(new Set());
    console.log("State reset for retry");
  };

  // ------------------------------------------------------------------------
  // Challenge-specific Handlers
  // ------------------------------------------------------------------------
  const handleTapWhatYouHear = () => {
    console.log("Tap What You Hear challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    if (!selectedOptionObj) return;

    const correctOption = options.find((option) => option.correct);
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, selectedOption as number)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            correctControls.play();
            setStatus("correct");
            setPercentage((prev) => prev + 100 / challenges.length);
            setHearts((prev) => Math.min(prev + 1, 5));
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    } else {
      startTransition(() => {
        reduceHearts(challenge.id)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            incorrectControls.play();
            setStatus("wrong");
            if (!response?.error) {
              setHearts((prev) => Math.max(prev - 1, 0));
            }
          })
          .catch((error) => {
            console.error("Error in reduceHearts:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    }
  };

  const handleAssist = () => {
    console.log("Assist challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    if (!selectedOptionObj) return;

    const correctOption = options.find((option) => option.correct);
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, selectedOption as number)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            correctControls.play();
            setStatus("correct");
            setPercentage((prev) => prev + 100 / challenges.length);
            setHearts((prev) => Math.min(prev + 1, 5));
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    } else {
      startTransition(() => {
        reduceHearts(challenge.id)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            incorrectControls.play();
            setStatus("wrong");
            if (!response?.error) {
              setHearts((prev) => Math.max(prev - 1, 0));
            }
          })
          .catch((error) => {
            console.error("Error in reduceHearts:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    }
  };

  const handleSelect = () => {
    console.log("Select challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    if (!selectedOptionObj) return;

    const correctOption = options.find((option) => option.correct);
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, selectedOption as number)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            correctControls.play();
            setStatus("correct");
            setPercentage((prev) => prev + 100 / challenges.length);
            setHearts((prev) => Math.min(prev + 1, 5));
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    } else {
      startTransition(() => {
        reduceHearts(challenge.id)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            incorrectControls.play();
            setStatus("wrong");
            if (!response?.error) {
              setHearts((prev) => Math.max(prev - 1, 0));
            }
          })
          .catch((error) => {
            console.error("Error in reduceHearts:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    }
  };

  const handleImageAudioSelect = () => {
    console.log("Image and Audio challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    if (!selectedOptionObj) return;

    const correctOption = options.find((option) => option.correct);
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, selectedOption as number)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            correctControls.play();
            setStatus("correct");
            setPercentage((prev) => prev + 100 / challenges.length);
            setHearts((prev) => Math.min(prev + 1, 5));
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    } else {
      startTransition(() => {
        reduceHearts(challenge.id)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            incorrectControls.play();
            setStatus("wrong");
            if (!response?.error) {
              setHearts((prev) => Math.max(prev - 1, 0));
            }
          })
          .catch((error) => {
            console.error("Error in reduceHearts:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    }
  };

  const handleDragAndDrop = () => {
    console.log("Drag and Drop challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const sequences = dragAndDropSelections
      .map((id) => options.find((option) => option.id === id)?.sequence ?? 0)
      .filter((seq) => seq > 0);

    console.log("Sequences in drop zone:", sequences);
    const challengeZoneSequences = options
      .filter((option) => !dragAndDropSelections.includes(option.id))
      .map((option) => option.sequence)
      .filter((seq): seq is number => seq !== null);

    console.log("Sequences in challenge zone:", challengeZoneSequences);

    const isCorrectOrder = isSequenceInOrder(sequences, challengeZoneSequences);
    const hasIncorrectOption = challengeZoneSequences.some((seq) => seq !== 99);

    if (hasIncorrectOption) {
      console.log("Challenge zone contains incorrect options.");
      setStatus("wrong");
      handleIncorrectAnswer();
      return;
    }

    if (isCorrectOrder && !hasIncorrectOption) {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, dragAndDropSelections.join(","))
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            correctControls.play();
            setStatus("correct");
            setPercentage((prev) => prev + 100 / challenges.length);
            setHearts((prev) => Math.min(prev + 1, 5));
          })
          .catch((error) => {
            console.error("Error updating challenge progress:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    } else {
      console.log("Wrong order selected.");
      setStatus("wrong");
      handleIncorrectAnswer();
    }
  };

  const handleFillInTheBlank = () => {
    console.log("Fill In The Blank challenge detected");
    const challenge = challenges[activeIndex];
    const options = challenge?.challengeOptions ?? [];
    const selectedOptionObj = options.find(
      (option) => option.id === selectedOption
    );
    if (!selectedOptionObj) return;

    const correctOption = options.find((option) => option.correct);
    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      startTransition(() => {
        upsertChallengeProgress(challenge.id, selectedOption as number)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            correctControls.play();
            setStatus("correct");
            setPercentage((prev) => prev + 100 / challenges.length);
            setHearts((prev) => Math.min(prev + 1, 5));
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    } else {
      startTransition(() => {
        reduceHearts(challenge.id)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            incorrectControls.play();
            setStatus("wrong");
            if (!response?.error) {
              setHearts((prev) => Math.max(prev - 1, 0));
            }
          })
          .catch((error) => {
            console.error("Error in reduceHearts:", error);
            toast.error("Something went wrong. Please try again.");
          });
      });
    }
  };

  const handleSpeakThis = () => {
    console.log("Speak This challenge detected");
    const challenge = challenges[activeIndex];
    startTransition(() => {
      upsertChallengeProgress(challenge.id, 1)
        .then((response) => {
          if (response?.error === "hearts") {
            openHeartsModal();
            return;
          }
          correctControls.play();
          setStatus("correct");
          setPercentage((prev) => prev + 100 / challenges.length);
          setHearts((prev) => Math.min(prev + 1, 5));
        })
        .catch((error) => {
          console.error("Error in upsertChallengeProgress:", error);
          toast.error("Something went wrong. Please try again.");
        });
    });
  };

  const handleSpeakThisAdvancedResult = (isCorrect: boolean) => {
    console.log("Speak This Advanced result received from child:", isCorrect);

    const challenge = challenges[activeIndex];
    if (isCorrect) {
      console.log("SpeakThisAdvanced: user is correct, upserting progress...");
      startTransition(() => {
        upsertChallengeProgress(challenge.id, 1)
          .then((response) => {
            if (response?.error === "hearts") {
              openHeartsModal();
              return;
            }
            console.log("SpeakThisAdvanced progress submitted successfully.");
            handleCorrectAnswer();
          })
          .catch((error) => {
            console.error("Error in upsertChallengeProgress:", error);
            toast.error("Something went wrong. Please try again.");
            setStatus("none");
          });
      });
    } else {
      console.log("SpeakThisAdvanced: user is incorrect, reducing hearts...");
      handleIncorrectAnswer();
    }
  };

  if (quizCompleted) {
    return (
      <Background>
        <div className="h-screen flex flex-col overflow-hidden">
          {finishAudio ? <>{finishAudio}</> : null}
          <Confetti
            width={width}
            height={height}
            recycle={false}
            numberOfPieces={500}
            tweenDuration={10000}
          />

          <div className="flex-1 overflow-y-auto flex flex-col gap-y-4 lg:gap-y-8 items-center justify-center text-center">
            <Image
              src="/finish.svg"
              alt="Finish"
              className="hidden lg:block"
              height={100}
              width={100}
            />
            <Image
              src="/finish.svg"
              alt="Finish"
              className="block lg:hidden"
              height={50}
              width={50}
            />
            <h1 className="text-xl lg:text-3xl font-bold text-neutral-700">
              Great job! <br /> You&apos;ve completed the lesson.
            </h1>
            <div className="flex items-center gap-x-4 w-full max-w-sm mx-auto">
              <ResultCard variant="points" value={challenges.length * 10} />
              <ResultCard variant="hearts" value={hearts} />
            </div>
          </div>

          <Footer
            lessonId={lessonId}
            status="completed"
            onCheck={handleSmartNavigation} // NEW: Use smart navigation instead of hardcoded /learn
            referrerInfo={referrerInfo} // NEW: Pass referrer information to Footer
            onSmartNavigate={handleSmartNavigation} // NEW: Pass smart navigation handler to Footer
          />
        </div>
      </Background>
    );
  }

  const currentChallenge = challenges[activeIndex];
  const options = currentChallenge?.challengeOptions ?? [];
  const title =
    currentChallenge.type === "ASSIST"
      ? "Which letter is missing from this word in the audio?"
      : `Q: ${currentChallenge.question}`;

  return (
    <Background>
      {incorrectAudio ? <>{incorrectAudio}</> : null}
      {correctAudio ? <>{correctAudio}</> : null}

      <div className="h-screen flex flex-col overflow-hidden relative">
        <Header
          hearts={hearts}
          percentage={percentage}
          hasActiveSubscription={!!userSubscription?.isActive}
          referrerInfo={referrerInfo} // NEW: Pass referrer information to Header
          onNavigateBack={handleSmartNavigation} // NEW: Pass smart navigation function to Header
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            width: "100%",
            zIndex: 50,
          }}
        />

        {/* Main content container adjusted to center the challenge */}
        <div
          className="flex-1 flex items-center justify-center overflow-y-auto bg-transparent"
          style={{ marginTop: "80px", marginBottom: "80px" }}
        >
          <div className="flex items-center justify-center px-6 lg:px-0 py-6 w-full max-w-[900px] flex-col gap-y-12 bg-transparent">
            {currentChallenge.type !== "DRAG_AND_DROP" && (
              <>
                {console.log(
                  "quiz.tsx: Rendering central question for challenge type:",
                  currentChallenge.type
                )}
                <h1 className="text-xl lg:text-2xl lg:text-start font-bold text-neutral-700">
                  {title}
                </h1>
              </>
            )}

            <div className="w-full flex flex-col pb-8 lg:pb-12">
              {currentChallenge.type === "FILL_IN_THE_BLANK" ? (
                <FillInTheBlank
                  options={options}
                  onSelect={(id) => onSelect(id, "fill-in-the-blank")}
                  status={status}
                  selectedOptions={selectedOption ? [selectedOption] : []}
                  disabled={pending}
                  sentence={currentChallenge.sentence || ""}
                  question={currentChallenge.question}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc}
                  mediaType={currentChallenge.mediaType}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  optionsStacked={!!currentChallenge.optionsStacked}
                />
              ) : currentChallenge.type === "ASSIST" ? (
                <Assist
                  options={options}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc}
                  sentence={currentChallenge.sentence}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                />
              ) : currentChallenge.type === "MATCHING" ? (
                <Matching
                  options={options}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  selectedPairOption={selectedPairOption}
                  disabled={pending}
                  matchedPairs={matchedPairs}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  sentence={currentChallenge.sentence}
                />
              ) : currentChallenge.type === "TAP_WHAT_YOU_HEAR" ? (
                <TapWhatYouHear
                  options={options}
                  onSelect={(id) => onSelect(id, "left")}
                  selectedOption={selectedOption}
                  status={status}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc}
                  sentence={currentChallenge.sentence || ""}
                />
              ) : currentChallenge.type === "IMAGE_AUDIO_SELECT" ? (
                <ImageAudioSelectComponent
                  options={options}
                  onSelect={(id) => onSelect(id, "left")}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={{
                    audioSrc: currentChallenge.audioSrc || "",
                    text: currentChallenge.question,
                  }}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  sentence={currentChallenge.sentence}
                />
              ) : currentChallenge.type === "DRAG_AND_DROP" ? (
                <DragAndDrop
                  options={options}
                  onSelect={(result) => {
                    if (Array.isArray(result)) {
                      setDragAndDropSelections(result);
                      if (status === "correct" || status === "wrong") {
                        return;
                      }
                    }
                  }}
                  status={status}
                  disabled={pending}
                  question={{
                    audioSrc: currentChallenge.audioSrc || "",
                    text: currentChallenge.question,
                  }}
                  type={currentChallenge.type}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  sentence={currentChallenge.sentence}
                  attempt={dragAndDropAttempt}
                />
              ) : currentChallenge.type === "SPEAK_THIS" ? (
                <SpeakThis
                  options={options}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc || ""}
                  sentence={currentChallenge.sentence || ""}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                />
              ) : currentChallenge.type === "SPEAK_THIS_ADVANCED" ? (
                <SpeakThisAdvanced
                  options={options}
                  onSelect={onSelect}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  audioSrc={currentChallenge.audioSrc || ""}
                  sentence={currentChallenge.sentence || ""}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  topCardText={currentChallenge.topCardText}
                  topCardAudio={currentChallenge.topCardAudio}
                  onSpeakThisAdvancedResult={handleSpeakThisAdvancedResult}
                />
              ) : (
                <Challenge
                  options={options}
                  onSelect={(id) => onSelect(id, "left")}
                  status={status}
                  selectedOption={selectedOption}
                  disabled={pending}
                  type={currentChallenge.type}
                  question={currentChallenge.question}
                  mediaType={currentChallenge.mediaType}
                  mediaUrl={currentChallenge.mediaUrl}
                  audioSrc={currentChallenge.audioSrc}
                  sentence={currentChallenge.sentence}
                />
              )}
            </div>
          </div>
        </div>

        <Footer
          disabled={
            pending ||
            (currentChallenge.type === "DRAG_AND_DROP"
              ? dragAndDropSelections.length === 0
              : !selectedOption &&
                currentChallenge.type !== "SPEAK_THIS_ADVANCED" &&
                currentChallenge.type !== "SPEAK_THIS")
          }
          status={status}
          onCheck={onContinue}
          lessonId={lessonId}
          referrerInfo={referrerInfo} // NEW: Pass referrer information to Footer
          onSmartNavigate={handleSmartNavigation} // NEW: Pass smart navigation handler to Footer
          style={{
            position: "fixed",
            bottom: 0,
            left: 0,
            right: 0,
            width: "100%",
            zIndex: 50,
          }}
        />
      </div>
    </Background>
  );
};
