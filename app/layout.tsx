import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Toaster } from "@/components/ui/sonner";
import { ExitModal } from "@/components/modals/exit-modal";
import { HeartsModal } from "@/components/modals/hearts-modal";
import { PracticeModal } from "@/components/modals/practice-modal";
import Script from "next/script";
import "./globals.css";
import { EventProvider } from "@/app/(main)/schedule/EventContext";
import { UnreadCountsProvider } from "@/app/context/UnreadCountsContext";
import { Providers } from "./providers";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // No calls to auth() or getOrCreateUser()

  return (
    <ClerkProvider signInUrl="/" signUpUrl="/">
      <Providers>
        <EventProvider>
          <UnreadCountsProvider>
            <html lang="en">
              <head>
                <Script
                  src="https://meet.jit.si/external_api.js"
                  strategy="afterInteractive"
                />
              </head>
              <body className="font-sans">
                <Toaster />
                <ExitModal />
                <HeartsModal />
                <PracticeModal />
                {children}
              </body>
            </html>
          </UnreadCountsProvider>
        </EventProvider>
      </Providers>
    </ClerkProvider>
  );
}
