// app/nooraniQadia/ChapterViewerClient.tsx

"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import ChapterPage from "../virtualClassroom/components/ChapterPage";
import { type Square } from "../virtualClassroom/components/ChapterPage";

// Define the shape of the data we expect from the server
interface ChapterData {
  id: number;
  title: string;
  order: number;
  squares: Square[];
}

interface ChapterViewerClientProps {
  initialChapterData: ChapterData;
}

export default function ChapterViewerClient({
  initialChapterData,
}: ChapterViewerClientProps) {
  const router = useRouter();

  // The state now comes from the initial data passed by the server component.
  // Note: We don't need to manage currentChapter in state anymore because the page will
  // re-render with new data when the URL changes.
  const [selectedSquareId, setSelectedSquareId] = useState<number | null>(null);
  const [hoveredSquareId, setHoveredSquareId] = useState<number | null>(null);

  // This handler now uses the router to trigger a full page reload with new data
  const handleChapterChange = (newChapterOrder: number) => {
    // This changes the URL's search parameter, causing Next.js to re-run the
    // server-side data fetching on page.tsx
    router.push(`/nooraniQadia?chapter=${newChapterOrder}`);
  };

  // Handler for square selection (client-side state)
  const handleSquareSelected = (squareId: number | null) => {
    setSelectedSquareId(squareId);
  };

  // Handler for square hover (client-side state)
  const handleSquareHover = (squareId: number | null) => {
    setHoveredSquareId(squareId);
  };

  return (
    // This new div creates a stable flex container that grows to fill its parent.
    // This prevents the layout from collapsing when ChapterPage's content changes.
    <div className="flex-1 flex flex-col min-h-0">
      <ChapterPage
        key={initialChapterData.order} // Use key to force re-mount on chapter change
        title={initialChapterData.title}
        squares={initialChapterData.squares}
        order={initialChapterData.order}
        onChapterChange={handleChapterChange} // This will now navigate the page
        selectedSquareId={selectedSquareId}
        onSquareSelected={handleSquareSelected}
        hoveredSquareId={hoveredSquareId}
        onSquareHover={handleSquareHover}
        isTeacher={true} // Example prop, can be passed from server if needed
        allowDrawing={true} // Example prop
      />
    </div>
  );
}
