// app/nooraniQadia/page.tsx

import { auth } from "@clerk/nextjs/server";
import { getOrCreateUser } from "@/lib/users";
import { getChapterWithSquaresByOrder } from "@/db/queries";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import nextDynamic from "next/dynamic";

// Dynamically import the client component
const ChapterViewerClient = nextDynamic(() => import("./ChapterViewerClient"), {
  ssr: false,
});

// This page is a Server Component, so it can be async
export default async function NooraniQaidaPage({
  searchParams,
}: {
  searchParams: { chapter?: string };
}) {
  const { userId } = auth();
  if (userId) {
    await getOrCreateUser();
  }

  const chapterOrder = searchParams.chapter
    ? parseInt(searchParams.chapter, 10)
    : 1;

  const chapterData = await getChapterWithSquaresByOrder(chapterOrder);

  if (!chapterData) {
    return (
      <div className="h-screen w-screen flex flex-col items-center justify-center">
        <h1 className="text-2xl font-bold">Chapter not found</h1>
        <Link href="/dashboard" className="text-blue-500 hover:underline mt-4">
          Return to Dashboard
        </Link>
      </div>
    );
  }

  return (
    <div
      className="h-screen w-screen flex flex-col text-sm overflow-hidden"
      style={{
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
    >
      {/* Header */}
      <header className="bg-white border-b border-gray-200 h-16 flex-shrink-0">
        <div className="h-full px-6 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard"
              className="flex items-center justify-center w-9 h-9 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300"
              title="Return to Dashboard"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </Link>
            <div className="h-8 w-px bg-gray-200"></div>
            <div className="flex items-center">
              <h1 className="text-2xl font-semibold text-neutral-800 tracking-wide">
                Iqra.
              </h1>
            </div>
          </div>
          <div className="flex items-center gap-4"></div>
        </div>
      </header>

      {/* Main layout - Centered */}
      <div className="flex-1 p-4 flex justify-center items-center overflow-hidden">
        {/* Chapter Content */}
        <div className="relative w-full h-full md:w-2/3 bg-white rounded-2xl shadow-sm border border-gray-200 p-4 flex flex-col min-h-0">
          <ChapterViewerClient initialChapterData={chapterData} />
        </div>
      </div>
    </div>
  );
}
