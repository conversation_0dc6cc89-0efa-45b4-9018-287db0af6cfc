import { google } from "googleapis";
import { JWT } from "google-auth-library";
import fs from "fs";
import path from "path";

// Path to your service account key JSON file
const serviceAccountKeyPath = path.join(
  process.cwd(),
  "google-service-account.json"
);
const credentials = JSON.parse(fs.readFileSync(serviceAccountKeyPath, "utf-8"));

// Google Calendar API setup
const calendar = google.calendar({ version: "v3" });
const SCOPES = ["https://www.googleapis.com/auth/calendar"];

// Set up authentication with the service account
const auth = new JWT({
  email: credentials.client_email,
  key: credentials.private_key.replace(/\\n/g, "\n"),
  scopes: SCOPES,
});

/**
 * Function to create a new Google Calendar event
 */
export const createGoogleEvent = async (eventData: {
  title: string;
  description: string;
  startTime: string;
  endTime: string;
}) => {
  try {
    const response = await calendar.events.insert({
      auth,
      calendarId: "primary", // Or use a specific calendar ID
      requestBody: {
        summary: eventData.title,
        description: eventData.description,
        start: {
          dateTime: eventData.startTime,
          timeZone: "UTC",
        },
        end: {
          dateTime: eventData.endTime,
          timeZone: "UTC",
        },
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error creating Google Calendar event:", error);
    throw new Error("Unable to create event");
  }
};
