import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function absoluteUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_APP_URL}${path}`;
}

/**
 * Shuffles an array using the Fisher-Yates algorithm.
 * @param array The array to shuffle.
 * @returns The shuffled array.
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffledArray = array.slice(); // Create a copy of the array
  for (let i = shuffledArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffledArray[i], shuffledArray[j]] = [shuffledArray[j], shuffledArray[i]];
  }
  return shuffledArray;
}

/**
 * Checks if the sequences are in order and ensures only elements with sequence 99 are left in the challenge zone.
 * @param sequences The sequences to check.
 * @param challengeZoneSequences The sequences left in the challenge zone.
 * @returns True if sequences are in order and only 99s are left in the challenge zone, false otherwise.
 */
export const isSequenceInOrder = (
  sequences: number[],
  challengeZoneSequences: number[]
): boolean => {
  // Check if the array is empty or has only one element
  if (sequences.length <= 1) {
    return true;
  }

  // Loop through the array and compare each element with the next one
  for (let i = 1; i < sequences.length; i++) {
    if (sequences[i] < sequences[i - 1]) {
      return false;
    }
  }

  // Check that only sequence 99 is left in the challenge zone
  const hasOnly99Left = challengeZoneSequences.every((seq) => seq === 99);

  return hasOnly99Left;
};
