import axios from "axios";

// Base URL for the API
const BASE_URL = "http://api.alquran.cloud/v1/surah/";

// Fetch function for a specific Surah with optional edition
export const fetchSurah = async (surahNumber, edition = "") => {
  try {
    // Build the full URL with optional edition
    const url = edition
      ? `${BASE_URL}${surahNumber}/${edition}`
      : `${BASE_URL}${surahNumber}`;
    const response = await axios.get(url);

    // Handle the response data
    return response.data;
  } catch (error) {
    console.log("We found an Error:", error);
    throw error; // Rethrow the error to handle it in the calling component
  }
};
