/**
 * responsiveUtilities.ts
 *
 * A simple utility file containing responsive Tailwind class strings
 * for different screen sizes. You can import these named exports and
 * apply them to your components' className props as needed.
 */

//
// Layout containers
//
export const layoutContainer = `
  w-full
  min-h-screen
  flex
  flex-col
  bg-gray-50
`;

export const layoutHeader = `
  h-16
  w-full
  flex-shrink-0
  bg-white
  border-b
  border-gray-200
  flex
  items-center
  justify-between
  px-6
`;

export const mainContentWrapper = `
  flex-1
  flex
  flex-col
  md:flex-row
  p-2
  gap-2
  overflow-hidden
`;

//
// Text & Headings
//
export const headingLarge = `
  text-2xl
  sm:text-3xl
  md:text-4xl
  lg:text-5xl
  font-bold
`;

export const headingMedium = `
  text-xl
  sm:text-2xl
  md:text-3xl
  font-semibold
`;

export const paragraphText = `
  text-base
  sm:text-lg
  md:text-xl
  text-gray-700
  leading-relaxed
`;

//
// Grids & Panels
//
export const squaresGrid = `
  grid
  grid-cols-2     /* 2 columns on very small screens */
  sm:grid-cols-3  /* 3 columns on small+ screens */
  md:grid-cols-5  /* 5 columns on medium+ screens */
  gap-2
  p-2
`;

export const panelClass = `
  w-full
  md:w-1/2
  bg-white
  rounded-xl
  shadow-sm
  border
  border-gray-200
  p-4
  flex
  flex-col
`;

/**
 * Feel free to add as many exports here as you need for your project.
 * For instance, if you want different text-sizes or custom breakpoints,
 * just define more named exports or expand the existing ones.
 */
