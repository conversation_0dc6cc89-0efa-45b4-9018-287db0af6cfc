import { auth, clerkClient } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";

type UserRole = "teacher" | "student";

interface UserRecord {
  userId: string;
  email: string;
  role: UserRole;
  displayName?: string;
  createdAt?: Date;
  schoolUsername?: string | null;
  schoolPassword?: string | null;
}

/**
 * Gets an existing user from the DB or creates them as a "student" by default if they don't exist.
 */
export async function getOrCreateUser(): Promise<UserRecord | null> {
  console.log("[getOrCreateUser] Function called.");

  const { userId } = auth();
  console.log("[getOrCreateUser] Authenticated userId:", userId);

  if (!userId) {
    console.error("[getOrCreateUser] No userId found. User is not logged in.");
    return null;
  }

  try {
    // Use select builder for Postgres-JS adapter
    const existing = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId));
    console.log(
      "[getOrCreateUser] DB query executed. Existing rows:",
      existing.length
    );

    if (existing.length > 0) {
      const row = existing[0];
      console.log(
        "[getOrCreateUser] User exists. Returning existing user data.",
        row
      );
      return {
        userId: row.userId,
        email: row.email,
        role: row.role as UserRole,
        displayName: row.displayName ?? undefined,
        createdAt: row.createdAt ?? undefined,
        schoolUsername: row.schoolUsername ?? null,
        schoolPassword: row.schoolPassword ?? null,
      };
    }

    console.log("[getOrCreateUser] User does not exist. Fetching from Clerk.");
    const clerkUser = await clerkClient.users.getUser(userId);
    console.log("[getOrCreateUser] Clerk user data retrieved:", clerkUser);

    const email =
      clerkUser?.emailAddresses?.[0]?.emailAddress || "<EMAIL>";
    const displayName = clerkUser?.fullName || "";

    console.log("[getOrCreateUser] Inserting new user into DB:", {
      userId,
      email,
      role: "student",
      displayName,
    });
    const inserted = await db
      .insert(users)
      .values({ userId, email, role: "student", displayName })
      .returning();
    console.log(
      "[getOrCreateUser] Insert operation completed. Inserted data:",
      inserted
    );

    const [created] = inserted;
    return {
      userId: created.userId,
      email: created.email,
      role: created.role as UserRole,
      displayName: created.displayName ?? undefined,
      createdAt: created.createdAt ?? undefined,
      schoolUsername: created.schoolUsername ?? null,
      schoolPassword: created.schoolPassword ?? null,
    };
  } catch (error) {
    console.error("[getOrCreateUser] Error occurred:", error);
    throw error;
  }
}

/**
 * Retrieves the current user's role from the DB.
 */
export async function getUserRole(): Promise<UserRole | null> {
  console.log("[getUserRole] Function called.");

  const { userId } = auth();
  console.log("[getUserRole] Authenticated userId:", userId);

  if (!userId) {
    return null;
  }

  try {
    const rows = await db
      .select({ role: users.role })
      .from(users)
      .where(eq(users.userId, userId));
    console.log("[getUserRole] Rows fetched:", rows.length);

    if (rows.length === 0) {
      return null;
    }

    return rows[0].role as UserRole;
  } catch (error) {
    console.error("[getUserRole] Error:", error);
    throw error;
  }
}

/**
 * Checks if the current user is a teacher.
 */
export async function isTeacher(): Promise<boolean> {
  try {
    const role = await getUserRole();
    return role === "teacher";
  } catch {
    return false;
  }
}

/**
 * Ensures a user by a known userId exists in the DB.
 */
export async function createUserIfMissing(
  userId: string,
  defaultRole: UserRole = "student"
): Promise<UserRecord | null> {
  console.log("[createUserIfMissing] Called with userId:", userId);

  if (!userId) return null;

  try {
    const existing = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId));
    console.log("[createUserIfMissing] Existing rows:", existing.length);

    if (existing.length > 0) {
      const row = existing[0];
      return {
        userId: row.userId,
        email: row.email,
        role: row.role as UserRole,
        displayName: row.displayName ?? undefined,
        createdAt: row.createdAt ?? undefined,
        schoolUsername: row.schoolUsername ?? null,
        schoolPassword: row.schoolPassword ?? null,
      };
    }

    const clerkUser = await clerkClient.users.getUser(userId);
    const email =
      clerkUser?.emailAddresses?.[0]?.emailAddress || "<EMAIL>";
    const displayName = clerkUser?.fullName || "";

    const inserted = await db
      .insert(users)
      .values({ userId, email, role: defaultRole, displayName })
      .returning();
    const [created] = inserted;
    return {
      userId: created.userId,
      email: created.email,
      role: created.role as UserRole,
      displayName: created.displayName ?? undefined,
      createdAt: created.createdAt ?? undefined,
      schoolUsername: created.schoolUsername ?? null,
      schoolPassword: created.schoolPassword ?? null,
    };
  } catch (error) {
    console.error("[createUserIfMissing] Error:", error);
    return null;
  }
}
