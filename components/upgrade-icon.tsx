// file: components/upgrade-icon.tsx

import * as React from "react";

export const UpgradeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="18px"
    height="18px"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props} // Allows passing className, etc.
  >
    <rect
      width="16"
      height="16"
      rx="8"
      fill="url(#paint0_linear_upgrade_icon)"
    />
    <path
      d="M8.66725 4.80043C8.66725 4.30571 8.02563 4.11145 7.75121 4.52307L5.0626 8.55599C4.84108 8.88827 5.07928 9.33335 5.47863 9.33335H7.3339V11.1996C7.3339 11.6943 7.97552 11.8886 8.24994 11.4769L10.9385 7.44401C11.1601 7.11173 10.9219 6.66665 10.5225 6.66665H8.66725V4.80043Z"
      fill="white"
    />
    <defs>
      <linearGradient
        id="paint0_linear_upgrade_icon" // Unique ID to avoid conflicts
        x1="8"
        y1="0"
        x2="8"
        y2="16"
        gradientUnits="userSpaceOnUse"
      >
        {/* Using Tailwind's gray colors for consistency with your theme */}
        <stop stopColor="#4B5563" /> {/* gray-600 */}
        <stop offset="1" stopColor="#1F2937" /> {/* gray-800 */}
      </linearGradient>
    </defs>
  </svg>
);
