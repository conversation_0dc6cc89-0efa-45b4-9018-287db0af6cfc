// components/CustomCursor.tsx
"use client";

import { useEffect, useState } from "react";
import { Motion } from "@/components/motion-wrapper";

// For hooks like useMotionValue, we need a different approach
const useMotionValue = (initialValue: number) => {
  const [value, setValue] = useState(initialValue);
  return {
    get: () => value,
    set: setValue,
  };
};

/**
 * How big (in CSS pixels) the cursor should appear on-screen,
 * regardless of the native size of Cursor.svg in /public.
 */
const CURSOR_SIZE = 60;

/**
 * Hot-spot tweak: shift the graphic so the arrow’s tip—not its
 * top-left corner—is the actual click point.  Adjust to taste.
 */
const HOTSPOT_OFFSET = 4;

export default function CustomCursor() {
  // Track the raw mouse position — no spring / smoothing
  const x = useMotionValue(-100);
  const y = useMotionValue(-100);

  useEffect(() => {
    const handleMove = (e: MouseEvent) => {
      x.set(e.clientX - HOTSPOT_OFFSET);
      y.set(e.clientY - HOTSPOT_OFFSET);
    };
    window.addEventListener("mousemove", handleMove);
    return () => window.removeEventListener("mousemove", handleMove);
  }, [x, y]);

  return (
    <>
      {/* Hide the system cursor everywhere */}
      <style jsx global>{`
        * {
          cursor: none !important;
        }
      `}</style>

      <Motion.div
        style={{ translateX: x, translateY: y }}
        className="
          fixed left-0 top-0
          pointer-events-none select-none
          z-[2147483647]              /* practically infinite z-index */
        "
      >
        <img
          src="/Cursor.svg" /* your large SVG in /public */
          alt=""
          width={CURSOR_SIZE}
          height={CURSOR_SIZE}
          draggable={false}
        />
      </Motion.div>
    </>
  );
}
