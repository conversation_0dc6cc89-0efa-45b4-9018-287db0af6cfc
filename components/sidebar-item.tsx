"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Manrope } from "next/font/google";

const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
});

type Props = {
  label: string;
  href: string;
  children: React.ReactNode;
  badge?: number;
  isCollapsed: boolean;
};

export const SidebarItem = ({
  label,
  href,
  children,
  badge,
  isCollapsed,
}: Props) => {
  const pathname = usePathname();
  const active =
    href === "/dashboard" ? pathname === href : pathname.startsWith(href);

  return (
    <Button
      variant="ghost"
      className={cn(
        "w-full h-11 px-3 justify-start font-medium text-sm transition-all duration-200 group",
        "text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",
        "hover:bg-gray-50 dark:hover:bg-gray-900/50",
        active &&
          "bg-[#1a1a18] text-white hover:bg-[#1a1a18] hover:text-white shadow-sm",
        "focus:outline-none",
        isCollapsed && "justify-center"
      )}
      asChild
    >
      <Link href={href} className={cn("relative", manrope.variable)}>
        <div
          className={cn(
            "flex w-full items-center",
            isCollapsed ? "justify-center" : "gap-x-3"
          )}
        >
          {/* Icon Container */}
          <div
            className={cn(
              "flex items-center justify-center flex-shrink-0 transition-all duration-200",
              isCollapsed ? "w-6 h-6" : "w-4 h-4"
            )}
          >
            {React.isValidElement(children)
              ? React.cloneElement(children as React.ReactElement, {
                  className: cn(
                    "transition-all duration-200",
                    isCollapsed ? "h-6 w-6" : "h-4 w-4",
                    active ? "text-white" : "text-current"
                  ),
                  "aria-hidden": "true",
                })
              : null}
          </div>

          {/* Label */}
          {!isCollapsed && (
            <span
              className={cn(
                "font-sans flex-grow truncate text-left text-sm font-medium transition-colors duration-200 min-w-0",
                active ? "text-white" : "text-current"
              )}
            >
              {label}
            </span>
          )}

          {/* Badge */}
          {!isCollapsed && badge !== undefined && badge > 0 && (
            <span
              className={cn(
                "font-sans ml-auto flex-shrink-0 rounded-full px-2 py-0.5 text-[10px] font-bold transition-all duration-200 min-w-[18px] h-[18px] flex items-center justify-center",
                active
                  ? "bg-white/20 text-white ring-1 ring-white/30"
                  : "bg-red-500 text-white shadow-sm group-hover:bg-red-600"
              )}
            >
              {badge > 9 ? "9+" : badge}
            </span>
          )}
        </div>
      </Link>
    </Button>
  );
};
