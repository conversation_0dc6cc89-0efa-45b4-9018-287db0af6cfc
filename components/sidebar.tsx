"use client";

import Link from "next/link";
import Image from "next/image";
import {
  Trophy,
  ShoppingCart,
  MessageSquare,
  Users,
  GraduationCap,
  Video,
  Calendar as CalendarIcon,
  LogOut,
  ChevronUp,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { SidebarItem } from "./sidebar-item";
import { useEffect, useState, useRef } from "react";
import { useUnreadCounts } from "@/app/context/UnreadCountsContext";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { useUser, ClerkLoading, ClerkLoaded } from "@clerk/nextjs";
import { Manrope, Newsreader } from "next/font/google";
import { UpgradeIcon } from "@/components/upgrade-icon";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";

const manrope = Manrope({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-manrope",
});

const newsreader = Newsreader({
  subsets: ["latin"],
  display: "swap",
  style: ["normal", "italic"],
  variable: "--font-newsreader",
});

interface UserData {
  displayName: string;
  email: string;
  role: string;
  avatarSrc: string | null;
}

type Props = {
  className?: string;
};

const SidebarContent = ({ className }: Props) => {
  const { user: clerkUser } = useUser();
  const { user: userData, isLoading: authLoading, isAuthenticated } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(true);
  const { unreadCounts, setUnreadCounts } = useUnreadCounts();
  const router = useRouter();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Derived state
  const isTeacher = userData?.role === "teacher";
  const isLoading = authLoading;

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch unread counts using React Query for better caching
  const { data: unreadCountsData } = useQuery({
    queryKey: ["unreadCounts"],
    queryFn: async () => {
      const res = await fetch("/api/messages/unread-counts");
      if (res.ok) {
        const data = await res.json();
        const counts: { [senderId: string]: number } = {};
        if (Array.isArray(data.unreadCounts)) {
          data.unreadCounts.forEach(
            (item: { senderId: string; unreadCount: number }) => {
              if (
                typeof item?.senderId === "string" &&
                typeof item?.unreadCount === "number"
              ) {
                counts[item.senderId] = item.unreadCount;
              }
            }
          );
        }
        return counts;
      }
      throw new Error("Failed to fetch unread counts");
    },
    enabled: isAuthenticated,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds instead of 5
  });

  // Update unread counts when data changes
  useEffect(() => {
    if (unreadCountsData) {
      setUnreadCounts(unreadCountsData);
    }
  }, [unreadCountsData, setUnreadCounts]);

  // Defer Socket.IO connection until user is authenticated and needs real-time features
  useEffect(() => {
    if (!isAuthenticated) return;

    // Dynamically import and initialize socket only when needed
    let socket: any = null;

    const initializeSocket = async () => {
      try {
        const { getSocket } = await import("@/lib/socketClient");
        socket = getSocket();

        const handleUnreadUpdate = () => {
          // Invalidate React Query cache to refetch unread counts
          // This is more efficient than the previous polling approach
        };

        socket.on("unread-update", handleUnreadUpdate);
      } catch (error) {
        console.error("[Sidebar] Error initializing socket:", error);
      }
    };

    initializeSocket();

    return () => {
      if (socket) {
        socket.off("unread-update");
      }
    };
  }, [isAuthenticated]);

  const distinctSendersWithUnread = Object.values(unreadCounts).filter(
    (count) => count > 0
  ).length;

  if (isLoading) {
    return (
      <div
        className={cn(
          "flex flex-col h-full bg-white dark:bg-black border-r border-gray-100 dark:border-gray-800 px-4 transition-all duration-300 w-64 flex-shrink-0",
          className
        )}
      >
        <div className="pt-6 pb-8 flex items-center gap-x-2 flex-shrink-0">
          <div className="h-7 w-[100px] bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div className="flex-grow overflow-y-auto animate-pulse space-y-6 pr-2">
          <div className="space-y-3">
            <div className="h-2.5 bg-gray-200 dark:bg-gray-700 rounded w-1/3 ml-1"></div>
            <div className="h-11 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-11 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div className="space-y-3">
            <div className="h-2.5 bg-gray-200 dark:bg-gray-700 rounded w-1/3 ml-1"></div>
            <div className="h-11 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-11 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
        <div className="pb-4 pt-3 flex-shrink-0 space-y-2">
          <div className="h-11 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-11 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  const handleSignOut = async () => {
    try {
      await fetch("/api/auth/sign-out", { method: "POST" });
      router.push("/");
    } catch (error) {
      // console.error("Error signing out:", error);
    }
  };

  const iconSize = isCollapsed ? "h-8 w-8" : "h-6 w-6";

  return (
    <div
      aria-expanded={!isCollapsed}
      onMouseEnter={() => setIsCollapsed(false)}
      onMouseLeave={() => setIsCollapsed(true)}
      className={cn(
        "group/sidebar flex flex-col h-full bg-white dark:bg-black border-r border-gray-100 dark:border-gray-800 transition-all duration-300 ease-in-out flex-shrink-0",
        isCollapsed ? "w-20 px-2" : "w-64 px-4",
        className,
        manrope.variable,
        newsreader.variable
      )}
    >
      {/* Header */}
      <div
        className={cn(
          "pt-6 pb-8 flex items-center flex-shrink-0",
          isCollapsed ? "justify-center" : "justify-start"
        )}
      >
        <Link href="/dashboard" className="flex items-center group">
          <div
            aria-label="Oostad Logo"
            className={cn(
              "bg-[#1a1a18] dark:bg-white [mask-repeat:no-repeat] [mask-position:center] transition-all duration-300",
              isCollapsed
                ? "w-16 h-16 [mask-image:url(/oostad2.svg)] [mask-size:contain]"
                : "w-[100px] h-[70px] [mask-image:url(/oostad.svg)] [mask-size:100%_auto]"
            )}
          />
        </Link>
      </div>

      {/* Navigation Menu */}
      <div className="flex flex-col flex-grow overflow-y-auto pr-2 -mr-2 text-[#8b8b89] space-y-6">
        {/* Main Menu Section */}
        <div className="flex flex-col space-y-1">
          <p
            className={cn(
              "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
              isCollapsed
                ? "opacity-0 select-none pointer-events-none"
                : "opacity-100"
            )}
          >
            Main Menu
          </p>
          <div className="space-y-1">
            <SidebarItem
              label="Dashboard"
              href="/dashboard"
              isCollapsed={isCollapsed}
            >
              <svg
                viewBox="0 0 256 256"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                aria-hidden="true"
                className={cn(iconSize)}
              >
                <rect fill="none" height="256" width="256" />
                <circle cx="76" cy="76" r="44" />
                <circle cx="180" cy="76" r="44" />
                <circle cx="76" cy="180" r="44" />
                <circle cx="180" cy="180" r="44" />
              </svg>
            </SidebarItem>
            <SidebarItem
              label="Learn"
              href="/learning-path"
              isCollapsed={isCollapsed}
            >
              <GraduationCap className={cn(iconSize)} />
            </SidebarItem>
            {isTeacher && (
              <SidebarItem
                label="Reports"
                href="/leaderboard"
                isCollapsed={isCollapsed}
              >
                <Trophy className={cn(iconSize)} />
              </SidebarItem>
            )}
          </div>
        </div>

        {/* Communication Section */}
        <div className="flex flex-col space-y-1">
          <p
            className={cn(
              "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
              isCollapsed
                ? "opacity-0 select-none pointer-events-none"
                : "opacity-100"
            )}
          >
            Communication
          </p>
          <div className="space-y-1">
            <SidebarItem
              label="Messages"
              href="/messages"
              badge={distinctSendersWithUnread}
              isCollapsed={isCollapsed}
            >
              <MessageSquare className={cn(iconSize)} />
            </SidebarItem>
            {isTeacher && (
              <SidebarItem
                label="My Students"
                href="/my-students"
                isCollapsed={isCollapsed}
              >
                <Users className={cn(iconSize)} />
              </SidebarItem>
            )}
            <SidebarItem
              label="Virtual Classroom"
              href="/virtualClassroom"
              isCollapsed={isCollapsed}
            >
              <Video className={cn(iconSize)} />
            </SidebarItem>
          </div>
        </div>

        {/* Tools Section */}
        <div className="flex flex-col space-y-1">
          <p
            className={cn(
              "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
              isCollapsed
                ? "opacity-0 select-none pointer-events-none"
                : "opacity-100"
            )}
          >
            Tools
          </p>
          <div className="space-y-1">
            <SidebarItem
              label="Schedule"
              href="/schedule"
              isCollapsed={isCollapsed}
            >
              <CalendarIcon className={cn(iconSize)} />
            </SidebarItem>
            <SidebarItem label="Shop" href="/shop" isCollapsed={isCollapsed}>
              <ShoppingCart className={cn(iconSize)} />
            </SidebarItem>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <div className="pb-4 pt-6 flex-shrink-0 flex flex-col space-y-2 border-t border-gray-100 dark:border-gray-800 mt-auto">
        {/* Upgrade Button */}
        <div className="w-full">
          <Link
            href="/subscription"
            className={cn(
              "relative w-full overflow-hidden group/upgrade z-10 p-0 isolate rounded-lg text-sm font-medium justify-start flex items-center px-3 h-11 transition-all duration-200",
              isCollapsed && "justify-center"
            )}
            aria-label="Upgrade subscription"
          >
            <div className="absolute ring-1 ring-inset group-hover/upgrade:ring-gray-400 dark:group-hover/upgrade:ring-gray-600 ring-gray-300 dark:ring-gray-800 rounded-lg z-10 inset-0 pointer-events-none transition-colors"></div>
            <div className="absolute inset-0 z-0 bg-[repeating-linear-gradient(135deg,theme(colors.white)_0,theme(colors.white)_4px,theme(colors.gray.50)_4px,theme(colors.gray.50)_8px)] dark:bg-[repeating-linear-gradient(135deg,theme(colors.black)_0,theme(colors.black)_4px,theme(colors.zinc.800)_4px,theme(colors.zinc.800)_8px)]"></div>
            <div className="absolute inset-0 group-hover/upgrade:bg-gray-200/70 dark:group-hover/upgrade:bg-white/10 transition-colors duration-200"></div>
            <div
              className={cn(
                "relative z-20 flex items-center w-full",
                isCollapsed ? "justify-center" : "gap-x-3"
              )}
            >
              <div
                className={cn(
                  "flex items-center justify-center flex-shrink-0",
                  isCollapsed ? "w-6 h-6" : "w-4 h-4"
                )}
              >
                <UpgradeIcon className="relative" />
              </div>
              {!isCollapsed && (
                <div className="font-sans flex-grow text-left text-sm font-semibold text-gray-900 dark:text-gray-200 whitespace-nowrap">
                  Upgrade
                </div>
              )}
            </div>
          </Link>
        </div>

        {/* User Profile Section */}
        <div className="relative" ref={dropdownRef}>
          {isDropdownOpen && !isCollapsed && (
            <div className="absolute bottom-full left-0 right-0 mb-2 w-full bg-white dark:bg-zinc-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-lg z-20 overflow-hidden font-sans">
              <div className="px-4 py-3">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                  Profile
                </h3>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 font-serif">
                  Your account information.
                </p>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700">
                <dl className="text-xs">
                  <div className="py-3 grid grid-cols-3 gap-4 px-4 items-center">
                    <dt className="font-medium text-gray-500 dark:text-gray-400 font-serif">
                      Name
                    </dt>
                    <dd className="text-gray-900 dark:text-gray-200 col-span-2 font-sans truncate">
                      {userData?.displayName || "Not available"}
                    </dd>
                  </div>
                  <div className="py-3 grid grid-cols-3 gap-4 px-4 items-center border-t border-gray-100 dark:border-gray-800">
                    <dt className="font-medium text-gray-500 dark:text-gray-400 font-serif">
                      Email
                    </dt>
                    <dd className="text-gray-900 dark:text-gray-200 col-span-2 font-sans truncate">
                      {userData?.email || "Not available"}
                    </dd>
                  </div>
                </dl>
              </div>
              <div className="px-4 py-3 bg-gray-50 dark:bg-zinc-900/50 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleSignOut}
                  className="flex items-center text-red-600 hover:text-red-800 dark:text-red-500 dark:hover:text-red-400 text-sm font-semibold font-sans w-full transition-colors duration-200"
                >
                  <LogOut className="h-4 w-4 mr-3 flex-shrink-0" />
                  Sign out
                </button>
              </div>
            </div>
          )}

          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className={cn(
              "font-sans w-full flex items-center h-11 px-3 rounded-lg text-sm font-semibold text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900/60 focus:outline-none transition-colors duration-200 group",
              isCollapsed && "justify-center"
            )}
          >
            <ClerkLoading>
              <div
                className={cn(
                  "rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse flex-shrink-0",
                  isCollapsed ? "h-8 w-8" : "h-6 w-6"
                )}
              ></div>
              {!isCollapsed && (
                <div className="ml-3 h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse"></div>
              )}
            </ClerkLoading>
            <ClerkLoaded>
              <div
                className={cn(
                  "relative rounded-full ring-1 ring-white dark:ring-black shadow-sm overflow-hidden bg-gray-200 flex-shrink-0",
                  isCollapsed ? "h-8 w-8" : "h-6 w-6"
                )}
              >
                {clerkUser?.imageUrl ? (
                  <Image
                    src={clerkUser.imageUrl}
                    alt="User Avatar"
                    fill
                    sizes={isCollapsed ? "32px" : "24px"}
                    className="object-cover"
                  />
                ) : (
                  <div className="font-sans flex items-center justify-center h-full w-full bg-black text-white text-xs font-bold">
                    {userData?.displayName?.charAt(0)?.toUpperCase() ||
                      clerkUser?.firstName?.charAt(0)?.toUpperCase() ||
                      "?"}
                  </div>
                )}
              </div>
              {!isCollapsed && (
                <span className="ml-3 truncate text-sm font-medium text-left flex-grow min-w-0">
                  {userData?.displayName || "User"}
                </span>
              )}
              {!isCollapsed && (
                <ChevronUp
                  className={cn(
                    "h-4 w-4 ml-2 text-gray-500 transition-transform flex-shrink-0 duration-200",
                    isDropdownOpen ? "rotate-0" : "rotate-180"
                  )}
                />
              )}
            </ClerkLoaded>
          </button>
        </div>
      </div>
    </div>
  );
};

export const Sidebar = ({ className }: Props) => {
  const searchParams = useSearchParams();
  const isEmbedded = searchParams?.get("embedded") === "true";

  if (isEmbedded) {
    return null;
  }

  return <SidebarContent className={className} />;
};

export default Sidebar;
