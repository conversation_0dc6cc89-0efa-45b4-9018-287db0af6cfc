"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useExitModal } from "@/store/use-exit-modal";

export const ExitModal = () => {
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);
  const { isOpen, close } = useExitModal();

  useEffect(() => setIsClient(true), []);

  if (!isClient) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={close}>
      <DialogContent className="max-w-md p-6 bg-white rounded-2xl">
        <DialogHeader>
          <div className="flex items-center w-full justify-center mb-5">
            <Image src="/mascot_sad.svg" alt="Mascot" height={80} width={80} />
          </div>
          <DialogTitle className="text-center font-semibold text-2xl text-gray-800">
            Wait, don&apos;t go!
          </DialogTitle>
          <DialogDescription className="text-center text-base text-gray-600 mt-2">
            You&apos;re about to leave the lesson. Are you sure?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-6 mb-2">
          <div className="flex flex-col gap-y-3 w-full">
            <Button
              onClick={close}
              className="w-full bg-white text-black rounded-xl px-4 py-3 font-medium transition-colors"
              size="lg"
            >
              Keep learning
            </Button>
            <Button
              onClick={() => {
                close();
                router.push("/learn");
              }}
              className="w-full bg-red-500 hover:bg-red-600 text-white rounded-xl px-4 py-3 font-medium transition-colors"
              size="lg"
            >
              End session
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
