"use client";

import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Loader } from "lucide-react";

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { isLoading, isAuthenticated, isFullyOnboarded, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      console.log("[AuthGuard] User not authenticated, redirecting to home");
      router.push("/");
      return;
    }

    if (!isLoading && isAuthenticated && !isFullyOnboarded) {
      console.log("[AuthGuard] User not fully onboarded, redirecting to /joinSchool");
      router.push("/joinSchool");
      return;
    }
  }, [isLoading, isAuthenticated, isFullyOnboarded, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader className="h-8 w-8 animate-spin text-gray-600" />
          <p className="text-sm text-gray-600">Loading your account...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !isFullyOnboarded) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader className="h-8 w-8 animate-spin text-gray-600" />
          <p className="text-sm text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
